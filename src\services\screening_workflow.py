#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整选股工作流管理器
实现需求文档的五步选股流程集成
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from ..database.manager import DatabaseManager
from ..services.sector_manager import SectorManager
from ..services.return_calculator import ReturnCalculator
from ..engines.sector_screening import SectorScreeningEngine, SectorScreeningParams
from ..engines.stock_screening import StockScreeningEngine, StockScreeningParams
from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


@dataclass
class ScreeningWorkflowParams:
    """选股工作流参数"""
    # 时间参数
    start_date: str
    end_date: str
    time_ranges: Optional[List[Tuple[str, str]]] = None  # 多时间段分析
    
    # 市场参数
    market_index: str = "000001.SH"
    sector_types: List[str] = field(default_factory=lambda: ["industry", "concept"])
    
    # 板块筛选参数
    max_sectors: int = 10
    min_sector_relative_strength: float = 0.0
    
    # 个股筛选参数
    stocks_per_sector: int = 3
    min_stock_relative_strength: float = 0.0
    max_stocks_total: int = 50
    
    # 过滤参数
    exclude_st: bool = True
    exclude_suspended: bool = True
    exclude_new_stocks: bool = True
    min_market_cap: Optional[float] = None
    
    # 分析参数
    enable_multi_timeframe: bool = False
    enable_intersection_analysis: bool = False
    min_intersection_appearances: int = 2


@dataclass
class ScreeningWorkflowResult:
    """选股工作流结果"""
    params: ScreeningWorkflowParams
    execution_time: datetime
    
    # 单时间段结果
    sector_result: Optional[Any] = None
    stock_result: Optional[Any] = None
    
    # 多时间段结果
    multi_timeframe_results: List[Any] = field(default_factory=list)
    intersection_analysis: Optional[Dict[str, Any]] = None
    
    # 最终候选股票池
    final_stock_pool: List[Dict[str, Any]] = field(default_factory=list)
    
    # 统计信息
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'params': {
                'start_date': self.params.start_date,
                'end_date': self.params.end_date,
                'market_index': self.params.market_index,
                'max_sectors': self.params.max_sectors,
                'stocks_per_sector': self.params.stocks_per_sector,
                'max_stocks_total': self.params.max_stocks_total
            },
            'execution_time': self.execution_time.isoformat(),
            'sector_summary': self.sector_result.to_dict() if self.sector_result else None,
            'stock_summary': self.stock_result.to_dict() if self.stock_result else None,
            'final_stock_pool': self.final_stock_pool,
            'statistics': self.statistics
        }


class ScreeningWorkflow:
    """选股工作流管理器"""
    
    def __init__(self, 
                 db_manager: DatabaseManager,
                 sector_manager: SectorManager,
                 return_calculator: ReturnCalculator):
        self.db_manager = db_manager
        self.sector_manager = sector_manager
        self.return_calculator = return_calculator
        
        # 初始化筛选引擎
        self.sector_engine = SectorScreeningEngine(sector_manager, return_calculator)
        self.stock_engine = StockScreeningEngine(sector_manager, return_calculator)
        
        self.is_running = False
    
    async def execute_screening(self, 
                              params: ScreeningWorkflowParams,
                              progress_callback: Optional[Callable] = None) -> ScreeningWorkflowResult:
        """
        执行完整的选股工作流
        
        Args:
            params: 工作流参数
            progress_callback: 进度回调函数
            
        Returns:
            ScreeningWorkflowResult: 工作流结果
        """
        try:
            if self.is_running:
                raise CalculationError("选股工作流正在运行中")
            
            self.is_running = True
            start_time = datetime.now()
            
            logger.info("开始执行威科夫相对强弱选股工作流")
            logger.info(f"参数: {params.start_date} -> {params.end_date}, 市场指数: {params.market_index}")
            
            result = ScreeningWorkflowResult(
                params=params,
                execution_time=start_time
            )
            
            # 第一步：数据准备与维护
            if progress_callback:
                progress_callback("第一步：数据准备与维护", 5)
            
            await self._prepare_data(params)
            
            # 第二步：时间区间设定与基础计算
            if progress_callback:
                progress_callback("第二步：涨幅预计算", 15)
            
            await self._precompute_returns(params)
            
            if params.enable_multi_timeframe and params.time_ranges:
                # 多时间段分析
                result = await self._execute_multi_timeframe_screening(params, result, progress_callback)
            else:
                # 单时间段分析
                result = await self._execute_single_timeframe_screening(params, result, progress_callback)
            
            # 第五步：结果汇总与输出
            if progress_callback:
                progress_callback("第五步：结果汇总与输出", 95)
            
            await self._finalize_results(result)
            
            # 保存结果到数据库
            await self._save_results(result)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"选股工作流执行完成，耗时: {execution_time:.2f}秒")
            
            if progress_callback:
                progress_callback("选股工作流执行完成", 100)
            
            return result
            
        except Exception as e:
            logger.error(f"选股工作流执行失败: {e}")
            raise CalculationError(f"选股工作流执行失败: {e}")
        finally:
            self.is_running = False
    
    async def _prepare_data(self, params: ScreeningWorkflowParams):
        """数据准备与维护"""
        try:
            # 检查板块数据是否存在
            sector_list = self.sector_manager.get_sector_list()
            if not sector_list:
                logger.info("初始化板块数据...")
                await self.sector_manager.initialize_sectors()
            
            # 检查板块历史数据
            # 这里可以添加数据完整性检查和更新逻辑
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            raise
    
    async def _precompute_returns(self, params: ScreeningWorkflowParams):
        """涨幅预计算"""
        try:
            time_ranges = []
            
            if params.time_ranges:
                time_ranges = params.time_ranges
            else:
                time_ranges = [(params.start_date, params.end_date)]
            
            # 预计算收益率
            await self.return_calculator.precompute_returns(time_ranges)
            
        except Exception as e:
            logger.error(f"涨幅预计算失败: {e}")
            raise
    
    async def _execute_single_timeframe_screening(self, 
                                                params: ScreeningWorkflowParams,
                                                result: ScreeningWorkflowResult,
                                                progress_callback: Optional[Callable]) -> ScreeningWorkflowResult:
        """执行单时间段筛选"""
        try:
            # 第三步：板块相对强弱筛选
            if progress_callback:
                progress_callback("第三步：板块相对强弱筛选", 30)
            
            sector_params = SectorScreeningParams(
                start_date=params.start_date,
                end_date=params.end_date,
                market_index=params.market_index,
                sector_types=params.sector_types,
                min_relative_strength=params.min_sector_relative_strength,
                max_sectors=params.max_sectors
            )
            
            sector_result = await self.sector_engine.screen_strong_sectors(sector_params)
            result.sector_result = sector_result
            
            # 第四步：板块内个股筛选
            if progress_callback:
                progress_callback("第四步：板块内个股筛选", 60)
            
            stock_params = StockScreeningParams(
                start_date=params.start_date,
                end_date=params.end_date,
                stocks_per_sector=params.stocks_per_sector,
                min_relative_strength=params.min_stock_relative_strength,
                exclude_st=params.exclude_st,
                exclude_suspended=params.exclude_suspended,
                exclude_new_stocks=params.exclude_new_stocks,
                min_market_cap=params.min_market_cap,
                max_stocks_total=params.max_stocks_total
            )
            
            stock_result = await self.stock_engine.screen_stocks_in_sectors(
                sector_result, stock_params
            )
            result.stock_result = stock_result
            
            return result
            
        except Exception as e:
            logger.error(f"单时间段筛选失败: {e}")
            raise
    
    async def _execute_multi_timeframe_screening(self, 
                                               params: ScreeningWorkflowParams,
                                               result: ScreeningWorkflowResult,
                                               progress_callback: Optional[Callable]) -> ScreeningWorkflowResult:
        """执行多时间段筛选"""
        try:
            if not params.time_ranges:
                raise ValueError("多时间段分析需要提供时间区间列表")
            
            # 第三步：多时间段板块筛选
            if progress_callback:
                progress_callback("第三步：多时间段板块筛选", 30)
            
            base_sector_params = SectorScreeningParams(
                start_date="",  # 将被覆盖
                end_date="",    # 将被覆盖
                market_index=params.market_index,
                sector_types=params.sector_types,
                min_relative_strength=params.min_sector_relative_strength,
                max_sectors=params.max_sectors
            )
            
            sector_results = await self.sector_engine.screen_multiple_timeframes(
                params.time_ranges, base_sector_params
            )
            
            # 第四步：多时间段个股筛选
            if progress_callback:
                progress_callback("第四步：多时间段个股筛选", 60)
            
            stock_results = []
            for sector_result in sector_results:
                stock_params = StockScreeningParams(
                    start_date=sector_result.params.start_date,
                    end_date=sector_result.params.end_date,
                    stocks_per_sector=params.stocks_per_sector,
                    min_relative_strength=params.min_stock_relative_strength,
                    exclude_st=params.exclude_st,
                    exclude_suspended=params.exclude_suspended,
                    exclude_new_stocks=params.exclude_new_stocks,
                    min_market_cap=params.min_market_cap,
                    max_stocks_total=params.max_stocks_total
                )
                
                stock_result = await self.stock_engine.screen_stocks_in_sectors(
                    sector_result, stock_params
                )
                stock_results.append(stock_result)
            
            result.multi_timeframe_results = list(zip(sector_results, stock_results))
            
            # 交集分析
            if params.enable_intersection_analysis:
                if progress_callback:
                    progress_callback("执行交集分析", 80)
                
                result.intersection_analysis = self._analyze_multi_timeframe_intersection(
                    stock_results, params.min_intersection_appearances
                )
            
            return result
            
        except Exception as e:
            logger.error(f"多时间段筛选失败: {e}")
            raise
    
    def _analyze_multi_timeframe_intersection(self, 
                                            stock_results: List[Any],
                                            min_appearances: int) -> Dict[str, Any]:
        """分析多时间段交集"""
        try:
            # 统计股票出现次数
            stock_counts = {}
            stock_details = {}
            
            for result in stock_results:
                time_range = f"{result.params.start_date}_{result.params.end_date}"
                
                for stock in result.selected_stocks:
                    code = stock.stock_code
                    
                    if code not in stock_counts:
                        stock_counts[code] = 0
                        stock_details[code] = {
                            'stock_name': stock.stock_name,
                            'appearances': [],
                            'avg_relative_strength': 0.0,
                            'avg_final_score': 0.0
                        }
                    
                    stock_counts[code] += 1
                    stock_details[code]['appearances'].append({
                        'time_range': time_range,
                        'sector_name': stock.sector_name,
                        'relative_strength': stock.relative_strength,
                        'final_score': stock.final_score
                    })
            
            # 筛选频繁出现的股票
            frequent_stocks = []
            for stock_code, count in stock_counts.items():
                if count >= min_appearances:
                    details = stock_details[stock_code]
                    appearances = details['appearances']
                    
                    avg_strength = sum(a['relative_strength'] for a in appearances) / len(appearances)
                    avg_score = sum(a['final_score'] for a in appearances) / len(appearances)
                    
                    frequent_stocks.append({
                        'stock_code': stock_code,
                        'stock_name': details['stock_name'],
                        'appearance_count': count,
                        'appearance_rate': count / len(stock_results),
                        'avg_relative_strength': avg_strength,
                        'avg_final_score': avg_score,
                        'appearances': appearances
                    })
            
            # 排序
            frequent_stocks.sort(
                key=lambda x: (x['appearance_count'], x['avg_final_score']), 
                reverse=True
            )
            
            return {
                'total_timeframes': len(stock_results),
                'min_appearances': min_appearances,
                'frequent_stocks': frequent_stocks,
                'intersection_count': len(frequent_stocks)
            }
            
        except Exception as e:
            logger.error(f"多时间段交集分析失败: {e}")
            return {}
    
    async def _finalize_results(self, result: ScreeningWorkflowResult):
        """结果汇总与输出"""
        try:
            # 生成最终候选股票池
            if result.stock_result:
                # 单时间段结果
                result.final_stock_pool = [
                    {
                        'stock_code': s.stock_code,
                        'stock_name': s.stock_name,
                        'sector_name': s.sector_name,
                        'stock_return': s.stock_return,
                        'sector_return': s.sector_return,
                        'relative_strength': s.relative_strength,
                        'final_score': s.final_score,
                        'rank_in_sector': s.rank_in_sector,
                        'sector_rank': s.sector_rank
                    }
                    for s in result.stock_result.selected_stocks
                ]
            elif result.intersection_analysis:
                # 多时间段交集结果
                result.final_stock_pool = result.intersection_analysis['frequent_stocks']
            
            # 生成统计信息
            result.statistics = self._generate_statistics(result)
            
        except Exception as e:
            logger.error(f"结果汇总失败: {e}")
            raise
    
    def _generate_statistics(self, result: ScreeningWorkflowResult) -> Dict[str, Any]:
        """生成统计信息"""
        try:
            stats = {
                'execution_summary': {
                    'execution_time': result.execution_time.isoformat(),
                    'is_multi_timeframe': bool(result.multi_timeframe_results),
                    'final_stock_count': len(result.final_stock_pool)
                }
            }
            
            if result.sector_result:
                stats['sector_summary'] = {
                    'market_return': result.sector_result.market_return,
                    'total_sectors': result.sector_result.total_sectors,
                    'selected_sectors': result.sector_result.selected_count
                }
            
            if result.stock_result:
                stats['stock_summary'] = {
                    'total_candidates': result.stock_result.total_candidates,
                    'selected_stocks': result.stock_result.selected_count,
                    'sectors_processed': result.stock_result.sectors_processed
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"生成统计信息失败: {e}")
            return {}
    
    async def _save_results(self, result: ScreeningWorkflowResult):
        """保存结果到数据库"""
        try:
            # 这里可以实现结果保存逻辑
            # 保存到 screening_results 表
            pass
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            # 不抛出异常，避免影响主流程
