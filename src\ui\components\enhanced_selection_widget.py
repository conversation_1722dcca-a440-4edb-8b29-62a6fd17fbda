#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强选股配置界面
集成板块筛选和个股筛选的双重筛选工作流
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QComboBox, QSpinBox, QDoubleSpinBox, 
    QCheckBox, QPushButton, QTableWidget, QTableWidgetItem,
    QGroupBox, QTabWidget, QSplitter, QProgressBar,
    QTextEdit, QDateEdit, QSlider, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, QDate, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPalette

from .sector_screening_widget import SectorScreeningWidget
from .selection_widget import SelectionWidget
from ...utils.logger import get_logger
from ..utils.error_handler import UIErrorHandler
from ..utils.export_manager import ExportManager

logger = get_logger(__name__)


class EnhancedSelectionWidget(QWidget):
    """增强选股配置界面 - 双重筛选工作流"""
    
    # 信号定义
    workflow_started = pyqtSignal(dict)  # 工作流开始
    workflow_completed = pyqtSignal(dict)  # 工作流完成
    sector_phase_completed = pyqtSignal(list)  # 板块筛选阶段完成
    stock_phase_completed = pyqtSignal(list)  # 个股筛选阶段完成
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化增强选股配置界面
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 状态变量
        self.is_running_workflow = False
        self.current_phase = "idle"  # idle, sector_screening, stock_screening
        self.selected_sectors: List[Dict] = []
        self.final_stocks: List[Dict] = []

        # 错误处理和导出管理
        self.error_handler = UIErrorHandler(self)
        self.export_manager = ExportManager(self)
        
        # UI组件引用
        self.workflow_tabs: Optional[QTabWidget] = None
        self.sector_widget: Optional[SectorScreeningWidget] = None
        self.stock_widget: Optional[SelectionWidget] = None
        self.workflow_control: Optional[QWidget] = None
        self.progress_widget: Optional[QWidget] = None
        
        self._init_ui()
        self._setup_connections()
        
        logger.info("增强选股配置界面初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题区域
        title_widget = self._create_title_widget()
        layout.addWidget(title_widget)
        
        # 工作流控制区域
        self.workflow_control = self._create_workflow_control()
        layout.addWidget(self.workflow_control)
        
        # 进度展示区域
        self.progress_widget = self._create_progress_widget()
        layout.addWidget(self.progress_widget)
        
        # 主要内容区域
        content_widget = self._create_content_widget()
        layout.addWidget(content_widget)
    
    def _create_title_widget(self) -> QWidget:
        """创建标题区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # 主标题
        title_label = QLabel("🎯 双重相对强弱筛选系统")
        title_label.setFont(QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # 状态指示器
        self.status_label = QLabel("💤 就绪")
        self.status_label.setFont(QFont("Microsoft YaHei UI", 10))
        self.status_label.setStyleSheet("""
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 15px;
            padding: 5px 15px;
            margin: 5px;
        """)
        layout.addWidget(self.status_label)
        
        return widget
    
    def _create_workflow_control(self) -> QWidget:
        """创建工作流控制区域"""
        widget = QGroupBox("🔄 筛选工作流控制")
        layout = QHBoxLayout(widget)
        
        # 工作流步骤说明
        steps_label = QLabel("""
        <b>五步筛选流程：</b><br/>
        1️⃣ 板块相对强弱筛选 → 2️⃣ 强势板块确认 → 3️⃣ 个股威科夫分析 → 4️⃣ 个股相对强弱验证 → 5️⃣ 最终选股结果
        """)
        steps_label.setStyleSheet("margin: 10px; line-height: 1.4;")
        layout.addWidget(steps_label)
        
        layout.addStretch()
        
        # 控制按钮
        button_layout = QVBoxLayout()
        
        self.start_workflow_btn = QPushButton("🚀 启动双重筛选")
        self.start_workflow_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.start_workflow_btn.clicked.connect(self._start_workflow)
        button_layout.addWidget(self.start_workflow_btn)
        
        self.stop_workflow_btn = QPushButton("⏹️ 停止筛选")
        self.stop_workflow_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.stop_workflow_btn.clicked.connect(self._stop_workflow)
        self.stop_workflow_btn.setEnabled(False)
        button_layout.addWidget(self.stop_workflow_btn)
        
        layout.addLayout(button_layout)
        
        return widget
    
    def _create_progress_widget(self) -> QWidget:
        """创建进度展示区域"""
        widget = QGroupBox("📊 筛选进度")
        layout = QVBoxLayout(widget)
        
        # 阶段进度条
        progress_layout = QHBoxLayout()
        
        # 阶段指示器
        self.phase_indicators = {}
        phases = [
            ("sector", "1️⃣ 板块筛选"),
            ("confirm", "2️⃣ 板块确认"),
            ("wyckoff", "3️⃣ 威科夫分析"),
            ("stock_rs", "4️⃣ 个股相对强弱"),
            ("final", "5️⃣ 最终结果")
        ]
        
        for phase_id, phase_name in phases:
            indicator = QLabel(phase_name)
            indicator.setStyleSheet("""
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 8px 12px;
                margin: 2px;
                font-weight: bold;
            """)
            indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.phase_indicators[phase_id] = indicator
            progress_layout.addWidget(indicator)
        
        layout.addLayout(progress_layout)
        
        # 详细进度条
        self.detail_progress = QProgressBar()
        self.detail_progress.setVisible(False)
        layout.addWidget(self.detail_progress)
        
        # 进度信息
        self.progress_info = QLabel("等待开始筛选...")
        self.progress_info.setStyleSheet("margin: 5px; color: #7f8c8d;")
        layout.addWidget(self.progress_info)
        
        return widget
    
    def _create_content_widget(self) -> QWidget:
        """创建主要内容区域"""
        # 创建标签页控件
        self.workflow_tabs = QTabWidget()
        
        # 板块筛选标签页
        self.sector_widget = SectorScreeningWidget()
        self.workflow_tabs.addTab(self.sector_widget, "📊 第一步：板块筛选")
        
        # 个股筛选标签页
        self.stock_widget = SelectionWidget()
        self.workflow_tabs.addTab(self.stock_widget, "🎯 第二步：个股筛选")
        
        # 结果汇总标签页
        results_widget = self._create_results_widget()
        self.workflow_tabs.addTab(results_widget, "📈 第三步：结果汇总")
        
        # 默认禁用后续标签页
        self.workflow_tabs.setTabEnabled(1, False)
        self.workflow_tabs.setTabEnabled(2, False)
        
        return self.workflow_tabs
    
    def _create_results_widget(self) -> QWidget:
        """创建结果汇总区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 结果统计
        stats_group = QGroupBox("📊 筛选统计")
        stats_layout = QGridLayout(stats_group)
        
        stats_layout.addWidget(QLabel("强势板块数量:"), 0, 0)
        self.sector_count_label = QLabel("0")
        self.sector_count_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(self.sector_count_label, 0, 1)
        
        stats_layout.addWidget(QLabel("候选个股数量:"), 0, 2)
        self.candidate_count_label = QLabel("0")
        self.candidate_count_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        stats_layout.addWidget(self.candidate_count_label, 0, 3)
        
        stats_layout.addWidget(QLabel("最终选股数量:"), 1, 0)
        self.final_count_label = QLabel("0")
        self.final_count_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(self.final_count_label, 1, 1)
        
        stats_layout.addWidget(QLabel("筛选成功率:"), 1, 2)
        self.success_rate_label = QLabel("0%")
        self.success_rate_label.setStyleSheet("font-weight: bold; color: #9b59b6;")
        stats_layout.addWidget(self.success_rate_label, 1, 3)
        
        layout.addWidget(stats_group)
        
        # 最终结果表格
        results_group = QGroupBox("🎯 最终选股结果")
        results_layout = QVBoxLayout(results_group)
        
        self.final_results_table = QTableWidget()
        self.final_results_table.setColumnCount(8)
        self.final_results_table.setHorizontalHeaderLabels([
            "排名", "股票代码", "股票名称", "所属板块", 
            "威科夫评分", "相对强弱度", "综合评分", "推荐度"
        ])
        self.final_results_table.setAlternatingRowColors(True)
        results_layout.addWidget(self.final_results_table)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        export_layout.addStretch()
        
        export_btn = QPushButton("📤 导出选股结果")
        export_btn.clicked.connect(self._export_final_results)
        export_layout.addWidget(export_btn)
        
        save_strategy_btn = QPushButton("💾 保存筛选策略")
        save_strategy_btn.clicked.connect(self._save_strategy)
        export_layout.addWidget(save_strategy_btn)
        
        results_layout.addLayout(export_layout)
        layout.addWidget(results_group)
        
        return widget

    def _setup_connections(self):
        """设置信号连接"""
        # 板块筛选信号连接
        if self.sector_widget:
            self.sector_widget.screening_completed.connect(self._on_sector_screening_completed)
            self.sector_widget.sector_selected.connect(self._on_sector_selected)

        # 个股筛选信号连接
        if self.stock_widget:
            self.stock_widget.selection_completed.connect(self._on_stock_selection_completed)

    def _start_workflow(self):
        """启动双重筛选工作流"""
        if self.is_running_workflow:
            return

        # 更新状态
        self.is_running_workflow = True
        self.current_phase = "sector_screening"
        self.selected_sectors.clear()
        self.final_stocks.clear()

        # 更新UI状态
        self.start_workflow_btn.setEnabled(False)
        self.stop_workflow_btn.setEnabled(True)
        self.detail_progress.setVisible(True)
        self.detail_progress.setValue(0)

        # 更新阶段指示器
        self._update_phase_indicator("sector", "active")
        self._update_progress_info("开始板块相对强弱筛选...")

        # 切换到板块筛选标签页
        self.workflow_tabs.setCurrentIndex(0)

        # 发送工作流开始信号
        self.workflow_started.emit({
            'timestamp': datetime.now().isoformat(),
            'phase': 'sector_screening'
        })

        # 启动板块筛选
        if self.sector_widget:
            self.sector_widget._start_screening()

        logger.info("双重筛选工作流已启动")

    def _stop_workflow(self):
        """停止双重筛选工作流"""
        if not self.is_running_workflow:
            return

        # 更新状态
        self.is_running_workflow = False
        self.current_phase = "idle"

        # 更新UI状态
        self.start_workflow_btn.setEnabled(True)
        self.stop_workflow_btn.setEnabled(False)
        self.detail_progress.setVisible(False)

        # 重置阶段指示器
        for phase_id in self.phase_indicators:
            self._update_phase_indicator(phase_id, "idle")

        self._update_progress_info("筛选工作流已停止")
        self._update_status("💤 就绪")

        # 停止当前筛选
        if self.current_phase == "sector_screening" and self.sector_widget:
            self.sector_widget._stop_screening()
        elif self.current_phase == "stock_screening" and self.stock_widget:
            # TODO: 添加个股筛选停止方法
            pass

        logger.info("双重筛选工作流已停止")

    def _on_sector_screening_completed(self, results: Dict[str, Any]):
        """板块筛选完成事件"""
        if not self.is_running_workflow or self.current_phase != "sector_screening":
            return

        # 保存板块筛选结果
        self.selected_sectors = results.get('results', [])
        sector_count = len(self.selected_sectors)

        # 更新统计信息
        self.sector_count_label.setText(str(sector_count))

        # 更新阶段指示器
        self._update_phase_indicator("sector", "completed")
        self._update_phase_indicator("confirm", "active")

        # 更新进度
        self.detail_progress.setValue(20)
        self._update_progress_info(f"板块筛选完成，发现 {sector_count} 个强势板块")
        self._update_status("🔍 板块确认中")

        # 发送板块阶段完成信号
        self.sector_phase_completed.emit(self.selected_sectors)

        if sector_count > 0:
            # 启动个股筛选阶段
            QTimer.singleShot(2000, self._start_stock_screening)
        else:
            # 没有强势板块，结束工作流
            self._complete_workflow_with_no_results()

        logger.info(f"板块筛选阶段完成，强势板块数量：{sector_count}")

    def _start_stock_screening(self):
        """启动个股筛选阶段"""
        if not self.is_running_workflow:
            return

        # 更新状态
        self.current_phase = "stock_screening"

        # 更新阶段指示器
        self._update_phase_indicator("confirm", "completed")
        self._update_phase_indicator("wyckoff", "active")

        # 更新进度
        self.detail_progress.setValue(40)
        self._update_progress_info("开始威科夫分析和个股筛选...")
        self._update_status("🎯 个股筛选中")

        # 启用个股筛选标签页
        self.workflow_tabs.setTabEnabled(1, True)
        self.workflow_tabs.setCurrentIndex(1)

        # 配置个股筛选参数（基于选中的板块）
        if self.stock_widget and self.selected_sectors:
            # TODO: 将选中的板块信息传递给个股筛选控件
            sector_codes = [sector['code'] for sector in self.selected_sectors]
            # self.stock_widget.set_target_sectors(sector_codes)

        # 模拟个股筛选过程
        self._simulate_stock_screening()

    def _simulate_stock_screening(self):
        """模拟个股筛选过程"""
        # 模拟威科夫分析阶段
        QTimer.singleShot(1000, lambda: self._update_wyckoff_progress())
        QTimer.singleShot(3000, lambda: self._update_stock_rs_progress())
        QTimer.singleShot(5000, lambda: self._complete_stock_screening())

    def _update_wyckoff_progress(self):
        """更新威科夫分析进度"""
        if not self.is_running_workflow:
            return

        self._update_phase_indicator("wyckoff", "completed")
        self._update_phase_indicator("stock_rs", "active")
        self.detail_progress.setValue(60)
        self._update_progress_info("威科夫分析完成，开始个股相对强弱验证...")

    def _update_stock_rs_progress(self):
        """更新个股相对强弱进度"""
        if not self.is_running_workflow:
            return

        self._update_phase_indicator("stock_rs", "completed")
        self._update_phase_indicator("final", "active")
        self.detail_progress.setValue(80)
        self._update_progress_info("个股相对强弱验证完成，生成最终结果...")

    def _complete_stock_screening(self):
        """完成个股筛选"""
        if not self.is_running_workflow:
            return

        # 生成模拟的最终选股结果
        self._generate_final_results()

        # 更新阶段指示器
        self._update_phase_indicator("final", "completed")

        # 更新进度
        self.detail_progress.setValue(100)
        final_count = len(self.final_stocks)
        self._update_progress_info(f"双重筛选完成！最终选出 {final_count} 只优质股票")
        self._update_status("✅ 筛选完成")

        # 更新统计信息
        self._update_statistics()

        # 启用结果标签页
        self.workflow_tabs.setTabEnabled(2, True)
        self.workflow_tabs.setCurrentIndex(2)

        # 填充结果表格
        self._populate_final_results_table()

        # 完成工作流
        self._complete_workflow()

    def _generate_final_results(self):
        """生成最终选股结果"""
        # 基于选中的板块生成模拟的个股结果
        mock_stocks = []

        for i, sector in enumerate(self.selected_sectors[:3]):  # 取前3个强势板块
            sector_stocks = [
                {
                    "rank": len(mock_stocks) + 1,
                    "code": f"{sector['code'][:2]}{1000 + j:04d}",
                    "name": f"{sector['name']}龙头{j+1}",
                    "sector": sector['name'],
                    "wyckoff_score": round(85 - j * 5 + i * 2, 1),
                    "relative_strength": round(sector['relative_strength'] * 0.8 + j * 0.5, 2),
                    "composite_score": 0,
                    "recommendation": ""
                }
                for j in range(2)  # 每个板块选2只股票
            ]

            # 计算综合评分和推荐度
            for stock in sector_stocks:
                stock["composite_score"] = round(
                    stock["wyckoff_score"] * 0.6 + stock["relative_strength"] * 0.4, 1
                )

                if stock["composite_score"] >= 80:
                    stock["recommendation"] = "强烈推荐"
                elif stock["composite_score"] >= 70:
                    stock["recommendation"] = "推荐"
                else:
                    stock["recommendation"] = "观察"

            mock_stocks.extend(sector_stocks)

        # 按综合评分排序
        mock_stocks.sort(key=lambda x: x["composite_score"], reverse=True)

        # 重新分配排名
        for i, stock in enumerate(mock_stocks):
            stock["rank"] = i + 1

        self.final_stocks = mock_stocks

    def _update_statistics(self):
        """更新统计信息"""
        sector_count = len(self.selected_sectors)
        candidate_count = sector_count * 10  # 假设每个板块有10个候选股票
        final_count = len(self.final_stocks)

        self.sector_count_label.setText(str(sector_count))
        self.candidate_count_label.setText(str(candidate_count))
        self.final_count_label.setText(str(final_count))

        # 计算成功率
        if candidate_count > 0:
            success_rate = round(final_count / candidate_count * 100, 1)
            self.success_rate_label.setText(f"{success_rate}%")
        else:
            self.success_rate_label.setText("0%")

    def _populate_final_results_table(self):
        """填充最终结果表格"""
        self.final_results_table.setRowCount(len(self.final_stocks))

        for row, stock in enumerate(self.final_stocks):
            self.final_results_table.setItem(row, 0, QTableWidgetItem(str(stock["rank"])))
            self.final_results_table.setItem(row, 1, QTableWidgetItem(stock["code"]))
            self.final_results_table.setItem(row, 2, QTableWidgetItem(stock["name"]))
            self.final_results_table.setItem(row, 3, QTableWidgetItem(stock["sector"]))
            self.final_results_table.setItem(row, 4, QTableWidgetItem(str(stock["wyckoff_score"])))
            self.final_results_table.setItem(row, 5, QTableWidgetItem(str(stock["relative_strength"])))
            self.final_results_table.setItem(row, 6, QTableWidgetItem(str(stock["composite_score"])))
            self.final_results_table.setItem(row, 7, QTableWidgetItem(stock["recommendation"]))

            # 设置行颜色
            if stock["recommendation"] == "强烈推荐":
                color = QColor(220, 255, 220)  # 浅绿色
            elif stock["recommendation"] == "推荐":
                color = QColor(255, 255, 220)  # 浅黄色
            else:
                color = QColor(255, 240, 240)  # 浅红色

            for col in range(8):
                item = self.final_results_table.item(row, col)
                if item:
                    item.setBackground(color)

        # 自动调整列宽
        self.final_results_table.resizeColumnsToContents()

    def _complete_workflow(self):
        """完成工作流"""
        # 更新状态
        self.is_running_workflow = False
        self.current_phase = "idle"

        # 更新UI状态
        self.start_workflow_btn.setEnabled(True)
        self.stop_workflow_btn.setEnabled(False)

        # 隐藏进度条
        QTimer.singleShot(3000, lambda: self.detail_progress.setVisible(False))

        # 发送工作流完成信号
        self.workflow_completed.emit({
            'timestamp': datetime.now().isoformat(),
            'sector_count': len(self.selected_sectors),
            'final_count': len(self.final_stocks),
            'results': self.final_stocks
        })

        logger.info(f"双重筛选工作流完成，最终选股数量：{len(self.final_stocks)}")

    def _complete_workflow_with_no_results(self):
        """没有结果时完成工作流"""
        # 更新状态
        self.is_running_workflow = False
        self.current_phase = "idle"

        # 更新UI状态
        self.start_workflow_btn.setEnabled(True)
        self.stop_workflow_btn.setEnabled(False)
        self.detail_progress.setVisible(False)

        # 重置阶段指示器
        for phase_id in self.phase_indicators:
            self._update_phase_indicator(phase_id, "idle")

        self._update_progress_info("未发现强势板块，请调整筛选条件")
        self._update_status("⚠️ 无结果")

        logger.info("双重筛选工作流完成，但未发现强势板块")

    def _update_phase_indicator(self, phase_id: str, status: str):
        """更新阶段指示器"""
        if phase_id not in self.phase_indicators:
            return

        indicator = self.phase_indicators[phase_id]

        if status == "idle":
            indicator.setStyleSheet("""
                background-color: #ecf0f1;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 8px 12px;
                margin: 2px;
                font-weight: bold;
                color: #7f8c8d;
            """)
        elif status == "active":
            indicator.setStyleSheet("""
                background-color: #3498db;
                border: 2px solid #2980b9;
                border-radius: 10px;
                padding: 8px 12px;
                margin: 2px;
                font-weight: bold;
                color: white;
            """)
        elif status == "completed":
            indicator.setStyleSheet("""
                background-color: #27ae60;
                border: 2px solid #229954;
                border-radius: 10px;
                padding: 8px 12px;
                margin: 2px;
                font-weight: bold;
                color: white;
            """)

    def _update_progress_info(self, message: str):
        """更新进度信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.progress_info.setText(f"[{timestamp}] {message}")

    def _update_status(self, status: str):
        """更新状态标签"""
        self.status_label.setText(status)

    def _on_sector_selected(self, sector_code: str):
        """板块选择事件"""
        logger.info(f"板块选择：{sector_code}")

    def _on_stock_selection_completed(self, results: Dict[str, Any]):
        """个股筛选完成事件"""
        logger.info(f"个股筛选完成：{results}")
        # 发送个股阶段完成信号
        self.stock_phase_completed.emit(results.get('selected_stocks', []))

    def _export_final_results(self):
        """导出最终结果"""
        if not self.final_stocks:
            self.error_handler.handle_warning("没有可导出的选股结果")
            return

        try:
            # 准备导出数据
            export_data = {
                'sector_results': self.selected_sectors,
                'stock_results': self.final_stocks,
                'parameters': {
                    'workflow_type': '双重相对强弱筛选',
                    'sector_count': len(self.selected_sectors),
                    'final_count': len(self.final_stocks)
                },
                'statistics': {
                    'sector_count': len(self.selected_sectors),
                    'candidate_count': len(self.selected_sectors) * 10,  # 估算
                    'final_count': len(self.final_stocks),
                    'success_rate': f"{len(self.final_stocks) / max(len(self.selected_sectors) * 10, 1) * 100:.1f}%",
                    'export_time': datetime.now().isoformat()
                }
            }

            # 执行导出
            success = self.export_manager.export_results(export_data, "report")
            if not success:
                self.error_handler.handle_error("export_failed", "导出启动失败")

        except Exception as e:
            self.error_handler.handle_error("export_failed", str(e))
            logger.error(f"导出失败: {e}")

    def _save_strategy(self):
        """保存筛选策略"""
        # TODO: 实现策略保存功能
        logger.info("保存筛选策略")

    def get_workflow_results(self) -> Dict[str, Any]:
        """获取工作流结果"""
        return {
            'selected_sectors': self.selected_sectors,
            'final_stocks': self.final_stocks,
            'is_running': self.is_running_workflow,
            'current_phase': self.current_phase
        }

    def reset_workflow(self):
        """重置工作流"""
        if self.is_running_workflow:
            self._stop_workflow()

        # 清空结果
        self.selected_sectors.clear()
        self.final_stocks.clear()

        # 重置UI
        self.final_results_table.setRowCount(0)
        self.sector_count_label.setText("0")
        self.candidate_count_label.setText("0")
        self.final_count_label.setText("0")
        self.success_rate_label.setText("0%")

        # 禁用后续标签页
        self.workflow_tabs.setTabEnabled(1, False)
        self.workflow_tabs.setTabEnabled(2, False)
        self.workflow_tabs.setCurrentIndex(0)

        # 重置阶段指示器
        for phase_id in self.phase_indicators:
            self._update_phase_indicator(phase_id, "idle")

        self._update_progress_info("工作流已重置，等待开始筛选...")
        self._update_status("💤 就绪")

        logger.info("双重筛选工作流已重置")
