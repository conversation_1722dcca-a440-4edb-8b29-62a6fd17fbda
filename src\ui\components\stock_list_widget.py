"""
股票列表控件

显示股票列表，支持搜索、过滤和排序功能
"""

from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLineEdit, QPushButton, QLabel, QComboBox, QHeaderView, QAbstractItemView,
    QGroupBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from ...utils.logger import get_logger
from ...services.data_service import DataService, StockInfo

logger = get_logger(__name__)


class StockListWidget(QWidget):
    """股票列表控件"""
    
    # 信号定义
    stock_selected = pyqtSignal(str)  # 股票代码
    stocks_loaded = pyqtSignal(int)   # 加载的股票数量
    
    def __init__(self, parent: Optional[QWidget] = None, data_service=None):
        """
        初始化股票列表控件

        Args:
            parent: 父控件
            data_service: 数据服务实例
        """
        super().__init__(parent)

        # 数据服务
        self.data_service = data_service or DataService()

        # 数据
        self.stocks_data: List[StockInfo] = []
        self.filtered_data: List[StockInfo] = []

        # 控件引用
        self.search_input: Optional[QLineEdit] = None
        self.filter_combo: Optional[QComboBox] = None
        self.table: Optional[QTableWidget] = None
        self.status_label: Optional[QLabel] = None
        self.refresh_button: Optional[QPushButton] = None

        # 搜索定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        
        self._init_ui()
        self._setup_table()
        self._load_stock_data()

        logger.info("股票列表控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📈 股票列表")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 搜索和过滤区域
        search_layout = QVBoxLayout()
        
        # 搜索框
        search_row = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入股票代码或名称...")
        self.search_input.textChanged.connect(self._on_search_changed)
        
        search_row.addWidget(search_label)
        search_row.addWidget(self.search_input)
        search_layout.addLayout(search_row)
        
        # 过滤器
        filter_row = QHBoxLayout()
        filter_label = QLabel("过滤:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "全部股票",
            "强势股票 (RS>1.2)",
            "弱势股票 (RS<0.8)",
            "累积阶段",
            "派发阶段"
        ])
        self.filter_combo.currentTextChanged.connect(self._on_filter_changed)
        
        filter_row.addWidget(filter_label)
        filter_row.addWidget(self.filter_combo)

        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self._on_refresh_clicked)
        filter_row.addWidget(self.refresh_button)

        search_layout.addLayout(filter_row)

        layout.addLayout(search_layout)
        
        # 股票表格
        self.table = QTableWidget()
        layout.addWidget(self.table)
        
        # 状态标签
        self.status_label = QLabel("加载中...")
        self.status_label.setStyleSheet("color: #666; font-size: 8pt;")
        layout.addWidget(self.status_label)
    
    def _setup_table(self):
        """设置表格"""
        if not self.table:
            return
        
        # 设置列
        columns = [
            "代码", "名称", "板块", "最新价", "涨跌幅", "成交量",
            "市值(亿)", "更新时间"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.table.setSortingEnabled(True)
        
        # 列宽设置
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 代码
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 名称
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 板块
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 价格
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 涨跌幅
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 成交量
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 市值
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 更新时间

        self.table.setColumnWidth(0, 90)   # 代码
        self.table.setColumnWidth(2, 80)   # 板块
        self.table.setColumnWidth(3, 80)   # 价格
        self.table.setColumnWidth(4, 80)   # 涨跌幅
        self.table.setColumnWidth(5, 100)  # 成交量
        self.table.setColumnWidth(6, 80)   # 市值
        self.table.setColumnWidth(7, 120)  # 更新时间
        self.table.setColumnWidth(5, 100)  # 成交量
        self.table.setColumnWidth(6, 80)   # 市值
        self.table.setColumnWidth(7, 120)  # 更新时间
        
        # 连接信号
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
    
    def _load_stock_data(self):
        """加载股票数据"""
        try:
            self.status_label.setText("正在加载股票数据...")

            # 从数据服务加载股票列表
            self.stocks_data = self.data_service.load_stock_list()
            self.filtered_data = self.stocks_data.copy()

            # 更新表格显示
            self._update_table_display()

            # 更新状态
            count = len(self.stocks_data)
            self.status_label.setText(f"已加载 {count} 只股票")

            # 发送信号
            self.stocks_loaded.emit(count)

            logger.info(f"股票数据加载完成，共{count}只股票")

        except Exception as e:
            logger.error(f"加载股票数据失败: {e}")
            self.status_label.setText(f"加载失败: {e}")

    def _update_table_display(self):
        """更新表格显示"""
        if not self.table or not self.filtered_data:
            return

        try:
            # 设置行数
            self.table.setRowCount(len(self.filtered_data))

            # 填充数据
            for row, stock in enumerate(self.filtered_data):
                # 代码
                self.table.setItem(row, 0, QTableWidgetItem(stock.symbol))

                # 名称
                self.table.setItem(row, 1, QTableWidgetItem(stock.name))

                # 板块
                self.table.setItem(row, 2, QTableWidgetItem(stock.sector))

                # 最新价
                price_item = QTableWidgetItem(f"{stock.price:.2f}")
                price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.table.setItem(row, 3, price_item)

                # 涨跌幅
                change_item = QTableWidgetItem(f"{stock.change_percent:+.2f}%")
                change_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

                # 根据涨跌设置颜色
                if stock.change_percent > 0:
                    change_item.setForeground(QColor("#F44336"))  # 红色
                elif stock.change_percent < 0:
                    change_item.setForeground(QColor("#4CAF50"))  # 绿色

                self.table.setItem(row, 4, change_item)

                # 成交量
                volume_text = self._format_volume(stock.volume)
                volume_item = QTableWidgetItem(volume_text)
                volume_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.table.setItem(row, 5, volume_item)

                # 市值
                market_cap_item = QTableWidgetItem(f"{stock.market_cap:.0f}")
                market_cap_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                self.table.setItem(row, 6, market_cap_item)

                # 更新时间
                time_text = stock.last_update.strftime("%H:%M:%S")
                self.table.setItem(row, 7, QTableWidgetItem(time_text))

        except Exception as e:
            logger.error(f"更新表格显示失败: {e}")

    def _format_volume(self, volume: int) -> str:
        """格式化成交量显示"""
        if volume >= 100000000:  # 1亿
            return f"{volume/100000000:.1f}亿"
        elif volume >= 10000:  # 1万
            return f"{volume/10000:.1f}万"
        else:
            return str(volume)

    def _on_search_changed(self):
        """搜索文本改变事件"""
        # 延迟搜索，避免频繁触发
        self.search_timer.stop()
        self.search_timer.start(300)  # 300ms延迟

    def _perform_search(self):
        """执行搜索"""
        search_text = self.search_input.text().strip().lower()

        if not search_text:
            self.filtered_data = self.stocks_data.copy()
        else:
            self.filtered_data = [
                stock for stock in self.stocks_data
                if search_text in stock.symbol.lower() or search_text in stock.name.lower()
            ]

        self._apply_filter()

    def _on_filter_changed(self):
        """过滤器改变事件"""
        self._apply_filter()

    def _apply_filter(self):
        """应用过滤器"""
        filter_text = self.filter_combo.currentText()

        if filter_text == "全部股票":
            # 不需要额外过滤
            pass
        elif filter_text.startswith("强势股票"):
            # 这里可以添加RS值过滤逻辑
            pass
        elif filter_text.startswith("弱势股票"):
            # 这里可以添加RS值过滤逻辑
            pass

        self._update_table_display()

        # 更新状态
        count = len(self.filtered_data)
        total = len(self.stocks_data)
        self.status_label.setText(f"显示 {count}/{total} 只股票")

    def _on_refresh_clicked(self):
        """刷新按钮点击事件"""
        # 改进的按钮状态管理
        original_text = self.refresh_button.text()
        self.refresh_button.setEnabled(False)
        self.refresh_button.setText("🔄 刷新中...")
        self.status_label.setText("正在加载股票数据...")

        try:
            self._load_stock_data()
            # 模拟加载完成后的成功反馈
            QTimer.singleShot(100, lambda: self.status_label.setText("股票数据加载完成"))
        except Exception as e:
            logger.error(f"刷新股票数据失败: {e}")
            self.status_label.setText(f"加载失败: {str(e)}")
        finally:
            self.refresh_button.setEnabled(True)
            self.refresh_button.setText(original_text)

    def _on_selection_changed(self):
        """表格选择改变事件"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_data):
            stock = self.filtered_data[current_row]
            self.stock_selected.emit(stock.symbol)
            logger.debug(f"选择股票: {stock.symbol} - {stock.name}")

    # 公共方法
    def refresh_data(self):
        """刷新数据"""
        self._load_stock_data()

    def get_selected_stock(self) -> Optional[StockInfo]:
        """获取当前选中的股票"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_data):
            return self.filtered_data[current_row]
        return None

    def search_stock(self, keyword: str):
        """搜索股票"""
        self.search_input.setText(keyword)
        self._perform_search()
