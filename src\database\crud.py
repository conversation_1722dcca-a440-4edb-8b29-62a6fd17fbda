"""
威科夫相对强弱选股系统 - 基础CRUD操作

提供针对各个数据表的标准化CRUD操作接口
"""

from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple, Union
import pandas as pd

from ..utils.logger import get_logger
from .manager import DatabaseManager

logger = get_logger(__name__)


class CRUDOperations:
    """
    基础CRUD操作类
    
    提供对各个数据表的标准化增删改查操作
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化CRUD操作
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        logger.info("CRUD操作模块初始化完成")
    
    # =============================================
    # 板块信息表操作
    # =============================================
    
    def create_sector_info(self, sector_code: str, sector_name: str, 
                          sector_type: str, parent_code: Optional[str] = None,
                          level: int = 1) -> bool:
        """
        创建板块信息
        
        Args:
            sector_code: 板块代码
            sector_name: 板块名称
            sector_type: 板块类型 (industry/concept)
            parent_code: 父板块代码
            level: 板块层级
            
        Returns:
            bool: 创建是否成功
        """
        try:
            sql = """
            INSERT OR IGNORE INTO sector_info 
            (sector_code, sector_name, sector_type, parent_code, level)
            VALUES (?, ?, ?, ?, ?)
            """
            rows_affected = self.db_manager.execute_non_query(
                sql, (sector_code, sector_name, sector_type, parent_code, level)
            )
            
            success = rows_affected > 0
            if success:
                logger.debug(f"创建板块信息成功: {sector_code} - {sector_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建板块信息失败: {e}")
            return False
    
    def get_sector_info(self, sector_code: Optional[str] = None, 
                       sector_type: Optional[str] = None,
                       is_active: bool = True) -> List[Dict[str, Any]]:
        """
        获取板块信息
        
        Args:
            sector_code: 板块代码，为None时获取所有
            sector_type: 板块类型筛选
            is_active: 是否只获取活跃板块
            
        Returns:
            List[Dict[str, Any]]: 板块信息列表
        """
        try:
            conditions = []
            params = []
            
            if sector_code:
                conditions.append("sector_code = ?")
                params.append(sector_code)
            
            if sector_type:
                conditions.append("sector_type = ?")
                params.append(sector_type)
            
            if is_active:
                conditions.append("is_active = 1")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"""
            SELECT sector_code, sector_name, sector_type, parent_code, 
                   level, is_active, create_date, update_time
            FROM sector_info
            WHERE {where_clause}
            ORDER BY sector_type, level, sector_code
            """
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            return [
                {
                    'sector_code': row[0],
                    'sector_name': row[1],
                    'sector_type': row[2],
                    'parent_code': row[3],
                    'level': row[4],
                    'is_active': bool(row[5]),
                    'create_date': row[6],
                    'update_time': row[7]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取板块信息失败: {e}")
            return []
    
    def update_sector_info(self, sector_code: str, **kwargs) -> bool:
        """
        更新板块信息
        
        Args:
            sector_code: 板块代码
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not kwargs:
                return True
            
            # 构建SET子句
            set_clauses = []
            params = []
            
            allowed_fields = ['sector_name', 'sector_type', 'parent_code', 'level', 'is_active']
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    set_clauses.append(f"{field} = ?")
                    params.append(value)
            
            if not set_clauses:
                logger.warning("没有有效的更新字段")
                return False
            
            params.append(sector_code)
            
            sql = f"""
            UPDATE sector_info 
            SET {', '.join(set_clauses)}
            WHERE sector_code = ?
            """
            
            rows_affected = self.db_manager.execute_non_query(sql, tuple(params))
            
            success = rows_affected > 0
            if success:
                logger.debug(f"更新板块信息成功: {sector_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新板块信息失败: {e}")
            return False
    
    # =============================================
    # 板块行情表操作
    # =============================================
    
    def insert_sector_quotes(self, quotes_data: List[Dict[str, Any]]) -> int:
        """
        批量插入板块行情数据
        
        Args:
            quotes_data: 行情数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        try:
            if not quotes_data:
                return 0
            
            sql = """
            INSERT OR REPLACE INTO sector_quotes 
            (sector_code, trade_date, open, high, low, close, volume, amount, change_pct, turnover_rate)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for data in quotes_data:
                params_list.append((
                    data.get('sector_code'),
                    data.get('trade_date'),
                    data.get('open'),
                    data.get('high'),
                    data.get('low'),
                    data.get('close'),
                    data.get('volume', 0),
                    data.get('amount', 0),
                    data.get('change_pct'),
                    data.get('turnover_rate')
                ))
            
            rows_affected = self.db_manager.execute_many(sql, params_list)
            
            logger.debug(f"批量插入板块行情数据: {rows_affected} 条记录")
            return rows_affected
            
        except Exception as e:
            logger.error(f"插入板块行情数据失败: {e}")
            return 0
    
    def get_sector_quotes(self, sector_code: str, start_date: str, 
                         end_date: str) -> pd.DataFrame:
        """
        获取板块行情数据
        
        Args:
            sector_code: 板块代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            pd.DataFrame: 行情数据
        """
        try:
            sql = """
            SELECT trade_date, open, high, low, close, volume, amount, change_pct, turnover_rate
            FROM sector_quotes
            WHERE sector_code = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date
            """
            
            return self.db_manager.query_to_dataframe(sql, (sector_code, start_date, end_date))
            
        except Exception as e:
            logger.error(f"获取板块行情数据失败: {e}")
            return pd.DataFrame()
    
    # =============================================
    # 个股信息表操作
    # =============================================
    
    def create_stock_info(self, stock_code: str, stock_name: str,
                         market: str, list_date: Optional[str] = None,
                         industry: Optional[str] = None, **kwargs) -> bool:
        """
        创建个股信息
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            market: 市场 (SH/SZ/BJ)
            list_date: 上市日期
            industry: 所属行业
            **kwargs: 其他字段
            
        Returns:
            bool: 创建是否成功
        """
        try:
            sql = """
            INSERT OR IGNORE INTO stock_info 
            (stock_code, stock_name, market, list_date, industry, status, 
             market_cap, total_shares, float_shares, is_st)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                stock_code,
                stock_name,
                market,
                list_date,
                industry,
                kwargs.get('status', 'active'),
                kwargs.get('market_cap'),
                kwargs.get('total_shares'),
                kwargs.get('float_shares'),
                kwargs.get('is_st', False)
            )
            
            rows_affected = self.db_manager.execute_non_query(sql, params)
            
            success = rows_affected > 0
            if success:
                logger.debug(f"创建个股信息成功: {stock_code} - {stock_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建个股信息失败: {e}")
            return False
    
    def get_stock_info(self, stock_code: Optional[str] = None,
                      market: Optional[str] = None,
                      status: str = 'active',
                      exclude_st: bool = False) -> List[Dict[str, Any]]:
        """
        获取个股信息
        
        Args:
            stock_code: 股票代码，为None时获取所有
            market: 市场筛选
            status: 状态筛选
            exclude_st: 是否排除ST股票
            
        Returns:
            List[Dict[str, Any]]: 个股信息列表
        """
        try:
            conditions = []
            params = []
            
            if stock_code:
                conditions.append("stock_code = ?")
                params.append(stock_code)
            
            if market:
                conditions.append("market = ?")
                params.append(market)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            if exclude_st:
                conditions.append("is_st = 0")
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"""
            SELECT stock_code, stock_name, market, list_date, delist_date,
                   status, industry, market_cap, total_shares, float_shares,
                   is_st, create_date, update_time
            FROM stock_info
            WHERE {where_clause}
            ORDER BY market, stock_code
            """
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            return [
                {
                    'stock_code': row[0],
                    'stock_name': row[1],
                    'market': row[2],
                    'list_date': row[3],
                    'delist_date': row[4],
                    'status': row[5],
                    'industry': row[6],
                    'market_cap': row[7],
                    'total_shares': row[8],
                    'float_shares': row[9],
                    'is_st': bool(row[10]),
                    'create_date': row[11],
                    'update_time': row[12]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取个股信息失败: {e}")
            return []
    
    # =============================================
    # 个股行情表操作
    # =============================================
    
    def insert_stock_quotes(self, quotes_data: List[Dict[str, Any]]) -> int:
        """
        批量插入个股行情数据
        
        Args:
            quotes_data: 行情数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        try:
            if not quotes_data:
                return 0
            
            sql = """
            INSERT OR REPLACE INTO stock_quotes 
            (stock_code, trade_date, open, high, low, close, volume, amount, 
             adj_factor, change_pct, turnover_rate, pe_ratio, pb_ratio)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for data in quotes_data:
                params_list.append((
                    data.get('stock_code'),
                    data.get('trade_date'),
                    data.get('open'),
                    data.get('high'),
                    data.get('low'),
                    data.get('close'),
                    data.get('volume', 0),
                    data.get('amount', 0),
                    data.get('adj_factor', 1.0),
                    data.get('change_pct'),
                    data.get('turnover_rate'),
                    data.get('pe_ratio'),
                    data.get('pb_ratio')
                ))
            
            rows_affected = self.db_manager.execute_many(sql, params_list)
            
            logger.debug(f"批量插入个股行情数据: {rows_affected} 条记录")
            return rows_affected
            
        except Exception as e:
            logger.error(f"插入个股行情数据失败: {e}")
            return 0
    
    def get_stock_quotes(self, stock_code: str, start_date: str, 
                        end_date: str, adjust_type: str = 'qfq') -> pd.DataFrame:
        """
        获取个股行情数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust_type: 复权类型 (qfq/hfq/none)
            
        Returns:
            pd.DataFrame: 行情数据
        """
        try:
            sql = """
            SELECT trade_date, open, high, low, close, volume, amount, 
                   adj_factor, change_pct, turnover_rate, pe_ratio, pb_ratio
            FROM stock_quotes
            WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date
            """
            
            df = self.db_manager.query_to_dataframe(sql, (stock_code, start_date, end_date))
            
            # 根据复权类型调整价格
            if not df.empty and adjust_type in ['qfq', 'hfq']:
                price_columns = ['open', 'high', 'low', 'close']
                if adjust_type == 'qfq':  # 前复权
                    latest_factor = df['adj_factor'].iloc[-1]
                    for col in price_columns:
                        df[col] = df[col] * df['adj_factor'] / latest_factor
                elif adjust_type == 'hfq':  # 后复权
                    for col in price_columns:
                        df[col] = df[col] * df['adj_factor']
            
            return df
            
        except Exception as e:
            logger.error(f"获取个股行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_stock_quote(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取股票最新行情
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[Dict[str, Any]]: 最新行情数据
        """
        try:
            sql = """
            SELECT trade_date, open, high, low, close, volume, amount,
                   change_pct, turnover_rate, pe_ratio, pb_ratio
            FROM stock_quotes
            WHERE stock_code = ?
            ORDER BY trade_date DESC
            LIMIT 1
            """
            
            result = self.db_manager.execute_query(sql, (stock_code,))
            
            if result:
                row = result[0]
                return {
                    'stock_code': stock_code,
                    'trade_date': row[0],
                    'open': row[1],
                    'high': row[2],
                    'low': row[3],
                    'close': row[4],
                    'volume': row[5],
                    'amount': row[6],
                    'change_pct': row[7],
                    'turnover_rate': row[8],
                    'pe_ratio': row[9],
                    'pb_ratio': row[10]
                }
            return None
            
        except Exception as e:
            logger.error(f"获取最新股票行情失败: {e}")
            return None
    
    def get_stock_quotes_by_date_range(self, stock_code: str, start_date: Union[str, date], 
                                     end_date: Union[str, date]) -> List[Dict[str, Any]]:
        """
        获取指定日期范围的股票行情
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 行情数据列表
        """
        try:
            sql = """
            SELECT trade_date, open, high, low, close, volume, amount,
                   change_pct, turnover_rate, pe_ratio, pb_ratio
            FROM stock_quotes
            WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
            ORDER BY trade_date
            """
            
            results = self.db_manager.execute_query(sql, (stock_code, start_date, end_date))
            
            return [
                {
                    'stock_code': stock_code,
                    'trade_date': row[0],
                    'open': row[1],
                    'high': row[2],
                    'low': row[3],
                    'close': row[4],
                    'volume': row[5],
                    'amount': row[6],
                    'change_pct': row[7],
                    'turnover_rate': row[8],
                    'pe_ratio': row[9],
                    'pb_ratio': row[10]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取指定日期范围股票行情失败: {e}")
            return []
    
    # =============================================
    # 板块成分股关系表操作
    # =============================================
    
    def create_sector_constituent(self, sector_code: str, stock_code: str,
                                 weight: float = 0.0, in_date: Optional[str] = None) -> bool:
        """
        创建板块成分股关系
        
        Args:
            sector_code: 板块代码
            stock_code: 股票代码
            weight: 权重
            in_date: 加入日期
            
        Returns:
            bool: 创建是否成功
        """
        try:
            if not in_date:
                in_date = date.today().strftime('%Y-%m-%d')
            
            sql = """
            INSERT OR IGNORE INTO sector_constituents 
            (sector_code, stock_code, weight, in_date)
            VALUES (?, ?, ?, ?)
            """
            
            rows_affected = self.db_manager.execute_non_query(
                sql, (sector_code, stock_code, weight, in_date)
            )
            
            success = rows_affected > 0
            if success:
                logger.debug(f"创建板块成分股关系成功: {sector_code} - {stock_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"创建板块成分股关系失败: {e}")
            return False
    
    def get_sector_constituents(self, sector_code: str, 
                               active_only: bool = True) -> List[Dict[str, Any]]:
        """
        获取板块成分股
        
        Args:
            sector_code: 板块代码
            active_only: 是否只获取活跃成分股
            
        Returns:
            List[Dict[str, Any]]: 成分股列表
        """
        try:
            conditions = ["sc.sector_code = ?"]
            params = [sector_code]
            
            if active_only:
                conditions.append("sc.is_active = 1")
                conditions.append("sc.out_date IS NULL")
                conditions.append("si.status = 'active'")
            
            where_clause = " AND ".join(conditions)
            
            sql = f"""
            SELECT sc.stock_code, si.stock_name, sc.weight, sc.in_date, sc.out_date
            FROM sector_constituents sc
            JOIN stock_info si ON sc.stock_code = si.stock_code
            WHERE {where_clause}
            ORDER BY sc.weight DESC, sc.stock_code
            """
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            return [
                {
                    'stock_code': row[0],
                    'stock_name': row[1],
                    'weight': row[2],
                    'in_date': row[3],
                    'out_date': row[4]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取板块成分股失败: {e}")
            return []
    
    def update_stock_info(self, stock_code: str, **kwargs) -> bool:
        """
        更新股票信息
        
        Args:
            stock_code: 股票代码
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not kwargs:
                return True
            
            # 构建SET子句
            set_clauses = []
            params = []
            
            allowed_fields = ['stock_name', 'market', 'list_date', 'delist_date', 
                            'status', 'industry', 'market_cap', 'total_shares', 
                            'float_shares', 'is_st']
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    set_clauses.append(f"{field} = ?")
                    params.append(value)
            
            if not set_clauses:
                logger.warning("没有有效的更新字段")
                return False
            
            params.append(stock_code)
            
            sql = f"""
            UPDATE stock_info 
            SET {', '.join(set_clauses)}
            WHERE stock_code = ?
            """
            
            rows_affected = self.db_manager.execute_non_query(sql, tuple(params))
            
            success = rows_affected > 0
            if success:
                logger.debug(f"更新股票信息成功: {stock_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新股票信息失败: {e}")
            return False
    
    def delete_sector_constituent(self, sector_code: str, stock_code: str) -> bool:
        """
        删除板块成分股关系
        
        Args:
            sector_code: 板块代码
            stock_code: 股票代码
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 软删除：设置out_date和is_active=0
            today = date.today().strftime('%Y-%m-%d')
            
            sql = """
            UPDATE sector_constituents 
            SET out_date = ?, is_active = 0
            WHERE sector_code = ? AND stock_code = ? AND is_active = 1
            """
            
            rows_affected = self.db_manager.execute_non_query(
                sql, (today, sector_code, stock_code)
            )
            
            success = rows_affected > 0
            if success:
                logger.debug(f"删除板块成分股关系成功: {sector_code} - {stock_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除板块成分股关系失败: {e}")
            return False
    
    def get_active_stocks(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取所有活跃股票
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 活跃股票列表
        """
        try:
            sql = """
            SELECT stock_code, stock_name, market, industry, list_date
            FROM stock_info
            WHERE status = 'active' AND delist_date IS NULL
            ORDER BY stock_code
            """
            
            if limit:
                sql += f" LIMIT {limit}"
            
            results = self.db_manager.execute_query(sql)
            
            return [
                {
                    'stock_code': row[0],
                    'stock_name': row[1],
                    'market': row[2],
                    'industry': row[3],
                    'list_date': row[4]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取活跃股票失败: {e}")
            return []
    
    # =============================================
    # 相对强弱结果表操作
    # =============================================
    
    def save_relative_strength_results(self, results_data: List[Dict[str, Any]]) -> int:
        """
        保存相对强弱计算结果
        
        Args:
            results_data: 计算结果数据列表
            
        Returns:
            int: 成功保存的记录数
        """
        try:
            if not results_data:
                return 0
            
            sql = """
            INSERT OR REPLACE INTO relative_strength_results 
            (symbol, symbol_type, start_date, end_date, period_days, 
             return_rate, benchmark_return, relative_strength, rank_in_type, percentile)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for data in results_data:
                params_list.append((
                    data.get('symbol'),
                    data.get('symbol_type'),
                    data.get('start_date'),
                    data.get('end_date'),
                    data.get('period_days'),
                    data.get('return_rate'),
                    data.get('benchmark_return'),
                    data.get('relative_strength'),
                    data.get('rank_in_type'),
                    data.get('percentile')
                ))
            
            rows_affected = self.db_manager.execute_many(sql, params_list)
            
            logger.debug(f"保存相对强弱结果: {rows_affected} 条记录")
            return rows_affected
            
        except Exception as e:
            logger.error(f"保存相对强弱结果失败: {e}")
            return 0
    
    def get_relative_strength_results(self, symbol_type: str, start_date: str,
                                    end_date: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取相对强弱结果
        
        Args:
            symbol_type: 标的类型 (stock/sector)
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 相对强弱结果列表
        """
        try:
            sql = """
            SELECT symbol, symbol_type, start_date, end_date, period_days,
                   return_rate, benchmark_return, relative_strength, 
                   rank_in_type, percentile, calculation_time
            FROM relative_strength_results
            WHERE symbol_type = ? AND start_date = ? AND end_date = ?
            ORDER BY relative_strength DESC
            LIMIT ?
            """
            
            results = self.db_manager.execute_query(
                sql, (symbol_type, start_date, end_date, limit)
            )
            
            return [
                {
                    'symbol': row[0],
                    'symbol_type': row[1],
                    'start_date': row[2],
                    'end_date': row[3],
                    'period_days': row[4],
                    'return_rate': row[5],
                    'benchmark_return': row[6],
                    'relative_strength': row[7],
                    'rank_in_type': row[8],
                    'percentile': row[9],
                    'calculation_time': row[10]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取相对强弱结果失败: {e}")
            return []
    
    # =============================================
    # 通用查询方法
    # =============================================
    
    def get_latest_trade_date(self, symbol_type: str = 'stock') -> Optional[str]:
        """
        获取最新交易日期
        
        Args:
            symbol_type: 标的类型 (stock/sector)
            
        Returns:
            Optional[str]: 最新交易日期
        """
        try:
            if symbol_type == 'stock':
                table_name = 'stock_quotes'
            else:
                table_name = 'sector_quotes'
            
            sql = f"SELECT MAX(trade_date) FROM {table_name}"
            result = self.db_manager.execute_query(sql)
            
            return result[0][0] if result and result[0][0] else None
            
        except Exception as e:
            logger.error(f"获取最新交易日期失败: {e}")
            return None
    
    def get_data_coverage(self, symbol: str, symbol_type: str = 'stock') -> Dict[str, Any]:
        """
        获取数据覆盖情况
        
        Args:
            symbol: 标的代码
            symbol_type: 标的类型 (stock/sector)
            
        Returns:
            Dict[str, Any]: 数据覆盖信息
        """
        try:
            if symbol_type == 'stock':
                table_name = 'stock_quotes'
                code_field = 'stock_code'
            else:
                table_name = 'sector_quotes'
                code_field = 'sector_code'
            
            sql = f"""
            SELECT MIN(trade_date) as start_date,
                   MAX(trade_date) as end_date,
                   COUNT(*) as total_records
            FROM {table_name}
            WHERE {code_field} = ?
            """
            
            result = self.db_manager.execute_query(sql, (symbol,))
            
            if result and result[0][0]:
                return {
                    'symbol': symbol,
                    'symbol_type': symbol_type,
                    'start_date': result[0][0],
                    'end_date': result[0][1],
                    'total_records': result[0][2]
                }
            else:
                return {
                    'symbol': symbol,
                    'symbol_type': symbol_type,
                    'start_date': None,
                    'end_date': None,
                    'total_records': 0
                }
            
        except Exception as e:
            logger.error(f"获取数据覆盖情况失败: {e}")
            return {'error': str(e)}