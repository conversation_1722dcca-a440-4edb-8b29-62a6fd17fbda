"""
配置验证器

提供配置文件的格式验证、内容验证等功能
"""

import re
from typing import Dict, Any, List, Optional, Union, Callable
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger

logger = get_logger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"
    WARNING = "warning" 
    ERROR = "error"


@dataclass
class ValidationResult:
    """验证结果"""
    level: ValidationLevel
    path: str
    message: str
    suggestion: Optional[str] = None


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        """初始化配置验证器"""
        self._rules: Dict[str, List[Callable]] = {}
        self._schema: Dict[str, Any] = {}
        self._setup_default_rules()
        
        logger.info("配置验证器初始化完成")
    
    def _setup_default_rules(self):
        """设置默认验证规则"""
        # 数据库配置验证规则
        self.add_rule('database.connection_pool_size', self._validate_positive_int, 
                     "连接池大小必须是正整数")
        self.add_rule('database.connection_timeout', self._validate_positive_int,
                     "连接超时时间必须是正整数")
        self.add_rule('database.query_timeout', self._validate_positive_int,
                     "查询超时时间必须是正整数")
        
        # 数据源配置验证规则
        self.add_rule('data_source.xtdata_timeout', self._validate_positive_int,
                     "XtData超时时间必须是正整数")
        self.add_rule('data_source.retry_count', self._validate_non_negative_int,
                     "重试次数必须是非负整数")
        self.add_rule('data_source.max_concurrent_requests', self._validate_positive_int,
                     "最大并发请求数必须是正整数")
        
        # UI配置验证规则
        self.add_rule('ui.window_width', lambda x: self._validate_range(x, 800, 3840),
                     "窗口宽度必须在800-3840之间")
        self.add_rule('ui.window_height', lambda x: self._validate_range(x, 600, 2160),
                     "窗口高度必须在600-2160之间")
        self.add_rule('ui.font_size', lambda x: self._validate_range(x, 8, 48),
                     "字体大小必须在8-48之间")
        
        # 性能配置验证规则
        self.add_rule('performance.max_memory_usage_mb', lambda x: self._validate_range(x, 512, 16384),
                     "最大内存使用量必须在512MB-16GB之间")
        self.add_rule('performance.cache_size_mb', lambda x: self._validate_range(x, 64, 2048),
                     "缓存大小必须在64MB-2GB之间")
    
    def add_rule(self, path: str, validator: Callable, message: str):
        """
        添加验证规则
        
        Args:
            path: 配置路径
            validator: 验证函数
            message: 验证失败消息
        """
        if path not in self._rules:
            self._rules[path] = []
        
        def rule_wrapper(value):
            if not validator(value):
                return ValidationResult(ValidationLevel.ERROR, path, message)
            return None
        
        self._rules[path].append(rule_wrapper)
        logger.debug(f"添加验证规则: {path}")
    
    def set_schema(self, schema: Dict[str, Any]):
        """
        设置配置架构
        
        Args:
            schema: 配置架构定义
        """
        self._schema = schema
        logger.info("配置架构已设置")
    
    def validate_config(self, config: Dict[str, Any], 
                       strict: bool = False) -> List[ValidationResult]:
        """
        验证配置
        
        Args:
            config: 配置字典
            strict: 是否严格模式
            
        Returns:
            验证结果列表
        """
        results = []
        
        try:
            # 架构验证
            if self._schema:
                schema_results = self._validate_schema(config, self._schema)
                results.extend(schema_results)
            
            # 规则验证
            rule_results = self._validate_rules(config)
            results.extend(rule_results)
            
            # 交叉验证
            cross_results = self._validate_cross_dependencies(config)
            results.extend(cross_results)
            
            # 业务逻辑验证
            business_results = self._validate_business_logic(config)
            results.extend(business_results)
            
            # 严格模式额外检查
            if strict:
                strict_results = self._validate_strict_mode(config)
                results.extend(strict_results)
            
            logger.info(f"配置验证完成，发现 {len(results)} 个问题")
            
        except Exception as e:
            logger.error(f"配置验证过程出错: {e}")
            results.append(ValidationResult(
                ValidationLevel.ERROR, 
                "general", 
                f"验证过程出错: {e}"
            ))
        
        return results
    
    def validate_file(self, config_file: str) -> List[ValidationResult]:
        """
        验证配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            验证结果列表
        """
        results = []
        
        try:
            config_path = Path(config_file)
            
            # 文件存在性检查
            if not config_path.exists():
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    config_file,
                    "配置文件不存在"
                ))
                return results
            
            # 文件权限检查
            if not config_path.is_file():
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    config_file,
                    "路径不是文件"
                ))
                return results
            
            # 文件格式检查
            if config_path.suffix.lower() not in ['.yaml', '.yml', '.json']:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    config_file,
                    "不支持的文件格式",
                    "建议使用.yaml或.json格式"
                ))
            
            # 文件大小检查
            file_size_mb = config_path.stat().st_size / (1024 * 1024)
            if file_size_mb > 10:
                results.append(ValidationResult(
                    ValidationLevel.WARNING,
                    config_file,
                    f"配置文件过大: {file_size_mb:.1f}MB",
                    "建议拆分大型配置文件"
                ))
            
            # 加载并验证内容
            from .config_manager import ConfigManager
            manager = ConfigManager()
            config = manager.load_config("temp", config_file)
            
            if config:
                content_results = self.validate_config(config)
                results.extend(content_results)
            else:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    config_file,
                    "无法加载配置文件内容"
                ))
            
        except Exception as e:
            logger.error(f"验证配置文件失败: {e}")
            results.append(ValidationResult(
                ValidationLevel.ERROR,
                config_file,
                f"验证过程出错: {e}"
            ))
        
        return results
    
    def _validate_schema(self, config: Dict[str, Any], 
                        schema: Dict[str, Any], 
                        path: str = "") -> List[ValidationResult]:
        """验证配置架构"""
        results = []
        
        try:
            for key, expected_type in schema.items():
                current_path = f"{path}.{key}" if path else key
                
                if key not in config:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        current_path,
                        "缺少必需的配置项"
                    ))
                    continue
                
                value = config[key]
                
                # 类型检查
                if isinstance(expected_type, type):
                    if not isinstance(value, expected_type):
                        results.append(ValidationResult(
                            ValidationLevel.ERROR,
                            current_path,
                            f"类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}"
                        ))
                elif isinstance(expected_type, dict):
                    # 嵌套字典验证
                    if isinstance(value, dict):
                        nested_results = self._validate_schema(value, expected_type, current_path)
                        results.extend(nested_results)
                    else:
                        results.append(ValidationResult(
                            ValidationLevel.ERROR,
                            current_path,
                            "期望字典类型"
                        ))
        
        except Exception as e:
            logger.error(f"架构验证失败: {e}")
        
        return results
    
    def _validate_rules(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """验证配置规则"""
        results = []
        
        try:
            for path, rules in self._rules.items():
                value = self._get_nested_value(config, path)
                
                if value is not None:
                    for rule in rules:
                        result = rule(value)
                        if result:
                            results.append(result)
        
        except Exception as e:
            logger.error(f"规则验证失败: {e}")
        
        return results
    
    def _validate_cross_dependencies(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """验证交叉依赖关系"""
        results = []
        
        try:
            # 示例：检查内存和缓存大小的关系
            max_memory = self._get_nested_value(config, 'performance.max_memory_usage_mb')
            cache_size = self._get_nested_value(config, 'performance.cache_size_mb')
            
            if max_memory and cache_size:
                if cache_size > max_memory * 0.5:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        'performance.cache_size_mb',
                        "缓存大小不应超过最大内存使用量的50%",
                        f"建议调整缓存大小到 {int(max_memory * 0.5)}MB 以下"
                    ))
            
            # 检查窗口大小合理性
            window_width = self._get_nested_value(config, 'ui.window_width')
            window_height = self._get_nested_value(config, 'ui.window_height')
            
            if window_width and window_height:
                aspect_ratio = window_width / window_height
                if aspect_ratio < 1.2 or aspect_ratio > 2.5:
                    results.append(ValidationResult(
                        ValidationLevel.INFO,
                        'ui.window_size',
                        f"窗口宽高比较特殊: {aspect_ratio:.2f}",
                        "建议使用常见的宽高比如 16:9 或 4:3"
                    ))
        
        except Exception as e:
            logger.error(f"交叉依赖验证失败: {e}")
        
        return results
    
    def _validate_business_logic(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """验证业务逻辑"""
        results = []
        
        try:
            # 验证计算周期设置
            periods = self._get_nested_value(config, 'calculation.default_periods')
            if periods:
                if not isinstance(periods, list) or not periods:
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        'calculation.default_periods',
                        "计算周期必须是非空列表"
                    ))
                elif len(periods) > 10:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        'calculation.default_periods',
                        "计算周期过多可能影响性能",
                        "建议减少到10个以内"
                    ))
                elif any(p <= 0 for p in periods if isinstance(p, (int, float))):
                    results.append(ValidationResult(
                        ValidationLevel.ERROR,
                        'calculation.default_periods',
                        "计算周期必须是正数"
                    ))
            
            # 验证数据源超时设置
            data_timeout = self._get_nested_value(config, 'data_source.xtdata_timeout')
            retry_count = self._get_nested_value(config, 'data_source.retry_count')
            
            if data_timeout and retry_count:
                total_timeout = data_timeout * (retry_count + 1)
                if total_timeout > 300:  # 5分钟
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        'data_source.timeout_settings',
                        f"总超时时间过长: {total_timeout}秒",
                        "建议减少超时时间或重试次数"
                    ))
        
        except Exception as e:
            logger.error(f"业务逻辑验证失败: {e}")
        
        return results
    
    def _validate_strict_mode(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """严格模式验证"""
        results = []
        
        try:
            # 检查未知配置项
            known_sections = {
                'database', 'data_source', 'calculation', 
                'ui', 'logging', 'notification', 'performance'
            }
            
            for section in config.keys():
                if section not in known_sections:
                    results.append(ValidationResult(
                        ValidationLevel.WARNING,
                        section,
                        "未知的配置部分",
                        "请确认配置项名称是否正确"
                    ))
            
            # 检查配置完整性
            required_sections = {'database', 'data_source', 'ui'}
            missing_sections = required_sections - set(config.keys())
            
            for section in missing_sections:
                results.append(ValidationResult(
                    ValidationLevel.ERROR,
                    section,
                    "缺少必需的配置部分"
                ))
        
        except Exception as e:
            logger.error(f"严格模式验证失败: {e}")
        
        return results
    
    def _get_nested_value(self, config: Dict[str, Any], path: str) -> Any:
        """获取嵌套配置值"""
        try:
            keys = path.split('.')
            current = config
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            
            return current
            
        except Exception:
            return None
    
    def _validate_positive_int(self, value: Any) -> bool:
        """验证正整数"""
        return isinstance(value, int) and value > 0
    
    def _validate_non_negative_int(self, value: Any) -> bool:
        """验证非负整数"""
        return isinstance(value, int) and value >= 0
    
    def _validate_range(self, value: Any, min_val: Union[int, float], 
                       max_val: Union[int, float]) -> bool:
        """验证数值范围"""
        return isinstance(value, (int, float)) and min_val <= value <= max_val
    
    def _validate_email(self, value: Any) -> bool:
        """验证邮箱格式"""
        if not isinstance(value, str):
            return False
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, value) is not None
    
    def _validate_url(self, value: Any) -> bool:
        """验证URL格式"""
        if not isinstance(value, str):
            return False
        
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return re.match(url_pattern, value) is not None
    
    def generate_report(self, results: List[ValidationResult]) -> str:
        """
        生成验证报告
        
        Args:
            results: 验证结果列表
            
        Returns:
            验证报告文本
        """
        if not results:
            return "✅ 配置验证通过，未发现问题\n"
        
        report_lines = ["📋 配置验证报告", "=" * 50, ""]
        
        # 按级别分组
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        infos = [r for r in results if r.level == ValidationLevel.INFO]
        
        # 摘要
        report_lines.extend([
            f"📊 验证摘要:",
            f"   ❌ 错误: {len(errors)}",
            f"   ⚠️  警告: {len(warnings)}",
            f"   ℹ️  信息: {len(infos)}",
            ""
        ])
        
        # 详细结果
        for level_name, items, emoji in [
            ("错误", errors, "❌"),
            ("警告", warnings, "⚠️"),
            ("信息", infos, "ℹ️")
        ]:
            if items:
                report_lines.extend([f"{emoji} {level_name}:", ""])
                for item in items:
                    report_lines.append(f"  • {item.path}: {item.message}")
                    if item.suggestion:
                        report_lines.append(f"    💡 建议: {item.suggestion}")
                    report_lines.append("")
        
        return "\n".join(report_lines)
    
    def export_report(self, results: List[ValidationResult], 
                     export_file: str) -> bool:
        """
        导出验证报告
        
        Args:
            results: 验证结果列表
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            report = self.generate_report(results)
            
            with open(export_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"验证报告导出成功: {export_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出验证报告失败: {e}")
            return False