#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟XtData模块

用于测试XtData适配器的逻辑，无需真实的MiniQMT客户端
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional


class MockXtData:
    """模拟的XtData模块"""
    
    def __init__(self):
        self.connected = False
        
        # 模拟股票列表
        self.mock_stocks = [
            "000001.SZ", "000002.SZ", "000858.SZ", "000876.SZ", "002415.SZ",
            "600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH"
        ]
        
        # 模拟板块列表
        self.mock_sectors = [
            "银行", "保险", "证券", "房地产", "建筑装饰",
            "钢铁", "煤炭", "石油石化", "化工", "医药生物",
            "食品饮料", "纺织服装", "轻工制造", "电力设备",
            "新能源", "5G", "人工智能", "区块链", "物联网"
        ]
    
    def reconnect(self, ip: str, port: int):
        """模拟重连"""
        print(f"Mock: 连接到 {ip}:{port}")
        self.connected = True
    
    def get_instrument_detail(self, symbol: str) -> Optional[Dict]:
        """模拟获取合约详情"""
        if symbol in self.mock_stocks:
            return {
                'InstrumentID': symbol,
                'InstrumentName': f"模拟股票{symbol[:6]}",
                'ExchangeID': symbol.split('.')[1]
            }
        return None
    
    def download_sector_data(self):
        """模拟下载板块数据"""
        print("Mock: 下载板块分类信息")
    
    def download_index_weight(self):
        """模拟下载指数权重"""
        print("Mock: 下载指数成分权重信息")
    
    def get_market_data(
        self,
        field_list: List[str],
        stock_list: List[str],
        period: str,
        start_time: str,
        end_time: str,
        count: int = -1,
        dividend_type: str = 'none',
        fill_data: bool = True
    ) -> Dict:
        """模拟获取市场数据"""
        if not stock_list:
            return {}
        
        symbol = stock_list[0]
        
        # 生成模拟的时间序列
        start_date = datetime.strptime(start_time, '%Y%m%d')
        end_date = datetime.strptime(end_time, '%Y%m%d')
        
        # 生成交易日（排除周末）
        dates = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                dates.append(current_date)
            current_date += timedelta(days=1)
        
        if not dates:
            return {}
        
        # 生成模拟价格数据
        num_days = len(dates)
        base_price = 10.0
        
        # 生成随机价格序列
        np.random.seed(42)  # 固定种子以获得可重复的结果
        price_changes = np.random.normal(0, 0.02, num_days)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))  # 确保价格不为负
        
        # 生成OHLC数据
        opens = []
        highs = []
        lows = []
        closes = prices
        volumes = []
        amounts = []
        
        for i, close_price in enumerate(closes):
            # 生成开盘价（基于前一日收盘价）
            if i == 0:
                open_price = close_price
            else:
                open_price = closes[i-1] * (1 + np.random.normal(0, 0.01))
            
            # 生成最高价和最低价
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
            
            # 生成成交量和成交额
            volume = int(np.random.uniform(1000000, 10000000))
            amount = volume * close_price
            
            opens.append(open_price)
            highs.append(high_price)
            lows.append(low_price)
            volumes.append(volume)
            amounts.append(amount)
        
        # 转换为时间戳
        timestamps = [int(date.timestamp()) for date in dates]
        
        return {
            'time': timestamps,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes,
            'amount': amounts
        }
    
    def download_history_data(
        self,
        stock_code: str,
        period: str,
        start_time: str,
        end_time: str,
        incrementally: bool = True
    ):
        """模拟下载历史数据"""
        print(f"Mock: 下载 {stock_code} 历史数据 ({period}, {start_time}-{end_time})")
    
    def download_history_data2(
        self,
        stock_list: List[str],
        period: str,
        start_time: str,
        end_time: str,
        callback=None,
        incrementally: bool = True
    ):
        """模拟批量下载历史数据"""
        print(f"Mock: 批量下载 {len(stock_list)} 只股票历史数据")
        
        if callback:
            for i, stock in enumerate(stock_list):
                callback({
                    'finished': i + 1,
                    'total': len(stock_list),
                    'stockcode': stock
                })
    
    def get_sector_list(self) -> List[str]:
        """模拟获取板块列表"""
        return self.mock_sectors
    
    def get_stock_list_in_sector(self, sector_name: str) -> List[str]:
        """模拟获取板块成分股"""
        if sector_name in ["沪深A股", "上海A股", "深圳A股"]:
            return self.mock_stocks
        elif sector_name in self.mock_sectors:
            # 返回部分股票作为该板块的成分股
            return self.mock_stocks[:5]
        return []
    
    def get_trading_calendar(
        self,
        market: str,
        start_time: str,
        end_time: str
    ) -> List[str]:
        """模拟获取交易日历"""
        start_date = datetime.strptime(start_time, '%Y%m%d')
        end_date = datetime.strptime(end_time, '%Y%m%d')
        
        trading_dates = []
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 周一到周五
                trading_dates.append(current_date.strftime('%Y%m%d'))
            current_date += timedelta(days=1)
        
        return trading_dates


# 创建模拟模块实例
xtdata = MockXtData()


# 模拟模块级别的函数
def reconnect(ip: str, port: int):
    return xtdata.reconnect(ip, port)

def get_instrument_detail(symbol: str):
    return xtdata.get_instrument_detail(symbol)

def download_sector_data():
    return xtdata.download_sector_data()

def download_index_weight():
    return xtdata.download_index_weight()

def get_market_data(*args, **kwargs):
    return xtdata.get_market_data(*args, **kwargs)

def download_history_data(*args, **kwargs):
    return xtdata.download_history_data(*args, **kwargs)

def download_history_data2(*args, **kwargs):
    return xtdata.download_history_data2(*args, **kwargs)

def get_sector_list():
    return xtdata.get_sector_list()

def get_stock_list_in_sector(sector_name: str):
    return xtdata.get_stock_list_in_sector(sector_name)

def get_trading_calendar(*args, **kwargs):
    return xtdata.get_trading_calendar(*args, **kwargs)
