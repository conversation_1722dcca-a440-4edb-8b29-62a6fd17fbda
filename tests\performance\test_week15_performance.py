#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第15周性能优化完整测试
验证算法性能、多线程处理、数据库优化、打包部署等功能
"""

import sys
import os
import time
import asyncio
import threading
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QLabel, QTextEdit, QGroupBox,
    QTabWidget, QProgressBar, QTableWidget, QTableWidgetItem
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.performance_optimizer import (
    performance_optimizer, profile, memoize,
    PerformanceProfiler, MemoryOptimizer, Async<PERSON>askManager, AlgorithmOptimizer
)
from src.database.query_optimizer import QueryOptimizer
from src.engines.wyckoff import WyckoffAnalysisEngine
from src.engines.relative_strength import RelativeStrengthEngine
from src.engines.selection import SelectionEngine
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceTestWorker(QThread):
    """性能测试工作线程"""

    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(str, dict)

    def __init__(self, test_type: str):
        super().__init__()
        self.test_type = test_type
        self.running = True

    def run(self):
        """运行性能测试"""
        try:
            if self.test_type == "algorithm_performance":
                result = self.test_algorithm_performance()
            elif self.test_type == "memory_optimization":
                result = self.test_memory_optimization()
            elif self.test_type == "async_processing":
                result = self.test_async_processing()
            elif self.test_type == "database_performance":
                result = self.test_database_performance()
            elif self.test_type == "concurrent_analysis":
                result = self.test_concurrent_analysis()
            else:
                result = {"error": f"未知测试类型: {self.test_type}"}

            self.result_ready.emit(self.test_type, result)

        except Exception as e:
            self.result_ready.emit(self.test_type, {"error": str(e)})

    @profile("algorithm_performance_test")
    def test_algorithm_performance(self) -> Dict[str, Any]:
        """测试算法性能"""
        results = {}

        # 生成测试数据
        test_data = np.random.randn(1000, 6)  # 1000天的OHLCV数据

        # 测试威科夫分析性能
        start_time = time.time()
        wyckoff_engine = WyckoffAnalysisEngine()

        for i in range(10):  # 测试10次
            wyckoff_result = wyckoff_engine.analyze_market_structure(test_data)
            self.progress_updated.emit(i * 10)

        wyckoff_time = (time.time() - start_time) / 10
        results["wyckoff_avg_time"] = wyckoff_time

        # 测试相对强弱计算性能
        start_time = time.time()
        rs_engine = RelativeStrengthEngine()

        for i in range(10):
            rs_result = rs_engine.calculate_relative_strength(test_data[:, 4], test_data[:, 4])  # 使用收盘价
            self.progress_updated.emit(50 + i * 5)

        rs_time = (time.time() - start_time) / 10
        results["rs_avg_time"] = rs_time

        # 测试向量化计算
        start_time = time.time()
        optimizer = AlgorithmOptimizer()

        for i in range(100):
            vectorized_result = optimizer.vectorize_calculation(test_data[:, 4], "moving_average")

        vectorized_time = (time.time() - start_time) / 100
        results["vectorized_avg_time"] = vectorized_time

        self.progress_updated.emit(100)

        return {
            "威科夫分析平均时间": f"{wyckoff_time:.4f}秒",
            "相对强弱计算平均时间": f"{rs_time:.4f}秒",
            "向量化计算平均时间": f"{vectorized_time:.6f}秒",
            "性能评级": "优秀" if wyckoff_time < 0.1 and rs_time < 0.05 else "良好" if wyckoff_time < 0.5 else "需要优化"
        }

    def test_memory_optimization(self) -> Dict[str, Any]:
        """测试内存优化"""
        import psutil

        # 记录初始内存
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024

        # 创建大量数据模拟内存压力
        large_data = []
        for i in range(1000):
            large_data.append(np.random.randn(1000, 100))
            self.progress_updated.emit(i // 10)

        # 记录峰值内存
        peak_memory = psutil.Process().memory_info().rss / 1024 / 1024

        # 执行内存优化
        memory_optimizer = MemoryOptimizer()
        saved_memory = memory_optimizer.optimize_memory()

        # 清理数据
        del large_data

        # 记录优化后内存
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024

        self.progress_updated.emit(100)

        return {
            "初始内存": f"{initial_memory:.1f}MB",
            "峰值内存": f"{peak_memory:.1f}MB",
            "最终内存": f"{final_memory:.1f}MB",
            "内存节省": f"{peak_memory - final_memory:.1f}MB",
            "优化效果": f"{saved_memory:.1f}%"
        }

    def test_async_processing(self) -> Dict[str, Any]:
        """测试异步处理"""
        async def async_task(data):
            await asyncio.sleep(0.01)  # 模拟异步操作
            return np.sum(data)

        def sync_task(data):
            time.sleep(0.01)  # 模拟同步操作
            return np.sum(data)

        # 准备测试数据
        test_data = [np.random.randn(100) for _ in range(50)]

        # 测试同步处理时间
        start_time = time.time()
        sync_results = []
        for i, data in enumerate(test_data):
            result = sync_task(data)
            sync_results.append(result)
            self.progress_updated.emit(i * 2)

        sync_time = time.time() - start_time

        # 测试异步处理时间
        start_time = time.time()

        async def run_async_tests():
            tasks = [async_task(data) for data in test_data]
            return await asyncio.gather(*tasks)

        # 在新的事件循环中运行异步测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        async_results = loop.run_until_complete(run_async_tests())
        loop.close()

        async_time = time.time() - start_time

        self.progress_updated.emit(100)

        speedup = sync_time / async_time if async_time > 0 else 0

        return {
            "同步处理时间": f"{sync_time:.3f}秒",
            "异步处理时间": f"{async_time:.3f}秒",
            "性能提升": f"{speedup:.2f}倍",
            "处理任务数": len(test_data),
            "异步优势": "显著" if speedup > 2 else "一般" if speedup > 1.2 else "不明显"
        }

    def test_database_performance(self) -> Dict[str, Any]:
        """测试数据库性能"""
        try:
            # 创建临时数据库
            import tempfile
            temp_db = tempfile.mktemp(suffix='.db')

            query_optimizer = QueryOptimizer(temp_db)

            # 创建测试表
            query_optimizer.execute_command("""
                CREATE TABLE test_stocks (
                    id INTEGER PRIMARY KEY,
                    symbol TEXT,
                    price REAL,
                    volume INTEGER,
                    timestamp TEXT
                )
            """)

            # 批量插入测试数据
            test_data = []
            for i in range(1000):
                test_data.append((
                    f"INSERT INTO test_stocks (symbol, price, volume, timestamp) VALUES (?, ?, ?, ?)",
                    (f"STOCK{i:04d}", 100 + i * 0.1, 1000 + i * 10, f"2024-01-{(i%30)+1:02d}")
                ))
                self.progress_updated.emit(i // 10)

            # 测试批量插入性能
            start_time = time.time()
            query_optimizer.execute_batch(test_data)
            batch_time = time.time() - start_time

            # 测试查询性能
            start_time = time.time()
            for i in range(100):
                results = query_optimizer.execute_query(
                    "SELECT * FROM test_stocks WHERE price > ? ORDER BY volume DESC LIMIT 10",
                    (100 + i,)
                )
            query_time = (time.time() - start_time) / 100

            # 获取性能分析
            performance_analysis = query_optimizer.analyze_performance()

            # 清理
            query_optimizer.shutdown()
            os.unlink(temp_db)

            self.progress_updated.emit(100)

            return {
                "批量插入时间": f"{batch_time:.3f}秒",
                "平均查询时间": f"{query_time:.6f}秒",
                "插入记录数": len(test_data),
                "缓存命中率": f"{performance_analysis.get('cache_hit_rate', 0):.1f}%",
                "数据库性能": "优秀" if query_time < 0.001 else "良好" if query_time < 0.01 else "需要优化"
            }

        except Exception as e:
            return {"error": f"数据库测试失败: {e}"}

    def test_concurrent_analysis(self) -> Dict[str, Any]:
        """测试并发分析"""
        import concurrent.futures

        def analyze_stock(stock_data):
            """分析单只股票"""
            # 模拟复杂分析
            time.sleep(0.1)
            return {
                "symbol": f"STOCK{len(stock_data)}",
                "analysis_result": np.mean(stock_data),
                "processed_time": time.time()
            }

        # 准备测试数据
        stocks_data = [np.random.randn(100) for _ in range(20)]

        # 测试串行处理
        start_time = time.time()
        serial_results = []
        for i, data in enumerate(stocks_data):
            result = analyze_stock(data)
            serial_results.append(result)
            self.progress_updated.emit(i * 2.5)

        serial_time = time.time() - start_time

        # 测试并行处理
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            parallel_results = list(executor.map(analyze_stock, stocks_data))

        parallel_time = time.time() - start_time

        self.progress_updated.emit(100)

        speedup = serial_time / parallel_time if parallel_time > 0 else 0

        return {
            "串行处理时间": f"{serial_time:.3f}秒",
            "并行处理时间": f"{parallel_time:.3f}秒",
            "并发加速比": f"{speedup:.2f}倍",
            "处理股票数": len(stocks_data),
            "并发效率": "高效" if speedup > 3 else "一般" if speedup > 1.5 else "低效"
        }

    def stop(self):
        """停止测试"""
        self.running = False


class Week15PerformanceTestSuite(QMainWindow):
    """第15周性能优化测试套件"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("第15周性能优化和部署准备 - 完整测试套件")
        self.setGeometry(100, 100, 1400, 900)

        # 测试工作线程
        self.test_workers: Dict[str, PerformanceTestWorker] = {}

        # 测试结果
        self.test_results: Dict[str, Dict[str, Any]] = {}

        self.setup_ui()
        self.apply_styles()

        logger.info("第15周性能测试套件初始化完成")

    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("🚀 第15周性能优化和部署准备 - 完整功能测试")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2196F3;
                padding: 15px;
                background: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # 标签页控件
        tab_widget = QTabWidget()

        # 性能测试标签页
        performance_tab = self._create_performance_tab()
        tab_widget.addTab(performance_tab, "⚡ 性能测试")

        # 测试结果标签页
        results_tab = self._create_results_tab()
        tab_widget.addTab(results_tab, "📊 测试结果")

        # 系统监控标签页
        monitor_tab = self._create_monitor_tab()
        tab_widget.addTab(monitor_tab, "📈 系统监控")

        # 部署测试标签页
        deploy_tab = self._create_deploy_tab()
        tab_widget.addTab(deploy_tab, "📦 部署测试")

        main_layout.addWidget(tab_widget)

        # 状态栏
        self.statusBar().showMessage("准备开始性能优化测试...")

        # 定时器更新系统监控
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_monitor)
        self.monitor_timer.start(2000)  # 每2秒更新一次

    def _create_performance_tab(self) -> QWidget:
        """创建性能测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 测试控制组
        control_group = QGroupBox("🎮 性能测试控制")
        control_layout = QVBoxLayout(control_group)

        # 测试按钮
        test_buttons = [
            ("算法性能测试", "algorithm_performance", "测试威科夫分析、相对强弱计算等核心算法性能"),
            ("内存优化测试", "memory_optimization", "测试内存使用优化和垃圾回收效果"),
            ("异步处理测试", "async_processing", "测试多线程和异步处理性能提升"),
            ("数据库性能测试", "database_performance", "测试查询优化、连接池、缓存效果"),
            ("并发分析测试", "concurrent_analysis", "测试多股票并发分析处理能力")
        ]

        self.test_progress_bars = {}

        for name, test_type, description in test_buttons:
            # 测试按钮和描述
            button_layout = QHBoxLayout()

            button = QPushButton(f"🧪 {name}")
            button.clicked.connect(lambda checked, t=test_type: self.start_performance_test(t))
            button_layout.addWidget(button)

            desc_label = QLabel(description)
            desc_label.setStyleSheet("color: #666; font-size: 12px;")
            button_layout.addWidget(desc_label, 1)

            control_layout.addLayout(button_layout)

            # 进度条
            progress_bar = QProgressBar()
            progress_bar.setVisible(False)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    text-align: center;
                    height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #4CAF50;
                    border-radius: 3px;
                }
            """)
            control_layout.addWidget(progress_bar)
            self.test_progress_bars[test_type] = progress_bar

        layout.addWidget(control_group)

        # 全面测试按钮
        comprehensive_button = QPushButton("🔄 运行全面性能测试")
        comprehensive_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                font-size: 14px;
                padding: 12px;
                margin: 10px;
            }
        """)
        comprehensive_button.clicked.connect(self.start_comprehensive_test)
        layout.addWidget(comprehensive_button)

        # 性能优化按钮
        optimize_button = QPushButton("⚡ 执行系统优化")
        optimize_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                font-size: 14px;
                padding: 12px;
                margin: 10px;
            }
        """)
        optimize_button.clicked.connect(self.optimize_system)
        layout.addWidget(optimize_button)

        layout.addStretch()
        return widget

    def _create_results_tab(self) -> QWidget:
        """创建测试结果标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels(["测试项目", "测试结果", "性能评级"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.results_table)

        # 详细结果文本
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMaximumHeight(200)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.results_text)

        # 清除按钮
        clear_button = QPushButton("清除结果")
        clear_button.clicked.connect(self.clear_results)
        layout.addWidget(clear_button)

        return widget

    def _create_monitor_tab(self) -> QWidget:
        """创建系统监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 系统状态组
        status_group = QGroupBox("💻 系统状态")
        status_layout = QVBoxLayout(status_group)

        self.system_status_text = QTextEdit()
        self.system_status_text.setReadOnly(True)
        self.system_status_text.setMaximumHeight(300)
        self.system_status_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        status_layout.addWidget(self.system_status_text)

        layout.addWidget(status_group)

        # 性能报告组
        report_group = QGroupBox("📋 性能报告")
        report_layout = QVBoxLayout(report_group)

        self.performance_report_text = QTextEdit()
        self.performance_report_text.setReadOnly(True)
        self.performance_report_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        report_layout.addWidget(self.performance_report_text)

        layout.addWidget(report_group)

        return widget

    def _create_deploy_tab(self) -> QWidget:
        """创建部署测试标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 部署测试组
        deploy_group = QGroupBox("📦 部署和打包测试")
        deploy_layout = QVBoxLayout(deploy_group)

        # 打包测试按钮
        package_button = QPushButton("🔨 测试应用程序打包")
        package_button.clicked.connect(self.test_packaging)
        deploy_layout.addWidget(package_button)

        # 依赖检查按钮
        deps_button = QPushButton("📋 检查依赖完整性")
        deps_button.clicked.connect(self.check_dependencies)
        deploy_layout.addWidget(deps_button)

        # 配置验证按钮
        config_button = QPushButton("⚙️ 验证配置文件")
        config_button.clicked.connect(self.validate_configs)
        deploy_layout.addWidget(config_button)

        layout.addWidget(deploy_group)

        # 部署结果
        self.deploy_results_text = QTextEdit()
        self.deploy_results_text.setReadOnly(True)
        self.deploy_results_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.deploy_results_text)

        return widget

    def start_performance_test(self, test_type: str):
        """开始性能测试"""
        if test_type in self.test_workers:
            return  # 测试已在运行

        # 显示进度条
        progress_bar = self.test_progress_bars[test_type]
        progress_bar.setVisible(True)
        progress_bar.setValue(0)

        # 创建并启动测试线程
        worker = PerformanceTestWorker(test_type)
        worker.progress_updated.connect(progress_bar.setValue)
        worker.result_ready.connect(self.on_test_completed)
        worker.start()

        self.test_workers[test_type] = worker
        self.log_message(f"开始{test_type}测试...")

    @pyqtSlot(str, dict)
    def on_test_completed(self, test_type: str, result: Dict[str, Any]):
        """测试完成回调"""
        # 隐藏进度条
        progress_bar = self.test_progress_bars[test_type]
        progress_bar.setVisible(False)

        # 保存结果
        self.test_results[test_type] = result

        # 更新结果显示
        self.update_results_display()

        # 清理工作线程
        if test_type in self.test_workers:
            del self.test_workers[test_type]

        self.log_message(f"{test_type}测试完成")

    def start_comprehensive_test(self):
        """开始全面测试"""
        self.log_message("🚀 开始全面性能测试...")

        test_types = [
            "algorithm_performance",
            "memory_optimization",
            "async_processing",
            "database_performance",
            "concurrent_analysis"
        ]

        for test_type in test_types:
            if test_type not in self.test_workers:
                QTimer.singleShot(1000, lambda t=test_type: self.start_performance_test(t))

    def optimize_system(self):
        """执行系统优化"""
        try:
            self.log_message("⚡ 开始系统优化...")

            # 执行性能优化
            optimization_results = performance_optimizer.optimize_system()

            self.log_message("✅ 系统优化完成:")
            for key, value in optimization_results.items():
                self.log_message(f"   {key}: {value}")

        except Exception as e:
            self.log_message(f"❌ 系统优化失败: {e}")

    def update_results_display(self):
        """更新结果显示"""
        # 清空表格
        self.results_table.setRowCount(0)

        # 添加测试结果
        for test_type, results in self.test_results.items():
            if "error" in results:
                continue

            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            # 测试项目
            test_name = {
                "algorithm_performance": "算法性能测试",
                "memory_optimization": "内存优化测试",
                "async_processing": "异步处理测试",
                "database_performance": "数据库性能测试",
                "concurrent_analysis": "并发分析测试"
            }.get(test_type, test_type)

            self.results_table.setItem(row, 0, QTableWidgetItem(test_name))

            # 测试结果（取第一个关键指标）
            key_metric = list(results.keys())[0] if results else "无结果"
            result_value = results.get(key_metric, "N/A")
            self.results_table.setItem(row, 1, QTableWidgetItem(str(result_value)))

            # 性能评级
            rating = self._get_performance_rating(test_type, results)
            self.results_table.setItem(row, 2, QTableWidgetItem(rating))

        # 调整列宽
        self.results_table.resizeColumnsToContents()

    def _get_performance_rating(self, test_type: str, results: Dict[str, Any]) -> str:
        """获取性能评级"""
        if "性能评级" in results:
            return results["性能评级"]
        elif "数据库性能" in results:
            return results["数据库性能"]
        elif "异步优势" in results:
            return results["异步优势"]
        elif "并发效率" in results:
            return results["并发效率"]
        else:
            return "良好"

    def update_system_monitor(self):
        """更新系统监控"""
        try:
            # 获取系统性能状态
            system_performance = performance_optimizer.get_system_performance()

            # 更新系统状态显示
            status_text = f"""系统性能监控 - {time.strftime('%H:%M:%S')}
{'='*50}

CPU使用率: {system_performance['cpu']['usage_percent']:.1f}%
CPU核心数: {system_performance['cpu']['core_count']}

内存总量: {system_performance['memory']['total_gb']:.1f}GB
内存可用: {system_performance['memory']['available_gb']:.1f}GB
内存使用率: {system_performance['memory']['usage_percent']:.1f}%
进程内存: {system_performance['memory']['process_memory_mb']:.1f}MB

磁盘总量: {system_performance['disk']['total_gb']:.1f}GB
磁盘可用: {system_performance['disk']['free_gb']:.1f}GB
磁盘使用率: {system_performance['disk']['usage_percent']:.1f}%
"""

            self.system_status_text.setPlainText(status_text)

            # 更新性能报告
            performance_report = system_performance.get('performance_report', {})
            cache_stats = system_performance.get('cache_stats', {})

            report_text = f"""性能分析报告
{'='*50}

函数调用统计:
总函数数: {performance_report.get('total_functions', 0)}
总调用次数: {performance_report.get('total_calls', 0)}

缓存统计:
缓存大小: {cache_stats.get('cache_size', 0)}
缓存命中: {cache_stats.get('cache_hits', 0)}
缓存未命中: {cache_stats.get('cache_misses', 0)}
命中率: {cache_stats.get('hit_rate', 0):.1f}%

性能状态: {'优秀' if system_performance['memory']['usage_percent'] < 70 else '良好' if system_performance['memory']['usage_percent'] < 85 else '需要优化'}
"""

            self.performance_report_text.setPlainText(report_text)

        except Exception as e:
            logger.error(f"更新系统监控失败: {e}")

    def test_packaging(self):
        """测试应用程序打包"""
        try:
            self.log_deploy_message("🔨 开始测试应用程序打包...")

            # 检查打包脚本是否存在
            package_script = "scripts/build_package.py"
            if os.path.exists(package_script):
                self.log_deploy_message("✅ 打包脚本存在")

                # 检查PyInstaller是否可用
                try:
                    import PyInstaller
                    self.log_deploy_message("✅ PyInstaller已安装")
                except ImportError:
                    self.log_deploy_message("⚠️ PyInstaller未安装，需要安装")

                # 检查必要文件
                required_files = ["main.py", "requirements.txt", "config.yaml"]
                for file in required_files:
                    if os.path.exists(file):
                        self.log_deploy_message(f"✅ {file} 存在")
                    else:
                        self.log_deploy_message(f"❌ {file} 缺失")

                self.log_deploy_message("📦 打包测试完成")

            else:
                self.log_deploy_message("❌ 打包脚本不存在")

        except Exception as e:
            self.log_deploy_message(f"❌ 打包测试失败: {e}")

    def check_dependencies(self):
        """检查依赖完整性"""
        try:
            self.log_deploy_message("📋 开始检查依赖完整性...")

            # 读取requirements.txt
            if os.path.exists("requirements.txt"):
                with open("requirements.txt", 'r', encoding='utf-8') as f:
                    requirements = f.readlines()

                missing_deps = []
                available_deps = []

                for req in requirements:
                    req = req.strip()
                    if req and not req.startswith('#'):
                        package_name = req.split('>=')[0].split('==')[0].split('<')[0]
                        try:
                            __import__(package_name.replace('-', '_'))
                            available_deps.append(package_name)
                        except ImportError:
                            missing_deps.append(package_name)

                self.log_deploy_message(f"✅ 可用依赖: {len(available_deps)}个")
                self.log_deploy_message(f"❌ 缺失依赖: {len(missing_deps)}个")

                if missing_deps:
                    self.log_deploy_message("缺失的依赖:")
                    for dep in missing_deps:
                        self.log_deploy_message(f"   - {dep}")

            else:
                self.log_deploy_message("❌ requirements.txt文件不存在")

        except Exception as e:
            self.log_deploy_message(f"❌ 依赖检查失败: {e}")

    def validate_configs(self):
        """验证配置文件"""
        try:
            self.log_deploy_message("⚙️ 开始验证配置文件...")

            config_files = ["config.yaml", "config/user_preferences.json"]

            for config_file in config_files:
                if os.path.exists(config_file):
                    self.log_deploy_message(f"✅ {config_file} 存在")

                    # 尝试加载配置
                    try:
                        if config_file.endswith('.yaml'):
                            import yaml
                            with open(config_file, 'r', encoding='utf-8') as f:
                                yaml.safe_load(f)
                        elif config_file.endswith('.json'):
                            import json
                            with open(config_file, 'r', encoding='utf-8') as f:
                                json.load(f)

                        self.log_deploy_message(f"✅ {config_file} 格式正确")

                    except Exception as e:
                        self.log_deploy_message(f"❌ {config_file} 格式错误: {e}")
                else:
                    self.log_deploy_message(f"⚠️ {config_file} 不存在")

            self.log_deploy_message("⚙️ 配置文件验证完成")

        except Exception as e:
            self.log_deploy_message(f"❌ 配置验证失败: {e}")

    def log_message(self, message: str):
        """记录消息到结果文本"""
        timestamp = time.strftime("%H:%M:%S")
        self.results_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()

    def log_deploy_message(self, message: str):
        """记录部署消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.deploy_results_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()

    def clear_results(self):
        """清除结果"""
        self.results_text.clear()
        self.results_table.setRowCount(0)
        self.test_results.clear()

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
        """)

    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有测试线程
        for worker in self.test_workers.values():
            worker.stop()
            worker.wait()

        # 停止定时器
        self.monitor_timer.stop()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("威科夫选股系统 - 第15周性能测试")
    app.setApplicationVersion("1.0")

    # 创建并显示测试窗口
    window = Week15PerformanceTestSuite()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()