-- 威科夫相对强弱选股系统 - 数据库表结构定义
-- 版本: 1.0.0
-- 创建时间: 2024-12-28

-- =============================================
-- 板块基础信息表
-- =============================================
CREATE TABLE IF NOT EXISTS sector_info (
    sector_code VARCHAR(20) PRIMARY KEY,
    sector_name VARCHAR(100) NOT NULL,
    sector_type VARCHAR(20) NOT NULL CHECK (sector_type IN ('industry', 'concept')),
    parent_code VARCHAR(20),
    level INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    create_date DATE DEFAULT (date('now')),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_code) REFERENCES sector_info(sector_code)
);

-- =============================================
-- 板块历史行情表
-- =============================================
CREATE TABLE IF NOT EXISTS sector_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sector_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open DECIMAL(10,3),
    high DECIMAL(10,3),
    low DECIMAL(10,3),
    close DECIMAL(10,3) NOT NULL,
    volume BIGINT DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    change_pct DECIMAL(8,4),
    turnover_rate DECIMAL(8,4),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(sector_code, trade_date),
    FOREIGN KEY (sector_code) REFERENCES sector_info(sector_code)
);

-- =============================================
-- 个股基础信息表
-- =============================================
CREATE TABLE IF NOT EXISTS stock_info (
    stock_code VARCHAR(20) PRIMARY KEY,
    stock_name VARCHAR(100) NOT NULL,
    list_date DATE,
    delist_date DATE,
    market VARCHAR(10) CHECK (market IN ('SH', 'SZ', 'BJ')),
    status VARCHAR(10) DEFAULT 'active' CHECK (status IN ('active', 'delisted', 'suspended', 'st')),
    industry VARCHAR(50),
    market_cap DECIMAL(15,2),
    total_shares BIGINT,
    float_shares BIGINT,
    is_st BOOLEAN DEFAULT 0,
    create_date DATE DEFAULT (date('now')),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 个股历史行情表
-- =============================================
CREATE TABLE IF NOT EXISTS stock_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open DECIMAL(10,3),
    high DECIMAL(10,3),
    low DECIMAL(10,3),
    close DECIMAL(10,3) NOT NULL,
    volume BIGINT DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    adj_factor DECIMAL(10,6) DEFAULT 1.0,
    change_pct DECIMAL(8,4),
    turnover_rate DECIMAL(8,4),
    pe_ratio DECIMAL(8,2),
    pb_ratio DECIMAL(8,2),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stock_code, trade_date),
    FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
);

-- =============================================
-- 板块成分股关系表
-- =============================================
CREATE TABLE IF NOT EXISTS sector_constituents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sector_code VARCHAR(20) NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100),
    weight DECIMAL(8,4) DEFAULT 0,
    in_date DATE DEFAULT (date('now')),
    out_date DATE,
    is_active BOOLEAN DEFAULT 1,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(sector_code, stock_code, in_date),
    FOREIGN KEY (sector_code) REFERENCES sector_info(sector_code),
    FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
);

-- =============================================
-- 市场指数信息表
-- =============================================
CREATE TABLE IF NOT EXISTS market_indices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    index_code VARCHAR(20) UNIQUE NOT NULL,
    index_name VARCHAR(100) NOT NULL,
    market VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 市场指数历史行情表
-- =============================================
CREATE TABLE IF NOT EXISTS market_index_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    index_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open DECIMAL(10,2) NOT NULL,
    high DECIMAL(10,2) NOT NULL,
    low DECIMAL(10,2) NOT NULL,
    close DECIMAL(10,2) NOT NULL,
    volume BIGINT DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    change_pct DECIMAL(8,4),
    prev_close DECIMAL(10,2),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(index_code, trade_date),
    FOREIGN KEY (index_code) REFERENCES market_indices(index_code)
);

-- =============================================
-- 相对强弱计算结果表
-- =============================================
CREATE TABLE IF NOT EXISTS relative_strength_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol VARCHAR(20) NOT NULL,
    symbol_type VARCHAR(10) NOT NULL CHECK (symbol_type IN ('stock', 'sector')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    period_days INTEGER NOT NULL,
    return_rate DECIMAL(8,4),
    benchmark_return DECIMAL(8,4),
    relative_strength DECIMAL(8,4),
    rank_in_type INTEGER,
    percentile DECIMAL(5,2),
    calculation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, symbol_type, start_date, end_date)
);

-- =============================================
-- 筛选结果记录表
-- =============================================
CREATE TABLE IF NOT EXISTS selection_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(50) NOT NULL,
    task_name VARCHAR(100),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    benchmark_symbol VARCHAR(20) DEFAULT '000300.SH',
    benchmark_return DECIMAL(8,4),
    top_sectors INTEGER DEFAULT 5,
    stocks_per_sector INTEGER DEFAULT 3,
    total_selected_stocks INTEGER,
    execution_time DECIMAL(8,3),
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('running', 'completed', 'failed')),
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 筛选结果详情表
-- =============================================
CREATE TABLE IF NOT EXISTS selection_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    symbol_type VARCHAR(10) NOT NULL CHECK (symbol_type IN ('stock', 'sector')),
    symbol_name VARCHAR(100),
    return_rate DECIMAL(8,4),
    relative_strength DECIMAL(8,4),
    rank_order INTEGER,
    sector_code VARCHAR(20),
    selection_reason TEXT,
    FOREIGN KEY (task_id) REFERENCES selection_results(task_id)
);

-- =============================================
-- 系统配置表
-- =============================================
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string' CHECK (config_type IN ('string', 'integer', 'float', 'boolean', 'json')),
    description TEXT,
    is_encrypted BOOLEAN DEFAULT 0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 数据同步日志表
-- =============================================
CREATE TABLE IF NOT EXISTS sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_source VARCHAR(50) NOT NULL,
    sync_type VARCHAR(20) NOT NULL CHECK (sync_type IN ('full', 'incremental')),
    table_name VARCHAR(50) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    records_processed INTEGER DEFAULT 0,
    records_inserted INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed')),
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- 创建索引以优化查询性能
-- =============================================

-- 板块信息表索引
CREATE INDEX IF NOT EXISTS idx_sector_info_type ON sector_info(sector_type);
CREATE INDEX IF NOT EXISTS idx_sector_info_active ON sector_info(is_active);
CREATE INDEX IF NOT EXISTS idx_sector_info_parent ON sector_info(parent_code);

-- 板块行情表索引
CREATE INDEX IF NOT EXISTS idx_sector_quotes_date ON sector_quotes(trade_date);
CREATE INDEX IF NOT EXISTS idx_sector_quotes_code_date ON sector_quotes(sector_code, trade_date);
CREATE INDEX IF NOT EXISTS idx_sector_quotes_date_range ON sector_quotes(trade_date DESC);

-- 个股信息表索引
CREATE INDEX IF NOT EXISTS idx_stock_info_market ON stock_info(market);
CREATE INDEX IF NOT EXISTS idx_stock_info_status ON stock_info(status);
CREATE INDEX IF NOT EXISTS idx_stock_info_industry ON stock_info(industry);
CREATE INDEX IF NOT EXISTS idx_stock_info_st ON stock_info(is_st);
CREATE INDEX IF NOT EXISTS idx_stock_info_list_date ON stock_info(list_date);

-- 个股行情表索引
CREATE INDEX IF NOT EXISTS idx_stock_quotes_date ON stock_quotes(trade_date);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_code_date ON stock_quotes(stock_code, trade_date);
CREATE INDEX IF NOT EXISTS idx_stock_quotes_date_range ON stock_quotes(trade_date DESC);

-- 板块成分股关系表索引
CREATE INDEX IF NOT EXISTS idx_sector_constituents_sector ON sector_constituents(sector_code);
CREATE INDEX IF NOT EXISTS idx_sector_constituents_stock ON sector_constituents(stock_code);
CREATE INDEX IF NOT EXISTS idx_sector_constituents_active ON sector_constituents(is_active);
CREATE INDEX IF NOT EXISTS idx_sector_constituents_date ON sector_constituents(in_date, out_date);

-- 相对强弱结果表索引
CREATE INDEX IF NOT EXISTS idx_rs_results_symbol ON relative_strength_results(symbol);
CREATE INDEX IF NOT EXISTS idx_rs_results_type ON relative_strength_results(symbol_type);
CREATE INDEX IF NOT EXISTS idx_rs_results_date ON relative_strength_results(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_rs_results_strength ON relative_strength_results(relative_strength DESC);

-- 筛选结果表索引
CREATE INDEX IF NOT EXISTS idx_selection_results_task ON selection_results(task_id);
CREATE INDEX IF NOT EXISTS idx_selection_results_date ON selection_results(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_selection_results_time ON selection_results(create_time DESC);

-- 筛选详情表索引
CREATE INDEX IF NOT EXISTS idx_selection_details_task ON selection_details(task_id);
CREATE INDEX IF NOT EXISTS idx_selection_details_symbol ON selection_details(symbol);
CREATE INDEX IF NOT EXISTS idx_selection_details_type ON selection_details(symbol_type);
CREATE INDEX IF NOT EXISTS idx_selection_details_rank ON selection_details(rank_order);

-- 系统配置表索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);

-- 同步日志表索引
CREATE INDEX IF NOT EXISTS idx_sync_logs_source ON sync_logs(data_source);
CREATE INDEX IF NOT EXISTS idx_sync_logs_table ON sync_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_logs_time ON sync_logs(create_time DESC);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(status);

-- =============================================
-- 创建视图以简化常用查询
-- =============================================

-- 活跃股票信息视图
CREATE VIEW IF NOT EXISTS v_active_stocks AS
SELECT 
    si.stock_code,
    si.stock_name,
    si.market,
    si.industry,
    si.market_cap,
    si.list_date,
    si.is_st
FROM stock_info si
WHERE si.status = 'active' 
    AND si.delist_date IS NULL;

-- 板块成分股当前关系视图
CREATE VIEW IF NOT EXISTS v_current_sector_constituents AS
SELECT 
    sc.sector_code,
    si.sector_name,
    sc.stock_code,
    sti.stock_name,
    sc.weight,
    sc.in_date
FROM sector_constituents sc
JOIN sector_info si ON sc.sector_code = si.sector_code
JOIN stock_info sti ON sc.stock_code = sti.stock_code
WHERE sc.is_active = 1 
    AND sc.out_date IS NULL
    AND si.is_active = 1
    AND sti.status = 'active';

-- 最新行情数据视图
CREATE VIEW IF NOT EXISTS v_latest_stock_quotes AS
SELECT 
    sq.stock_code,
    si.stock_name,
    sq.trade_date,
    sq.close,
    sq.change_pct,
    sq.volume,
    sq.amount,
    sq.turnover_rate
FROM stock_quotes sq
JOIN stock_info si ON sq.stock_code = si.stock_code
WHERE sq.trade_date = (
    SELECT MAX(trade_date) 
    FROM stock_quotes sq2 
    WHERE sq2.stock_code = sq.stock_code
);

-- =============================================
-- 初始化系统配置数据
-- =============================================
INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('db_version', '1.0.0', 'string', '数据库版本号'),
('last_update_time', datetime('now'), 'string', '最后更新时间'),
('data_retention_days', '365', 'integer', '数据保留天数'),
('auto_backup_enabled', 'true', 'boolean', '是否启用自动备份'),
('backup_interval_hours', '24', 'integer', '备份间隔小时数'),
('max_concurrent_connections', '10', 'integer', '最大并发连接数'),
('query_timeout_seconds', '30', 'integer', '查询超时时间（秒）'),
('batch_insert_size', '1000', 'integer', '批量插入大小'),
('enable_query_log', 'false', 'boolean', '是否启用查询日志'),
('performance_monitoring', 'true', 'boolean', '是否启用性能监控');

-- =============================================
-- 创建触发器以维护数据一致性
-- =============================================

-- 更新时间触发器 - 板块信息表
CREATE TRIGGER IF NOT EXISTS tr_sector_info_update_time
    AFTER UPDATE ON sector_info
BEGIN
    UPDATE sector_info 
    SET update_time = CURRENT_TIMESTAMP 
    WHERE sector_code = NEW.sector_code;
END;

-- 更新时间触发器 - 个股信息表
CREATE TRIGGER IF NOT EXISTS tr_stock_info_update_time
    AFTER UPDATE ON stock_info
BEGIN
    UPDATE stock_info 
    SET update_time = CURRENT_TIMESTAMP 
    WHERE stock_code = NEW.stock_code;
END;

-- 更新时间触发器 - 板块成分股关系表
CREATE TRIGGER IF NOT EXISTS tr_sector_constituents_update_time
    AFTER UPDATE ON sector_constituents
BEGIN
    UPDATE sector_constituents 
    SET update_time = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- 数据完整性触发器 - 防止删除有行情数据的股票
CREATE TRIGGER IF NOT EXISTS tr_prevent_stock_deletion
    BEFORE DELETE ON stock_info
    FOR EACH ROW
    WHEN EXISTS (SELECT 1 FROM stock_quotes WHERE stock_code = OLD.stock_code)
BEGIN
    SELECT RAISE(ABORT, '无法删除有历史行情数据的股票，请先清理相关数据');
END;

-- 数据完整性触发器 - 防止删除有成分股的板块
CREATE TRIGGER IF NOT EXISTS tr_prevent_sector_deletion
    BEFORE DELETE ON sector_info
    FOR EACH ROW
    WHEN EXISTS (SELECT 1 FROM sector_constituents WHERE sector_code = OLD.sector_code AND is_active = 1)
BEGIN
    SELECT RAISE(ABORT, '无法删除有活跃成分股的板块，请先移除成分股关系');
END;