"""
第13周系统集成测试
测试各模块间的兼容性和集成情况
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pandas as pd

from src.data_sources.manager import DataSourceManager
from src.data_sources.base import DataSourceConfig
from src.engines.wyckoff import WyckoffAnalysisEngine
from src.engines.relative_strength import RelativeStrengthEngine
from src.engines.selection import StockSelectionEngine
from src.database.manager import DatabaseManager
from src.config.config_manager import ConfigManager
from src.utils.cache import CacheManager
from src.utils.monitor import SystemMonitor


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """配置管理器"""
        return ConfigManager()
    
    @pytest.fixture
    def database_manager(self):
        """数据库管理器"""
        return DatabaseManager()
    
    @pytest.fixture
    def data_source_manager(self):
        """数据源管理器"""
        return DataSourceManager()
    
    @pytest.fixture
    def cache_manager(self):
        """缓存管理器"""
        return CacheManager()
    
    @pytest.fixture
    def system_monitor(self):
        """系统监控器"""
        return SystemMonitor()
    
    @pytest.fixture
    def sample_market_data(self):
        """样本市场数据"""
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        data = []
        
        for i, date in enumerate(dates):
            # 生成模拟股票数据
            base_price = 100 + i * 0.1
            data.append({
                'trade_date': date.strftime('%Y-%m-%d'),
                'open': base_price + (i % 5) * 0.5,
                'high': base_price + (i % 7) * 0.8,
                'low': base_price - (i % 3) * 0.3,
                'close': base_price + (i % 4) * 0.2,
                'volume': 1000000 + (i % 1000) * 1000
            })
        
        return pd.DataFrame(data)
    
    def test_config_database_integration(self, config_manager, database_manager):
        """测试配置管理器与数据库管理器的集成"""
        # 测试配置加载
        config = config_manager.get_config()
        assert config is not None
        
        # 测试数据库连接配置
        db_config = config.get('database', {})
        assert isinstance(db_config, dict)
        
        # 测试数据库初始化
        result = database_manager.initialize()
        assert result is True
        
        # 测试数据库连接
        assert database_manager.is_connected() is True
    
    def test_data_source_database_integration(self, data_source_manager, database_manager):
        """测试数据源管理器与数据库管理器的集成"""
        # 初始化数据库
        database_manager.initialize()
        
        # 测试数据源管理器状态
        available_sources = data_source_manager.get_available_sources()
        assert isinstance(available_sources, list)
        
        # 测试数据源统计信息
        all_stats = data_source_manager.get_all_stats()
        assert isinstance(all_stats, dict)
    
    def test_cache_integration(self, cache_manager, sample_market_data):
        """测试缓存系统集成"""
        # 测试缓存设置和获取
        cache_key = "test_market_data"
        cache_manager.set(cache_key, sample_market_data.to_dict())
        
        cached_data = cache_manager.get(cache_key)
        assert cached_data is not None
        
        # 测试缓存统计
        stats = cache_manager.get_stats()
        assert isinstance(stats, dict)
        assert stats['hits'] >= 0
        assert stats['misses'] >= 0
    
    def test_analysis_engines_integration(self, sample_market_data):
        """测试分析引擎集成"""
        # 创建分析引擎
        wyckoff_engine = WyckoffAnalysisEngine()
        rs_engine = RelativeStrengthEngine()
        selection_engine = StockSelectionEngine()
        
        # 测试威科夫分析引擎
        wyckoff_result = wyckoff_engine.analyze_market_structure(sample_market_data)
        assert wyckoff_result is not None
        assert hasattr(wyckoff_result, 'phase')
        assert hasattr(wyckoff_result, 'price_trend')
        
        # 测试相对强弱引擎
        benchmark_data = sample_market_data.copy()
        benchmark_data['close'] = benchmark_data['close'] * 0.9  # 基准表现稍差
        
        rs_value = rs_engine.calculate_rs_value(sample_market_data, benchmark_data)
        assert rs_value is not None
        assert isinstance(rs_value, float)
        assert rs_value > 0
        
        # 测试选股引擎
        from src.engines.selection import WyckoffStrategy, StrategyConfig, StrategyType
        
        # 添加策略
        wyckoff_config = StrategyConfig(
            name="test_wyckoff",
            strategy_type=StrategyType.WYCKOFF_ACCUMULATION,
            weight=1.0
        )
        wyckoff_strategy = WyckoffStrategy(wyckoff_config)
        selection_engine.add_strategy(wyckoff_strategy)
        
        selection_engine.set_benchmark(benchmark_data)
        stocks_data = {"TEST_STOCK": sample_market_data}  # 修正为字典格式
        selection_result = selection_engine.select_stocks(stocks_data)
        assert isinstance(selection_result, list)
    
    def test_system_monitor_integration(self, system_monitor):
        """测试系统监控集成"""
        # 测试系统资源监控
        resource_info = system_monitor.get_system_resources()
        assert isinstance(resource_info, dict)
        assert 'cpu_percent' in resource_info
        assert 'memory_percent' in resource_info
        assert 'disk_usage' in resource_info
        
        # 测试进程监控
        process_info = system_monitor.get_process_info()
        assert isinstance(process_info, dict)
        assert 'pid' in process_info
        assert 'memory_info' in process_info
        
        # 测试网络监控
        network_info = system_monitor.get_network_info()
        assert isinstance(network_info, dict)
    
    def test_error_handling_integration(self, data_source_manager):
        """测试错误处理集成"""
        # 测试数据源错误处理
        try:
            # 尝试获取不存在的数据源
            nonexistent_source = data_source_manager.get_source_by_name("nonexistent")
            assert nonexistent_source is None
        except Exception as e:
            # 确保异常被正确处理
            assert isinstance(e, Exception)
    
    def test_performance_integration(self, sample_market_data):
        """测试性能集成"""
        import time
        
        # 测试威科夫分析性能
        wyckoff_engine = WyckoffAnalysisEngine()
        
        start_time = time.time()
        result = wyckoff_engine.analyze_market_structure(sample_market_data)
        end_time = time.time()
        
        # 确保分析在合理时间内完成（< 1秒）
        analysis_time = end_time - start_time
        assert analysis_time < 1.0, f"威科夫分析耗时过长: {analysis_time:.3f}秒"
        
        # 测试相对强弱分析性能
        rs_engine = RelativeStrengthEngine()
        benchmark_data = sample_market_data.copy()
        
        start_time = time.time()
        rs_value = rs_engine.calculate_rs_value(sample_market_data, benchmark_data)
        end_time = time.time()
        
        rs_time = end_time - start_time
        assert rs_time < 1.0, f"相对强弱分析耗时过长: {rs_time:.3f}秒"
    
    def test_memory_usage_integration(self, sample_market_data, system_monitor):
        """测试内存使用集成"""
        # 获取初始内存使用
        initial_memory = system_monitor.get_process_info()['memory_info']['rss']
        
        # 执行一些内存密集型操作
        wyckoff_engine = WyckoffAnalysisEngine()
        rs_engine = RelativeStrengthEngine()
        
        # 多次分析以测试内存泄漏
        for i in range(10):
            wyckoff_result = wyckoff_engine.analyze_market_structure(sample_market_data)
            rs_value = rs_engine.calculate_rs_value(sample_market_data, sample_market_data)
        
        # 获取最终内存使用
        final_memory = system_monitor.get_process_info()['memory_info']['rss']
        
        # 确保内存增长在合理范围内（< 100MB）
        memory_growth = final_memory - initial_memory
        assert memory_growth < 100 * 1024 * 1024, f"内存增长过大: {memory_growth / 1024 / 1024:.2f}MB"
    
    def test_concurrent_operations_integration(self, sample_market_data):
        """测试并发操作集成"""
        import threading
        import time
        
        results = []
        errors = []
        
        def analysis_worker(data, worker_id):
            """分析工作线程"""
            try:
                wyckoff_engine = WyckoffAnalysisEngine()
                result = wyckoff_engine.analyze_market_structure(data)
                results.append(f"Worker {worker_id} completed")
            except Exception as e:
                errors.append(f"Worker {worker_id} failed: {e}")
        
        # 创建多个工作线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=analysis_worker, args=(sample_market_data, i))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)  # 10秒超时
        
        # 检查结果
        assert len(errors) == 0, f"并发操作出现错误: {errors}"
        assert len(results) == 5, f"并发操作完成数量不正确: {len(results)}"
    
    def test_data_flow_integration(self, database_manager, cache_manager, sample_market_data):
        """测试数据流集成"""
        # 初始化数据库
        database_manager.initialize()
        
        # 测试数据存储流程
        table_name = "test_market_data"
        
        # 存储数据到数据库
        result = database_manager.execute_query(
            f"CREATE TABLE IF NOT EXISTS {table_name} (id INTEGER PRIMARY KEY, data TEXT)"
        )
        assert result is True
        
        # 缓存数据
        cache_key = f"{table_name}_cache"
        cache_manager.set(cache_key, sample_market_data.to_dict())
        
        # 验证缓存
        cached_data = cache_manager.get(cache_key)
        assert cached_data is not None
        
        # 清理
        database_manager.execute_query(f"DROP TABLE IF EXISTS {table_name}")
        cache_manager.clear()
    
    def test_configuration_consistency(self, config_manager):
        """测试配置一致性"""
        # 测试配置加载
        config = config_manager.get_config()
        assert config is not None
        
        # 测试必要的配置项
        required_sections = ['database', 'cache', 'logging', 'system']
        for section in required_sections:
            assert section in config, f"缺少必要的配置节: {section}"
        
        # 测试配置验证
        validation_result = config_manager.validate_config()
        assert validation_result is True
    
    def test_system_shutdown_integration(self, database_manager, cache_manager, system_monitor):
        """测试系统关闭集成"""
        # 初始化各组件
        database_manager.initialize()
        
        # 测试优雅关闭
        try:
            # 关闭数据库连接
            database_manager.close()
            
            # 清理缓存
            cache_manager.clear()
            
            # 停止监控
            system_monitor.stop()
            
            # 验证关闭状态
            assert database_manager.is_connected() is False
            
        except Exception as e:
            pytest.fail(f"系统关闭过程中出现错误: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 