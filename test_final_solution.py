#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解决方案测试脚本
验证修复后的数据源是否能正常工作
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logger
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="test_final",
        level="INFO",
        log_to_file=True,
        log_to_console=True
    )


def test_improved_adapter():
    """测试改进后的适配器"""
    print("=" * 60)
    print("测试改进后的XtData适配器")
    print("=" * 60)
    
    try:
        # 创建配置
        config = DataSourceConfig(
            name="test_improved",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                "host": "127.0.0.1",
                "port": 58610
            }
        )
        
        # 创建适配器
        adapter = XtDataAdapter(config)
        print("✅ 适配器创建成功")
        
        # 测试连接
        if adapter.connect():
            print("✅ 适配器连接成功")
            
            # 测试获取股票列表
            stock_list = adapter.get_stock_list()
            if stock_list and len(stock_list) > 0:
                print(f"✅ 获取股票列表成功，共 {len(stock_list)} 只股票")
                
                # 测试获取市场数据（应该能够使用实时数据替代）
                test_symbol = "000001.SZ"  # 平安银行
                print(f"\n测试获取 {test_symbol} 的市场数据...")
                
                try:
                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
                    
                    market_data = adapter.get_market_data(
                        symbol=test_symbol,
                        period='1d',
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if market_data and not market_data.data.empty:
                        print(f"✅ 获取市场数据成功！")
                        print(f"  - 数据行数: {len(market_data.data)}")
                        print(f"  - 数据列: {list(market_data.data.columns)}")
                        print(f"  - 数据源: {market_data.source}")
                        print(f"  - 数据类型: {market_data.data_type}")
                        
                        # 显示数据样例
                        print("\n数据样例:")
                        print(market_data.data.head())
                        
                        return True
                    else:
                        print("❌ 获取市场数据失败或数据为空")
                        return False
                        
                except Exception as e:
                    print(f"❌ 获取市场数据异常: {e}")
                    return False
            else:
                print("❌ 获取股票列表失败")
                return False
        else:
            print("❌ 适配器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_stocks():
    """测试多只股票数据获取"""
    print("\n" + "=" * 60)
    print("测试多只股票数据获取")
    print("=" * 60)
    
    try:
        config = DataSourceConfig(
            name="test_multiple",
            enabled=True,
            config={"host": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        
        if adapter.connect():
            # 测试多只知名股票
            test_symbols = ["000001.SZ", "600000.SH", "000002.SZ"]
            success_count = 0
            
            for symbol in test_symbols:
                try:
                    print(f"\n测试股票: {symbol}")
                    
                    market_data = adapter.get_market_data(
                        symbol=symbol,
                        period='1d'
                    )
                    
                    if market_data and not market_data.data.empty:
                        print(f"  ✅ 成功获取数据，{len(market_data.data)} 行")
                        success_count += 1
                    else:
                        print(f"  ❌ 数据为空")
                        
                except Exception as e:
                    print(f"  ❌ 获取失败: {e}")
            
            print(f"\n总结: {success_count}/{len(test_symbols)} 只股票获取成功")
            return success_count > 0
        else:
            print("❌ 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 多股票测试失败: {e}")
        return False


def test_data_service_integration():
    """测试数据服务集成"""
    print("\n" + "=" * 60)
    print("测试数据服务集成")
    print("=" * 60)
    
    try:
        from src.services.data_service import DataService
        from src.data_sources.manager import DataSourceManager
        
        # 创建数据源管理器
        data_manager = DataSourceManager()
        
        # 创建XtData适配器
        config = DataSourceConfig(
            name="test_service",
            enabled=True,
            config={"host": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        data_manager.add_source("xtdata", adapter, priority=10)
        
        # 创建数据服务
        data_service = DataService(
            data_source_manager=data_manager,
            use_real_data=True
        )
        
        print("✅ 数据服务创建成功")
        
        # 测试获取股票列表
        stocks = data_service.get_stock_list()
        if stocks and len(stocks) > 0:
            print(f"✅ 通过数据服务获取股票列表成功，共 {len(stocks)} 只股票")
            return True
        else:
            print("❌ 通过数据服务获取股票列表失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据服务集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("最终解决方案测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    setup_logging()
    
    # 执行测试
    tests = [
        ("改进后的适配器", test_improved_adapter),
        ("多只股票数据获取", test_multiple_stocks),
        ("数据服务集成", test_data_service_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed_tests = sum(1 for _, result in results if result)
    total_tests = len(results)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试数: {passed_tests}")
    print(f"失败测试数: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！数据源问题已解决。")
        print("\n系统现在可以:")
        print("- ✅ 连接XtData数据源")
        print("- ✅ 获取股票列表")
        print("- ✅ 获取实时市场数据")
        print("- ✅ 在历史数据不可用时自动使用实时数据")
        print("- ✅ 提供详细的错误诊断信息")
        return 0
    else:
        print(f"\n⚠️  部分测试失败，但系统基本功能可用。")
        print("建议:")
        print("1. 检查MiniQMT客户端状态")
        print("2. 确认网络连接")
        print("3. 考虑使用模拟数据模式进行开发")
        return 1


if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
