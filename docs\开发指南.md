# 威科夫相对强弱选股系统 - 开发指南

## 开发环境设置

### 系统要求
- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 最低4GB内存，推荐8GB
- 至少10GB可用存储空间

### 环境搭建步骤

1. **克隆项目**
```bash
git clone <repository_url>
cd xdqr
```

2. **创建并激活虚拟环境**
```bash
python -m venv .venv
.venv\Scripts\activate
```

3. **升级pip并安装依赖**
```bash
python -m pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

4. **验证安装**
```bash
python main.py
```

## 项目结构说明

```
xdqr/
├── src/                    # 源代码目录
│   ├── core/              # 核心业务逻辑
│   ├── data_sources/      # 数据源接口
│   ├── database/          # 数据库管理
│   ├── engines/           # 计算引擎
│   ├── ui/                # 用户界面
│   ├── utils/             # 工具函数
│   └── config/            # 配置管理
├── tests/                 # 测试代码
├── docs/                  # 文档目录
├── data/                  # 数据存储
├── logs/                  # 日志文件
├── resources/             # 静态资源
└── memory-bank/           # 项目记忆库
```

## 开发规范

### 代码风格
- 遵循PEP 8编码规范
- 使用类型提示
- 编写完整的文档字符串
- 行长度限制为88字符

### 代码质量工具

1. **代码格式化**
```bash
black src/
```

2. **代码风格检查**
```bash
flake8 src/
```

3. **类型检查**
```bash
mypy src/
```

4. **运行测试**
```bash
pytest tests/ --cov=src
```

### 提交规范
- 使用有意义的提交信息
- 遵循约定式提交格式
- 每次提交前运行代码质量检查

## 开发流程

### 功能开发流程
1. 创建功能分支
2. 编写代码和测试
3. 运行代码质量检查
4. 提交代码并创建PR
5. 代码审查和合并

### 测试策略
- 单元测试：测试独立功能模块
- 集成测试：测试模块间交互
- 界面测试：使用pytest-qt测试GUI
- 覆盖率目标：>80%

## 配置管理

### 配置文件
- `config.yaml`：主配置文件
- `pyproject.toml`：项目配置和工具设置
- `.flake8`：代码风格检查配置

### 环境变量
可以通过环境变量覆盖配置文件设置：
- `XDQR_DEBUG=true`：启用调试模式
- `XDQR_LOG_LEVEL=DEBUG`：设置日志级别

## 调试和日志

### 日志系统
项目使用loguru作为日志库，提供：
- 控制台彩色输出
- 文件自动轮转
- 结构化日志记录

### 使用示例
```python
from src.utils.logger import get_logger

logger = get_logger(__name__)
logger.info("这是信息日志")
logger.error("这是错误日志")
```

## 数据源集成

### XtData集成
- 需要安装MiniQMT客户端
- 配置连接参数
- 测试数据获取功能

### 其他数据源
- TuShare Pro API
- AkShare
- 自定义数据源

## 性能优化

### 建议
- 使用NumPy向量化计算
- 实现适当的缓存机制
- 使用多进程处理独立任务
- 优化数据库查询

### 性能监控
- 使用memory_profiler监控内存使用
- 使用line_profiler分析代码性能
- 设置性能基准测试

## 故障排除

### 常见问题
1. **虚拟环境问题**
   - 确保正确激活虚拟环境
   - 重新创建虚拟环境

2. **依赖安装问题**
   - 检查Python版本兼容性
   - 使用国内镜像源

3. **数据源连接问题**
   - 检查MiniQMT客户端状态
   - 验证网络连接

### 日志分析
- 查看logs/app.log文件
- 使用DEBUG级别获取详细信息
- 检查错误堆栈信息

## 贡献指南

### 代码贡献
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 文档贡献
- 改进现有文档
- 添加使用示例
- 翻译文档

### 问题反馈
- 使用GitHub Issues
- 提供详细的问题描述
- 包含复现步骤

## 部署和打包

### 开发版本
```bash
python main.py
```

### 生产版本打包
```bash
# 使用PyInstaller打包
pyinstaller --onefile --windowed main.py
```

### 安装包制作
- 使用NSIS制作Windows安装包
- 包含必要的依赖和资源文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者
- 参与项目讨论

---

**注意**：本指南会随着项目发展持续更新，请定期查看最新版本。