#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验改进测试和验证
第14周：用户体验优化
"""

import sys
import os
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QTextEdit, QComboBox,
    QGroupBox, QScrollArea, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.utils.user_friendly_errors import (
    UserFriendlyErrorDialog, error_translator, UserFriendlyError, ErrorSolution, ErrorSeverity
)
from src.utils.exceptions import DataSourceError, CalculationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class UXTestWindow(QMainWindow):
    """用户体验测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("用户体验优化测试 - 第14周")
        self.setGeometry(100, 100, 1000, 700)
        
        # 设置中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧：测试控制面板
        self.setup_test_panel(main_layout)
        
        # 右侧：结果展示区域
        self.setup_result_panel(main_layout)
        
        # 应用样式
        self.apply_styles()
        
        logger.info("用户体验测试窗口初始化完成")
    
    def setup_test_panel(self, main_layout):
        """设置测试控制面板"""
        test_panel = QGroupBox("错误消息测试")
        test_panel.setFixedWidth(350)
        test_layout = QVBoxLayout(test_panel)
        
        # 错误类型选择
        type_label = QLabel("选择错误类型:")
        type_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        test_layout.addWidget(type_label)
        
        self.error_type_combo = QComboBox()
        self.error_type_combo.addItems([
            "XtData连接失败",
            "网络连接超时", 
            "身份验证失败",
            "数据不足",
            "计算错误",
            "界面初始化失败",
            "文件未找到",
            "权限不足",
            "通用错误",
            "系统崩溃"
        ])
        test_layout.addWidget(self.error_type_combo)
        
        # 测试按钮
        test_button = QPushButton("🧪 测试错误消息")
        test_button.clicked.connect(self.test_error_message)
        test_layout.addWidget(test_button)
        
        test_layout.addWidget(QLabel())  # 分隔符
        
        # 用户体验测试按钮
        ux_tests = [
            ("📱 界面响应性测试", self.test_ui_responsiveness),
            ("🎯 操作流程测试", self.test_operation_flow),
            ("💡 提示信息测试", self.test_tooltip_system),
            ("⚙️ 用户偏好测试", self.test_user_preferences),
            ("📚 帮助系统测试", self.test_help_system)
        ]
        
        for text, handler in ux_tests:
            button = QPushButton(text)
            button.clicked.connect(handler)
            test_layout.addWidget(button)
        
        test_layout.addStretch()
        main_layout.addWidget(test_panel)
    
    def setup_result_panel(self, main_layout):
        """设置结果展示面板"""
        result_panel = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_panel)
        
        # 结果文本区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        result_layout.addWidget(self.result_text)
        
        # 清除按钮
        clear_button = QPushButton("清除结果")
        clear_button.clicked.connect(self.result_text.clear)
        result_layout.addWidget(clear_button)
        
        main_layout.addWidget(result_panel)
    
    def test_error_message(self):
        """测试错误消息"""
        error_type = self.error_type_combo.currentText()
        
        try:
            # 根据选择的类型创建相应的错误
            if error_type == "XtData连接失败":
                error = DataSourceError("XtData模块不可用，请检查MiniQMT安装")
            elif error_type == "网络连接超时":
                error = DataSourceError("连接超时，网络连接失败")
            elif error_type == "身份验证失败":
                error = DataSourceError("认证失败，用户名或密码错误")
            elif error_type == "数据不足":
                error = CalculationError("数据不足，无法进行有效分析")
            elif error_type == "计算错误":
                error = ZeroDivisionError("division by zero")
            elif error_type == "界面初始化失败":
                error = RuntimeError("界面初始化失败，控件创建失败")
            elif error_type == "文件未找到":
                error = FileNotFoundError("配置文件不存在")
            elif error_type == "权限不足":
                error = PermissionError("权限拒绝，访问被拒绝")
            elif error_type == "通用错误":
                error = ValueError("参数值错误")
            else:  # 系统崩溃
                error = Exception("系统内部错误，内存访问违规")
            
            # 显示用户友好错误对话框
            UserFriendlyErrorDialog.show_error(self, error, {"test_context": True})
            
            # 记录测试结果
            self.log_test_result(f"✅ 错误消息测试完成: {error_type}")
            
        except Exception as e:
            self.log_test_result(f"❌ 错误消息测试失败: {e}")
    
    def test_ui_responsiveness(self):
        """测试界面响应性"""
        self.log_test_result("🔄 开始界面响应性测试...")
        
        # 模拟耗时操作
        import time
        start_time = time.time()
        
        # 测试界面更新
        for i in range(10):
            QApplication.processEvents()
            time.sleep(0.01)  # 模拟短暂延迟
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        if response_time < 100:
            self.log_test_result(f"✅ 界面响应性测试通过: {response_time:.1f}ms")
        else:
            self.log_test_result(f"⚠️ 界面响应性需要优化: {response_time:.1f}ms")
    
    def test_operation_flow(self):
        """测试操作流程"""
        self.log_test_result("📋 开始操作流程测试...")
        
        # 模拟用户操作流程
        operations = [
            "启动应用程序",
            "连接数据源",
            "加载股票列表", 
            "选择股票",
            "执行分析",
            "查看结果",
            "导出数据"
        ]
        
        for i, operation in enumerate(operations):
            self.log_test_result(f"   {i+1}. {operation} - 模拟完成")
            QApplication.processEvents()
        
        self.log_test_result("✅ 操作流程测试完成")
    
    def test_tooltip_system(self):
        """测试提示信息系统"""
        self.log_test_result("💡 开始提示信息系统测试...")
        
        # 测试工具提示
        test_widget = QPushButton("测试按钮")
        test_widget.setToolTip("这是一个测试工具提示")
        
        # 检查工具提示是否设置成功
        if test_widget.toolTip():
            self.log_test_result("✅ 工具提示系统正常")
        else:
            self.log_test_result("❌ 工具提示系统异常")
        
        # 测试状态栏消息
        self.statusBar().showMessage("测试状态栏消息", 2000)
        self.log_test_result("✅ 状态栏消息系统正常")
    
    def test_user_preferences(self):
        """测试用户偏好设置"""
        self.log_test_result("⚙️ 开始用户偏好设置测试...")
        
        # 模拟偏好设置
        preferences = {
            "theme": "light",
            "language": "zh_CN",
            "auto_save": True,
            "update_interval": 5000
        }
        
        for key, value in preferences.items():
            self.log_test_result(f"   设置 {key}: {value}")
        
        self.log_test_result("✅ 用户偏好设置测试完成")
    
    def test_help_system(self):
        """测试帮助系统"""
        self.log_test_result("📚 开始帮助系统测试...")
        
        # 模拟帮助内容
        help_topics = [
            "快速入门指南",
            "功能介绍",
            "常见问题",
            "快捷键说明",
            "联系支持"
        ]
        
        for topic in help_topics:
            self.log_test_result(f"   帮助主题: {topic} - 可用")
        
        self.log_test_result("✅ 帮助系统测试完成")
    
    def log_test_result(self, message: str):
        """记录测试结果"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.result_text.append(f"[{timestamp}] {message}")
        QApplication.processEvents()
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QComboBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("威科夫选股系统 - UX测试")
    app.setApplicationVersion("1.0")
    
    # 创建并显示测试窗口
    window = UXTestWindow()
    window.show()
    
    # 显示欢迎消息
    window.log_test_result("🎉 用户体验优化测试系统启动完成")
    window.log_test_result("📝 请选择要测试的功能，验证用户体验改进效果")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
