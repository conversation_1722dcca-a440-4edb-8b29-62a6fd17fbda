#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据下载脚本

自动从XtData下载股票数据并存储到数据库
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig
from src.database.database_manager import DatabaseManager
from src.services.data_download_service import DataDownloadService, DownloadProgress
from src.config.config_manager import ConfigManager
from src.utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="data_download",
        level="INFO",
        log_to_file=True,
        log_to_console=True
    )


def progress_callback(progress: DownloadProgress):
    """进度回调函数"""
    stage_names = {
        'stock_list': '获取股票列表',
        'basic_info': '下载基本信息',
        'price_data': '下载历史数据'
    }
    
    stage_name = stage_names.get(progress.stage, progress.stage)
    
    print(f"\r[{stage_name}] {progress.progress_percent:.1f}% "
          f"({progress.completed_stocks}/{progress.total_stocks}) "
          f"当前: {progress.current_stock} "
          f"错误: {progress.error_count}", end='', flush=True)


async def main():
    """主函数"""
    try:
        print("🚀 开始下载股票数据...")
        
        # 设置日志
        setup_logging()
        
        # 加载配置
        config_manager = ConfigManager()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
        config_data = config_manager.load_config("config", config_path)
        
        # 创建数据源管理器
        data_manager = DataSourceManager()
        
        # 配置XtData数据源
        xtdata_config = config_data.get("data_sources", {}).get("xtdata", {})
        if not xtdata_config.get("enabled", True):
            print("❌ XtData数据源未启用")
            return False
        
        print("📡 正在连接XtData数据源...")
        
        # 创建数据源配置
        config = DataSourceConfig(
            name=xtdata_config.get("name", "XtData"),
            enabled=xtdata_config.get("enabled", True),
            timeout=xtdata_config.get("timeout", 30),
            retry_times=xtdata_config.get("retry_times", 3),
            auto_reconnect=xtdata_config.get("auto_reconnect", True),
            config=xtdata_config.get("config", {})
        )
        
        # 创建XtData数据源
        xtdata_source = XtDataAdapter(config)
        
        # 添加到管理器并测试连接
        data_manager.add_source("xtdata", xtdata_source, priority=10)
        
        if not data_manager.test_connection("xtdata"):
            print("❌ XtData数据源连接失败")
            return False
        
        print("✅ XtData数据源连接成功")
        
        # 创建数据库管理器
        print("💾 初始化数据库...")
        db_manager = DatabaseManager()
        
        # 创建数据下载服务
        download_service = DataDownloadService(data_manager, db_manager)
        
        # 检查是否已有股票基本信息
        stock_count = db_manager.get_stock_count()
        print(f"📋 当前数据库中有 {stock_count} 只股票")

        if stock_count >= 5150:
            print("✅ 股票基本信息已完整，开始下载历史数据...")
            include_history = True
            skip_basic_info = True
        else:
            print("📊 需要下载股票基本信息和历史数据...")
            include_history = True
            skip_basic_info = False

        # 获取下载配置
        download_config = config_data.get('data_download', {})
        history_days = download_config.get('history_days', 365)

        print(f"📊 开始下载数据 (包含{history_days}天历史数据: {include_history})")
        print("=" * 60)

        # 开始下载
        success = await download_service.download_all_data(
            progress_callback=progress_callback,
            include_history=include_history,
            history_days=history_days,
            skip_basic_info=skip_basic_info
        )
        
        print("\n" + "=" * 60)
        
        if success:
            print("🎉 数据下载完成！")
            
            # 显示统计信息
            stats = db_manager.get_database_stats()
            print(f"📈 统计信息:")
            print(f"   - 股票数量: {stats.get('total_stocks', 0)}")
            print(f"   - 行情记录: {stats.get('total_quotes', 0)}")
            print(f"   - 最新交易日: {stats.get('latest_trade_date', 'N/A')}")
            print(f"   - 数据库大小: {stats.get('db_size_mb', 0):.2f} MB")
            
            return True
        else:
            print("❌ 数据下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 下载过程中发生错误: {e}")
        logger.error(f"数据下载失败: {e}")
        return False


if __name__ == "__main__":
    # 运行下载
    success = asyncio.run(main())
    
    if success:
        print("\n✅ 数据下载任务完成")
        sys.exit(0)
    else:
        print("\n❌ 数据下载任务失败")
        sys.exit(1)
