#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源问题修复脚本
自动诊断并修复股票数据下载问题
"""

import sys
import os
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="fix_data_source",
        level="INFO",
        log_to_file=True,
        log_to_console=True
    )


def check_xtdata_availability():
    """检查XtData是否可用"""
    try:
        import xtquant.xtdata as xtdata
        # 尝试获取股票列表
        stock_list = xtdata.get_stock_list_in_sector("沪深A股")
        if stock_list and len(stock_list) > 0:
            print(f"✅ XtData可用，获取到 {len(stock_list)} 只股票")
            return True
        else:
            print("❌ XtData不可用：无法获取股票列表")
            return False
    except ImportError:
        print("❌ XtData不可用：xtquant库未安装")
        return False
    except Exception as e:
        print(f"❌ XtData不可用：{e}")
        return False


def update_config_for_mock_data():
    """更新配置以使用模拟数据"""
    config_path = "config.yaml"
    
    try:
        # 读取现有配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新配置
        if 'data_download' not in config:
            config['data_download'] = {}
        
        config['data_download']['use_real_data'] = False
        config['data_download']['enabled'] = True
        
        # 备份原配置
        backup_path = f"{config_path}.backup"
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 写入新配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ 配置已更新为使用模拟数据模式")
        print(f"原配置已备份到: {backup_path}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False


def set_mock_data_environment():
    """设置模拟数据环境变量"""
    os.environ['USE_MOCK_DATA'] = 'true'
    print("✅ 已设置模拟数据环境变量")


def create_mock_data_if_needed():
    """如果需要，创建模拟数据"""
    mock_file = "mock_xtdata.py"
    
    if os.path.exists(mock_file):
        print(f"✅ 模拟数据文件已存在: {mock_file}")
        return True
    
    print(f"❌ 模拟数据文件不存在: {mock_file}")
    print("请确保mock_xtdata.py文件存在于项目根目录")
    return False


def test_data_service():
    """测试数据服务"""
    try:
        from src.services.data_service import DataService
        
        # 创建数据服务（使用模拟数据）
        data_service = DataService(use_real_data=False)
        
        # 测试获取股票列表
        stocks = data_service.get_stock_list()
        if stocks and len(stocks) > 0:
            print(f"✅ 数据服务测试成功，获取到 {len(stocks)} 只股票")
            return True
        else:
            print("❌ 数据服务测试失败：无法获取股票列表")
            return False
            
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        return False


def fix_import_issues():
    """修复导入问题"""
    try:
        # 检查关键模块是否可以导入
        from src.data_sources.xtdata_adapter import XtDataAdapter
        from src.data_sources.base import DataSourceConfig
        from src.services.data_service import DataService
        
        print("✅ 关键模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def create_startup_script():
    """创建启动脚本"""
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据系统启动脚本（模拟数据模式）
"""

import os
import sys
from pathlib import Path

# 设置模拟数据模式
os.environ['USE_MOCK_DATA'] = 'true'

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

if __name__ == "__main__":
    from main import main
    main()
'''
    
    script_path = "start_with_mock_data.py"
    
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ 创建启动脚本: {script_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False


def main():
    """主函数"""
    print("股票数据源问题修复工具")
    print("=" * 60)
    
    setup_logging()
    
    # 1. 检查XtData可用性
    print("1. 检查XtData可用性...")
    xtdata_available = check_xtdata_availability()
    
    if xtdata_available:
        print("✅ XtData可用，无需修复")
        return 0
    
    print("\n2. XtData不可用，开始修复...")
    
    # 2. 检查模拟数据文件
    print("检查模拟数据文件...")
    if not create_mock_data_if_needed():
        print("❌ 无法使用模拟数据，请检查mock_xtdata.py文件")
        return 1
    
    # 3. 设置环境变量
    print("设置模拟数据环境...")
    set_mock_data_environment()
    
    # 4. 更新配置
    print("更新配置文件...")
    if not update_config_for_mock_data():
        print("❌ 配置更新失败")
        return 1
    
    # 5. 检查导入
    print("检查模块导入...")
    if not fix_import_issues():
        print("❌ 模块导入失败")
        return 1
    
    # 6. 测试数据服务
    print("测试数据服务...")
    if not test_data_service():
        print("❌ 数据服务测试失败")
        return 1
    
    # 7. 创建启动脚本
    print("创建启动脚本...")
    create_startup_script()
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)
    print("现在可以使用以下方式启动系统：")
    print("1. 运行: python start_with_mock_data.py")
    print("2. 或者设置环境变量 USE_MOCK_DATA=true 后运行 python main.py")
    print("\n注意：系统现在使用模拟数据模式，不会连接真实的股票数据源。")
    print("如果需要使用真实数据，请安装并配置XtData客户端。")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
