"""
选股配置和结果展示控件

提供选股参数配置和结果展示功能
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QProgressBar, QTextEdit, QSplitter,
    QTabWidget, QSlider, QLineEdit, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from ...utils.logger import get_logger
from ...services.data_service import DataService

logger = get_logger(__name__)


class SelectionWidget(QWidget):
    """选股配置和结果展示控件"""
    
    # 信号定义
    selection_started = pyqtSignal(dict)  # 选股参数
    selection_completed = pyqtSignal(dict)  # 选股结果
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化选股控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)

        # 数据服务
        self.data_service = DataService()

        # 选股结果
        self.selection_results: List[Dict[str, Any]] = []
        self.is_selecting: bool = False

        # 控件引用
        self.config_tabs: Optional[QTabWidget] = None
        self.results_table: Optional[QTableWidget] = None
        self.progress_bar: Optional[QProgressBar] = None
        self.log_text: Optional[QTextEdit] = None
        self.start_button: Optional[QPushButton] = None
        self.stop_button: Optional[QPushButton] = None

        # 参数控件引用
        self.wyckoff_params = {}
        self.rs_params = {}
        self.filter_params = {}

        self._init_ui()
        self._load_default_config()

        logger.info("选股控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("🎯 智能选股系统")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：配置区域
        config_widget = self._create_config_widget()
        splitter.addWidget(config_widget)
        
        # 下半部分：结果区域
        results_widget = self._create_results_widget()
        splitter.addWidget(results_widget)
        
        # 设置分割比例
        splitter.setSizes([300, 500])
        
        layout.addWidget(splitter)
    
    def _create_config_widget(self) -> QWidget:
        """创建配置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 创建标签页
        self.config_tabs = QTabWidget()

        # 威科夫参数配置标签页
        wyckoff_tab = self._create_wyckoff_config_tab()
        self.config_tabs.addTab(wyckoff_tab, "🔍 威科夫参数")

        # 相对强弱参数配置标签页
        rs_tab = self._create_rs_config_tab()
        self.config_tabs.addTab(rs_tab, "📊 相对强弱参数")

        # 筛选条件配置标签页
        filter_tab = self._create_filter_config_tab()
        self.config_tabs.addTab(filter_tab, "🎯 筛选条件")

        # 策略组合配置标签页
        strategy_tab = self._create_strategy_config_tab()
        self.config_tabs.addTab(strategy_tab, "⚖️ 策略组合")

        layout.addWidget(self.config_tabs)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("🚀 开始选股")
        self.start_button.clicked.connect(self._start_selection)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px 16px; }")
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("⏹️ 停止选股")
        self.stop_button.clicked.connect(self._stop_selection)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; padding: 8px 16px; }")
        button_layout.addWidget(self.stop_button)

        reset_button = QPushButton("🔄 重置参数")
        reset_button.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_button)

        save_button = QPushButton("💾 保存配置")
        save_button.clicked.connect(self._save_config)
        button_layout.addWidget(save_button)

        load_button = QPushButton("📂 加载配置")
        load_button.clicked.connect(self._load_config)
        button_layout.addWidget(load_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        return widget

    def _create_wyckoff_config_tab(self) -> QWidget:
        """创建威科夫参数配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 威科夫阶段参数
        phase_group = QGroupBox("市场阶段参数")
        phase_layout = QGridLayout(phase_group)

        # 累积阶段参数
        phase_layout.addWidget(QLabel("累积阶段权重:"), 0, 0)
        self.wyckoff_params['accumulation_weight'] = QDoubleSpinBox()
        self.wyckoff_params['accumulation_weight'].setRange(0.0, 1.0)
        self.wyckoff_params['accumulation_weight'].setSingleStep(0.1)
        self.wyckoff_params['accumulation_weight'].setValue(0.8)
        phase_layout.addWidget(self.wyckoff_params['accumulation_weight'], 0, 1)

        # 上涨阶段参数
        phase_layout.addWidget(QLabel("上涨阶段权重:"), 0, 2)
        self.wyckoff_params['markup_weight'] = QDoubleSpinBox()
        self.wyckoff_params['markup_weight'].setRange(0.0, 1.0)
        self.wyckoff_params['markup_weight'].setSingleStep(0.1)
        self.wyckoff_params['markup_weight'].setValue(0.6)
        phase_layout.addWidget(self.wyckoff_params['markup_weight'], 0, 3)

        # 派发阶段参数
        phase_layout.addWidget(QLabel("派发阶段权重:"), 1, 0)
        self.wyckoff_params['distribution_weight'] = QDoubleSpinBox()
        self.wyckoff_params['distribution_weight'].setRange(0.0, 1.0)
        self.wyckoff_params['distribution_weight'].setSingleStep(0.1)
        self.wyckoff_params['distribution_weight'].setValue(0.2)
        phase_layout.addWidget(self.wyckoff_params['distribution_weight'], 1, 1)

        # 下跌阶段参数
        phase_layout.addWidget(QLabel("下跌阶段权重:"), 1, 2)
        self.wyckoff_params['markdown_weight'] = QDoubleSpinBox()
        self.wyckoff_params['markdown_weight'].setRange(0.0, 1.0)
        self.wyckoff_params['markdown_weight'].setSingleStep(0.1)
        self.wyckoff_params['markdown_weight'].setValue(0.1)
        phase_layout.addWidget(self.wyckoff_params['markdown_weight'], 1, 3)

        scroll_layout.addWidget(phase_group)

        # 置信度参数
        confidence_group = QGroupBox("置信度参数")
        confidence_layout = QGridLayout(confidence_group)

        confidence_layout.addWidget(QLabel("最低置信度:"), 0, 0)
        self.wyckoff_params['min_confidence'] = QDoubleSpinBox()
        self.wyckoff_params['min_confidence'].setRange(0.0, 1.0)
        self.wyckoff_params['min_confidence'].setSingleStep(0.05)
        self.wyckoff_params['min_confidence'].setValue(0.6)
        confidence_layout.addWidget(self.wyckoff_params['min_confidence'], 0, 1)

        confidence_layout.addWidget(QLabel("高置信度阈值:"), 0, 2)
        self.wyckoff_params['high_confidence'] = QDoubleSpinBox()
        self.wyckoff_params['high_confidence'].setRange(0.0, 1.0)
        self.wyckoff_params['high_confidence'].setSingleStep(0.05)
        self.wyckoff_params['high_confidence'].setValue(0.8)
        confidence_layout.addWidget(self.wyckoff_params['high_confidence'], 0, 3)

        scroll_layout.addWidget(confidence_group)

        # 技术指标参数
        technical_group = QGroupBox("技术指标参数")
        technical_layout = QGridLayout(technical_group)

        technical_layout.addWidget(QLabel("成交量确认:"), 0, 0)
        self.wyckoff_params['volume_confirmation'] = QCheckBox()
        self.wyckoff_params['volume_confirmation'].setChecked(True)
        technical_layout.addWidget(self.wyckoff_params['volume_confirmation'], 0, 1)

        technical_layout.addWidget(QLabel("价格确认:"), 0, 2)
        self.wyckoff_params['price_confirmation'] = QCheckBox()
        self.wyckoff_params['price_confirmation'].setChecked(True)
        technical_layout.addWidget(self.wyckoff_params['price_confirmation'], 0, 3)

        scroll_layout.addWidget(technical_group)
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return widget

    def _create_rs_config_tab(self) -> QWidget:
        """创建相对强弱参数配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # RS值参数
        rs_group = QGroupBox("RS值参数")
        rs_layout = QGridLayout(rs_group)

        rs_layout.addWidget(QLabel("最低RS值:"), 0, 0)
        self.rs_params['min_rs_value'] = QDoubleSpinBox()
        self.rs_params['min_rs_value'].setRange(0.0, 3.0)
        self.rs_params['min_rs_value'].setSingleStep(0.1)
        self.rs_params['min_rs_value'].setValue(1.0)
        rs_layout.addWidget(self.rs_params['min_rs_value'], 0, 1)

        rs_layout.addWidget(QLabel("强势RS阈值:"), 0, 2)
        self.rs_params['strong_rs_threshold'] = QDoubleSpinBox()
        self.rs_params['strong_rs_threshold'].setRange(0.0, 3.0)
        self.rs_params['strong_rs_threshold'].setSingleStep(0.1)
        self.rs_params['strong_rs_threshold'].setValue(1.2)
        rs_layout.addWidget(self.rs_params['strong_rs_threshold'], 0, 3)

        rs_layout.addWidget(QLabel("超强RS阈值:"), 1, 0)
        self.rs_params['very_strong_rs_threshold'] = QDoubleSpinBox()
        self.rs_params['very_strong_rs_threshold'].setRange(0.0, 3.0)
        self.rs_params['very_strong_rs_threshold'].setSingleStep(0.1)
        self.rs_params['very_strong_rs_threshold'].setValue(1.5)
        rs_layout.addWidget(self.rs_params['very_strong_rs_threshold'], 1, 1)

        scroll_layout.addWidget(rs_group)

        # 排名参数
        ranking_group = QGroupBox("排名参数")
        ranking_layout = QGridLayout(ranking_group)

        ranking_layout.addWidget(QLabel("最低排名百分位:"), 0, 0)
        self.rs_params['min_percentile'] = QDoubleSpinBox()
        self.rs_params['min_percentile'].setRange(0.0, 100.0)
        self.rs_params['min_percentile'].setSingleStep(5.0)
        self.rs_params['min_percentile'].setValue(70.0)
        ranking_layout.addWidget(self.rs_params['min_percentile'], 0, 1)

        ranking_layout.addWidget(QLabel("顶级排名百分位:"), 0, 2)
        self.rs_params['top_percentile'] = QDoubleSpinBox()
        self.rs_params['top_percentile'].setRange(0.0, 100.0)
        self.rs_params['top_percentile'].setSingleStep(5.0)
        self.rs_params['top_percentile'].setValue(90.0)
        ranking_layout.addWidget(self.rs_params['top_percentile'], 0, 3)

        scroll_layout.addWidget(ranking_group)

        # 趋势参数
        trend_group = QGroupBox("趋势参数")
        trend_layout = QGridLayout(trend_group)

        trend_layout.addWidget(QLabel("最低动量分数:"), 0, 0)
        self.rs_params['min_momentum'] = QDoubleSpinBox()
        self.rs_params['min_momentum'].setRange(0.0, 1.0)
        self.rs_params['min_momentum'].setSingleStep(0.1)
        self.rs_params['min_momentum'].setValue(0.5)
        trend_layout.addWidget(self.rs_params['min_momentum'], 0, 1)

        trend_layout.addWidget(QLabel("最低趋势强度:"), 0, 2)
        self.rs_params['min_trend_strength'] = QDoubleSpinBox()
        self.rs_params['min_trend_strength'].setRange(0.0, 1.0)
        self.rs_params['min_trend_strength'].setSingleStep(0.1)
        self.rs_params['min_trend_strength'].setValue(0.6)
        trend_layout.addWidget(self.rs_params['min_trend_strength'], 0, 3)

        scroll_layout.addWidget(trend_group)
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return widget

    def _create_filter_config_tab(self) -> QWidget:
        """创建筛选条件配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 价格筛选
        price_group = QGroupBox("价格筛选")
        price_layout = QGridLayout(price_group)

        price_layout.addWidget(QLabel("启用价格筛选:"), 0, 0)
        self.filter_params['enable_price_filter'] = QCheckBox()
        self.filter_params['enable_price_filter'].setChecked(True)
        price_layout.addWidget(self.filter_params['enable_price_filter'], 0, 1)

        price_layout.addWidget(QLabel("最低价格:"), 1, 0)
        self.filter_params['min_price'] = QDoubleSpinBox()
        self.filter_params['min_price'].setRange(0.0, 1000.0)
        self.filter_params['min_price'].setSingleStep(1.0)
        self.filter_params['min_price'].setValue(5.0)
        price_layout.addWidget(self.filter_params['min_price'], 1, 1)

        price_layout.addWidget(QLabel("最高价格:"), 1, 2)
        self.filter_params['max_price'] = QDoubleSpinBox()
        self.filter_params['max_price'].setRange(0.0, 10000.0)
        self.filter_params['max_price'].setSingleStep(10.0)
        self.filter_params['max_price'].setValue(500.0)
        price_layout.addWidget(self.filter_params['max_price'], 1, 3)

        scroll_layout.addWidget(price_group)

        # 成交量筛选
        volume_group = QGroupBox("成交量筛选")
        volume_layout = QGridLayout(volume_group)

        volume_layout.addWidget(QLabel("启用成交量筛选:"), 0, 0)
        self.filter_params['enable_volume_filter'] = QCheckBox()
        self.filter_params['enable_volume_filter'].setChecked(True)
        volume_layout.addWidget(self.filter_params['enable_volume_filter'], 0, 1)

        volume_layout.addWidget(QLabel("最低成交量(万):"), 1, 0)
        self.filter_params['min_volume'] = QSpinBox()
        self.filter_params['min_volume'].setRange(0, 1000000)
        self.filter_params['min_volume'].setSingleStep(1000)
        self.filter_params['min_volume'].setValue(10000)
        volume_layout.addWidget(self.filter_params['min_volume'], 1, 1)

        volume_layout.addWidget(QLabel("成交量比率:"), 1, 2)
        self.filter_params['volume_ratio'] = QDoubleSpinBox()
        self.filter_params['volume_ratio'].setRange(0.0, 10.0)
        self.filter_params['volume_ratio'].setSingleStep(0.1)
        self.filter_params['volume_ratio'].setValue(1.2)
        volume_layout.addWidget(self.filter_params['volume_ratio'], 1, 3)

        scroll_layout.addWidget(volume_group)

        # 市值筛选
        market_cap_group = QGroupBox("市值筛选")
        market_cap_layout = QGridLayout(market_cap_group)

        market_cap_layout.addWidget(QLabel("启用市值筛选:"), 0, 0)
        self.filter_params['enable_market_cap_filter'] = QCheckBox()
        self.filter_params['enable_market_cap_filter'].setChecked(False)
        market_cap_layout.addWidget(self.filter_params['enable_market_cap_filter'], 0, 1)

        market_cap_layout.addWidget(QLabel("最低市值(亿):"), 1, 0)
        self.filter_params['min_market_cap'] = QSpinBox()
        self.filter_params['min_market_cap'].setRange(0, 100000)
        self.filter_params['min_market_cap'].setSingleStep(10)
        self.filter_params['min_market_cap'].setValue(50)
        market_cap_layout.addWidget(self.filter_params['min_market_cap'], 1, 1)

        market_cap_layout.addWidget(QLabel("最高市值(亿):"), 1, 2)
        self.filter_params['max_market_cap'] = QSpinBox()
        self.filter_params['max_market_cap'].setRange(0, 100000)
        self.filter_params['max_market_cap'].setSingleStep(100)
        self.filter_params['max_market_cap'].setValue(10000)
        market_cap_layout.addWidget(self.filter_params['max_market_cap'], 1, 3)

        scroll_layout.addWidget(market_cap_group)

        # 板块筛选
        sector_group = QGroupBox("板块筛选")
        sector_layout = QGridLayout(sector_group)

        sector_layout.addWidget(QLabel("启用板块筛选:"), 0, 0)
        self.filter_params['enable_sector_filter'] = QCheckBox()
        self.filter_params['enable_sector_filter'].setChecked(False)
        sector_layout.addWidget(self.filter_params['enable_sector_filter'], 0, 1)

        sector_layout.addWidget(QLabel("包含板块:"), 1, 0)
        self.filter_params['include_sectors'] = QComboBox()
        self.filter_params['include_sectors'].addItems([
            "全部板块", "银行", "房地产", "食品饮料", "保险", "医药生物",
            "电子", "计算机", "通信", "汽车", "机械设备"
        ])
        sector_layout.addWidget(self.filter_params['include_sectors'], 1, 1)

        sector_layout.addWidget(QLabel("排除板块:"), 1, 2)
        self.filter_params['exclude_sectors'] = QComboBox()
        self.filter_params['exclude_sectors'].addItems([
            "无", "ST股票", "退市风险", "停牌股票"
        ])
        sector_layout.addWidget(self.filter_params['exclude_sectors'], 1, 3)

        scroll_layout.addWidget(sector_group)
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return widget

    def _create_strategy_config_tab(self) -> QWidget:
        """创建策略组合配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 策略权重配置
        weight_group = QGroupBox("策略权重配置")
        weight_layout = QGridLayout(weight_group)

        # 威科夫策略权重
        weight_layout.addWidget(QLabel("威科夫策略权重:"), 0, 0)
        self.wyckoff_weight_slider = QSlider(Qt.Orientation.Horizontal)
        self.wyckoff_weight_slider.setRange(0, 100)
        self.wyckoff_weight_slider.setValue(60)
        self.wyckoff_weight_slider.valueChanged.connect(self._update_weight_labels)
        weight_layout.addWidget(self.wyckoff_weight_slider, 0, 1)

        self.wyckoff_weight_label = QLabel("60%")
        self.wyckoff_weight_label.setMinimumWidth(50)
        weight_layout.addWidget(self.wyckoff_weight_label, 0, 2)

        # 相对强弱策略权重
        weight_layout.addWidget(QLabel("相对强弱策略权重:"), 1, 0)
        self.rs_weight_slider = QSlider(Qt.Orientation.Horizontal)
        self.rs_weight_slider.setRange(0, 100)
        self.rs_weight_slider.setValue(40)
        self.rs_weight_slider.valueChanged.connect(self._update_weight_labels)
        weight_layout.addWidget(self.rs_weight_slider, 1, 1)

        self.rs_weight_label = QLabel("40%")
        self.rs_weight_label.setMinimumWidth(50)
        weight_layout.addWidget(self.rs_weight_label, 1, 2)

        layout.addWidget(weight_group)

        # 选股策略配置
        strategy_group = QGroupBox("选股策略配置")
        strategy_layout = QGridLayout(strategy_group)

        strategy_layout.addWidget(QLabel("选股模式:"), 0, 0)
        self.selection_mode_combo = QComboBox()
        self.selection_mode_combo.addItems([
            "综合评分模式",
            "威科夫优先模式",
            "相对强弱优先模式",
            "平衡模式"
        ])
        strategy_layout.addWidget(self.selection_mode_combo, 0, 1)

        strategy_layout.addWidget(QLabel("最大选股数量:"), 1, 0)
        self.max_selections_spin = QSpinBox()
        self.max_selections_spin.setRange(1, 100)
        self.max_selections_spin.setValue(20)
        strategy_layout.addWidget(self.max_selections_spin, 1, 1)

        strategy_layout.addWidget(QLabel("最低综合分数:"), 2, 0)
        self.min_total_score_spin = QDoubleSpinBox()
        self.min_total_score_spin.setRange(0.0, 1.0)
        self.min_total_score_spin.setSingleStep(0.1)
        self.min_total_score_spin.setValue(0.6)
        strategy_layout.addWidget(self.min_total_score_spin, 2, 1)

        layout.addWidget(strategy_group)

        # 风险控制配置
        risk_group = QGroupBox("风险控制配置")
        risk_layout = QGridLayout(risk_group)

        risk_layout.addWidget(QLabel("启用风险控制:"), 0, 0)
        self.enable_risk_control = QCheckBox()
        self.enable_risk_control.setChecked(True)
        risk_layout.addWidget(self.enable_risk_control, 0, 1)

        risk_layout.addWidget(QLabel("单板块最大占比:"), 1, 0)
        self.max_sector_ratio_spin = QDoubleSpinBox()
        self.max_sector_ratio_spin.setRange(0.0, 1.0)
        self.max_sector_ratio_spin.setSingleStep(0.1)
        self.max_sector_ratio_spin.setValue(0.3)
        risk_layout.addWidget(self.max_sector_ratio_spin, 1, 1)

        risk_layout.addWidget(QLabel("分散化要求:"), 2, 0)
        self.diversification_combo = QComboBox()
        self.diversification_combo.addItems([
            "低分散化", "中等分散化", "高分散化"
        ])
        self.diversification_combo.setCurrentText("中等分散化")
        risk_layout.addWidget(self.diversification_combo, 2, 1)

        layout.addWidget(risk_group)
        layout.addStretch()

        return widget

    def _update_weight_labels(self):
        """更新权重标签"""
        wyckoff_weight = self.wyckoff_weight_slider.value()
        rs_weight = self.rs_weight_slider.value()

        # 确保权重总和为100%
        total = wyckoff_weight + rs_weight
        if total > 100:
            if self.sender() == self.wyckoff_weight_slider:
                rs_weight = 100 - wyckoff_weight
                self.rs_weight_slider.setValue(rs_weight)
            else:
                wyckoff_weight = 100 - rs_weight
                self.wyckoff_weight_slider.setValue(wyckoff_weight)

        self.wyckoff_weight_label.setText(f"{wyckoff_weight}%")
        self.rs_weight_label.setText(f"{rs_weight}%")
    
    def _create_results_widget(self) -> QWidget:
        """创建结果区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 结果标题
        results_label = QLabel("📊 选股结果")
        results_label.setFont(QFont("Microsoft YaHei UI", 11, QFont.Weight.Bold))
        layout.addWidget(results_label)
        
        # 结果表格
        self.results_table = QTableWidget()
        self._setup_results_table()
        layout.addWidget(self.results_table)
        
        # 日志区域
        log_label = QLabel("📝 选股日志")
        log_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        return widget
    
    def _setup_results_table(self):
        """设置结果表格"""
        if not self.results_table:
            return
        
        # 设置列
        columns = [
            "排名", "股票代码", "股票名称", "综合得分", 
            "威科夫得分", "RS得分", "选股理由"
        ]
        
        self.results_table.setColumnCount(len(columns))
        self.results_table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.results_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.results_table.setSortingEnabled(True)
        
        # 列宽设置
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)   # 排名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)   # 代码
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)   # 名称
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)   # 综合得分
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)   # 威科夫得分
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)   # RS得分
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch) # 选股理由
        
        self.results_table.setColumnWidth(0, 60)   # 排名
        self.results_table.setColumnWidth(1, 80)   # 代码
        self.results_table.setColumnWidth(2, 120)  # 名称
        self.results_table.setColumnWidth(3, 80)   # 综合得分
        self.results_table.setColumnWidth(4, 80)   # 威科夫得分
        self.results_table.setColumnWidth(5, 80)   # RS得分
    
    def _load_default_config(self):
        """加载默认配置"""
        # 加载预设配置方案
        self._load_preset_configs()

        # 应用默认的平衡型策略
        if hasattr(self, 'saved_configs') and "平衡型策略" in self.saved_configs:
            self._apply_config(self.saved_configs["平衡型策略"])

        self._add_log("系统已加载默认选股配置（平衡型策略）")
    
    def _start_selection(self):
        """开始选股"""
        if self.is_selecting:
            return
        
        # 获取配置参数
        config = self._get_selection_config()
        
        # 更新UI状态
        self.is_selecting = True
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.export_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 清空之前的结果
        self.results_table.setRowCount(0)
        self.selection_results.clear()
        
        self._add_log("开始执行智能选股...")
        self._add_log(f"配置参数：威科夫权重={config['wyckoff_weight']:.1f}, RS权重={config['rs_weight']:.1f}")
        
        # 发送选股开始信号
        self.selection_started.emit(config)
        
        # 模拟选股过程
        self._simulate_selection()
        
        logger.info("选股任务已启动")

    def _stop_selection(self):
        """停止选股"""
        if not self.is_selecting:
            return

        self.is_selecting = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        if self.selection_results:
            self.export_button.setEnabled(True)

        self._add_log("选股任务已停止")
        logger.info("选股任务已停止")

    def _simulate_selection(self):
        """模拟选股过程"""
        # 获取股票列表
        stock_list = self.data_service.load_stock_list()

        # 模拟分析过程
        total_stocks = len(stock_list)
        analyzed_count = 0

        for stock in stock_list:
            if not self.is_selecting:
                break

            # 模拟分析延迟
            QTimer.singleShot(100 * analyzed_count, lambda s=stock: self._analyze_stock(s))
            analyzed_count += 1

            # 更新进度
            progress = int((analyzed_count / total_stocks) * 100)
            self.progress_bar.setValue(progress)

        # 完成选股
        QTimer.singleShot(100 * total_stocks + 500, self._complete_selection)

    def _analyze_stock(self, stock):
        """分析单只股票"""
        if not self.is_selecting:
            return

        try:
            # 执行分析
            analysis_result = self.data_service.analyze_stock(stock.symbol)

            if analysis_result:
                # 计算综合得分
                wyckoff_score = self._calculate_wyckoff_score(analysis_result)
                rs_score = self._calculate_rs_score(analysis_result)

                # 获取权重
                wyckoff_weight = self.wyckoff_weight_slider.value() / 100.0
                rs_weight = self.rs_weight_slider.value() / 100.0

                # 计算综合得分
                total_score = wyckoff_score * wyckoff_weight + rs_score * rs_weight

                # 检查是否符合筛选条件
                if self._passes_filters(stock, analysis_result, total_score):
                    # 添加到结果
                    result = {
                        'symbol': stock.symbol,
                        'name': stock.name,
                        'total_score': total_score,
                        'wyckoff_score': wyckoff_score,
                        'rs_score': rs_score,
                        'reason': self._generate_selection_reason(analysis_result, wyckoff_score, rs_score)
                    }

                    self.selection_results.append(result)
                    self._add_result_to_table(result)

                    self._add_log(f"发现优质股票：{stock.symbol} - {stock.name} (得分: {total_score:.3f})")

        except Exception as e:
            logger.error(f"分析股票失败 {stock.symbol}: {e}")

    def _calculate_wyckoff_score(self, analysis_result) -> float:
        """计算威科夫得分"""
        if not analysis_result.wyckoff_analysis:
            return 0.0

        wyckoff = analysis_result.wyckoff_analysis

        # 基础得分基于置信度
        base_score = wyckoff.confidence

        # 根据市场阶段调整得分
        phase_weights = {
            'accumulation': self.wyckoff_params.get('accumulation_weight', QDoubleSpinBox()).value(),
            'markup': self.wyckoff_params.get('markup_weight', QDoubleSpinBox()).value(),
            'distribution': self.wyckoff_params.get('distribution_weight', QDoubleSpinBox()).value(),
            'markdown': self.wyckoff_params.get('markdown_weight', QDoubleSpinBox()).value()
        }

        phase_name = wyckoff.phase.value.lower() if wyckoff.phase else 'unknown'
        phase_weight = phase_weights.get(phase_name, 0.5)

        return base_score * phase_weight

    def _calculate_rs_score(self, analysis_result) -> float:
        """计算相对强弱得分"""
        if not analysis_result.rs_ranking:
            return 0.0

        rs_result = analysis_result.rs_ranking

        # 基于RS值计算得分
        rs_value = rs_result.rs_value
        if rs_value >= 1.5:
            rs_score = 1.0
        elif rs_value >= 1.2:
            rs_score = 0.8
        elif rs_value >= 1.0:
            rs_score = 0.6
        elif rs_value >= 0.8:
            rs_score = 0.4
        else:
            rs_score = 0.2

        # 基于百分位调整得分
        if rs_result.rs_percentile:
            percentile_bonus = (rs_result.rs_percentile - 50) / 50 * 0.2
            rs_score += percentile_bonus

        return max(0.0, min(1.0, rs_score))

    def _passes_filters(self, stock, analysis_result, total_score) -> bool:
        """检查是否通过筛选条件"""
        # 检查综合得分
        min_score = self.min_total_score_spin.value()
        if total_score < min_score:
            return False

        # 检查价格筛选
        if self.filter_params.get('enable_price_filter', QCheckBox()).isChecked():
            min_price = self.filter_params.get('min_price', QDoubleSpinBox()).value()
            max_price = self.filter_params.get('max_price', QDoubleSpinBox()).value()
            if not (min_price <= stock.price <= max_price):
                return False

        # 检查成交量筛选
        if self.filter_params.get('enable_volume_filter', QCheckBox()).isChecked():
            min_volume = self.filter_params.get('min_volume', QSpinBox()).value() * 10000
            if stock.volume < min_volume:
                return False

        # 检查市值筛选
        if self.filter_params.get('enable_market_cap_filter', QCheckBox()).isChecked():
            min_cap = self.filter_params.get('min_market_cap', QSpinBox()).value()
            max_cap = self.filter_params.get('max_market_cap', QSpinBox()).value()
            if not (min_cap <= stock.market_cap <= max_cap):
                return False

        return True

    def _generate_selection_reason(self, analysis_result, wyckoff_score, rs_score) -> str:
        """生成选股理由"""
        reasons = []

        if wyckoff_score > 0.7:
            if analysis_result.wyckoff_analysis:
                phase = analysis_result.wyckoff_analysis.phase.value if analysis_result.wyckoff_analysis.phase else "未知"
                reasons.append(f"威科夫{phase}阶段(得分:{wyckoff_score:.2f})")

        if rs_score > 0.7:
            if analysis_result.rs_ranking:
                rs_value = analysis_result.rs_ranking.rs_value
                reasons.append(f"相对强势(RS:{rs_value:.2f})")

        if not reasons:
            reasons.append("综合评分较高")

        return "; ".join(reasons)

    def _add_result_to_table(self, result):
        """添加结果到表格"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # 排名
        rank_item = QTableWidgetItem(str(row + 1))
        rank_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 0, rank_item)

        # 股票代码
        symbol_item = QTableWidgetItem(result['symbol'])
        symbol_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 1, symbol_item)

        # 股票名称
        name_item = QTableWidgetItem(result['name'])
        self.results_table.setItem(row, 2, name_item)

        # 综合得分
        total_score_item = QTableWidgetItem(f"{result['total_score']:.3f}")
        total_score_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        if result['total_score'] >= 0.8:
            total_score_item.setForeground(QColor("#F44336"))  # 红色
        elif result['total_score'] >= 0.6:
            total_score_item.setForeground(QColor("#FF9800"))  # 橙色
        self.results_table.setItem(row, 3, total_score_item)

        # 威科夫得分
        wyckoff_item = QTableWidgetItem(f"{result['wyckoff_score']:.3f}")
        wyckoff_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 4, wyckoff_item)

        # RS得分
        rs_item = QTableWidgetItem(f"{result['rs_score']:.3f}")
        rs_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 5, rs_item)

        # 选股理由
        reason_item = QTableWidgetItem(result['reason'])
        self.results_table.setItem(row, 6, reason_item)

    def _complete_selection(self):
        """完成选股"""
        if not self.is_selecting:
            return

        # 排序结果
        self.selection_results.sort(key=lambda x: x['total_score'], reverse=True)

        # 限制结果数量
        max_selections = self.max_selections_spin.value()
        if len(self.selection_results) > max_selections:
            self.selection_results = self.selection_results[:max_selections]

        # 重新填充表格
        self._refresh_results_table()

        # 更新UI状态
        self.is_selecting = False
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.export_button.setEnabled(True)

        # 添加完成日志
        count = len(self.selection_results)
        self._add_log(f"选股完成！共选出 {count} 只优质股票")

        # 发送完成信号
        self.selection_completed.emit({
            'results': self.selection_results,
            'count': count
        })

        logger.info(f"选股任务完成，共选出{count}只股票")

    def _refresh_results_table(self):
        """刷新结果表格"""
        self.results_table.setRowCount(0)
        for result in self.selection_results:
            self._add_result_to_table(result)

    def _get_selection_config(self) -> Dict[str, Any]:
        """获取选股配置"""
        return {
            'wyckoff_weight': self.wyckoff_weight_slider.value() / 100.0,
            'rs_weight': self.rs_weight_slider.value() / 100.0,
            'max_selections': self.max_selections_spin.value(),
            'min_total_score': self.min_total_score_spin.value(),
            'selection_mode': self.selection_mode_combo.currentText(),
            'wyckoff_params': {k: v.value() if hasattr(v, 'value') else v.isChecked()
                             for k, v in self.wyckoff_params.items()},
            'rs_params': {k: v.value() if hasattr(v, 'value') else v.isChecked()
                         for k, v in self.rs_params.items()},
            'filter_params': {k: (v.value() if hasattr(v, 'value') else
                                  v.isChecked() if hasattr(v, 'isChecked') else
                                  v.currentText() if hasattr(v, 'currentText') else str(v))
                            for k, v in self.filter_params.items()}
        }

    def _add_log(self, message: str):
        """添加日志"""
        if self.log_text:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] {message}")

    def _reset_config(self):
        """重置配置"""
        self._load_default_config()
        self._add_log("配置已重置为默认值")

    def _save_config(self):
        """保存配置"""
        from PyQt6.QtWidgets import QInputDialog

        # 获取当前配置
        config = self._get_selection_config()

        # 弹出对话框输入方案名称
        name, ok = QInputDialog.getText(self, '保存配置方案', '请输入方案名称:')

        if ok and name.strip():
            # 这里应该保存到文件或数据库，现在先模拟
            self.saved_configs = getattr(self, 'saved_configs', {})
            self.saved_configs[name.strip()] = config

            self._add_log(f"配置方案 '{name.strip()}' 已保存")
            logger.info(f"保存配置方案: {name.strip()}")
        else:
            self._add_log("保存配置已取消")

    def _load_config(self):
        """加载配置"""
        from PyQt6.QtWidgets import QInputDialog

        # 获取已保存的配置列表
        saved_configs = getattr(self, 'saved_configs', {})

        if not saved_configs:
            self._add_log("没有已保存的配置方案")
            return

        # 弹出对话框选择方案
        items = list(saved_configs.keys())
        name, ok = QInputDialog.getItem(self, '加载配置方案', '请选择方案:', items, 0, False)

        if ok and name:
            config = saved_configs[name]
            self._apply_config(config)
            self._add_log(f"已加载配置方案 '{name}'")
            logger.info(f"加载配置方案: {name}")
        else:
            self._add_log("加载配置已取消")

    def _apply_config(self, config: Dict[str, Any]):
        """应用配置"""
        try:
            # 应用策略权重
            if 'wyckoff_weight' in config:
                wyckoff_weight = int(config['wyckoff_weight'] * 100)
                self.wyckoff_weight_slider.setValue(wyckoff_weight)

            if 'rs_weight' in config:
                rs_weight = int(config['rs_weight'] * 100)
                self.rs_weight_slider.setValue(rs_weight)

            # 应用其他参数
            if 'max_selections' in config:
                self.max_selections_spin.setValue(config['max_selections'])

            if 'min_total_score' in config:
                self.min_total_score_spin.setValue(config['min_total_score'])

            if 'selection_mode' in config:
                index = self.selection_mode_combo.findText(config['selection_mode'])
                if index >= 0:
                    self.selection_mode_combo.setCurrentIndex(index)

            # 应用威科夫参数
            wyckoff_params = config.get('wyckoff_params', {})
            for key, value in wyckoff_params.items():
                if key in self.wyckoff_params:
                    widget = self.wyckoff_params[key]
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)
                    elif hasattr(widget, 'setChecked'):
                        widget.setChecked(value)

            # 应用RS参数
            rs_params = config.get('rs_params', {})
            for key, value in rs_params.items():
                if key in self.rs_params:
                    widget = self.rs_params[key]
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)
                    elif hasattr(widget, 'setChecked'):
                        widget.setChecked(value)

            # 应用筛选参数
            filter_params = config.get('filter_params', {})
            for key, value in filter_params.items():
                if key in self.filter_params:
                    widget = self.filter_params[key]
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)
                    elif hasattr(widget, 'setChecked'):
                        widget.setChecked(value)

            self._update_weight_labels()

        except Exception as e:
            logger.error(f"应用配置失败: {e}")
            self._add_log(f"应用配置失败: {e}")

    def _load_preset_configs(self):
        """加载预设配置方案"""
        # 定义几个预设方案
        preset_configs = {
            "保守型策略": {
                'wyckoff_weight': 0.7,
                'rs_weight': 0.3,
                'max_selections': 10,
                'min_total_score': 0.7,
                'selection_mode': '威科夫优先模式',
                'wyckoff_params': {
                    'accumulation_weight': 0.9,
                    'markup_weight': 0.7,
                    'distribution_weight': 0.1,
                    'markdown_weight': 0.0,
                    'min_confidence': 0.7
                },
                'rs_params': {
                    'min_rs_value': 1.1,
                    'min_percentile': 80.0
                }
            },
            "激进型策略": {
                'wyckoff_weight': 0.4,
                'rs_weight': 0.6,
                'max_selections': 30,
                'min_total_score': 0.5,
                'selection_mode': '相对强弱优先模式',
                'wyckoff_params': {
                    'accumulation_weight': 0.8,
                    'markup_weight': 0.9,
                    'distribution_weight': 0.3,
                    'markdown_weight': 0.1,
                    'min_confidence': 0.5
                },
                'rs_params': {
                    'min_rs_value': 0.9,
                    'min_percentile': 60.0
                }
            },
            "平衡型策略": {
                'wyckoff_weight': 0.5,
                'rs_weight': 0.5,
                'max_selections': 20,
                'min_total_score': 0.6,
                'selection_mode': '平衡模式',
                'wyckoff_params': {
                    'accumulation_weight': 0.8,
                    'markup_weight': 0.6,
                    'distribution_weight': 0.2,
                    'markdown_weight': 0.1,
                    'min_confidence': 0.6
                },
                'rs_params': {
                    'min_rs_value': 1.0,
                    'min_percentile': 70.0
                }
            }
        }

        # 保存预设配置
        if not hasattr(self, 'saved_configs'):
            self.saved_configs = {}

        self.saved_configs.update(preset_configs)
        self._add_log("已加载预设配置方案：保守型、激进型、平衡型")

    # 公共方法
    def get_selection_results(self) -> List[Dict[str, Any]]:
        """获取选股结果"""
        return self.selection_results.copy()

    def clear_results(self):
        """清空结果"""
        self.selection_results.clear()
        self.results_table.setRowCount(0)
        self.log_text.clear()
        self.export_button.setEnabled(False)
        self._add_log("结果已清空")
