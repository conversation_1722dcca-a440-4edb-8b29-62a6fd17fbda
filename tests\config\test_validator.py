"""
配置验证器测试用例

测试ConfigValidator的功能：
- 配置结构验证
- 数据类型验证
- 值范围验证
- 必需字段验证
"""

import pytest
from src.config.validator import ConfigValidator
from src.utils.exceptions import ConfigurationError, ValidationError


class TestConfigValidator:
    """配置验证器基础测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_valid_config_structure(self):
        """测试有效配置结构"""
        valid_config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    'path': 'test.db',
                    'pool_size': 10,
                    'timeout': 30
                },
                'logging': {
                    'level': 'INFO',
                    'file_path': 'logs/test.log'
                },
                'cache_size': 1000
            },
            'data_sources': {
                'xtdata': {
                    'enabled': True,
                    'host': 'localhost',
                    'port': 58610
                }
            }
        }
        
        result = self.validator.validate(valid_config)
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        invalid_config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    # 缺少 'path' 字段
                    'pool_size': 10
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any('path' in error for error in result.errors)
    
    def test_invalid_data_types(self):
        """测试无效数据类型"""
        invalid_config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    'path': 'test.db',
                    'pool_size': 'invalid',  # 应该是整数
                    'timeout': 30
                },
                'cache_size': 'invalid'  # 应该是整数
            }
        }
        
        result = self.validator.validate(invalid_config)
        assert result.is_valid is False
        assert len(result.errors) >= 2
    
    def test_invalid_value_ranges(self):
        """测试无效值范围"""
        invalid_config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    'path': 'test.db',
                    'pool_size': -1,  # 应该是正数
                    'timeout': 0      # 应该大于0
                },
                'cache_size': -100    # 应该是正数
            }
        }
        
        result = self.validator.validate(invalid_config)
        assert result.is_valid is False
        assert len(result.errors) >= 3


class TestDatabaseConfigValidation:
    """数据库配置验证测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_valid_database_config(self):
        """测试有效数据库配置"""
        db_config = {
            'path': 'data/test.db',
            'pool_size': 15,
            'timeout': 60,
            'backup_enabled': True,
            'backup_interval': 7200
        }
        
        result = self.validator.validate_database_config(db_config)
        assert result.is_valid is True
    
    def test_invalid_database_path(self):
        """测试无效数据库路径"""
        db_config = {
            'path': '',  # 空路径
            'pool_size': 10,
            'timeout': 30
        }
        
        result = self.validator.validate_database_config(db_config)
        assert result.is_valid is False
        assert any('path' in error for error in result.errors)
    
    def test_invalid_pool_size(self):
        """测试无效连接池大小"""
        db_config = {
            'path': 'test.db',
            'pool_size': 0,  # 应该大于0
            'timeout': 30
        }
        
        result = self.validator.validate_database_config(db_config)
        assert result.is_valid is False
        assert any('pool_size' in error for error in result.errors)
    
    def test_invalid_timeout(self):
        """测试无效超时时间"""
        db_config = {
            'path': 'test.db',
            'pool_size': 10,
            'timeout': -5  # 应该是正数
        }
        
        result = self.validator.validate_database_config(db_config)
        assert result.is_valid is False
        assert any('timeout' in error for error in result.errors)


class TestLoggingConfigValidation:
    """日志配置验证测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_valid_logging_config(self):
        """测试有效日志配置"""
        log_config = {
            'level': 'DEBUG',
            'file_path': 'logs/app.log',
            'max_file_size': 10485760,
            'backup_count': 5,
            'console_output': True
        }
        
        result = self.validator.validate_logging_config(log_config)
        assert result.is_valid is True
    
    def test_invalid_log_level(self):
        """测试无效日志级别"""
        log_config = {
            'level': 'INVALID_LEVEL',
            'file_path': 'logs/app.log'
        }
        
        result = self.validator.validate_logging_config(log_config)
        assert result.is_valid is False
        assert any('level' in error for error in result.errors)
    
    def test_valid_log_levels(self):
        """测试所有有效日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        
        for level in valid_levels:
            log_config = {
                'level': level,
                'file_path': 'logs/app.log'
            }
            
            result = self.validator.validate_logging_config(log_config)
            assert result.is_valid is True, f"Level {level} should be valid"
    
    def test_invalid_file_size(self):
        """测试无效文件大小"""
        log_config = {
            'level': 'INFO',
            'file_path': 'logs/app.log',
            'max_file_size': -1  # 应该是正数
        }
        
        result = self.validator.validate_logging_config(log_config)
        assert result.is_valid is False
        assert any('max_file_size' in error for error in result.errors)


class TestDataSourceConfigValidation:
    """数据源配置验证测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_valid_data_source_config(self):
        """测试有效数据源配置"""
        ds_config = {
            'xtdata': {
                'enabled': True,
                'host': 'localhost',
                'port': 58610,
                'timeout': 30,
                'retry_count': 3
            },
            'tushare': {
                'enabled': False,
                'token': 'test_token',
                'timeout': 60
            }
        }
        
        result = self.validator.validate_data_source_config(ds_config)
        assert result.is_valid is True
    
    def test_invalid_port_number(self):
        """测试无效端口号"""
        ds_config = {
            'xtdata': {
                'enabled': True,
                'host': 'localhost',
                'port': 70000,  # 超出有效范围
                'timeout': 30
            }
        }
        
        result = self.validator.validate_data_source_config(ds_config)
        assert result.is_valid is False
        assert any('port' in error for error in result.errors)
    
    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        ds_config = {
            'xtdata': {
                'enabled': True,
                # 缺少 'host' 字段
                'port': 58610
            }
        }
        
        result = self.validator.validate_data_source_config(ds_config)
        assert result.is_valid is False
        assert any('host' in error for error in result.errors)


class TestValidationResult:
    """验证结果测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_validation_result_structure(self):
        """测试验证结果结构"""
        config = {'version': '1.0.0'}
        result = self.validator.validate(config)
        
        assert hasattr(result, 'is_valid')
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validation_with_warnings(self):
        """测试带警告的验证"""
        config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    'path': 'test.db',
                    'pool_size': 100,  # 可能触发警告：值过大
                    'timeout': 30
                },
                'cache_size': 10000  # 可能触发警告：值过大
            }
        }
        
        result = self.validator.validate(config)
        # 即使有警告，配置仍然可能是有效的
        assert isinstance(result.warnings, list)
    
    def test_error_message_format(self):
        """测试错误消息格式"""
        invalid_config = {
            'system': {
                'database': {
                    'pool_size': 'invalid'
                }
            }
        }
        
        result = self.validator.validate(invalid_config)
        assert result.is_valid is False
        
        # 错误消息应该包含字段路径和错误描述
        for error in result.errors:
            assert isinstance(error, str)
            assert len(error) > 0


class TestValidatorIntegration:
    """验证器集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.validator = ConfigValidator()
    
    def test_complete_config_validation(self):
        """测试完整配置验证"""
        complete_config = {
            'version': '1.0.0',
            'system': {
                'database': {
                    'path': 'data/xdqr.db',
                    'pool_size': 10,
                    'timeout': 30,
                    'backup_enabled': True,
                    'backup_interval': 3600
                },
                'logging': {
                    'level': 'INFO',
                    'file_path': 'logs/xdqr.log',
                    'max_file_size': 10485760,
                    'backup_count': 5,
                    'console_output': True
                },
                'data_sources': {
                    'primary': 'xtdata',
                    'fallback': ['tushare'],
                    'timeout': 30,
                    'retry_count': 3,
                    'retry_delay': 1.0
                },
                'cache_size': 1000,
                'max_workers': 4,
                'debug_mode': False
            },
            'data_sources': {
                'xtdata': {
                    'enabled': True,
                    'host': 'localhost',
                    'port': 58610,
                    'timeout': 30
                },
                'tushare': {
                    'enabled': False,
                    'token': '',
                    'timeout': 60
                }
            }
        }
        
        result = self.validator.validate(complete_config)
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_custom_validation_rules(self):
        """测试自定义验证规则"""
        # 可以添加自定义验证规则
        custom_rules = {
            'system.cache_size': lambda x: x <= 5000,
            'system.max_workers': lambda x: 1 <= x <= 16
        }
        
        validator = ConfigValidator(custom_rules=custom_rules)
        
        config = {
            'system': {
                'cache_size': 6000,  # 违反自定义规则
                'max_workers': 20    # 违反自定义规则
            }
        }
        
        result = validator.validate(config)
        # 应该检测到自定义规则违反
        assert result.is_valid is False


if __name__ == '__main__':
    pytest.main([__file__])
