# 测试目录说明

本目录包含威科夫相对强弱选股系统的所有测试文件，按功能模块组织。

## 📁 目录结构

```
tests/
├── __init__.py                 # 测试包初始化文件
├── README.md                   # 本说明文件
├── pytest.ini                 # pytest配置文件
├── run_tests.py               # 测试运行脚本
├── test_ux_fixes.py           # UX修复测试
│
├── ui/                        # 用户界面测试
│   ├── test_day2_features.py
│   ├── test_day3_features.py
│   ├── test_day4_features.py
│   ├── test_enhanced_selection.py      # 增强选股界面测试
│   ├── test_sector_screening_ui.py     # 板块筛选界面测试
│   ├── test_ui_final.py               # UI最终验证测试
│   ├── test_ui_simple.py              # UI简单测试
│   └── test_ux_improvements.py        # UX改进测试
│
├── core/                      # 核心功能测试
│   ├── test_core_screening.py         # 核心筛选功能测试
│   └── test_core_simple.py           # 核心功能简单测试
│
├── engines/                   # 引擎测试
│   ├── test_relative_strength.py      # 相对强弱引擎测试
│   ├── test_selection.py             # 选股引擎测试
│   └── test_wyckoff.py               # 威科夫引擎测试
│
├── services/                  # 服务测试
│   └── test_history_fix.py           # 历史数据修复测试
│
├── data_sources/              # 数据源测试
│   ├── test_xtdata_adapter.py         # XtData适配器测试
│   ├── test_xtdata_history.py         # XtData历史数据测试
│   ├── test_xtdata_mock.py           # XtData模拟测试
│   └── test_xtquant_connection.py     # XtQuant连接测试
│
├── database/                  # 数据库测试
│   ├── test_integration.py           # 数据库集成测试
│   ├── test_optimization.py          # 数据库优化测试
│   └── test_simple.py               # 数据库简单测试
│
├── integration/               # 集成测试
│   ├── test_real_data.py             # 真实数据测试
│   ├── test_real_data_integration.py  # 真实数据集成测试
│   ├── test_system_integration.py     # 系统集成测试
│   ├── test_system_integration_week13.py # 第13周集成测试
│   └── test_xtdata_integration.py     # XtData集成测试
│
├── performance/               # 性能测试
│   └── test_week15_performance.py     # 第15周性能测试
│
├── week_tests/                # 周测试
│   ├── test_week14_ux_complete.py     # 第14周UX完成测试
│   └── test_week16_final_system.py    # 第16周最终系统测试
│
├── utils/                     # 工具测试
│   ├── test_helpers.py               # 辅助工具测试
│   ├── test_libraries.py             # 库测试
│   └── test_monitor.py               # 监控测试
│
├── config/                    # 配置测试
│   ├── test_config_manager.py         # 配置管理器测试
│   ├── test_environment.py           # 环境测试
│   ├── test_settings.py              # 设置测试
│   └── test_validator.py             # 验证器测试
│
└── unit/                      # 单元测试
    ├── data_sources/
    └── test_data_sources.py           # 数据源单元测试
```

## 🚀 运行测试

### 使用测试运行脚本

```bash
# 运行所有测试
python tests/run_tests.py --all

# 列出所有可用测试
python tests/run_tests.py --list

# 运行特定目录的测试
python tests/run_tests.py --dir tests/ui

# 运行特定测试文件
python tests/run_tests.py test_ui_simple

# 显示详细输出
python tests/run_tests.py --all --verbose
```

### 使用pytest（如果已安装）

```bash
# 运行所有测试
pytest tests/

# 运行特定目录
pytest tests/ui/

# 运行特定文件
pytest tests/ui/test_ui_simple.py

# 运行带标记的测试
pytest -m ui
pytest -m integration
```

### 直接运行单个测试文件

```bash
# 从项目根目录运行
python tests/ui/test_ui_simple.py
```

## 📋 测试分类

### 按功能分类

- **UI测试** (`tests/ui/`): 用户界面相关测试
- **核心测试** (`tests/core/`): 核心业务逻辑测试
- **引擎测试** (`tests/engines/`): 分析引擎测试
- **服务测试** (`tests/services/`): 服务层测试
- **数据源测试** (`tests/data_sources/`): 数据源连接和处理测试
- **数据库测试** (`tests/database/`): 数据库操作测试
- **集成测试** (`tests/integration/`): 系统集成测试
- **性能测试** (`tests/performance/`): 性能和压力测试
- **工具测试** (`tests/utils/`): 工具和辅助功能测试

### 按测试类型分类

- **单元测试**: 测试单个函数或类的功能
- **集成测试**: 测试多个组件之间的交互
- **UI测试**: 测试用户界面的功能和交互
- **性能测试**: 测试系统性能和响应时间
- **端到端测试**: 测试完整的用户工作流

## 🔧 测试开发指南

### 添加新测试

1. **确定测试类型**: 根据测试内容选择合适的目录
2. **创建测试文件**: 文件名以 `test_` 开头
3. **编写测试代码**: 遵循项目的测试规范
4. **更新文档**: 在本README中记录新测试

### 测试文件命名规范

- 文件名: `test_<功能名称>.py`
- 类名: `Test<功能名称>`
- 方法名: `test_<具体测试内容>`

### 导入路径

所有测试文件都已配置正确的导入路径，可以直接导入项目模块：

```python
from src.ui.main_window import MainWindow
from src.engines.wyckoff import WyckoffEngine
```

## 📊 测试覆盖率

目前的测试覆盖了以下主要功能：

- ✅ 用户界面组件
- ✅ 核心筛选算法
- ✅ 数据源连接
- ✅ 数据库操作
- ✅ 系统集成
- ✅ 性能测试

## 🐛 问题排查

如果测试运行失败，请检查：

1. **依赖安装**: 确保所有依赖包已正确安装
2. **数据库连接**: 确保数据库文件存在且可访问
3. **数据源配置**: 确保数据源配置正确
4. **环境变量**: 检查必要的环境变量设置
5. **文件权限**: 确保测试文件有执行权限

## 📝 维护说明

- 定期运行所有测试确保系统稳定性
- 新功能开发时同步添加相应测试
- 重构代码时更新相关测试
- 保持测试文档的及时更新
