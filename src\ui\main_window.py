"""
主窗口类

实现威科夫相对强弱选股系统的主界面，包含：
- 现代化的界面布局
- 菜单栏和工具栏
- 状态栏和进度指示
- 多标签页管理
- 响应式设计
"""

import sys
from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QToolBar, QStatusBar, QProgressBar,
    QLabel, QSplitter, QFrame, QMessageBox, QSystemTrayIcon
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

from ..utils.logger import get_logger
from .styles.style_manager import StyleManager
from .utils.ux_enhancer import UXEnhancer
from .utils.user_guide import UserGuideManager
from .utils.feature_guides import FeatureGuideManager
from .utils.tooltip_enhancer import ToolTipEnhancer, add_button_tooltip, TOOLTIP_CONTENTS
from .utils.context_help import ContextHelpManager
from .utils.faq_system import FAQManager
from .utils.theme_manager import theme_manager, ThemeType
from .utils.user_preferences import user_preferences
from .dialogs.preferences_dialog import PreferencesDialog
from .components.stock_list_widget import StockListWidget
from .components.analysis_widget import AnalysisWidget
from .components.selection_widget import SelectionWidget
from .components.chart_widget import ChartWidget
from .components.system_settings_widget import SystemSettingsWidget
from .components.log_viewer_widget import LogViewerWidget
from .components.data_source_config_widget import DataSourceConfigWidget
from .components.system_monitor_widget import SystemMonitorWidget
from .components.help_system_widget import HelpSystemWidget
from .components.sector_screening_widget import SectorScreeningWidget
from .components.enhanced_selection_widget import EnhancedSelectionWidget

logger = get_logger(__name__)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    data_refresh_requested = pyqtSignal()
    analysis_requested = pyqtSignal(str)  # 股票代码
    selection_requested = pyqtSignal(dict)  # 选股参数
    
    def __init__(self, parent: Optional[QWidget] = None, data_manager=None, data_service=None):
        """
        初始化主窗口

        Args:
            parent: 父窗口
            data_manager: 数据源管理器
            data_service: 数据服务
        """
        super().__init__(parent)

        # 数据源管理器和数据服务
        self.data_manager = data_manager
        self.data_service = data_service
        
        # 窗口属性
        self.setWindowTitle("威科夫相对强弱选股系统 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)
        
        # 样式管理器
        self.style_manager = StyleManager()

        # 用户体验增强器
        self.ux_enhancer = UXEnhancer(self)

        # 用户向导管理器
        self.guide_manager = UserGuideManager(self)

        # 功能向导管理器
        self.feature_guide_manager = FeatureGuideManager(self)

        # 工具提示增强器
        self.tooltip_enhancer = ToolTipEnhancer(self)

        # 上下文帮助管理器
        self.context_help_manager = ContextHelpManager(self)

        # FAQ管理器
        self.faq_manager = FAQManager(self)

        # 初始化用户偏好设置
        self._init_user_preferences()
        
        # 组件引用
        self.central_widget: Optional[QWidget] = None
        self.tab_widget: Optional[QTabWidget] = None
        self.stock_list_widget: Optional[StockListWidget] = None
        self.analysis_widget: Optional[AnalysisWidget] = None
        self.selection_widget: Optional[SelectionWidget] = None
        self.chart_widget: Optional[ChartWidget] = None
        self.system_settings_widget: Optional[SystemSettingsWidget] = None
        self.log_viewer_widget: Optional[LogViewerWidget] = None
        self.data_source_config_widget: Optional[DataSourceConfigWidget] = None
        self.system_monitor_widget: Optional[SystemMonitorWidget] = None
        self.help_system_widget: Optional[HelpSystemWidget] = None
        
        # 状态栏组件
        self.status_label: Optional[QLabel] = None
        self.progress_bar: Optional[QProgressBar] = None
        self.connection_status: Optional[QLabel] = None
        self.time_label: Optional[QLabel] = None

        # 定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.time_timer: Optional[QTimer] = None
        
        # 初始化界面
        self._init_ui()
        self._init_menu_bar()
        self._init_tool_bar()
        self._init_status_bar()
        self._apply_styles()
        self._connect_signals()
        
        # 启动状态更新
        self.status_timer.start(5000)  # 每5秒更新一次状态

        # 延迟显示欢迎向导
        QTimer.singleShot(1000, self._show_welcome_guide_if_needed)

        # 更新数据源连接状态
        QTimer.singleShot(500, self._update_data_source_status)

        logger.info("主窗口初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 创建中央窗口
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        self.tab_widget.setTabsClosable(False)
        
        # 创建各个功能页面
        self._create_stock_analysis_tab()
        self._create_selection_tab()
        self._create_monitoring_tab()
        self._create_settings_tab()
        
        main_layout.addWidget(self.tab_widget)
    
    def _create_stock_analysis_tab(self):
        """创建股票分析标签页"""
        analysis_tab = QWidget()
        layout = QHBoxLayout(analysis_tab)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：股票列表
        self.stock_list_widget = StockListWidget(data_service=self.data_service)
        self.stock_list_widget.setMinimumWidth(300)
        self.stock_list_widget.setMaximumWidth(400)
        
        # 右侧：分析结果和图表
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 上半部分：分析结果
        self.analysis_widget = AnalysisWidget()
        self.analysis_widget.setMinimumHeight(300)
        
        # 下半部分：图表
        self.chart_widget = ChartWidget()
        self.chart_widget.setMinimumHeight(400)
        
        right_layout.addWidget(self.analysis_widget)
        right_layout.addWidget(self.chart_widget)
        
        # 添加到分割器
        splitter.addWidget(self.stock_list_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([300, 900])
        
        layout.addWidget(splitter)
        
        self.tab_widget.addTab(analysis_tab, "📊 股票分析")
    
    def _create_selection_tab(self):
        """创建选股标签页"""
        selection_tab = QWidget()
        layout = QVBoxLayout(selection_tab)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建选股子标签页
        selection_tabs = QTabWidget()

        # 双重筛选工作流标签页（新增）
        self.enhanced_selection_widget = EnhancedSelectionWidget()
        selection_tabs.addTab(self.enhanced_selection_widget, "🎯 双重筛选工作流")

        # 板块筛选标签页
        self.sector_screening_widget = SectorScreeningWidget()
        selection_tabs.addTab(self.sector_screening_widget, "📊 板块筛选")

        # 智能选股标签页
        self.selection_widget = SelectionWidget()
        selection_tabs.addTab(self.selection_widget, "🔍 智能选股")

        layout.addWidget(selection_tabs)

        # 连接双重筛选工作流信号
        self.enhanced_selection_widget.workflow_started.connect(self._on_workflow_started)
        self.enhanced_selection_widget.workflow_completed.connect(self._on_workflow_completed)
        self.enhanced_selection_widget.sector_phase_completed.connect(self._on_sector_phase_completed)
        self.enhanced_selection_widget.stock_phase_completed.connect(self._on_stock_phase_completed)

        # 连接板块筛选信号
        self.sector_screening_widget.sector_selected.connect(self._on_sector_selected)
        self.sector_screening_widget.screening_completed.connect(self._on_sector_screening_completed)

        self.tab_widget.addTab(selection_tab, "🎯 选股系统")
    
    def _create_monitoring_tab(self):
        """创建监控标签页"""
        # 创建系统监控控件
        self.system_monitor_widget = SystemMonitorWidget()

        # 连接信号
        self.system_monitor_widget.alert_triggered.connect(self._on_system_alert)

        self.tab_widget.addTab(self.system_monitor_widget, "📈 系统监控")
    
    def _create_settings_tab(self):
        """创建设置标签页"""
        # 创建系统设置控件
        self.system_settings_widget = SystemSettingsWidget()

        # 连接信号
        self.system_settings_widget.theme_changed.connect(self._on_theme_changed)
        self.system_settings_widget.settings_changed.connect(self._on_settings_changed)

        self.tab_widget.addTab(self.system_settings_widget, "⚙️ 系统设置")
    
    def _init_menu_bar(self):
        """初始化菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 数据下载
        download_action = QAction("📥 下载股票数据(&D)", self)
        download_action.setShortcut("Ctrl+D")
        download_action.setStatusTip("从数据源下载最新股票数据")
        download_action.triggered.connect(self._on_download_data)
        file_menu.addAction(download_action)

        # 数据刷新
        refresh_action = QAction("🔄 刷新数据(&R)", self)
        refresh_action.setShortcut("Ctrl+R")
        refresh_action.setStatusTip("刷新股票数据")
        refresh_action.triggered.connect(self._on_refresh_data)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        # 导出数据
        export_action = QAction("导出数据(&E)", self)
        export_action.setShortcut("Ctrl+E")
        export_action.setStatusTip("导出分析结果")
        export_action.triggered.connect(self._on_export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("退出程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 分析菜单
        analysis_menu = menubar.addMenu("分析(&A)")
        
        # 威科夫分析
        wyckoff_action = QAction("威科夫分析(&W)", self)
        wyckoff_action.setShortcut("Ctrl+W")
        wyckoff_action.setStatusTip("执行威科夫分析")
        wyckoff_action.triggered.connect(self._on_wyckoff_analysis)
        analysis_menu.addAction(wyckoff_action)
        
        # 相对强弱分析
        rs_action = QAction("相对强弱分析(&S)", self)
        rs_action.setShortcut("Ctrl+S")
        rs_action.setStatusTip("执行相对强弱分析")
        rs_action.triggered.connect(self._on_rs_analysis)
        analysis_menu.addAction(rs_action)
        
        # 选股菜单
        selection_menu = menubar.addMenu("选股(&S)")

        # 双重筛选工作流
        workflow_action = QAction("🎯 双重筛选工作流(&W)", self)
        workflow_action.setShortcut("Ctrl+W")
        workflow_action.setStatusTip("执行板块+个股双重相对强弱筛选")
        workflow_action.triggered.connect(self._on_dual_screening_workflow)
        selection_menu.addAction(workflow_action)

        selection_menu.addSeparator()

        # 板块筛选
        sector_screening_action = QAction("📊 板块筛选(&B)", self)
        sector_screening_action.setShortcut("Ctrl+B")
        sector_screening_action.setStatusTip("执行板块相对强弱筛选")
        sector_screening_action.triggered.connect(self._on_sector_screening)
        selection_menu.addAction(sector_screening_action)

        # 智能选股
        smart_selection_action = QAction("🔍 智能选股(&I)", self)
        smart_selection_action.setShortcut("Ctrl+I")
        smart_selection_action.setStatusTip("执行智能选股")
        smart_selection_action.triggered.connect(self._on_smart_selection)
        selection_menu.addAction(smart_selection_action)

        # 系统管理菜单
        system_menu = menubar.addMenu("系统(&Y)")

        # 数据源配置
        datasource_action = QAction("数据源配置(&D)", self)
        datasource_action.setShortcut("Ctrl+D")
        datasource_action.setStatusTip("配置数据源连接")
        datasource_action.triggered.connect(self._on_data_source_config)
        system_menu.addAction(datasource_action)

        # 重新连接数据源
        reconnect_action = QAction("重新连接数据源(&C)", self)
        reconnect_action.setShortcut("Ctrl+Shift+R")
        reconnect_action.setStatusTip("重新连接到数据源")
        reconnect_action.triggered.connect(self._on_reconnect_data_source)
        system_menu.addAction(reconnect_action)

        # 系统设置
        settings_action = QAction("系统设置(&S)", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.setStatusTip("打开系统设置")
        settings_action.triggered.connect(self._on_system_settings)
        system_menu.addAction(settings_action)

        # 偏好设置
        preferences_action = QAction("偏好设置(&P)", self)
        preferences_action.setShortcut("Ctrl+Shift+,")
        preferences_action.setStatusTip("打开偏好设置")
        preferences_action.triggered.connect(self._on_preferences)
        system_menu.addAction(preferences_action)

        # 日志查看器
        logs_action = QAction("日志查看器(&L)", self)
        logs_action.setShortcut("Ctrl+L")
        logs_action.setStatusTip("查看系统日志")
        logs_action.triggered.connect(self._on_view_logs)
        system_menu.addAction(logs_action)

        system_menu.addSeparator()

        # 性能监控
        monitor_action = QAction("性能监控(&M)", self)
        monitor_action.setStatusTip("查看系统性能")
        monitor_action.triggered.connect(self._on_performance_monitor)
        system_menu.addAction(monitor_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 使用向导
        guide_action = QAction("使用向导(&G)", self)
        guide_action.setShortcut("Ctrl+G")
        guide_action.setStatusTip("显示使用向导")
        guide_action.triggered.connect(self._on_show_guide)
        help_menu.addAction(guide_action)

        help_menu.addSeparator()

        # 功能向导子菜单
        feature_guide_menu = help_menu.addMenu("功能向导(&F)")

        # 威科夫分析向导
        wyckoff_guide_action = QAction("威科夫分析向导", self)
        wyckoff_guide_action.setStatusTip("显示威科夫分析操作向导")
        wyckoff_guide_action.triggered.connect(self._on_show_wyckoff_guide)
        feature_guide_menu.addAction(wyckoff_guide_action)

        # 相对强弱分析向导
        rs_guide_action = QAction("相对强弱分析向导", self)
        rs_guide_action.setStatusTip("显示相对强弱分析操作向导")
        rs_guide_action.triggered.connect(self._on_show_rs_guide)
        feature_guide_menu.addAction(rs_guide_action)

        # 智能选股向导
        selection_guide_action = QAction("智能选股向导", self)
        selection_guide_action.setStatusTip("显示智能选股操作向导")
        selection_guide_action.triggered.connect(self._on_show_selection_guide)
        feature_guide_menu.addAction(selection_guide_action)

        # 用户帮助
        user_help_action = QAction("用户帮助(&H)", self)
        user_help_action.setShortcut("F1")
        user_help_action.setStatusTip("显示当前页面的帮助信息")
        user_help_action.triggered.connect(self._on_context_help)
        help_menu.addAction(user_help_action)

        # 常见问题
        faq_action = QAction("常见问题(&Q)", self)
        faq_action.setShortcut("F2")
        faq_action.setStatusTip("查看常见问题解答")
        faq_action.triggered.connect(self._on_show_faq)
        help_menu.addAction(faq_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于本软件")
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _init_tool_bar(self):
        """初始化工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        
        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setStatusTip("刷新数据")
        refresh_action.setShortcut("F5")
        refresh_action.setToolTip(f"<b>刷新数据</b><br/>{TOOLTIP_CONTENTS['refresh_data']['content']}<br/><i>快捷键: F5</i>")
        refresh_action.triggered.connect(self._on_refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # 威科夫分析按钮
        wyckoff_action = QAction("威科夫", self)
        wyckoff_action.setStatusTip("威科夫分析")
        wyckoff_action.setShortcut("Ctrl+W")
        wyckoff_action.setToolTip(f"<b>威科夫分析</b><br/>{TOOLTIP_CONTENTS['wyckoff_analysis']['content']}<br/><i>快捷键: Ctrl+W</i>")
        wyckoff_action.triggered.connect(self._on_wyckoff_analysis)
        toolbar.addAction(wyckoff_action)

        # 相对强弱按钮
        rs_action = QAction("相对强弱", self)
        rs_action.setStatusTip("相对强弱分析")
        rs_action.setShortcut("Ctrl+R")
        rs_action.setToolTip(f"<b>相对强弱分析</b><br/>{TOOLTIP_CONTENTS['rs_analysis']['content']}<br/><i>快捷键: Ctrl+R</i>")
        rs_action.triggered.connect(self._on_rs_analysis)
        toolbar.addAction(rs_action)

        # 双重筛选工作流按钮
        workflow_action = QAction("双重筛选", self)
        workflow_action.setStatusTip("双重相对强弱筛选工作流")
        workflow_action.setShortcut("Ctrl+W")
        workflow_action.setToolTip("<b>双重筛选工作流</b><br/>板块筛选 + 个股筛选的完整工作流<br/><i>快捷键: Ctrl+W</i>")
        workflow_action.triggered.connect(self._on_dual_screening_workflow)
        toolbar.addAction(workflow_action)

        # 板块筛选按钮
        sector_action = QAction("板块筛选", self)
        sector_action.setStatusTip("板块相对强弱筛选")
        sector_action.setShortcut("Ctrl+B")
        sector_action.setToolTip("<b>板块筛选</b><br/>基于相对强弱理论筛选强势板块<br/><i>快捷键: Ctrl+B</i>")
        sector_action.triggered.connect(self._on_sector_screening)
        toolbar.addAction(sector_action)

        # 选股按钮
        selection_action = QAction("智能选股", self)
        selection_action.setStatusTip("智能选股")
        selection_action.setShortcut("Ctrl+S")
        selection_action.setToolTip(f"<b>智能选股</b><br/>{TOOLTIP_CONTENTS['smart_selection']['content']}<br/><i>快捷键: Ctrl+S</i>")
        selection_action.triggered.connect(self._on_smart_selection)
        toolbar.addAction(selection_action)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setStatusTip("导出数据")
        export_action.setShortcut("Ctrl+E")
        export_action.setToolTip(f"<b>导出数据</b><br/>{TOOLTIP_CONTENTS['export_data']['content']}<br/><i>快捷键: Ctrl+E</i>")
        export_action.triggered.connect(self._on_export_data)
        toolbar.addAction(export_action)
    
    def _init_status_bar(self):
        """初始化状态栏"""
        status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # 连接状态
        self.connection_status = QLabel("数据源：未连接")
        self.connection_status.setStyleSheet("color: red;")
        status_bar.addPermanentWidget(self.connection_status)
        
        # 时间标签
        self.time_label = QLabel()
        self._update_time_display()
        status_bar.addPermanentWidget(self.time_label)

        # 启动时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self._update_time_display)
        self.time_timer.start(1000)  # 每秒更新一次
    
    def _apply_styles(self):
        """应用样式"""
        self.style_manager.apply_main_window_style(self)
    
    def _connect_signals(self):
        """连接信号"""
        if self.stock_list_widget:
            self.stock_list_widget.stock_selected.connect(self._on_stock_selected)
        
        if self.selection_widget:
            self.selection_widget.selection_completed.connect(self._on_selection_completed)
    
    def _update_status(self):
        """更新状态栏"""
        # 这里可以添加实时状态更新逻辑
        pass

    def _update_time_display(self):
        """更新时间显示"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'time_label') and self.time_label:
                self.time_label.setText(f"🕐 {current_time}")
        except Exception as e:
            logger.warning(f"更新时间显示失败: {e}")

    def _update_data_source_status(self):
        """更新数据源连接状态"""
        try:
            if self.data_manager:
                # 测试数据源连接
                connected = self.data_manager.test_connection()
                if connected:
                    # 获取活动数据源名称
                    active_source = self.data_manager.get_active_source_name()
                    self.update_connection_status(True, active_source or "XtData")
                    self.set_status("数据源连接正常", 3000)
                    logger.info("数据源连接状态：已连接")
                else:
                    self.update_connection_status(False)
                    self.set_status("数据源连接失败，请检查MiniQMT客户端", 5000)
                    logger.warning("数据源连接状态：未连接")

                    # 显示连接失败的用户友好提示
                    self._show_connection_error_dialog()
            else:
                self.update_connection_status(False)
                self.set_status("数据源未初始化", 3000)
                logger.warning("数据源管理器未初始化")

        except Exception as e:
            self.update_connection_status(False)
            self.set_status(f"数据源状态检查失败: {e}", 5000)
            logger.error(f"数据源状态检查失败: {e}")

    def _show_connection_error_dialog(self):
        """显示连接错误对话框"""
        from src.ui.utils.user_friendly_errors import UserFriendlyErrorDialog
        from src.utils.exceptions import DataSourceError

        try:
            # 创建用户友好的错误提示
            error = DataSourceError("XtData数据源连接失败，请检查MiniQMT客户端是否正在运行")
            UserFriendlyErrorDialog.show_error(self, error, {
                "auto_retry": True,
                "show_manual_retry": True,
                "context": "startup_connection"
            })
        except Exception as e:
            logger.error(f"显示连接错误对话框失败: {e}")
            # 降级到简单消息框
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "数据源连接失败",
                "无法连接到XtData数据源。\n\n请确保：\n1. MiniQMT客户端正在运行\n2. xtdata服务已启动\n3. 网络连接正常\n\n您可以稍后通过菜单重新连接。"
            )
    
    # 事件处理方法
    def _on_download_data(self):
        """下载股票数据"""
        try:
            if not self.data_service:
                self.ux_enhancer.show_warning("数据服务不可用")
                return

            # 检查是否正在下载
            if self.data_service.is_downloading():
                self.ux_enhancer.show_warning("数据下载正在进行中，请稍候...")
                return

            # 显示下载对话框
            from .dialogs.data_download_dialog import DataDownloadDialog

            dialog = DataDownloadDialog(self.data_service, self)
            dialog.download_completed.connect(self._on_download_completed)
            dialog.exec()

        except Exception as e:
            logger.error(f"启动数据下载失败: {e}")
            self.ux_enhancer.show_error(f"启动数据下载失败: {e}")

    def _on_download_completed(self, success: bool):
        """数据下载完成"""
        try:
            if success:
                self.ux_enhancer.show_success("股票数据下载完成！")
                self.set_status("数据下载完成")

                # 刷新界面数据
                if self.stock_list_widget:
                    self.stock_list_widget.refresh_data()

                # 更新数据源状态
                self._update_data_source_status()

                logger.info("数据下载完成，界面已刷新")
            else:
                self.ux_enhancer.show_warning("数据下载失败，请检查网络连接和数据源配置")
                self.set_status("数据下载失败")
                logger.warning("数据下载失败")

        except Exception as e:
            logger.error(f"处理下载完成事件失败: {e}")

    def _on_refresh_data(self):
        """刷新数据"""
        self.set_status("正在刷新数据...", show_progress=True)
        self.ux_enhancer.show_loading("正在刷新股票数据...")
        self.ux_enhancer.show_info("开始刷新数据")
        self.data_refresh_requested.emit()
        logger.info("数据刷新请求已发送")

        # 模拟刷新完成（实际应该在数据刷新完成后调用）
        QTimer.singleShot(2000, self._on_refresh_completed)
    
    def _on_refresh_completed(self):
        """数据刷新完成"""
        self.ux_enhancer.hide_loading()
        self.ux_enhancer.show_success("数据刷新完成")
        self.set_status("数据刷新完成")

    def _on_export_data(self):
        """导出数据"""
        self.set_status("导出数据功能开发中...")
        self.ux_enhancer.show_info("导出数据功能即将推出")
        logger.info("导出数据请求")
    
    def _on_wyckoff_analysis(self):
        """威科夫分析"""
        self.set_status("执行威科夫分析...")
        self.ux_enhancer.show_loading("正在执行威科夫分析...")
        self.ux_enhancer.show_info("开始威科夫市场结构分析")
        logger.info("威科夫分析请求")

        # 模拟分析完成
        QTimer.singleShot(3000, lambda: self._on_analysis_completed("威科夫分析"))

    def _on_rs_analysis(self):
        """相对强弱分析"""
        self.set_status("执行相对强弱分析...")
        self.ux_enhancer.show_loading("正在计算相对强弱指标...")
        self.ux_enhancer.show_info("开始相对强弱计算")
        logger.info("相对强弱分析请求")

        # 模拟分析完成
        QTimer.singleShot(2500, lambda: self._on_analysis_completed("相对强弱分析"))

    def _on_sector_screening(self):
        """板块筛选"""
        self.set_status("执行板块筛选...")
        self.ux_enhancer.show_loading("正在执行板块相对强弱筛选...")
        self.ux_enhancer.show_info("开始板块筛选分析")
        self.tab_widget.setCurrentIndex(1)  # 切换到选股标签页

        # 切换到板块筛选子标签页
        if hasattr(self, 'sector_screening_widget'):
            # 找到选股标签页中的子标签页控件
            selection_tab_widget = self.tab_widget.widget(1)
            if selection_tab_widget:
                # 获取子标签页控件
                for child in selection_tab_widget.findChildren(QTabWidget):
                    child.setCurrentIndex(1)  # 切换到板块筛选标签页（现在是第2个）
                    break

        logger.info("板块筛选请求")

        # 模拟筛选完成
        QTimer.singleShot(3000, lambda: self._on_analysis_completed("板块筛选"))

    def _on_dual_screening_workflow(self):
        """双重筛选工作流"""
        self.set_status("启动双重筛选工作流...")
        self.ux_enhancer.show_loading("正在启动双重相对强弱筛选工作流...")
        self.ux_enhancer.show_info("开始双重筛选：板块筛选 → 个股筛选")
        self.tab_widget.setCurrentIndex(1)  # 切换到选股标签页

        # 切换到双重筛选工作流子标签页
        if hasattr(self, 'enhanced_selection_widget'):
            # 找到选股标签页中的子标签页控件
            selection_tab_widget = self.tab_widget.widget(1)
            if selection_tab_widget:
                # 获取子标签页控件
                for child in selection_tab_widget.findChildren(QTabWidget):
                    child.setCurrentIndex(0)  # 切换到双重筛选工作流标签页
                    break

        logger.info("双重筛选工作流请求")

        # 模拟工作流完成
        QTimer.singleShot(8000, lambda: self._on_analysis_completed("双重筛选工作流"))

    def _on_smart_selection(self):
        """智能选股"""
        self.set_status("执行智能选股...")
        self.ux_enhancer.show_loading("正在执行智能选股算法...")
        self.ux_enhancer.show_info("开始智能选股分析")
        self.tab_widget.setCurrentIndex(1)  # 切换到选股标签页

        # 切换到智能选股子标签页
        if hasattr(self, 'selection_widget'):
            # 找到选股标签页中的子标签页控件
            selection_tab_widget = self.tab_widget.widget(1)
            if selection_tab_widget:
                # 获取子标签页控件
                for child in selection_tab_widget.findChildren(QTabWidget):
                    child.setCurrentIndex(2)  # 切换到智能选股标签页（现在是第3个）
                    break

        logger.info("智能选股请求")

        # 模拟选股完成
        QTimer.singleShot(4000, lambda: self._on_analysis_completed("智能选股"))

    def _on_analysis_completed(self, analysis_type: str):
        """分析完成"""
        self.ux_enhancer.hide_loading()
        self.ux_enhancer.show_success(f"{analysis_type}完成")
        self.set_status(f"{analysis_type}完成")
    
    def _on_about(self):
        """关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "威科夫相对强弱选股系统 v1.0\n\n"
            "基于威科夫理论和相对强弱分析的智能选股系统\n"
            "技术栈：Python + PyQt6 + SQLite\n\n"
            "© 2025 量化投资系统"
        )
    
    def _on_stock_selected(self, symbol: str):
        """股票选择事件"""
        self.set_status(f"已选择股票：{symbol}")

        # 通知分析控件
        if self.analysis_widget:
            self.analysis_widget.set_current_stock(symbol)

        # 通知图表控件
        if self.chart_widget:
            self.chart_widget.set_current_stock(symbol)

        self.analysis_requested.emit(symbol)
        logger.info(f"股票选择：{symbol}")
    
    def _on_selection_completed(self, results: Dict[str, Any]):
        """选股完成事件"""
        count = len(results.get('selected_stocks', []))
        self.set_status(f"选股完成，共选出 {count} 只股票")
        logger.info(f"选股完成，结果数量：{count}")

    def _on_sector_selected(self, sector_code: str):
        """板块选择事件"""
        self.set_status(f"已选择板块：{sector_code}")
        logger.info(f"板块选择：{sector_code}")

    def _on_sector_screening_completed(self, results: Dict[str, Any]):
        """板块筛选完成事件"""
        count = results.get('count', 0)
        self.set_status(f"板块筛选完成，共筛选出 {count} 个强势板块")
        logger.info(f"板块筛选完成，结果数量：{count}")

        # 可以在这里触发后续的个股筛选
        if count > 0:
            self.ux_enhancer.show_success(f"发现 {count} 个强势板块，可进行个股筛选")
        else:
            self.ux_enhancer.show_warning("未发现强势板块，请调整筛选条件")

    def _on_workflow_started(self, config: Dict[str, Any]):
        """双重筛选工作流开始事件"""
        self.set_status("🚀 双重筛选工作流已启动")
        self.ux_enhancer.show_loading("正在执行双重相对强弱筛选工作流...")
        logger.info(f"双重筛选工作流开始：{config}")

    def _on_workflow_completed(self, results: Dict[str, Any]):
        """双重筛选工作流完成事件"""
        sector_count = results.get('sector_count', 0)
        final_count = results.get('final_count', 0)

        self.set_status(f"✅ 双重筛选完成：{sector_count} 个强势板块 → {final_count} 只优质股票")

        if final_count > 0:
            self.ux_enhancer.show_success(f"双重筛选成功！最终选出 {final_count} 只优质股票")
        else:
            self.ux_enhancer.show_warning("双重筛选未发现符合条件的股票，请调整筛选参数")

        logger.info(f"双重筛选工作流完成：板块数={sector_count}, 最终股票数={final_count}")

    def _on_sector_phase_completed(self, sectors: List[Dict]):
        """板块筛选阶段完成事件"""
        count = len(sectors)
        self.set_status(f"📊 板块筛选阶段完成，发现 {count} 个强势板块")
        logger.info(f"板块筛选阶段完成，强势板块数量：{count}")

    def _on_stock_phase_completed(self, stocks: List[Dict]):
        """个股筛选阶段完成事件"""
        count = len(stocks)
        self.set_status(f"🎯 个股筛选阶段完成，候选股票 {count} 只")
        logger.info(f"个股筛选阶段完成，候选股票数量：{count}")
    
    # 公共方法
    def set_status(self, message: str, timeout: int = 0, show_progress: bool = False):
        """
        设置状态栏消息
        
        Args:
            message: 状态消息
            timeout: 超时时间（毫秒），0表示不自动清除
            show_progress: 是否显示进度条
        """
        if self.status_label:
            self.status_label.setText(message)
        
        if self.progress_bar:
            self.progress_bar.setVisible(show_progress)
        
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.set_status("就绪"))
        
        logger.debug(f"状态更新：{message}")
    
    def update_connection_status(self, connected: bool, source: str = ""):
        """
        更新连接状态
        
        Args:
            connected: 是否连接
            source: 数据源名称
        """
        if self.connection_status:
            if connected:
                self.connection_status.setText(f"数据源：{source} 已连接")
                self.connection_status.setStyleSheet("color: green;")
            else:
                self.connection_status.setText("数据源：未连接")
                self.connection_status.setStyleSheet("color: red;")

    def _on_system_settings(self):
        """系统设置菜单事件"""
        # 切换到系统设置标签页
        if self.tab_widget and self.system_settings_widget:
            for i in range(self.tab_widget.count()):
                if self.tab_widget.widget(i) == self.system_settings_widget:
                    self.tab_widget.setCurrentIndex(i)
                    break

        self.set_status("已打开系统设置")
        logger.info("打开系统设置")

    def _on_view_logs(self):
        """查看日志菜单事件"""
        # 创建日志查看器窗口
        if not hasattr(self, '_log_viewer_window') or not self._log_viewer_window:
            self._log_viewer_window = QWidget()
            self._log_viewer_window.setWindowTitle("系统日志查看器")
            self._log_viewer_window.setMinimumSize(800, 600)

            layout = QVBoxLayout(self._log_viewer_window)
            self.log_viewer_widget = LogViewerWidget()
            layout.addWidget(self.log_viewer_widget)

        self._log_viewer_window.show()
        self._log_viewer_window.raise_()
        self._log_viewer_window.activateWindow()

        self.set_status("已打开日志查看器")
        logger.info("打开日志查看器")

    def _on_data_source_config(self):
        """数据源配置菜单事件"""
        # 创建数据源配置窗口
        if not hasattr(self, '_data_source_config_window') or not self._data_source_config_window:
            self._data_source_config_window = QWidget()
            self._data_source_config_window.setWindowTitle("数据源配置")
            self._data_source_config_window.setMinimumSize(900, 700)

            layout = QVBoxLayout(self._data_source_config_window)
            self.data_source_config_widget = DataSourceConfigWidget()

            # 连接信号
            self.data_source_config_widget.config_changed.connect(self._on_data_source_config_changed)
            self.data_source_config_widget.connection_tested.connect(self._on_connection_tested)

            layout.addWidget(self.data_source_config_widget)

        self._data_source_config_window.show()
        self._data_source_config_window.raise_()
        self._data_source_config_window.activateWindow()

        self.set_status("已打开数据源配置")
        logger.info("打开数据源配置")

    def _on_reconnect_data_source(self):
        """重新连接数据源菜单事件"""
        try:
            self.set_status("正在重新连接数据源...", 0, True)
            logger.info("开始重新连接数据源")

            if self.data_manager:
                # 重新初始化数据源
                success = self.data_manager.test_connection()

                if success:
                    active_source = self.data_manager.get_active_source_name()
                    self.update_connection_status(True, active_source or "XtData")
                    self.set_status("数据源重新连接成功", 3000)
                    logger.info("数据源重新连接成功")

                    # 显示成功消息
                    from PyQt6.QtWidgets import QMessageBox
                    QMessageBox.information(
                        self,
                        "连接成功",
                        f"已成功连接到数据源：{active_source or 'XtData'}"
                    )
                else:
                    self.update_connection_status(False)
                    self.set_status("数据源重新连接失败", 5000)
                    logger.warning("数据源重新连接失败")

                    # 显示连接失败的用户友好提示
                    self._show_connection_error_dialog()
            else:
                self.set_status("数据源管理器未初始化", 3000)
                logger.error("数据源管理器未初始化")

                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    self,
                    "连接失败",
                    "数据源管理器未初始化，请重启程序。"
                )

        except Exception as e:
            self.update_connection_status(False)
            self.set_status(f"重新连接失败: {e}", 5000)
            logger.error(f"重新连接数据源失败: {e}")

            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "连接错误",
                f"重新连接数据源时发生错误：\n{e}"
            )

    def _on_user_help(self):
        """用户帮助菜单事件"""
        # 创建用户帮助窗口
        if not hasattr(self, '_user_help_window') or not self._user_help_window:
            self._user_help_window = QWidget()
            self._user_help_window.setWindowTitle("用户帮助")
            self._user_help_window.setMinimumSize(1000, 700)

            layout = QVBoxLayout(self._user_help_window)
            self.help_system_widget = HelpSystemWidget()

            # 连接信号
            self.help_system_widget.help_topic_selected.connect(self._on_help_topic_selected)

            layout.addWidget(self.help_system_widget)

        self._user_help_window.show()
        self._user_help_window.raise_()
        self._user_help_window.activateWindow()

        self.set_status("已打开用户帮助")
        logger.info("打开用户帮助")

    def _on_performance_monitor(self):
        """性能监控菜单事件"""
        # 切换到系统监控标签页
        if self.tab_widget and self.system_monitor_widget:
            for i in range(self.tab_widget.count()):
                if self.tab_widget.widget(i) == self.system_monitor_widget:
                    self.tab_widget.setCurrentIndex(i)
                    break

        self.set_status("已打开性能监控")
        logger.info("打开性能监控")

    def _on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        try:
            # 应用新主题
            self.style_manager.set_theme(theme_name)
            self.setStyleSheet(self.style_manager.get_main_window_style())

            self.set_status(f"主题已切换为：{theme_name}")
            logger.info(f"主题已切换为：{theme_name}")

        except Exception as e:
            logger.error(f"切换主题失败: {e}")
            self.set_status(f"切换主题失败: {e}")

    def _on_settings_changed(self, settings: Dict[str, Any]):
        """系统设置变更事件"""
        try:
            # 应用性能设置
            performance_settings = settings.get('performance', {})
            if performance_settings:
                logger.info(f"应用性能设置: {performance_settings}")

            # 应用缓存设置
            cache_settings = settings.get('cache', {})
            if cache_settings:
                logger.info(f"应用缓存设置: {cache_settings}")

            # 应用日志设置
            logging_settings = settings.get('logging', {})
            if logging_settings:
                logger.info(f"应用日志设置: {logging_settings}")

            self.set_status("系统设置已应用")
            logger.info("系统设置已应用")

        except Exception as e:
            logger.error(f"应用系统设置失败: {e}")
            self.set_status(f"应用系统设置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出威科夫相对强弱选股系统吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            logger.info("用户确认退出程序")
            event.accept()
        else:
            event.ignore()

    def _on_data_source_config_changed(self, configs: Dict[str, Any]):
        """数据源配置变更事件"""
        try:
            logger.info(f"数据源配置已变更: {list(configs.keys())}")
            self.set_status("数据源配置已更新")

        except Exception as e:
            logger.error(f"处理数据源配置变更失败: {e}")
            self.set_status(f"配置更新失败: {e}")

    def _on_connection_tested(self, source_name: str, success: bool):
        """连接测试结果事件"""
        if success:
            self.set_status(f"数据源 {source_name} 连接测试成功")
            self.ux_enhancer.show_success(f"数据源 {source_name} 连接成功")
            logger.info(f"数据源 {source_name} 连接测试成功")
        else:
            self.set_status(f"数据源 {source_name} 连接测试失败")
            self.ux_enhancer.show_error(f"数据源 {source_name} 连接失败，请检查配置")
            logger.warning(f"数据源 {source_name} 连接测试失败")

    def _on_help_topic_selected(self, topic: str):
        """帮助主题选中事件"""
        logger.info(f"用户查看帮助主题: {topic}")

    def _on_system_alert(self, alert_type: str, message: str):
        """系统警报事件"""
        logger.warning(f"系统警报 [{alert_type}]: {message}")
        self.set_status(f"⚠️ 系统警报: {message}")

        # 根据警报类型显示不同级别的通知
        if alert_type.lower() in ['cpu', '内存', '磁盘']:
            self.ux_enhancer.show_warning(f"系统资源警报: {message}")
        else:
            self.ux_enhancer.show_error(f"系统警报: {message}")

    def _show_welcome_guide_if_needed(self):
        """如果需要则显示欢迎向导"""
        try:
            if self.guide_manager.should_show_welcome_guide():
                self.guide_manager.show_welcome_guide()
                logger.info("显示欢迎向导")
        except Exception as e:
            logger.error(f"显示欢迎向导失败: {e}")
            self.ux_enhancer.show_user_friendly_error(e, {'operation': '显示欢迎向导'})

    def _on_show_guide(self):
        """显示使用向导"""
        try:
            self.guide_manager.show_welcome_guide()
            logger.info("用户手动打开使用向导")
        except Exception as e:
            logger.error(f"显示使用向导失败: {e}")
            self.ux_enhancer.show_error("显示使用向导失败")

    def _on_show_wyckoff_guide(self):
        """显示威科夫分析向导"""
        try:
            self.feature_guide_manager.show_wyckoff_guide()
            logger.info("显示威科夫分析向导")
        except Exception as e:
            logger.error(f"显示威科夫分析向导失败: {e}")
            self.ux_enhancer.show_error("显示威科夫分析向导失败")

    def _on_show_rs_guide(self):
        """显示相对强弱分析向导"""
        try:
            self.feature_guide_manager.show_rs_guide()
            logger.info("显示相对强弱分析向导")
        except Exception as e:
            logger.error(f"显示相对强弱分析向导失败: {e}")
            self.ux_enhancer.show_error("显示相对强弱分析向导失败")

    def _on_show_selection_guide(self):
        """显示智能选股向导"""
        try:
            self.feature_guide_manager.show_selection_guide()
            logger.info("显示智能选股向导")
        except Exception as e:
            logger.error(f"显示智能选股向导失败: {e}")
            self.ux_enhancer.show_error("显示智能选股向导失败")

    def _on_context_help(self):
        """显示上下文帮助"""
        try:
            current_context = self.context_help_manager.get_current_context()
            self.context_help_manager.show_context_help(current_context)
            logger.info(f"显示上下文帮助: {current_context}")
        except Exception as e:
            logger.error(f"显示上下文帮助失败: {e}")
            self.ux_enhancer.show_error("显示帮助信息失败")

    def _on_show_faq(self):
        """显示常见问题"""
        try:
            self.faq_manager.show_faq_dialog()
            logger.info("显示常见问题对话框")
        except Exception as e:
            logger.error(f"显示常见问题失败: {e}")
            self.ux_enhancer.show_error("显示常见问题失败")

    def _on_preferences(self):
        """偏好设置菜单事件"""
        try:
            dialog = PreferencesDialog(self)
            dialog.preferences_applied.connect(self._on_preferences_applied)
            dialog.exec()
            logger.info("显示偏好设置对话框")
        except Exception as e:
            logger.error(f"显示偏好设置失败: {e}")
            self.ux_enhancer.show_error("显示偏好设置失败")

    def _on_preferences_applied(self):
        """偏好设置应用处理"""
        try:
            # 应用界面偏好设置
            self._apply_ui_preferences()

            # 应用主题设置
            ui_prefs = user_preferences.get_ui_preferences()
            theme_type = ThemeType.LIGHT if ui_prefs.theme == "light" else ThemeType.DARK
            theme_manager.set_theme(theme_type)

            self.set_status("偏好设置已应用")
            logger.info("偏好设置已应用")

        except Exception as e:
            logger.error(f"应用偏好设置失败: {e}")
            self.set_status(f"应用偏好设置失败: {e}")

    def _init_user_preferences(self):
        """初始化用户偏好设置"""
        try:
            # 加载用户偏好设置
            user_preferences.load_preferences()

            # 应用界面偏好设置
            self._apply_ui_preferences()

            # 应用主题设置
            ui_prefs = user_preferences.get_ui_preferences()
            theme_type = ThemeType.LIGHT if ui_prefs.theme == "light" else ThemeType.DARK
            theme_manager.set_theme(theme_type)

            logger.info("用户偏好设置初始化完成")

        except Exception as e:
            logger.error(f"初始化用户偏好设置失败: {e}")

    def _apply_ui_preferences(self):
        """应用界面偏好设置"""
        try:
            ui_prefs = user_preferences.get_ui_preferences()

            # 应用窗口大小
            if not ui_prefs.window_maximized:
                self.resize(ui_prefs.window_width, ui_prefs.window_height)
            else:
                self.showMaximized()

            # 应用工具栏和状态栏显示
            if hasattr(self, 'toolbar'):
                self.toolbar.setVisible(ui_prefs.show_toolbar)
            if hasattr(self, 'statusbar'):
                self.statusbar.setVisible(ui_prefs.show_statusbar)

            # 应用字体设置
            font = self.font()
            font.setFamily(ui_prefs.font_family)
            font.setPointSize(ui_prefs.font_size)
            self.setFont(font)

            logger.debug("界面偏好设置已应用")

        except Exception as e:
            logger.error(f"应用界面偏好设置失败: {e}")
