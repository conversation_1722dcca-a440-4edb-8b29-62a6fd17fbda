"""
数据库管理器

处理股票数据的存储、查询和更新
"""

import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import json
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.exceptions import DatabaseError

logger = get_logger(__name__)


@dataclass
class StockBasicInfo:
    """股票基本信息"""
    stock_code: str
    stock_name: str
    market: str
    industry: str = ""
    market_cap: float = 0.0
    total_shares: int = 0
    float_shares: int = 0
    list_date: Optional[str] = None
    is_st: bool = False
    status: str = "active"


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/stock_analysis.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"数据库管理器初始化完成: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 读取并执行schema.sql
                schema_path = Path(__file__).parent / "schema.sql"
                if schema_path.exists():
                    with open(schema_path, 'r', encoding='utf-8') as f:
                        schema_sql = f.read()
                    conn.executescript(schema_sql)
                else:
                    # 如果schema.sql不存在，创建基本表结构
                    self._create_basic_tables(conn)
                
                conn.commit()
                logger.info("数据库表结构初始化完成")
                
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise DatabaseError(f"初始化数据库失败: {e}")
    
    def _create_basic_tables(self, conn: sqlite3.Connection):
        """创建基本表结构"""
        # 股票基本信息表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_info (
                stock_code VARCHAR(20) PRIMARY KEY,
                stock_name VARCHAR(100) NOT NULL,
                list_date DATE,
                delist_date DATE,
                market VARCHAR(10) CHECK (market IN ('SH', 'SZ', 'BJ')),
                status VARCHAR(10) DEFAULT 'active',
                industry VARCHAR(50),
                market_cap DECIMAL(15,2),
                total_shares BIGINT,
                float_shares BIGINT,
                is_st BOOLEAN DEFAULT 0,
                create_date DATE DEFAULT (date('now')),
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 股票历史行情表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS stock_quotes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code VARCHAR(20) NOT NULL,
                trade_date DATE NOT NULL,
                open DECIMAL(10,3),
                high DECIMAL(10,3),
                low DECIMAL(10,3),
                close DECIMAL(10,3) NOT NULL,
                volume BIGINT DEFAULT 0,
                amount DECIMAL(15,2) DEFAULT 0,
                adj_factor DECIMAL(10,6) DEFAULT 1.0,
                change_pct DECIMAL(8,4),
                turnover_rate DECIMAL(8,4),
                pe_ratio DECIMAL(8,2),
                pb_ratio DECIMAL(8,2),
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(stock_code, trade_date),
                FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
            )
        """)
        
        # 数据更新记录表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS data_update_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                update_type VARCHAR(50) NOT NULL,
                stock_code VARCHAR(20),
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'success',
                error_message TEXT,
                records_count INTEGER DEFAULT 0
            )
        """)
        
        # 系统配置表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS system_config (
                config_key VARCHAR(100) PRIMARY KEY,
                config_value TEXT,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引
        conn.execute("CREATE INDEX IF NOT EXISTS idx_stock_quotes_date ON stock_quotes(trade_date)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_stock_quotes_code ON stock_quotes(stock_code)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_stock_info_market ON stock_info(market)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_stock_info_industry ON stock_info(industry)")
    
    def insert_stock_info(self, stock_info) -> bool:
        """
        插入或更新股票基本信息

        Args:
            stock_info: 股票基本信息（字典或对象）

        Returns:
            bool: 操作是否成功
        """
        try:
            # 处理字典格式的输入
            if isinstance(stock_info, dict):
                stock_code = stock_info.get('stock_code')
                stock_name = stock_info.get('stock_name')
                market = stock_info.get('market')
                industry = stock_info.get('industry')
                market_cap = stock_info.get('market_cap')
                total_shares = stock_info.get('total_shares')
                float_shares = stock_info.get('float_shares')
                list_date = stock_info.get('list_date')
                is_st = stock_info.get('is_st', False)
                status = stock_info.get('status', 'active')
            else:
                # 处理对象格式的输入
                stock_code = stock_info.stock_code
                stock_name = stock_info.stock_name
                market = stock_info.market
                industry = stock_info.industry
                market_cap = stock_info.market_cap
                total_shares = getattr(stock_info, 'total_shares', None)
                float_shares = getattr(stock_info, 'float_shares', None)
                list_date = getattr(stock_info, 'list_date', None)
                is_st = stock_info.is_st
                status = stock_info.status

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO stock_info (
                        stock_code, stock_name, market, industry, market_cap,
                        total_shares, float_shares, list_date, is_st, status, update_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stock_code,
                    stock_name,
                    market,
                    industry,
                    market_cap,
                    total_shares,
                    float_shares,
                    list_date,
                    is_st,
                    status,
                    datetime.now()
                ))

                conn.commit()
                return True

        except Exception as e:
            stock_code_str = stock_info.get('stock_code') if isinstance(stock_info, dict) else getattr(stock_info, 'stock_code', 'unknown')
            logger.error(f"插入股票信息失败 {stock_code_str}: {e}")
            return False
    
    def insert_stock_quotes(self, stock_code: str, quotes_data: Any) -> bool:
        """
        插入股票历史行情数据
        
        Args:
            stock_code: 股票代码
            quotes_data: 行情数据
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 转换数据格式
            if isinstance(quotes_data, pd.DataFrame):
                df = quotes_data
            elif isinstance(quotes_data, list) and len(quotes_data) > 0:
                # 假设是字典列表格式
                df = pd.DataFrame(quotes_data)
            else:
                logger.warning(f"不支持的数据格式: {type(quotes_data)}")
                return False
            
            if df.empty:
                return True
            
            # 标准化列名
            column_mapping = {
                'date': 'trade_date',
                'open_price': 'open',
                'high_price': 'high',
                'low_price': 'low',
                'close_price': 'close',
                'volume': 'volume',
                'amount': 'amount'
            }

            df = df.rename(columns=column_mapping)

            # 确保必要的列存在
            required_columns = ['trade_date', 'open', 'high', 'low', 'close']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"缺少必要列: {col}")
                    return False
            
            # 添加股票代码
            df['stock_code'] = stock_code
            
            # 处理日期格式
            if 'trade_date' in df.columns:
                df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                # 批量插入数据
                for _, row in df.iterrows():
                    try:
                        conn.execute("""
                            INSERT OR REPLACE INTO stock_quotes (
                                stock_code, trade_date, open, high,
                                low, close, volume, amount
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            row['stock_code'],
                            row['trade_date'],
                            row.get('open', 0),
                            row.get('high', 0),
                            row.get('low', 0),
                            row.get('close', 0),
                            row.get('volume', 0),
                            row.get('amount', 0)
                        ))
                    except Exception as e:
                        logger.warning(f"插入行情数据失败 {stock_code} {row.get('trade_date')}: {e}")
                        continue
                
                conn.commit()
                
                # 记录更新日志
                self._log_update('stock_quotes', stock_code, len(df))
                
                return True
                
        except Exception as e:
            logger.error(f"插入股票行情数据失败 {stock_code}: {e}")
            return False
    
    def get_stock_list(self, market: Optional[str] = None, status: str = 'active') -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Args:
            market: 市场代码 ('SH', 'SZ', 'BJ')
            status: 股票状态
            
        Returns:
            List[Dict]: 股票列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                sql = "SELECT * FROM stock_info WHERE status = ?"
                params = [status]
                
                if market:
                    sql += " AND market = ?"
                    params.append(market)
                
                sql += " ORDER BY stock_code"
                
                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_stock_quotes(
        self, 
        stock_code: str, 
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: Optional[int] = None
    ) -> pd.DataFrame:
        """
        获取股票历史行情
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制条数
            
        Returns:
            pd.DataFrame: 行情数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = """
                    SELECT trade_date, open, high, low, close, volume, amount
                    FROM stock_quotes
                    WHERE stock_code = ?
                """
                params = [stock_code]
                
                if start_date:
                    sql += " AND trade_date >= ?"
                    params.append(start_date)
                
                if end_date:
                    sql += " AND trade_date <= ?"
                    params.append(end_date)
                
                sql += " ORDER BY trade_date DESC"
                
                if limit:
                    sql += f" LIMIT {limit}"
                
                df = pd.read_sql_query(sql, conn, params=params)
                
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                    df.set_index('trade_date', inplace=True)
                
                return df
                
        except Exception as e:
            logger.error(f"获取股票行情失败 {stock_code}: {e}")
            return pd.DataFrame()
    
    def _log_update(self, update_type: str, stock_code: Optional[str] = None, records_count: int = 0):
        """记录更新日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO data_update_log (update_type, stock_code, records_count)
                    VALUES (?, ?, ?)
                """, (update_type, stock_code, records_count))
                conn.commit()
        except Exception as e:
            logger.warning(f"记录更新日志失败: {e}")
    
    def update_last_update_time(self):
        """更新最后更新时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO system_config (config_key, config_value)
                    VALUES ('last_update_time', ?)
                """, (datetime.now().isoformat(),))
                conn.commit()
        except Exception as e:
            logger.warning(f"更新最后更新时间失败: {e}")
    
    def get_last_update_time(self) -> Optional[datetime]:
        """获取最后更新时间"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT config_value FROM system_config 
                    WHERE config_key = 'last_update_time'
                """)
                row = cursor.fetchone()
                
                if row:
                    return datetime.fromisoformat(row[0])
                return None
                
        except Exception as e:
            logger.warning(f"获取最后更新时间失败: {e}")
            return None
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                stats = {}
                
                # 股票数量
                cursor = conn.execute("SELECT COUNT(*) FROM stock_info WHERE status = 'active'")
                stats['total_stocks'] = cursor.fetchone()[0]
                
                # 行情数据条数
                cursor = conn.execute("SELECT COUNT(*) FROM stock_quotes")
                stats['total_quotes'] = cursor.fetchone()[0]
                
                # 最新交易日
                cursor = conn.execute("SELECT MAX(trade_date) FROM stock_quotes")
                latest_date = cursor.fetchone()[0]
                stats['latest_trade_date'] = latest_date
                
                # 数据库文件大小
                stats['db_size_mb'] = self.db_path.stat().st_size / (1024 * 1024)
                
                return stats
                
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM stock_quotes WHERE trade_date < ?
                """, (cutoff_date,))

                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"清理了 {deleted_count} 条旧行情数据")
                return deleted_count

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return 0

    def get_stock_count(self) -> int:
        """获取股票数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM stock_info")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取股票数量失败: {e}")
            return 0

    def get_quote_count(self) -> int:
        """获取行情记录数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM stock_quotes")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取行情记录数量失败: {e}")
            return 0

    def get_recent_stocks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近添加的股票"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT stock_code, stock_name, market, update_time
                    FROM stock_info
                    ORDER BY update_time DESC
                    LIMIT ?
                """, (limit,))

                columns = ['stock_code', 'stock_name', 'market', 'update_time']
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取最近股票失败: {e}")
            return []

    def check_duplicate_stocks(self) -> int:
        """检查重复的股票代码"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM (
                        SELECT stock_code, COUNT(*) as cnt
                        FROM stock_info
                        GROUP BY stock_code
                        HAVING cnt > 1
                    )
                """)
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"检查重复股票失败: {e}")
            return 0

    def check_empty_stock_names(self) -> int:
        """检查空的股票名称"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM stock_info
                    WHERE stock_name IS NULL OR stock_name = '' OR stock_name = stock_code
                """)
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"检查空股票名称失败: {e}")
            return 0

    def get_market_distribution(self) -> Dict[str, int]:
        """获取市场分布"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT market, COUNT(*) as count
                    FROM stock_info
                    GROUP BY market
                    ORDER BY count DESC
                """)
                return dict(cursor.fetchall())
        except Exception as e:
            logger.error(f"获取市场分布失败: {e}")
            return {}

    def get_all_stocks(self) -> List[Dict[str, Any]]:
        """获取所有股票信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT stock_code, stock_name, market, industry, is_st, status
                    FROM stock_info
                    ORDER BY stock_code
                """)

                columns = ['stock_code', 'stock_name', 'market', 'industry', 'is_st', 'status']
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取所有股票失败: {e}")
            return []

    def get_stock_quote_count(self, stock_code: str) -> int:
        """获取指定股票的行情记录数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM stock_quotes WHERE stock_code = ?", (stock_code,))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 行情记录数量失败: {e}")
            return 0

    def insert_stock_quote(self, stock_code: str, quote_data: Dict[str, Any]) -> bool:
        """插入单条股票行情数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO stock_quotes (
                        stock_code, trade_date, open, high, low, close,
                        volume, amount, update_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    stock_code,
                    quote_data.get('trade_date'),
                    quote_data.get('open'),
                    quote_data.get('high'),
                    quote_data.get('low'),
                    quote_data.get('close'),
                    quote_data.get('volume'),
                    quote_data.get('amount'),
                    datetime.now()
                ))

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"插入股票 {stock_code} 行情数据失败: {e}")
            return False

    def insert_stock_quotes_batch(self, stock_code: str, quotes_data: List[Dict[str, Any]]) -> bool:
        """批量插入股票行情数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                data_tuples = []
                for quote_data in quotes_data:
                    data_tuples.append((
                        stock_code,
                        quote_data.get('trade_date'),
                        quote_data.get('open'),
                        quote_data.get('high'),
                        quote_data.get('low'),
                        quote_data.get('close'),
                        quote_data.get('volume'),
                        quote_data.get('amount'),
                        datetime.now()
                    ))

                conn.executemany("""
                    INSERT OR REPLACE INTO stock_quotes (
                        stock_code, trade_date, open, high, low, close,
                        volume, amount, update_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, data_tuples)

                conn.commit()
                return True

        except Exception as e:
            logger.error(f"批量插入股票 {stock_code} 行情数据失败: {e}")
            return False
