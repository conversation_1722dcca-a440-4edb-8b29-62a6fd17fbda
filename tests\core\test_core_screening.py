#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心筛选功能测试脚本
测试新实现的板块筛选和个股筛选功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.manager import DatabaseManager
from src.data_sources.manager import DataSourceManager
from src.services.sector_manager import SectorManager
from src.services.return_calculator import ReturnCalculator
from src.engines.sector_screening import SectorScreeningEngine, SectorScreeningParams
from src.engines.stock_screening import StockScreeningEngine, StockScreeningParams
from src.services.screening_workflow import ScreeningWorkflow, ScreeningWorkflowParams
from src.utils.cache import CacheManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class CoreScreeningTester:
    """核心筛选功能测试器"""
    
    def __init__(self):
        self.db_manager = None
        self.data_source_manager = None
        self.sector_manager = None
        self.return_calculator = None
        self.sector_engine = None
        self.stock_engine = None
        self.workflow = None
    
    async def initialize(self):
        """初始化测试环境"""
        try:
            print("🔧 初始化测试环境...")
            
            # 初始化数据库
            db_path = "data/test_screening.db"
            self.db_manager = DatabaseManager(db_path)
            if not self.db_manager.initialize():
                raise Exception("数据库初始化失败")
            
            # 初始化数据源管理器
            self.data_source_manager = DataSourceManager()
            
            # 初始化板块管理器
            self.sector_manager = SectorManager(self.db_manager, self.data_source_manager)
            
            # 初始化收益率计算器
            cache_manager = CacheManager()
            self.return_calculator = ReturnCalculator(
                self.db_manager, self.sector_manager, cache_manager
            )
            
            # 初始化筛选引擎
            self.sector_engine = SectorScreeningEngine(self.sector_manager, self.return_calculator)
            self.stock_engine = StockScreeningEngine(self.sector_manager, self.return_calculator)
            
            # 初始化工作流
            self.workflow = ScreeningWorkflow(
                self.db_manager, self.sector_manager, self.return_calculator
            )
            
            print("✅ 测试环境初始化完成")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    async def test_sector_management(self):
        """测试板块管理功能"""
        try:
            print("\n📊 测试板块管理功能...")
            
            # 1. 初始化板块数据
            print("  - 初始化板块数据...")
            success = await self.sector_manager.initialize_sectors()
            assert success, "板块数据初始化失败"
            
            # 2. 获取板块列表
            print("  - 获取板块列表...")
            industry_sectors = self.sector_manager.get_sector_list("industry")
            concept_sectors = self.sector_manager.get_sector_list("concept")
            
            print(f"    行业板块数量: {len(industry_sectors)}")
            print(f"    概念板块数量: {len(concept_sectors)}")
            
            assert len(industry_sectors) > 0, "行业板块数据为空"
            assert len(concept_sectors) > 0, "概念板块数据为空"
            
            # 3. 下载板块历史数据（模拟）
            print("  - 下载板块历史数据...")
            sector_codes = [s['sector_code'] for s in industry_sectors[:5]]  # 测试前5个
            success = await self.sector_manager.download_sector_data(
                sector_codes, days=30
            )
            assert success, "板块历史数据下载失败"
            
            # 4. 先插入测试股票信息
            print("  - 插入测试股票信息...")
            test_stocks = ["000001.SZ", "000002.SZ", "000858.SZ"]
            for stock_code in test_stocks:
                try:
                    sql = """
                    INSERT OR IGNORE INTO stock_info
                    (stock_code, stock_name, market, status)
                    VALUES (?, ?, ?, ?)
                    """
                    with self.db_manager.get_connection() as conn:
                        conn.execute(sql, (stock_code, f"测试股票{stock_code}", "SZ", "active"))
                        conn.commit()
                except Exception as e:
                    print(f"    插入股票{stock_code}失败: {e}")

            # 5. 更新板块成分股（模拟）
            print("  - 更新板块成分股...")
            test_sector = industry_sectors[0]['sector_code']
            success = await self.sector_manager.update_sector_constituents(
                test_sector, test_stocks
            )
            assert success, "板块成分股更新失败"

            # 验证成分股
            constituents = self.sector_manager.get_sector_constituents(test_sector)
            print(f"    板块 {test_sector} 成分股数量: {len(constituents)}")
            
            print("✅ 板块管理功能测试通过")
            
        except Exception as e:
            print(f"❌ 板块管理功能测试失败: {e}")
            raise
    
    async def test_return_calculation(self):
        """测试收益率计算功能"""
        try:
            print("\n📈 测试收益率计算功能...")
            
            # 设置测试参数
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            # 1. 测试市场指数收益率计算
            print("  - 测试市场指数收益率计算...")
            market_symbols = ["000001.SH", "399001.SZ"]
            market_returns = await self.return_calculator.calculate_batch_returns(
                market_symbols, 'market', start_date, end_date
            )
            
            print(f"    市场指数收益率: {market_returns}")
            assert len(market_returns) > 0, "市场指数收益率计算失败"
            
            # 2. 测试板块收益率计算
            print("  - 测试板块收益率计算...")
            sector_list = self.sector_manager.get_sector_list()
            sector_symbols = [s['sector_code'] for s in sector_list[:5]]
            sector_returns = await self.return_calculator.calculate_batch_returns(
                sector_symbols, 'sector', start_date, end_date
            )
            
            print(f"    板块收益率数量: {len(sector_returns)}")
            assert len(sector_returns) > 0, "板块收益率计算失败"
            
            # 3. 测试预计算功能
            print("  - 测试预计算功能...")
            time_ranges = [(start_date, end_date)]
            stats = await self.return_calculator.precompute_returns(time_ranges)
            
            print(f"    预计算统计: {stats['completed_ranges']}/{stats['total_ranges']}")
            assert stats['completed_ranges'] > 0, "预计算功能失败"
            
            print("✅ 收益率计算功能测试通过")
            
        except Exception as e:
            print(f"❌ 收益率计算功能测试失败: {e}")
            raise
    
    async def test_sector_screening(self):
        """测试板块筛选功能"""
        try:
            print("\n🔍 测试板块筛选功能...")
            
            # 设置筛选参数
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            params = SectorScreeningParams(
                start_date=start_date,
                end_date=end_date,
                market_index="000001.SH",
                sector_types=["industry", "concept"],
                min_relative_strength=0.0,
                max_sectors=10
            )
            
            # 执行板块筛选
            print("  - 执行板块筛选...")
            result = await self.sector_engine.screen_strong_sectors(params)
            
            print(f"    市场收益率: {result.market_return:.4f}")
            print(f"    总板块数: {result.total_sectors}")
            print(f"    强势板块数: {result.selected_count}")
            
            # 显示前5个强势板块
            print("    前5个强势板块:")
            for i, sector in enumerate(result.strong_sectors[:5]):
                print(f"      {i+1}. {sector.sector_name} ({sector.sector_code})")
                print(f"         板块收益率: {sector.sector_return:.4f}")
                print(f"         相对强弱度: {sector.relative_strength:.4f}")
            
            assert result.selected_count > 0, "没有筛选出强势板块"
            assert result.market_return is not None, "市场收益率计算失败"
            
            print("✅ 板块筛选功能测试通过")
            return result
            
        except Exception as e:
            print(f"❌ 板块筛选功能测试失败: {e}")
            raise
    
    async def test_stock_screening(self, sector_result):
        """测试个股筛选功能"""
        try:
            print("\n🎯 测试个股筛选功能...")
            
            # 设置筛选参数
            params = StockScreeningParams(
                start_date=sector_result.params.start_date,
                end_date=sector_result.params.end_date,
                stocks_per_sector=3,
                min_relative_strength=0.0,
                exclude_st=True,
                exclude_suspended=True,
                exclude_new_stocks=True,
                max_stocks_total=20
            )
            
            # 执行个股筛选
            print("  - 执行个股筛选...")
            result = await self.stock_engine.screen_stocks_in_sectors(
                sector_result, params
            )
            
            print(f"    候选股票总数: {result.total_candidates}")
            print(f"    处理板块数: {result.sectors_processed}")
            print(f"    选出股票数: {result.selected_count}")
            
            # 显示前10只股票
            print("    前10只选出的股票:")
            for i, stock in enumerate(result.selected_stocks[:10]):
                print(f"      {i+1}. {stock.stock_name} ({stock.stock_code})")
                print(f"         所属板块: {stock.sector_name}")
                print(f"         个股收益率: {stock.stock_return:.4f}")
                print(f"         相对强弱度: {stock.relative_strength:.4f}")
                print(f"         综合得分: {stock.final_score:.4f}")
            
            assert result.selected_count > 0, "没有筛选出股票"
            
            print("✅ 个股筛选功能测试通过")
            return result
            
        except Exception as e:
            print(f"❌ 个股筛选功能测试失败: {e}")
            raise
    
    async def test_complete_workflow(self):
        """测试完整工作流"""
        try:
            print("\n🚀 测试完整选股工作流...")
            
            # 设置工作流参数
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            params = ScreeningWorkflowParams(
                start_date=start_date,
                end_date=end_date,
                market_index="000001.SH",
                sector_types=["industry", "concept"],
                max_sectors=8,
                min_sector_relative_strength=0.0,
                stocks_per_sector=2,
                min_stock_relative_strength=0.0,
                max_stocks_total=15,
                exclude_st=True,
                exclude_suspended=True,
                exclude_new_stocks=True
            )
            
            # 执行完整工作流
            print("  - 执行完整工作流...")
            
            def progress_callback(message, progress):
                print(f"    进度 {progress:.1f}%: {message}")
            
            result = await self.workflow.execute_screening(params, progress_callback)
            
            print(f"    执行时间: {result.execution_time}")
            print(f"    最终股票池大小: {len(result.final_stock_pool)}")
            
            # 显示最终结果
            print("    最终股票池:")
            for i, stock in enumerate(result.final_stock_pool[:10]):
                print(f"      {i+1}. {stock['stock_name']} ({stock['stock_code']})")
                print(f"         板块: {stock['sector_name']}")
                print(f"         相对强弱度: {stock['relative_strength']:.4f}")
                print(f"         综合得分: {stock['final_score']:.4f}")
            
            assert len(result.final_stock_pool) > 0, "最终股票池为空"
            
            print("✅ 完整工作流测试通过")
            return result
            
        except Exception as e:
            print(f"❌ 完整工作流测试失败: {e}")
            raise
    
    async def run_all_tests(self):
        """运行所有测试"""
        try:
            print("🧪 开始核心筛选功能测试")
            print("=" * 60)
            
            # 初始化
            await self.initialize()
            
            # 运行各项测试
            await self.test_sector_management()
            await self.test_return_calculation()
            sector_result = await self.test_sector_screening()
            await self.test_stock_screening(sector_result)
            await self.test_complete_workflow()
            
            print("\n" + "=" * 60)
            print("🎉 所有核心筛选功能测试通过！")
            print("\n✅ 实现的功能:")
            print("  - 板块数据管理和维护")
            print("  - 涨幅批量计算和缓存")
            print("  - 板块相对强弱筛选")
            print("  - 板块内个股筛选")
            print("  - 完整选股工作流集成")
            print("\n🚀 系统已具备完整的威科夫相对强弱选股能力！")
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            return False
        
        return True


async def main():
    """主函数"""
    tester = CoreScreeningTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 下一步建议:")
        print("  1. 集成真实的板块数据源")
        print("  2. 完善用户界面集成")
        print("  3. 添加更多筛选条件")
        print("  4. 实现多时间段并行分析")
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
