#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源模块单元测试
测试数据源接口、适配器、管理器等核心功能
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime, timedelta
import tempfile
import os
import json

# 导入被测试的模块
from src.data_sources import (
    DataSourceConfig, MarketData, SectorInfo, StockInfo,
    DataSourceException, ConnectionException, DataException,
    XtDataAdapter, DataSourceManager, DataSourceStatus,
    ConnectionPool, ConnectionStatus, ConnectionInfo,
    DataFormatter, DataQualityReport,
    DataSourceConfigManager, ConfigValidator
)


class TestDataSourceConfig(unittest.TestCase):
    """测试数据源配置类"""
    
    def test_config_creation(self):
        """测试配置创建"""
        config = DataSourceConfig(
            name="test_source",
            enabled=True,
            timeout=30,
            config={"ip": "127.0.0.1", "port": 58610}
        )
        
        self.assertEqual(config.name, "test_source")
        self.assertTrue(config.enabled)
        self.assertEqual(config.timeout, 30)
        self.assertEqual(config.config["ip"], "127.0.0.1")
    
    def test_config_defaults(self):
        """测试默认配置"""
        config = DataSourceConfig(name="test")
        
        self.assertTrue(config.enabled)
        self.assertEqual(config.timeout, 30)
        self.assertEqual(config.retry_times, 3)
        self.assertTrue(config.auto_reconnect)
        self.assertEqual(config.config, {})


class TestMarketData(unittest.TestCase):
    """测试市场数据类"""
    
    def setUp(self):
        """设置测试数据"""
        self.valid_data = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02'],
            'open': [10.0, 10.5],
            'high': [10.5, 11.0],
            'low': [9.8, 10.2],
            'close': [10.2, 10.8],
            'volume': [1000000, 1200000]
        })
    
    def test_valid_market_data(self):
        """测试有效的市场数据"""
        market_data = MarketData(
            symbol="000001.SZ",
            data=self.valid_data,
            source="test_source",
            update_time=datetime.now()
        )
        
        self.assertEqual(market_data.symbol, "000001.SZ")
        self.assertEqual(market_data.source, "test_source")
        self.assertEqual(market_data.data_type, "daily")
    
    def test_invalid_market_data(self):
        """测试无效的市场数据"""
        invalid_data = pd.DataFrame({
            'trade_date': ['2024-01-01'],
            'open': [10.0]
            # 缺少必要列
        })
        
        with self.assertRaises(ValueError):
            MarketData(
                symbol="000001.SZ",
                data=invalid_data,
                source="test_source",
                update_time=datetime.now()
            )


class TestSectorInfo(unittest.TestCase):
    """测试板块信息类"""
    
    def test_sector_info_creation(self):
        """测试板块信息创建"""
        sector = SectorInfo(
            sector_code="BK001",
            sector_name="银行",
            sector_type="industry",
            constituents=["000001.SZ", "600036.SH"]
        )
        
        self.assertEqual(sector.sector_code, "BK001")
        self.assertEqual(sector.sector_name, "银行")
        self.assertEqual(len(sector.constituents), 2)
        self.assertIsInstance(sector.update_time, datetime)


class TestXtDataAdapter(unittest.TestCase):
    """测试XtData适配器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = DataSourceConfig(
            name="xtdata_test",
            config={"ip": "127.0.0.1", "port": 58610}
        )
    
    @patch('src.data_sources.xtdata_adapter.logger')
    def test_init_without_xtdata(self, mock_logger):
        """测试没有XtData模块时的初始化"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            with self.assertRaises(ConnectionException):
                XtDataAdapter(self.config)
    
    @patch('src.data_sources.xtdata_adapter.logger')
    @patch('src.data_sources.xtdata_adapter.xtdata', create=True)
    def test_init_with_xtdata(self, mock_xtdata, mock_logger):
        """测试有XtData模块时的初始化"""
        adapter = XtDataAdapter(self.config)
        
        self.assertEqual(adapter.name, "xtdata_test")
        self.assertEqual(adapter.ip, "127.0.0.1")
        self.assertEqual(adapter.port, 58610)
        self.assertFalse(adapter.connected)
    
    @patch('src.data_sources.xtdata_adapter.xtdata', create=True)
    def test_connect_success(self, mock_xtdata):
        """测试连接成功"""
        # 模拟连接成功
        mock_xtdata.get_market_data_ex.return_value = {"000001.SZ": pd.DataFrame()}
        
        adapter = XtDataAdapter(self.config)
        result = adapter.connect()
        
        self.assertTrue(result)
        self.assertTrue(adapter.connected)
    
    @patch('src.data_sources.xtdata_adapter.xtdata', create=True)
    def test_connect_failure(self, mock_xtdata):
        """测试连接失败"""
        # 模拟连接失败
        mock_xtdata.get_market_data_ex.side_effect = Exception("Connection failed")
        
        adapter = XtDataAdapter(self.config)
        result = adapter.connect()
        
        self.assertFalse(result)
        self.assertFalse(adapter.connected)


class TestDataSourceManager(unittest.TestCase):
    """测试数据源管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.manager = DataSourceManager()
        
        # 创建模拟数据源
        self.mock_source = Mock()
        self.mock_source.name = "test_source"
        self.mock_source.is_connected = True
        self.mock_source.connect.return_value = True
        self.mock_source.test_connection.return_value = True
    
    def test_register_source(self):
        """测试注册数据源"""
        self.manager.register_source(self.mock_source)
        
        self.assertIn("test_source", self.manager._sources)
        self.assertEqual(self.manager._sources["test_source"], self.mock_source)
    
    def test_unregister_source(self):
        """测试注销数据源"""
        self.manager.register_source(self.mock_source)
        result = self.manager.unregister_source("test_source")
        
        self.assertTrue(result)
        self.assertNotIn("test_source", self.manager._sources)
    
    def test_get_primary_source(self):
        """测试获取主数据源"""
        self.manager.register_source(self.mock_source)
        self.manager.set_primary_source("test_source")
        
        primary = self.manager.get_primary_source()
        self.assertEqual(primary, self.mock_source)
    
    def test_health_check(self):
        """测试健康检查"""
        self.manager.register_source(self.mock_source)
        
        status = self.manager.health_check()
        
        self.assertIn("test_source", status)
        self.assertEqual(status["test_source"], DataSourceStatus.HEALTHY)


class TestConnectionPool(unittest.TestCase):
    """测试连接池"""
    
    def setUp(self):
        """设置测试环境"""
        def mock_factory():
            mock_conn = Mock()
            mock_conn.is_connected = True
            mock_conn.connect.return_value = True
            mock_conn.test_connection.return_value = True
            return mock_conn
        
        self.pool = ConnectionPool(
            name="test_pool",
            factory=mock_factory,
            max_size=5,
            min_size=2
        )
    
    def test_pool_initialization(self):
        """测试连接池初始化"""
        self.assertEqual(self.pool.name, "test_pool")
        self.assertEqual(self.pool.max_size, 5)
        self.assertEqual(self.pool.min_size, 2)
    
    def test_get_connection(self):
        """测试获取连接"""
        conn = self.pool.get_connection()
        
        self.assertIsNotNone(conn)
        self.assertTrue(hasattr(conn, 'is_connected'))
    
    def test_return_connection(self):
        """测试归还连接"""
        conn = self.pool.get_connection()
        result = self.pool.return_connection(conn)
        
        self.assertTrue(result)
    
    def test_pool_status(self):
        """测试连接池状态"""
        status = self.pool.get_status()
        
        self.assertIn('total_connections', status)
        self.assertIn('active_connections', status)
        self.assertIn('idle_connections', status)


class TestDataFormatter(unittest.TestCase):
    """测试数据格式化工具"""
    
    def setUp(self):
        """设置测试环境"""
        self.formatter = DataFormatter()
        
        # 创建测试数据
        self.test_data = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02'],
            'o': [10.0, 10.5],
            'h': [10.5, 11.0],
            'l': [9.8, 10.2],
            'c': [10.2, 10.8],
            'v': [1000000, 1200000]
        })
    
    def test_standardize_columns(self):
        """测试列标准化"""
        column_mapping = {
            'date': 'trade_date',
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        }
        
        result = self.formatter.standardize_columns(self.test_data, column_mapping)
        
        expected_columns = ['trade_date', 'open', 'high', 'low', 'close', 'volume']
        self.assertTrue(all(col in result.columns for col in expected_columns))
    
    def test_validate_data_quality(self):
        """测试数据质量验证"""
        # 创建有问题的数据
        bad_data = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02'],
            'open': [10.0, None],  # 包含空值
            'high': [10.5, 11.0],
            'low': [9.8, 10.2],
            'close': [10.2, 10.8],
            'volume': [1000000, -100]  # 包含负值
        })
        
        report = self.formatter.validate_data_quality(bad_data)
        
        self.assertIsInstance(report, DataQualityReport)
        self.assertGreater(report.missing_values, 0)
        self.assertGreater(report.anomalies, 0)
    
    def test_clean_data(self):
        """测试数据清洗"""
        # 创建需要清洗的数据
        dirty_data = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'open': [10.0, None, 10.5],
            'high': [10.5, 11.0, 11.2],
            'low': [9.8, 10.2, 10.1],
            'close': [10.2, 10.8, 10.9],
            'volume': [1000000, 1200000, 1100000]
        })
        
        cleaned_data = self.formatter.clean_data(dirty_data)
        
        # 检查是否移除了包含空值的行
        self.assertFalse(cleaned_data.isnull().any().any())
    
    def test_convert_data_types(self):
        """测试数据类型转换"""
        # 创建字符串类型的数值数据
        str_data = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02'],
            'open': ['10.0', '10.5'],
            'high': ['10.5', '11.0'],
            'low': ['9.8', '10.2'],
            'close': ['10.2', '10.8'],
            'volume': ['1000000', '1200000']
        })
        
        converted_data = self.formatter.convert_data_types(str_data)
        
        # 检查数值列是否转换为正确的类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            self.assertTrue(pd.api.types.is_numeric_dtype(converted_data[col]))


class TestDataSourceConfigManager(unittest.TestCase):
    """测试数据源配置管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
        self.manager = DataSourceConfigManager(self.config_file)
        
        # 创建测试配置
        self.test_config = {
            "data_sources": {
                "xtdata": {
                    "name": "XtData",
                    "enabled": True,
                    "config": {
                        "ip": "127.0.0.1",
                        "port": 58610
                    }
                }
            }
        }
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_load_config(self):
        """测试配置保存和加载"""
        # 保存配置
        self.manager.save_config(self.test_config)
        
        # 加载配置
        loaded_config = self.manager.load_config()
        
        self.assertEqual(loaded_config["data_sources"]["xtdata"]["name"], "XtData")
        self.assertTrue(loaded_config["data_sources"]["xtdata"]["enabled"])
    
    def test_validate_config(self):
        """测试配置验证"""
        validator = ConfigValidator()
        
        # 测试有效配置
        is_valid, errors = validator.validate(self.test_config)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # 测试无效配置
        invalid_config = {"invalid": "config"}
        is_valid, errors = validator.validate(invalid_config)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_get_data_source_config(self):
        """测试获取数据源配置"""
        self.manager.save_config(self.test_config)
        
        xtdata_config = self.manager.get_data_source_config("xtdata")
        
        self.assertIsNotNone(xtdata_config)
        self.assertEqual(xtdata_config.name, "XtData")
        self.assertTrue(xtdata_config.enabled)
    
    def test_update_data_source_config(self):
        """测试更新数据源配置"""
        self.manager.save_config(self.test_config)
        
        # 更新配置
        new_config = DataSourceConfig(
            name="XtData_Updated",
            enabled=False,
            config={"ip": "*************", "port": 58611}
        )
        
        result = self.manager.update_data_source_config("xtdata", new_config)
        self.assertTrue(result)
        
        # 验证更新
        updated_config = self.manager.get_data_source_config("xtdata")
        self.assertEqual(updated_config.name, "XtData_Updated")
        self.assertFalse(updated_config.enabled)


class TestConfigValidator(unittest.TestCase):
    """测试配置验证器"""
    
    def setUp(self):
        """设置测试环境"""
        self.validator = ConfigValidator()
    
    def test_validate_valid_config(self):
        """测试验证有效配置"""
        valid_config = {
            "data_sources": {
                "xtdata": {
                    "name": "XtData",
                    "enabled": True,
                    "timeout": 30,
                    "retry_times": 3,
                    "config": {
                        "ip": "127.0.0.1",
                        "port": 58610
                    }
                }
            }
        }
        
        is_valid, errors = self.validator.validate(valid_config)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_invalid_config(self):
        """测试验证无效配置"""
        invalid_configs = [
            {},  # 空配置
            {"data_sources": {}},  # 空数据源
            {"data_sources": {"test": {}}},  # 缺少必要字段
            {"data_sources": {"test": {"name": ""}}},  # 空名称
        ]
        
        for config in invalid_configs:
            is_valid, errors = self.validator.validate(config)
            self.assertFalse(is_valid)
            self.assertGreater(len(errors), 0)


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestDataSourceConfig,
        TestMarketData,
        TestSectorInfo,
        TestXtDataAdapter,
        TestDataSourceManager,
        TestConnectionPool,
        TestDataFormatter,
        TestDataSourceConfigManager,
        TestConfigValidator
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果统计
    print(f"\n测试统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}") 