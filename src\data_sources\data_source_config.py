#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源配置管理器
提供配置验证、模板管理、热重载、加密等功能
"""

import os
import json
import yaml
import hashlib
import base64
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path
import threading
import time
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .base import DataSourceConfig, DataSourceException
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConfigValidationRule:
    """配置验证规则"""
    field_name: str
    required: bool = False
    data_type: type = str
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None
    custom_validator: Optional[callable] = None
    error_message: str = ""


@dataclass
class ConfigTemplate:
    """配置模板"""
    name: str
    description: str
    template_data: Dict[str, Any]
    version: str = "1.0"
    created_time: datetime = field(default_factory=datetime.now)
    updated_time: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.rules: Dict[str, List[ConfigValidationRule]] = {}
        self._register_default_rules()
    
    def _register_default_rules(self):
        """注册默认验证规则"""
        # 数据源基础配置规则
        self.add_rule('data_sources', ConfigValidationRule(
            field_name='name',
            required=True,
            data_type=str,
            error_message='数据源名称不能为空'
        ))
        
        self.add_rule('data_sources', ConfigValidationRule(
            field_name='enabled',
            required=False,
            data_type=bool,
            error_message='enabled字段必须是布尔值'
        ))
        
        self.add_rule('data_sources', ConfigValidationRule(
            field_name='timeout',
            required=False,
            data_type=int,
            min_value=1,
            max_value=300,
            error_message='timeout必须在1-300秒之间'
        ))
        
        self.add_rule('data_sources', ConfigValidationRule(
            field_name='retry_times',
            required=False,
            data_type=int,
            min_value=0,
            max_value=10,
            error_message='retry_times必须在0-10之间'
        ))
        
        # XtData特定配置规则
        self.add_rule('xtdata', ConfigValidationRule(
            field_name='ip',
            required=False,
            data_type=str,
            pattern=r'^(\d{1,3}\.){3}\d{1,3}$',
            error_message='IP地址格式不正确'
        ))
        
        self.add_rule('xtdata', ConfigValidationRule(
            field_name='port',
            required=False,
            data_type=int,
            min_value=1,
            max_value=65535,
            error_message='端口号必须在1-65535之间'
        ))
    
    def add_rule(self, config_section: str, rule: ConfigValidationRule):
        """添加验证规则"""
        if config_section not in self.rules:
            self.rules[config_section] = []
        self.rules[config_section].append(rule)
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        验证配置
        
        Args:
            config: 配置字典
            
        Returns:
            List[str]: 错误信息列表，空列表表示验证通过
        """
        errors = []
        
        for section_name, section_config in config.items():
            if section_name not in self.rules:
                continue
            
            section_errors = self._validate_section(section_name, section_config)
            errors.extend(section_errors)
        
        return errors
    
    def _validate_section(self, section_name: str, section_config: Any) -> List[str]:
        """验证配置节"""
        errors = []
        rules = self.rules.get(section_name, [])
        
        # 如果是列表，验证每个元素
        if isinstance(section_config, list):
            for i, item in enumerate(section_config):
                item_errors = self._validate_item(rules, item, f"{section_name}[{i}]")
                errors.extend(item_errors)
        else:
            item_errors = self._validate_item(rules, section_config, section_name)
            errors.extend(item_errors)
        
        return errors
    
    def _validate_item(self, rules: List[ConfigValidationRule], item: Any, path: str) -> List[str]:
        """验证单个配置项"""
        errors = []
        
        if not isinstance(item, dict):
            return errors
        
        for rule in rules:
            field_value = item.get(rule.field_name)
            
            # 检查必填字段
            if rule.required and (field_value is None or field_value == ''):
                errors.append(f"{path}.{rule.field_name}: {rule.error_message or '必填字段不能为空'}")
                continue
            
            # 如果字段不存在且非必填，跳过验证
            if field_value is None:
                continue
            
            # 检查数据类型
            if not isinstance(field_value, rule.data_type):
                errors.append(f"{path}.{rule.field_name}: 数据类型错误，期望 {rule.data_type.__name__}")
                continue
            
            # 检查数值范围
            if rule.data_type in (int, float):
                if rule.min_value is not None and field_value < rule.min_value:
                    errors.append(f"{path}.{rule.field_name}: 值不能小于 {rule.min_value}")
                
                if rule.max_value is not None and field_value > rule.max_value:
                    errors.append(f"{path}.{rule.field_name}: 值不能大于 {rule.max_value}")
            
            # 检查允许的值
            if rule.allowed_values and field_value not in rule.allowed_values:
                errors.append(f"{path}.{rule.field_name}: 值必须是 {rule.allowed_values} 中的一个")
            
            # 检查正则表达式
            if rule.pattern and isinstance(field_value, str):
                import re
                if not re.match(rule.pattern, field_value):
                    errors.append(f"{path}.{rule.field_name}: {rule.error_message or '格式不正确'}")
            
            # 自定义验证器
            if rule.custom_validator:
                try:
                    if not rule.custom_validator(field_value):
                        errors.append(f"{path}.{rule.field_name}: {rule.error_message or '自定义验证失败'}")
                except Exception as e:
                    errors.append(f"{path}.{rule.field_name}: 自定义验证异常: {e}")
        
        return errors


class ConfigEncryption:
    """配置加密器"""
    
    def __init__(self, password: str = None):
        """
        初始化加密器
        
        Args:
            password: 加密密码，如果为空则使用默认密码
        """
        if password is None:
            password = os.getenv('XDQR_CONFIG_PASSWORD', 'default_password_change_me')
        
        self.password = password.encode()
        self._fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """创建Fernet加密器"""
        # 使用PBKDF2生成密钥
        salt = b'xdqr_config_salt'  # 在生产环境中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """加密字符串"""
        encrypted = self._fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密字符串"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted = self._fernet.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def encrypt_config_values(self, config: Dict[str, Any], sensitive_keys: List[str]) -> Dict[str, Any]:
        """加密配置中的敏感值"""
        encrypted_config = {}
        
        for key, value in config.items():
            if key in sensitive_keys and isinstance(value, str):
                encrypted_config[key] = self.encrypt(value)
                encrypted_config[f"{key}_encrypted"] = True
            elif isinstance(value, dict):
                encrypted_config[key] = self.encrypt_config_values(value, sensitive_keys)
            else:
                encrypted_config[key] = value
        
        return encrypted_config
    
    def decrypt_config_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """解密配置中的敏感值"""
        decrypted_config = {}
        
        for key, value in config.items():
            if key.endswith('_encrypted'):
                continue
            
            if f"{key}_encrypted" in config and config[f"{key}_encrypted"]:
                decrypted_config[key] = self.decrypt(value)
            elif isinstance(value, dict):
                decrypted_config[key] = self.decrypt_config_values(value)
            else:
                decrypted_config[key] = value
        
        return decrypted_config


class DataSourceConfigManager:
    """数据源配置管理器"""
    
    def __init__(self, 
                 config_dir: str = "config",
                 enable_encryption: bool = False,
                 enable_hot_reload: bool = True,
                 hot_reload_interval: int = 5):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
            enable_encryption: 是否启用加密
            enable_hot_reload: 是否启用热重载
            hot_reload_interval: 热重载检查间隔（秒）
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.enable_encryption = enable_encryption
        self.enable_hot_reload = enable_hot_reload
        self.hot_reload_interval = hot_reload_interval
        
        # 配置存储
        self._configs: Dict[str, Dict[str, Any]] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._lock = threading.RLock()
        
        # 组件
        self.validator = ConfigValidator()
        self.encryptor = ConfigEncryption() if enable_encryption else None
        self.templates: Dict[str, ConfigTemplate] = {}
        
        # 热重载线程
        self._hot_reload_thread = None
        self._stop_hot_reload = False
        
        # 加载默认模板
        self._load_default_templates()
        
        # 启动热重载
        if enable_hot_reload:
            self._start_hot_reload()
        
        logger.info(f"配置管理器初始化完成: {config_dir}, 加密: {enable_encryption}, 热重载: {enable_hot_reload}")
    
    def load_config(self, config_name: str, validate: bool = True) -> Dict[str, Any]:
        """
        加载配置
        
        Args:
            config_name: 配置名称
            validate: 是否验证配置
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        try:
            with self._lock:
                # 检查内存缓存
                if config_name in self._configs:
                    return self._configs[config_name].copy()
                
                # 从文件加载
                config_file = self.config_dir / f"{config_name}.yaml"
                if not config_file.exists():
                    config_file = self.config_dir / f"{config_name}.yml"
                
                if not config_file.exists():
                    config_file = self.config_dir / f"{config_name}.json"
                
                if not config_file.exists():
                    raise FileNotFoundError(f"配置文件不存在: {config_name}")
                
                # 读取配置文件
                config_data = self._read_config_file(config_file)
                
                # 解密敏感信息
                if self.enable_encryption and self.encryptor:
                    config_data = self.encryptor.decrypt_config_values(config_data)
                
                # 验证配置
                if validate:
                    errors = self.validator.validate_config(config_data)
                    if errors:
                        raise DataSourceException(f"配置验证失败: {'; '.join(errors)}")
                
                # 缓存配置
                self._configs[config_name] = config_data
                self._file_timestamps[config_name] = config_file.stat().st_mtime
                
                logger.debug(f"配置加载成功: {config_name}")
                return config_data.copy()
                
        except Exception as e:
            logger.error(f"加载配置失败: {config_name}, 错误: {e}")
            raise DataSourceException(f"加载配置失败: {e}") from e
    
    def save_config(self, 
                   config_name: str, 
                   config_data: Dict[str, Any],
                   encrypt_sensitive: bool = None,
                   validate: bool = True) -> bool:
        """
        保存配置
        
        Args:
            config_name: 配置名称
            config_data: 配置数据
            encrypt_sensitive: 是否加密敏感信息
            validate: 是否验证配置
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            if validate:
                errors = self.validator.validate_config(config_data)
                if errors:
                    raise DataSourceException(f"配置验证失败: {'; '.join(errors)}")
            
            # 加密敏感信息
            data_to_save = config_data.copy()
            if encrypt_sensitive is None:
                encrypt_sensitive = self.enable_encryption
            
            if encrypt_sensitive and self.encryptor:
                sensitive_keys = ['password', 'token', 'secret', 'key']
                data_to_save = self.encryptor.encrypt_config_values(data_to_save, sensitive_keys)
            
            # 保存到文件
            config_file = self.config_dir / f"{config_name}.yaml"
            self._write_config_file(config_file, data_to_save)
            
            # 更新缓存
            with self._lock:
                self._configs[config_name] = config_data
                self._file_timestamps[config_name] = config_file.stat().st_mtime
            
            logger.info(f"配置保存成功: {config_name}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {config_name}, 错误: {e}")
            return False
    
    def create_data_source_config(self, 
                                 name: str,
                                 source_type: str,
                                 **kwargs) -> DataSourceConfig:
        """
        创建数据源配置
        
        Args:
            name: 数据源名称
            source_type: 数据源类型
            **kwargs: 其他配置参数
            
        Returns:
            DataSourceConfig: 数据源配置对象
        """
        config_dict = {
            'name': name,
            'enabled': kwargs.get('enabled', True),
            'timeout': kwargs.get('timeout', 30),
            'retry_times': kwargs.get('retry_times', 3),
            'auto_reconnect': kwargs.get('auto_reconnect', True),
            'config': kwargs.get('config', {})
        }
        
        # 添加特定数据源的配置
        if source_type == 'xtdata':
            config_dict['config'].update({
                'ip': kwargs.get('ip', '127.0.0.1'),
                'port': kwargs.get('port', 58610)
            })
        
        return DataSourceConfig(**config_dict)
    
    def get_template(self, template_name: str) -> Optional[ConfigTemplate]:
        """获取配置模板"""
        return self.templates.get(template_name)
    
    def create_template(self, 
                       name: str,
                       description: str,
                       template_data: Dict[str, Any],
                       tags: List[str] = None) -> ConfigTemplate:
        """
        创建配置模板
        
        Args:
            name: 模板名称
            description: 模板描述
            template_data: 模板数据
            tags: 标签列表
            
        Returns:
            ConfigTemplate: 配置模板
        """
        template = ConfigTemplate(
            name=name,
            description=description,
            template_data=template_data,
            tags=tags or []
        )
        
        self.templates[name] = template
        
        # 保存模板到文件
        template_file = self.config_dir / "templates" / f"{name}.yaml"
        template_file.parent.mkdir(exist_ok=True)
        
        template_dict = asdict(template)
        self._write_config_file(template_file, template_dict)
        
        logger.info(f"配置模板创建成功: {name}")
        return template
    
    def apply_template(self, 
                      template_name: str,
                      config_name: str,
                      overrides: Dict[str, Any] = None) -> bool:
        """
        应用配置模板
        
        Args:
            template_name: 模板名称
            config_name: 配置名称
            overrides: 覆盖参数
            
        Returns:
            bool: 应用是否成功
        """
        try:
            template = self.get_template(template_name)
            if not template:
                raise ValueError(f"模板不存在: {template_name}")
            
            # 复制模板数据
            config_data = template.template_data.copy()
            
            # 应用覆盖参数
            if overrides:
                config_data = self._deep_update(config_data, overrides)
            
            # 保存配置
            return self.save_config(config_name, config_data)
            
        except Exception as e:
            logger.error(f"应用模板失败: {template_name} -> {config_name}, 错误: {e}")
            return False
    
    def export_config(self, 
                     config_name: str,
                     export_path: str,
                     format: str = 'yaml',
                     include_sensitive: bool = False) -> bool:
        """
        导出配置
        
        Args:
            config_name: 配置名称
            export_path: 导出路径
            format: 导出格式 ('yaml' 或 'json')
            include_sensitive: 是否包含敏感信息
            
        Returns:
            bool: 导出是否成功
        """
        try:
            config_data = self.load_config(config_name, validate=False)
            
            # 移除敏感信息
            if not include_sensitive:
                config_data = self._remove_sensitive_data(config_data)
            
            export_file = Path(export_path)
            export_file.parent.mkdir(parents=True, exist_ok=True)
            
            if format.lower() == 'json':
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            else:  # yaml
                with open(export_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置导出成功: {config_name} -> {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置失败: {config_name}, 错误: {e}")
            return False
    
    def import_config(self, 
                     import_path: str,
                     config_name: str = None,
                     validate: bool = True) -> bool:
        """
        导入配置
        
        Args:
            import_path: 导入路径
            config_name: 配置名称，如果为空则使用文件名
            validate: 是否验证配置
            
        Returns:
            bool: 导入是否成功
        """
        try:
            import_file = Path(import_path)
            if not import_file.exists():
                raise FileNotFoundError(f"导入文件不存在: {import_path}")
            
            if config_name is None:
                config_name = import_file.stem
            
            # 读取配置文件
            config_data = self._read_config_file(import_file)
            
            # 保存配置
            return self.save_config(config_name, config_data, validate=validate)
            
        except Exception as e:
            logger.error(f"导入配置失败: {import_path}, 错误: {e}")
            return False
    
    def _read_config_file(self, file_path: Path) -> Dict[str, Any]:
        """读取配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            elif file_path.suffix.lower() == '.json':
                return json.load(f) or {}
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
    
    def _write_config_file(self, file_path: Path, data: Dict[str, Any]) -> None:
        """写入配置文件"""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            elif file_path.suffix.lower() == '.json':
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
    
    def _load_default_templates(self) -> None:
        """加载默认模板"""
        # XtData模板
        xtdata_template = {
            'data_sources': [
                {
                    'name': 'xtdata_primary',
                    'enabled': True,
                    'timeout': 30,
                    'retry_times': 3,
                    'auto_reconnect': True,
                    'config': {
                        'ip': '127.0.0.1',
                        'port': 58610
                    }
                }
            ]
        }
        
        self.create_template(
            name='xtdata_default',
            description='XtData默认配置模板',
            template_data=xtdata_template,
            tags=['xtdata', 'default']
        )
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Dict[str, Any]:
        """深度更新字典"""
        result = base_dict.copy()
        
        for key, value in update_dict.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_update(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _remove_sensitive_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """移除敏感数据"""
        sensitive_keys = ['password', 'token', 'secret', 'key']
        cleaned_config = {}
        
        for key, value in config.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                cleaned_config[key] = '***'
            elif isinstance(value, dict):
                cleaned_config[key] = self._remove_sensitive_data(value)
            else:
                cleaned_config[key] = value
        
        return cleaned_config
    
    def _start_hot_reload(self) -> None:
        """启动热重载线程"""
        if self._hot_reload_thread and self._hot_reload_thread.is_alive():
            return
        
        self._stop_hot_reload = False
        self._hot_reload_thread = threading.Thread(
            target=self._hot_reload_worker,
            daemon=True,
            name="ConfigHotReload"
        )
        self._hot_reload_thread.start()
        logger.info("配置热重载线程已启动")
    
    def _hot_reload_worker(self) -> None:
        """热重载工作线程"""
        while not self._stop_hot_reload:
            try:
                self._check_file_changes()
                time.sleep(self.hot_reload_interval)
            except Exception as e:
                logger.error(f"热重载线程异常: {e}")
                time.sleep(10)
    
    def _check_file_changes(self) -> None:
        """检查文件变更"""
        with self._lock:
            for config_name, cached_timestamp in list(self._file_timestamps.items()):
                config_file = self.config_dir / f"{config_name}.yaml"
                if not config_file.exists():
                    config_file = self.config_dir / f"{config_name}.yml"
                if not config_file.exists():
                    config_file = self.config_dir / f"{config_name}.json"
                
                if config_file.exists():
                    current_timestamp = config_file.stat().st_mtime
                    if current_timestamp > cached_timestamp:
                        logger.info(f"检测到配置文件变更，重新加载: {config_name}")
                        try:
                            # 重新加载配置
                            del self._configs[config_name]
                            self.load_config(config_name, validate=True)
                        except Exception as e:
                            logger.error(f"重新加载配置失败: {config_name}, 错误: {e}")
    
    def stop_hot_reload(self) -> None:
        """停止热重载"""
        self._stop_hot_reload = True
        if self._hot_reload_thread:
            self._hot_reload_thread.join(timeout=5)
        logger.info("配置热重载线程已停止")
    
    def shutdown(self) -> None:
        """关闭配置管理器"""
        logger.info("开始关闭配置管理器")
        self.stop_hot_reload()
        
        with self._lock:
            self._configs.clear()
            self._file_timestamps.clear()
        
        logger.info("配置管理器已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()