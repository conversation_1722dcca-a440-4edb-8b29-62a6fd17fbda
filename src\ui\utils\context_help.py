"""
上下文相关帮助系统

根据用户当前操作的界面区域提供相关的帮助信息
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QFrame, QScrollArea, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QFont, QPixmap, QIcon, QKeySequence
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ContextHelpDialog(QDialog):
    """上下文帮助对话框"""
    
    def __init__(self, context: str, parent: Optional[QWidget] = None):
        """
        初始化上下文帮助对话框
        
        Args:
            context: 上下文标识
            parent: 父控件
        """
        super().__init__(parent)
        self.context = context
        self.help_content = self._get_help_content(context)
        
        self._init_ui()
        self._apply_styles()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"帮助 - {self.help_content.get('title', '未知')}")
        self.setFixedSize(600, 450)
        self.setModal(False)  # 非模态对话框，允许用户继续操作
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel(self.help_content.get('title', '帮助'))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2196F3;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 标签页控件
        tab_widget = QTabWidget()
        
        # 概述标签页
        overview_tab = self._create_overview_tab()
        tab_widget.addTab(overview_tab, "概述")
        
        # 操作步骤标签页
        steps_tab = self._create_steps_tab()
        tab_widget.addTab(steps_tab, "操作步骤")
        
        # 常见问题标签页
        faq_tab = self._create_faq_tab()
        tab_widget.addTab(faq_tab, "常见问题")
        
        # 快捷键标签页
        shortcuts_tab = self._create_shortcuts_tab()
        tab_widget.addTab(shortcuts_tab, "快捷键")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #ccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        
    def _create_overview_tab(self) -> QWidget:
        """创建概述标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setPlainText(self.help_content.get('overview', '暂无概述信息'))
        content.setStyleSheet("""
            QTextEdit {
                border: none;
                font-size: 14px;
                line-height: 1.6;
                padding: 10px;
            }
        """)
        
        layout.addWidget(content)
        return widget
        
    def _create_steps_tab(self) -> QWidget:
        """创建操作步骤标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        
        steps = self.help_content.get('steps', [])
        if steps:
            steps_text = ""
            for i, step in enumerate(steps, 1):
                steps_text += f"{i}. {step}\n\n"
            content.setPlainText(steps_text)
        else:
            content.setPlainText("暂无操作步骤信息")
            
        content.setStyleSheet("""
            QTextEdit {
                border: none;
                font-size: 14px;
                line-height: 1.6;
                padding: 10px;
            }
        """)
        
        layout.addWidget(content)
        return widget
        
    def _create_faq_tab(self) -> QWidget:
        """创建常见问题标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        
        faq = self.help_content.get('faq', [])
        if faq:
            faq_text = ""
            for item in faq:
                faq_text += f"Q: {item.get('question', '')}\n"
                faq_text += f"A: {item.get('answer', '')}\n\n"
            content.setPlainText(faq_text)
        else:
            content.setPlainText("暂无常见问题信息")
            
        content.setStyleSheet("""
            QTextEdit {
                border: none;
                font-size: 14px;
                line-height: 1.6;
                padding: 10px;
            }
        """)
        
        layout.addWidget(content)
        return widget
        
    def _create_shortcuts_tab(self) -> QWidget:
        """创建快捷键标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        
        shortcuts = self.help_content.get('shortcuts', [])
        if shortcuts:
            shortcuts_text = ""
            for shortcut in shortcuts:
                shortcuts_text += f"{shortcut.get('key', '')}: {shortcut.get('description', '')}\n"
            content.setPlainText(shortcuts_text)
        else:
            content.setPlainText("暂无快捷键信息")
            
        content.setStyleSheet("""
            QTextEdit {
                border: none;
                font-size: 14px;
                line-height: 1.6;
                padding: 10px;
            }
        """)
        
        layout.addWidget(content)
        return widget
        
    def _get_help_content(self, context: str) -> Dict[str, Any]:
        """获取帮助内容"""
        return CONTEXT_HELP_CONTENT.get(context, {
            'title': '帮助',
            'overview': '暂无帮助信息',
            'steps': [],
            'faq': [],
            'shortcuts': []
        })


class ContextHelpManager:
    """上下文帮助管理器"""
    
    def __init__(self, main_window):
        """
        初始化上下文帮助管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.active_help_dialogs: Dict[str, ContextHelpDialog] = {}
        
        logger.info("上下文帮助管理器初始化完成")
        
    def show_context_help(self, context: str):
        """
        显示上下文帮助
        
        Args:
            context: 上下文标识
        """
        try:
            if context not in self.active_help_dialogs:
                dialog = ContextHelpDialog(context, self.main_window)
                dialog.finished.connect(lambda: self._on_help_dialog_closed(context))
                self.active_help_dialogs[context] = dialog
                
            self.active_help_dialogs[context].show()
            self.active_help_dialogs[context].raise_()
            self.active_help_dialogs[context].activateWindow()
            
            logger.info(f"显示上下文帮助: {context}")
            
        except Exception as e:
            logger.error(f"显示上下文帮助失败: {e}")
            
    def _on_help_dialog_closed(self, context: str):
        """帮助对话框关闭处理"""
        if context in self.active_help_dialogs:
            del self.active_help_dialogs[context]
            
    def get_current_context(self) -> str:
        """获取当前上下文"""
        # 根据当前活动的标签页确定上下文
        try:
            current_tab = self.main_window.tab_widget.currentIndex()
            tab_contexts = {
                0: 'stock_data',
                1: 'analysis_results', 
                2: 'chart_analysis',
                3: 'selection_config',
                4: 'system_management'
            }
            return tab_contexts.get(current_tab, 'main_window')
        except:
            return 'main_window'


# 上下文帮助内容定义
CONTEXT_HELP_CONTENT = {
    'main_window': {
        'title': '主界面帮助',
        'overview': '''威科夫相对强弱选股系统主界面包含以下主要区域：

• 菜单栏：提供文件操作、分析工具、帮助等功能
• 工具栏：快速访问常用功能的按钮
• 标签页区域：不同功能模块的切换
• 状态栏：显示系统状态和操作提示

系统采用标签页设计，每个标签页对应一个功能模块，您可以通过点击标签页或使用快捷键在不同功能间切换。''',
        'steps': [
            '启动系统后，首先在"股票数据"页面刷新数据',
            '选择要分析的股票',
            '使用工具栏按钮或菜单执行分析功能',
            '在相应的标签页查看分析结果',
            '根据需要配置选股参数并执行智能选股'
        ],
        'faq': [
            {
                'question': '如何开始使用系统？',
                'answer': '建议先查看使用向导（帮助→使用向导），然后从刷新股票数据开始。'
            },
            {
                'question': '系统运行缓慢怎么办？',
                'answer': '检查数据源连接状态，确保网络正常，必要时重启系统。'
            }
        ],
        'shortcuts': [
            {'key': 'F5', 'description': '刷新数据'},
            {'key': 'Ctrl+W', 'description': '威科夫分析'},
            {'key': 'Ctrl+R', 'description': '相对强弱分析'},
            {'key': 'Ctrl+S', 'description': '智能选股'},
            {'key': 'Ctrl+G', 'description': '显示使用向导'},
            {'key': 'F1', 'description': '显示帮助'}
        ]
    },
    'stock_data': {
        'title': '股票数据帮助',
        'overview': '''股票数据页面是系统的起点，提供股票列表查看、搜索、筛选等功能。

主要功能：
• 显示所有可用股票的基本信息
• 支持按股票代码或名称搜索
• 提供市场分类筛选（沪市/深市）
• 刷新获取最新股票数据
• 选择股票进行后续分析''',
        'steps': [
            '点击"刷新"按钮获取最新股票数据',
            '使用搜索框快速查找目标股票',
            '通过市场筛选器缩小选择范围',
            '单击股票行选中要分析的股票',
            '选中的股票会在其他页面显示分析结果'
        ],
        'faq': [
            {
                'question': '为什么股票列表是空的？',
                'answer': '请点击刷新按钮加载数据，确保数据源连接正常。'
            },
            {
                'question': '如何搜索特定股票？',
                'answer': '在搜索框中输入股票代码或名称的部分内容即可筛选。'
            }
        ],
        'shortcuts': [
            {'key': 'F5', 'description': '刷新股票数据'},
            {'key': 'Ctrl+F', 'description': '聚焦搜索框'},
            {'key': '↑↓', 'description': '上下选择股票'}
        ]
    },
    'wyckoff_analysis': {
        'title': '威科夫分析帮助',
        'overview': '''威科夫分析基于理查德·威科夫的市场理论，通过分析价格和成交量的关系来识别市场的累积和派发阶段。

分析要素：
• 市场阶段识别（累积、上涨、派发、下跌）
• 供需关系分析
• 支撑阻力位确定
• 成交量确认信号''',
        'steps': [
            '在股票数据页面选择要分析的股票',
            '点击工具栏的"威科夫"按钮或使用Ctrl+W',
            '系统自动分析股票的威科夫结构',
            '在分析结果页面查看详细分析报告',
            '结合图表页面的可视化结果进行判断'
        ],
        'faq': [
            {
                'question': '威科夫分析适用于所有股票吗？',
                'answer': '威科夫分析更适用于流动性好、成交活跃的股票。'
            },
            {
                'question': '如何理解累积和派发阶段？',
                'answer': '累积期是主力吸筹阶段，派发期是主力出货阶段，可参考功能向导了解详情。'
            }
        ],
        'shortcuts': [
            {'key': 'Ctrl+W', 'description': '执行威科夫分析'},
            {'key': 'F1', 'description': '威科夫分析向导'}
        ]
    },
    'analysis_results': {
        'title': '分析结果帮助',
        'overview': '''分析结果页面显示威科夫分析和相对强弱分析的详细结果。

主要内容：
• 威科夫市场阶段判断和置信度
• 关键支撑阻力位标识
• 相对强弱值和排名
• 供需关系评估
• 投资建议和风险提示''',
        'steps': [
            '确保已选择股票并执行了分析',
            '查看威科夫分析结果部分',
            '关注市场阶段和置信度指标',
            '查看相对强弱分析结果',
            '结合投资建议制定操作策略'
        ],
        'faq': [
            {
                'question': '置信度是什么意思？',
                'answer': '置信度表示分析结果的可靠程度，数值越高表示结果越可信。'
            },
            {
                'question': '如何理解相对强弱值？',
                'answer': 'RS值大于1表示强于大盘，小于1表示弱于大盘，数值越大表示相对越强势。'
            }
        ],
        'shortcuts': [
            {'key': 'Ctrl+W', 'description': '重新执行威科夫分析'},
            {'key': 'Ctrl+R', 'description': '重新执行相对强弱分析'}
        ]
    },
    'chart_analysis': {
        'title': '图表分析帮助',
        'overview': '''图表分析页面提供股票价格和成交量的可视化分析工具。

功能特性：
• K线图显示价格走势
• 成交量柱状图
• 威科夫关键位标注
• 技术指标叠加
• 多时间周期切换''',
        'steps': [
            '选择要查看的股票',
            '在图表分析页面查看K线图',
            '观察价格和成交量的配合关系',
            '查看威科夫关键位标注',
            '切换不同时间周期进行分析'
        ],
        'faq': [
            {
                'question': '如何切换时间周期？',
                'answer': '使用图表上方的时间周期按钮，支持日线、周线、月线等。'
            },
            {
                'question': '图表上的标注是什么意思？',
                'answer': '标注显示威科夫分析识别的关键支撑阻力位和重要信号点。'
            }
        ],
        'shortcuts': [
            {'key': '←→', 'description': '左右移动图表'},
            {'key': '↑↓', 'description': '放大缩小图表'}
        ]
    },
    'selection_config': {
        'title': '选股配置帮助',
        'overview': '''选股配置页面允许您设置智能选股的各项参数和策略。

配置项目：
• 威科夫策略参数
• 相对强弱筛选条件
• 风险控制设置
• 选股数量限制
• 评分权重分配''',
        'steps': [
            '设置威科夫策略偏好（累积期/上涨期）',
            '配置相对强弱筛选条件',
            '设置风险控制参数',
            '调整评分权重分配',
            '保存配置并执行选股'
        ],
        'faq': [
            {
                'question': '如何设置合适的选股参数？',
                'answer': '建议从宽松条件开始，根据选股结果逐步调整参数。'
            },
            {
                'question': '评分权重如何分配？',
                'answer': '根据您的投资偏好调整，如更重视威科夫分析则提高其权重。'
            }
        ],
        'shortcuts': [
            {'key': 'Ctrl+S', 'description': '执行智能选股'},
            {'key': 'Ctrl+Shift+S', 'description': '保存当前配置'}
        ]
    },
    'system_management': {
        'title': '系统管理帮助',
        'overview': '''系统管理页面提供数据源配置、系统设置、日志查看等管理功能。

管理功能：
• 数据源连接配置和测试
• 系统参数设置
• 日志查看和导出
• 性能监控
• 帮助和关于信息''',
        'steps': [
            '配置数据源连接参数',
            '测试数据源连接状态',
            '调整系统设置参数',
            '查看系统运行日志',
            '监控系统性能状态'
        ],
        'faq': [
            {
                'question': '数据源连接失败怎么办？',
                'answer': '检查网络连接，确认数据源软件正在运行，参考连接配置指南。'
            },
            {
                'question': '如何查看系统日志？',
                'answer': '在系统管理页面的日志查看部分，可以按级别和时间筛选日志。'
            }
        ],
        'shortcuts': [
            {'key': 'Ctrl+,', 'description': '打开系统设置'},
            {'key': 'F12', 'description': '打开开发者工具'}
        ]
    }
}
