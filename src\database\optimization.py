"""
威科夫相对强弱选股系统 - 数据库优化工具

提供批量数据插入优化、索引管理、性能监控等功能
"""

import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Iterator
from contextlib import contextmanager
import pandas as pd

from ..utils.logger import get_logger
from .manager import DatabaseManager

logger = get_logger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time: Optional[float] = None
        self.operation_name: str = ""
        self.records_processed: int = 0
    
    def start(self, operation_name: str) -> None:
        """开始监控"""
        self.operation_name = operation_name
        self.start_time = time.time()
        self.records_processed = 0
    
    def add_records(self, count: int) -> None:
        """添加处理记录数"""
        self.records_processed += count
    
    def finish(self) -> Dict[str, Any]:
        """结束监控并返回统计信息"""
        if self.start_time is None:
            return {}
        
        duration = time.time() - self.start_time
        
        return {
            'operation_name': self.operation_name,
            'duration_seconds': duration,
            'records_processed': self.records_processed,
            'records_per_second': self.records_processed / duration if duration > 0 else 0
        }


class DatabaseOptimizer:
    """
    数据库优化器
    
    提供批量数据操作优化、索引管理、性能监控等功能
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化数据库优化器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.performance_monitor = PerformanceMonitor()
        self._batch_size = 1000
        self._lock = threading.RLock()
        
        logger.info("数据库优化器初始化完成")
    
    def set_batch_size(self, batch_size: int) -> None:
        """
        设置批量操作大小
        
        Args:
            batch_size: 批量大小
        """
        if batch_size > 0:
            self._batch_size = batch_size
            logger.info(f"批量操作大小设置为: {batch_size}")
    
    @contextmanager
    def bulk_insert_mode(self):
        """
        批量插入模式上下文管理器
        
        在此模式下优化数据库设置以提高插入性能
        """
        original_settings = {}
        
        try:
            with self.db_manager.get_connection() as conn:
                # 保存原始设置
                cursor = conn.execute("PRAGMA synchronous")
                original_settings['synchronous'] = cursor.fetchone()[0]
                
                cursor = conn.execute("PRAGMA journal_mode")
                original_settings['journal_mode'] = cursor.fetchone()[0]
                
                cursor = conn.execute("PRAGMA cache_size")
                original_settings['cache_size'] = cursor.fetchone()[0]
                
                # 设置批量插入优化参数
                conn.execute("PRAGMA synchronous = OFF")  # 关闭同步写入
                conn.execute("PRAGMA journal_mode = MEMORY")  # 使用内存日志
                conn.execute("PRAGMA cache_size = 50000")  # 增大缓存
                conn.execute("PRAGMA temp_store = MEMORY")  # 临时表存储在内存
                conn.execute("PRAGMA locking_mode = EXCLUSIVE")  # 独占锁模式
                
                logger.debug("批量插入模式已启用")
                yield
                
        finally:
            # 恢复原始设置
            try:
                with self.db_manager.get_connection() as conn:
                    conn.execute(f"PRAGMA synchronous = {original_settings.get('synchronous', 'NORMAL')}")
                    conn.execute(f"PRAGMA journal_mode = {original_settings.get('journal_mode', 'WAL')}")
                    conn.execute(f"PRAGMA cache_size = {original_settings.get('cache_size', 10000)}")
                    conn.execute("PRAGMA locking_mode = NORMAL")
                    
                logger.debug("批量插入模式已关闭，设置已恢复")
            except Exception as e:
                logger.error(f"恢复数据库设置失败: {e}")
    
    def bulk_insert_stock_quotes(self, quotes_data: List[Dict[str, Any]]) -> int:
        """
        批量插入个股行情数据（优化版本）
        
        Args:
            quotes_data: 行情数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        if not quotes_data:
            return 0
        
        self.performance_monitor.start(f"bulk_insert_stock_quotes({len(quotes_data)} records)")
        
        try:
            with self.bulk_insert_mode():
                total_inserted = 0
                
                # 分批插入
                for batch in self._batch_iterator(quotes_data, self._batch_size):
                    batch_inserted = self._insert_stock_quotes_batch(batch)
                    total_inserted += batch_inserted
                    self.performance_monitor.add_records(batch_inserted)
                
                # 记录性能统计
                stats = self.performance_monitor.finish()
                logger.info(f"批量插入股票行情完成: {stats}")
                
                return total_inserted
                
        except Exception as e:
            logger.error(f"批量插入股票行情失败: {e}")
            return 0
    
    def bulk_insert_sector_quotes(self, quotes_data: List[Dict[str, Any]]) -> int:
        """
        批量插入板块行情数据（优化版本）
        
        Args:
            quotes_data: 行情数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        if not quotes_data:
            return 0
        
        self.performance_monitor.start(f"bulk_insert_sector_quotes({len(quotes_data)} records)")
        
        try:
            with self.bulk_insert_mode():
                total_inserted = 0
                
                # 分批插入
                for batch in self._batch_iterator(quotes_data, self._batch_size):
                    batch_inserted = self._insert_sector_quotes_batch(batch)
                    total_inserted += batch_inserted
                    self.performance_monitor.add_records(batch_inserted)
                
                # 记录性能统计
                stats = self.performance_monitor.finish()
                logger.info(f"批量插入板块行情完成: {stats}")
                
                return total_inserted
                
        except Exception as e:
            logger.error(f"批量插入板块行情失败: {e}")
            return 0
    
    def _insert_stock_quotes_batch(self, batch_data: List[Dict[str, Any]]) -> int:
        """插入单批股票行情数据"""
        try:
            sql = """
            INSERT OR REPLACE INTO stock_quotes 
            (stock_code, trade_date, open, high, low, close, volume, amount, 
             adj_factor, change_pct, turnover_rate, pe_ratio, pb_ratio)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for data in batch_data:
                params_list.append((
                    data.get('stock_code'),
                    data.get('trade_date'),
                    data.get('open'),
                    data.get('high'),
                    data.get('low'),
                    data.get('close'),
                    data.get('volume', 0),
                    data.get('amount', 0),
                    data.get('adj_factor', 1.0),
                    data.get('change_pct'),
                    data.get('turnover_rate'),
                    data.get('pe_ratio'),
                    data.get('pb_ratio')
                ))
            
            return self.db_manager.execute_many(sql, params_list)
            
        except Exception as e:
            logger.error(f"插入股票行情批次失败: {e}")
            return 0
    
    def _insert_sector_quotes_batch(self, batch_data: List[Dict[str, Any]]) -> int:
        """插入单批板块行情数据"""
        try:
            sql = """
            INSERT OR REPLACE INTO sector_quotes 
            (sector_code, trade_date, open, high, low, close, volume, amount, change_pct, turnover_rate)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params_list = []
            for data in batch_data:
                params_list.append((
                    data.get('sector_code'),
                    data.get('trade_date'),
                    data.get('open'),
                    data.get('high'),
                    data.get('low'),
                    data.get('close'),
                    data.get('volume', 0),
                    data.get('amount', 0),
                    data.get('change_pct'),
                    data.get('turnover_rate')
                ))
            
            return self.db_manager.execute_many(sql, params_list)
            
        except Exception as e:
            logger.error(f"插入板块行情批次失败: {e}")
            return 0
    
    def _batch_iterator(self, data: List[Any], batch_size: int) -> Iterator[List[Any]]:
        """
        批次迭代器
        
        Args:
            data: 数据列表
            batch_size: 批次大小
            
        Yields:
            List[Any]: 数据批次
        """
        for i in range(0, len(data), batch_size):
            yield data[i:i + batch_size]
    
    def bulk_upsert_from_dataframe(self, df: pd.DataFrame, table_name: str, 
                                  conflict_columns: List[str]) -> int:
        """
        从DataFrame批量插入或更新数据
        
        Args:
            df: 数据DataFrame
            table_name: 目标表名
            conflict_columns: 冲突检测列
            
        Returns:
            int: 影响的记录数
        """
        if df.empty:
            return 0
        
        self.performance_monitor.start(f"bulk_upsert_from_dataframe({len(df)} records to {table_name})")
        
        try:
            with self.bulk_insert_mode():
                # 生成UPSERT SQL
                columns = df.columns.tolist()
                placeholders = ', '.join(['?' for _ in columns])
                
                # 构建ON CONFLICT子句
                conflict_clause = ', '.join(conflict_columns)
                update_clause = ', '.join([f"{col} = excluded.{col}" for col in columns if col not in conflict_columns])
                
                sql = f"""
                INSERT INTO {table_name} ({', '.join(columns)})
                VALUES ({placeholders})
                ON CONFLICT ({conflict_clause})
                DO UPDATE SET {update_clause}
                """
                
                # 分批处理
                total_processed = 0
                
                for batch_df in self._dataframe_batch_iterator(df, self._batch_size):
                    # 转换为参数列表
                    params_list = [tuple(row) for row in batch_df.values]
                    
                    batch_affected = self.db_manager.execute_many(sql, params_list)
                    total_processed += batch_affected
                    self.performance_monitor.add_records(len(params_list))
                
                # 记录性能统计
                stats = self.performance_monitor.finish()
                logger.info(f"DataFrame批量操作完成: {stats}")
                
                return total_processed
                
        except Exception as e:
            logger.error(f"DataFrame批量操作失败: {e}")
            return 0
    
    def _dataframe_batch_iterator(self, df: pd.DataFrame, batch_size: int) -> Iterator[pd.DataFrame]:
        """
        DataFrame批次迭代器
        
        Args:
            df: 数据DataFrame
            batch_size: 批次大小
            
        Yields:
            pd.DataFrame: 数据批次
        """
        for i in range(0, len(df), batch_size):
            yield df.iloc[i:i + batch_size]
    
    def optimize_table_indexes(self, table_name: str) -> bool:
        """
        优化表索引
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 优化是否成功
        """
        try:
            index_definitions = {
                'stock_quotes': [
                    ('idx_stock_quotes_code_date_optimized', 'stock_code, trade_date DESC'),
                    ('idx_stock_quotes_date_volume', 'trade_date, volume DESC'),
                    ('idx_stock_quotes_close_range', 'close')
                ],
                'sector_quotes': [
                    ('idx_sector_quotes_code_date_optimized', 'sector_code, trade_date DESC'),
                    ('idx_sector_quotes_date_volume', 'trade_date, volume DESC'),
                    ('idx_sector_quotes_close_range', 'close')
                ],
                'relative_strength_results': [
                    ('idx_rs_results_comprehensive', 'symbol_type, start_date, end_date, relative_strength DESC'),
                    ('idx_rs_results_ranking', 'symbol_type, relative_strength DESC, rank_in_type')
                ]
            }
            
            if table_name not in index_definitions:
                logger.warning(f"表 {table_name} 没有预定义的索引优化方案")
                return False
            
            with self.db_manager.get_connection() as conn:
                for index_name, index_columns in index_definitions[table_name]:
                    try:
                        # 检查索引是否已存在
                        cursor = conn.execute(
                            "SELECT name FROM sqlite_master WHERE type='index' AND name=?",
                            (index_name,)
                        )
                        
                        if cursor.fetchone():
                            logger.debug(f"索引 {index_name} 已存在，跳过创建")
                            continue
                        
                        # 创建索引
                        sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({index_columns})"
                        conn.execute(sql)
                        
                        logger.info(f"创建索引成功: {index_name}")
                        
                    except Exception as e:
                        logger.error(f"创建索引 {index_name} 失败: {e}")
                
                conn.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"优化表索引失败: {e}")
            return False
    
    def analyze_table_statistics(self, table_name: str) -> Dict[str, Any]:
        """
        分析表统计信息
        
        Args:
            table_name: 表名
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {}
            
            with self.db_manager.get_connection() as conn:
                # 基本统计
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name}")
                stats['total_records'] = cursor.fetchone()[0]
                
                # 表大小信息
                cursor = conn.execute(
                    "SELECT SUM(pgsize) FROM dbstat WHERE name=?", (table_name,)
                )
                result = cursor.fetchone()
                stats['table_size_bytes'] = result[0] if result and result[0] else 0
                
                # 索引信息
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='index' AND tbl_name=?",
                    (table_name,)
                )
                stats['indexes'] = [row[0] for row in cursor.fetchall() if row[0]]
                
                # 特定表的详细统计
                if table_name in ['stock_quotes', 'sector_quotes']:
                    # 日期范围
                    cursor = conn.execute(f"SELECT MIN(trade_date), MAX(trade_date) FROM {table_name}")
                    date_range = cursor.fetchone()
                    stats['date_range'] = {
                        'start_date': date_range[0],
                        'end_date': date_range[1]
                    }
                    
                    # 代码数量
                    code_field = 'stock_code' if table_name == 'stock_quotes' else 'sector_code'
                    cursor = conn.execute(f"SELECT COUNT(DISTINCT {code_field}) FROM {table_name}")
                    stats['unique_symbols'] = cursor.fetchone()[0]
                
                elif table_name == 'relative_strength_results':
                    # 按类型统计
                    cursor = conn.execute("""
                        SELECT symbol_type, COUNT(*) 
                        FROM relative_strength_results 
                        GROUP BY symbol_type
                    """)
                    stats['by_type'] = dict(cursor.fetchall())
            
            return stats
            
        except Exception as e:
            logger.error(f"分析表统计信息失败: {e}")
            return {'error': str(e)}
    
    def vacuum_and_analyze(self) -> bool:
        """
        执行数据库清理和分析
        
        Returns:
            bool: 操作是否成功
        """
        try:
            self.performance_monitor.start("vacuum_and_analyze")
            
            with self.db_manager.get_connection() as conn:
                # 获取清理前的大小
                cursor = conn.execute("PRAGMA page_count")
                pages_before = cursor.fetchone()[0]
                
                cursor = conn.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                
                size_before = pages_before * page_size
                
                # 执行VACUUM
                logger.info("开始执行数据库VACUUM操作")
                conn.execute("VACUUM")
                
                # 执行ANALYZE
                logger.info("开始执行数据库ANALYZE操作")
                conn.execute("ANALYZE")
                
                # 获取清理后的大小
                cursor = conn.execute("PRAGMA page_count")
                pages_after = cursor.fetchone()[0]
                
                size_after = pages_after * page_size
                saved_bytes = size_before - size_after
                
                # 记录性能统计
                stats = self.performance_monitor.finish()
                stats.update({
                    'size_before_bytes': size_before,
                    'size_after_bytes': size_after,
                    'saved_bytes': saved_bytes,
                    'saved_percentage': (saved_bytes / size_before * 100) if size_before > 0 else 0
                })
                
                logger.info(f"数据库清理和分析完成: {stats}")
                
            return True
            
        except Exception as e:
            logger.error(f"数据库清理和分析失败: {e}")
            return False
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取慢查询信息（模拟实现）
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 慢查询列表
        """
        # SQLite没有内置的慢查询日志，这里提供一个框架
        # 实际使用中可以通过日志分析或性能监控来实现
        
        try:
            # 这里可以查询性能监控表（如果已实现）
            # 或者返回一些常见的可能慢查询场景
            
            potential_slow_queries = [
                {
                    'query_type': '全表扫描',
                    'description': '未使用索引的大表查询',
                    'suggestion': '为常用查询字段添加索引'
                },
                {
                    'query_type': '复杂JOIN',
                    'description': '多表连接查询',
                    'suggestion': '优化JOIN条件和索引'
                },
                {
                    'query_type': '大范围日期查询',
                    'description': '跨大时间范围的行情数据查询',
                    'suggestion': '考虑数据分区或缓存'
                }
            ]
            
            return potential_slow_queries[:limit]
            
        except Exception as e:
            logger.error(f"获取慢查询信息失败: {e}")
            return []
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """
        获取优化建议
        
        Returns:
            List[Dict[str, Any]]: 优化建议列表
        """
        recommendations = []
        
        try:
            # 检查表大小和记录数
            main_tables = ['stock_quotes', 'sector_quotes', 'relative_strength_results']
            
            for table_name in main_tables:
                stats = self.analyze_table_statistics(table_name)
                
                if stats.get('total_records', 0) > 100000:
                    recommendations.append({
                        'type': 'index_optimization',
                        'table': table_name,
                        'description': f'表 {table_name} 记录数较多，建议优化索引',
                        'action': f'执行 optimize_table_indexes("{table_name}")'
                    })
                
                if stats.get('table_size_bytes', 0) > 100 * 1024 * 1024:  # 100MB
                    recommendations.append({
                        'type': 'vacuum_needed',
                        'table': table_name,
                        'description': f'表 {table_name} 占用空间较大，建议执行清理',
                        'action': 'vacuum_and_analyze()'
                    })
            
            # 检查索引覆盖
            with self.db_manager.get_connection() as conn:
                for table_name in main_tables:
                    cursor = conn.execute(
                        "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=?",
                        (table_name,)
                    )
                    index_count = cursor.fetchone()[0]
                    
                    if index_count < 3:
                        recommendations.append({
                            'type': 'missing_indexes',
                            'table': table_name,
                            'description': f'表 {table_name} 索引数量较少，可能影响查询性能',
                            'action': f'optimize_table_indexes("{table_name}")'
                        })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取优化建议失败: {e}")
            return [{'type': 'error', 'description': f'获取建议失败: {e}'}]