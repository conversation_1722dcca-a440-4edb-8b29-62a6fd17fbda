"""
用户友好错误消息系统

将技术性错误转换为用户可理解的友好提示，并提供解决方案
"""

from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
import re
from ...utils.logger import get_logger
from ...utils.exceptions import ErrorCode

logger = get_logger(__name__)


@dataclass
class ErrorSolution:
    """错误解决方案"""
    title: str                    # 解决方案标题
    steps: List[str]             # 具体步骤
    prevention: Optional[str]     # 预防措施
    related_help: Optional[str]   # 相关帮助链接


@dataclass
class UserFriendlyError:
    """用户友好错误信息"""
    title: str                    # 错误标题
    description: str              # 错误描述
    cause: str                   # 问题原因
    solutions: List[ErrorSolution]  # 解决方案列表
    severity: str                # 严重程度：info, warning, error, critical
    error_code: Optional[str]    # 错误代码
    technical_details: Optional[str]  # 技术详情（可选显示）


class ErrorSeverity:
    """错误严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class UserFriendlyErrorTranslator:
    """用户友好错误翻译器"""
    
    def __init__(self):
        """初始化错误翻译器"""
        self.error_patterns = self._init_error_patterns()
        self.error_solutions = self._init_error_solutions()
        logger.info("用户友好错误翻译器初始化完成")
    
    def translate_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> UserFriendlyError:
        """
        将技术错误翻译为用户友好错误
        
        Args:
            error: 原始错误
            context: 错误上下文
            
        Returns:
            用户友好错误信息
        """
        try:
            error_message = str(error)
            error_type = type(error).__name__
            
            # 尝试匹配已知错误模式
            for pattern_info in self.error_patterns:
                if self._match_error_pattern(error_message, error_type, pattern_info):
                    return self._create_user_friendly_error(pattern_info, error_message, context)
            
            # 如果没有匹配的模式，创建通用错误
            return self._create_generic_error(error, context)
            
        except Exception as e:
            logger.error(f"错误翻译失败: {e}")
            return self._create_fallback_error(error)
    
    def _init_error_patterns(self) -> List[Dict[str, Any]]:
        """初始化错误模式"""
        return [
            # 数据源连接错误
            {
                'patterns': [
                    r'XtData模块不可用',
                    r'MiniQMT.*安装',
                    r'xtdata.*导入失败'
                ],
                'error_types': ['DataSourceError', 'ImportError'],
                'error_id': 'xtdata_not_available',
                'title': 'XtData数据源连接失败',
                'description': '无法连接到XtData数据源，这通常是因为MiniQMT客户端未正确安装或配置。',
                'cause': 'MiniQMT客户端未安装、未启动，或者xtquant库未正确安装',
                'severity': ErrorSeverity.ERROR
            },
            {
                'patterns': [
                    r'连接.*超时',
                    r'网络.*连接失败',
                    r'timeout.*occurred'
                ],
                'error_types': ['DataSourceError', 'TimeoutError'],
                'error_id': 'network_timeout',
                'title': '网络连接超时',
                'description': '与数据服务器的连接超时，可能是网络问题或服务器繁忙。',
                'cause': '网络连接不稳定、服务器响应慢或防火墙阻止连接',
                'severity': ErrorSeverity.WARNING
            },
            {
                'patterns': [
                    r'认证.*失败',
                    r'授权.*错误',
                    r'authentication.*failed'
                ],
                'error_types': ['DataSourceError'],
                'error_id': 'auth_failed',
                'title': '身份验证失败',
                'description': '无法通过身份验证，请检查您的登录凭据。',
                'cause': '用户名或密码错误，或者账户权限不足',
                'severity': ErrorSeverity.ERROR
            },
            # 数据分析错误
            {
                'patterns': [
                    r'数据不足',
                    r'insufficient.*data',
                    r'样本.*太少'
                ],
                'error_types': ['CalculationError', 'ValueError'],
                'error_id': 'insufficient_data',
                'title': '数据不足',
                'description': '当前股票的历史数据不足以进行有效分析。',
                'cause': '股票上市时间较短、停牌时间较长或数据源缺失部分数据',
                'severity': ErrorSeverity.WARNING
            },
            {
                'patterns': [
                    r'计算.*错误',
                    r'数值.*异常',
                    r'division by zero'
                ],
                'error_types': ['CalculationError', 'ZeroDivisionError', 'ValueError'],
                'error_id': 'calculation_error',
                'title': '计算错误',
                'description': '在数据分析过程中遇到计算错误，可能是数据异常导致。',
                'cause': '数据中包含异常值、空值或计算参数设置不当',
                'severity': ErrorSeverity.ERROR
            },
            # 界面错误
            {
                'patterns': [
                    r'界面.*初始化失败',
                    r'UI.*error',
                    r'控件.*创建失败'
                ],
                'error_types': ['UIError', 'RuntimeError'],
                'error_id': 'ui_init_error',
                'title': '界面初始化失败',
                'description': '用户界面组件初始化失败，可能影响部分功能的使用。',
                'cause': '系统资源不足、显示驱动问题或软件冲突',
                'severity': ErrorSeverity.ERROR
            },
            # 文件操作错误
            {
                'patterns': [
                    r'文件.*不存在',
                    r'No such file',
                    r'FileNotFoundError'
                ],
                'error_types': ['FileNotFoundError'],
                'error_id': 'file_not_found',
                'title': '文件未找到',
                'description': '系统无法找到所需的文件，可能已被删除或移动。',
                'cause': '文件被意外删除、路径变更或权限不足',
                'severity': ErrorSeverity.WARNING
            },
            {
                'patterns': [
                    r'权限.*拒绝',
                    r'Permission denied',
                    r'访问.*被拒绝'
                ],
                'error_types': ['PermissionError'],
                'error_id': 'permission_denied',
                'title': '权限不足',
                'description': '没有足够的权限执行此操作。',
                'cause': '文件或目录权限设置限制了访问',
                'severity': ErrorSeverity.ERROR
            }
        ]
    
    def _init_error_solutions(self) -> Dict[str, List[ErrorSolution]]:
        """初始化错误解决方案"""
        return {
            'xtdata_not_available': [
                ErrorSolution(
                    title="安装MiniQMT客户端",
                    steps=[
                        "访问迅投官网或联系您的券商获取MiniQMT安装包",
                        "下载并安装最新版本的MiniQMT客户端",
                        "启动MiniQMT并使用您的券商账户登录",
                        "确保客户端显示'已连接'状态"
                    ],
                    prevention="定期更新MiniQMT客户端到最新版本",
                    related_help="数据源配置向导"
                ),
                ErrorSolution(
                    title="安装xtquant库",
                    steps=[
                        "打开命令行或终端",
                        "执行命令：pip install xtquant",
                        "等待安装完成，确保无错误信息",
                        "重启本应用程序"
                    ],
                    prevention="使用虚拟环境管理Python依赖",
                    related_help="环境配置说明"
                )
            ],
            'network_timeout': [
                ErrorSolution(
                    title="检查网络连接",
                    steps=[
                        "确认网络连接正常，可以访问互联网",
                        "尝试访问其他网站验证网络状态",
                        "检查是否使用了代理服务器",
                        "重启网络设备（路由器、调制解调器）"
                    ],
                    prevention="使用稳定的网络连接，避免在网络高峰期进行大量数据操作",
                    related_help="网络配置指南"
                ),
                ErrorSolution(
                    title="调整超时设置",
                    steps=[
                        "进入系统设置→数据源配置",
                        "增加连接超时时间（建议60秒以上）",
                        "增加重试次数（建议3-5次）",
                        "保存设置并重新测试连接"
                    ],
                    prevention="根据网络环境合理设置超时参数",
                    related_help="数据源配置说明"
                )
            ],
            'auth_failed': [
                ErrorSolution(
                    title="检查登录凭据",
                    steps=[
                        "确认用户名和密码输入正确",
                        "检查是否有大小写错误",
                        "尝试在券商官网登录验证账户状态",
                        "联系券商确认账户是否正常"
                    ],
                    prevention="定期更新密码，确保账户安全",
                    related_help="账户管理说明"
                )
            ],
            'insufficient_data': [
                ErrorSolution(
                    title="选择其他股票",
                    steps=[
                        "选择上市时间较长的股票（建议1年以上）",
                        "避免选择经常停牌的股票",
                        "优先选择主板大盘股进行分析",
                        "检查股票是否有足够的交易数据"
                    ],
                    prevention="建立股票筛选标准，优先分析数据充足的标的",
                    related_help="股票选择指南"
                ),
                ErrorSolution(
                    title="调整分析参数",
                    steps=[
                        "减少分析所需的最小数据量",
                        "缩短分析时间周期",
                        "使用更宽松的数据要求",
                        "等待更多数据积累后再次分析"
                    ],
                    prevention="根据股票特点调整分析参数",
                    related_help="分析参数配置"
                )
            ]
        }
    
    def _match_error_pattern(self, error_message: str, error_type: str, pattern_info: Dict[str, Any]) -> bool:
        """匹配错误模式"""
        # 检查错误类型
        if 'error_types' in pattern_info and error_type not in pattern_info['error_types']:
            return False
        
        # 检查错误消息模式
        for pattern in pattern_info['patterns']:
            if re.search(pattern, error_message, re.IGNORECASE):
                return True
        
        return False
    
    def _create_user_friendly_error(self, pattern_info: Dict[str, Any], 
                                   error_message: str, context: Optional[Dict[str, Any]]) -> UserFriendlyError:
        """创建用户友好错误"""
        error_id = pattern_info['error_id']
        solutions = self.error_solutions.get(error_id, [])
        
        return UserFriendlyError(
            title=pattern_info['title'],
            description=pattern_info['description'],
            cause=pattern_info['cause'],
            solutions=solutions,
            severity=pattern_info['severity'],
            error_code=error_id,
            technical_details=error_message
        )
    
    def _create_generic_error(self, error: Exception, context: Optional[Dict[str, Any]]) -> UserFriendlyError:
        """创建通用错误"""
        error_type = type(error).__name__
        error_message = str(error)
        
        return UserFriendlyError(
            title=f"操作失败 ({error_type})",
            description="系统在执行操作时遇到了问题，请尝试重新操作或联系技术支持。",
            cause="可能是临时的系统问题或操作参数不当",
            solutions=[
                ErrorSolution(
                    title="基本故障排除",
                    steps=[
                        "重新尝试刚才的操作",
                        "检查网络连接是否正常",
                        "重启应用程序",
                        "如果问题持续，请联系技术支持"
                    ],
                    prevention="定期保存工作进度，避免数据丢失",
                    related_help="故障排除指南"
                )
            ],
            severity=ErrorSeverity.ERROR,
            error_code="generic_error",
            technical_details=f"{error_type}: {error_message}"
        )
    
    def _create_fallback_error(self, error: Exception) -> UserFriendlyError:
        """创建备用错误（当翻译过程本身失败时）"""
        return UserFriendlyError(
            title="系统错误",
            description="系统遇到了意外错误，请重启应用程序或联系技术支持。",
            cause="系统内部错误",
            solutions=[
                ErrorSolution(
                    title="紧急处理",
                    steps=[
                        "保存当前工作（如果可能）",
                        "重启应用程序",
                        "联系技术支持并提供错误信息"
                    ],
                    prevention="定期备份重要数据",
                    related_help="技术支持联系方式"
                )
            ],
            severity=ErrorSeverity.CRITICAL,
            error_code="system_error",
            technical_details=str(error)
        )


# 全局错误翻译器实例
error_translator = UserFriendlyErrorTranslator()


class UserFriendlyErrorDialog:
    """用户友好错误对话框"""

    @staticmethod
    def show_error(parent, error: Exception, context: Optional[Dict[str, Any]] = None):
        """
        显示用户友好错误对话框

        Args:
            parent: 父窗口
            error: 原始错误
            context: 错误上下文
        """
        from PyQt6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QTextEdit, QTabWidget, QWidget, QScrollArea, QFrame
        )
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont, QIcon

        try:
            # 翻译错误
            friendly_error = error_translator.translate_error(error, context)

            # 创建对话框
            dialog = QDialog(parent)
            dialog.setWindowTitle("操作提示")
            dialog.setFixedSize(600, 500)
            dialog.setModal(True)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(15)
            layout.setContentsMargins(20, 20, 20, 20)

            # 错误图标和标题
            header_layout = QHBoxLayout()

            # 根据严重程度选择图标
            icon_label = QLabel()
            if friendly_error.severity == ErrorSeverity.CRITICAL:
                icon_label.setText("🚨")
                icon_label.setStyleSheet("font-size: 32px;")
            elif friendly_error.severity == ErrorSeverity.ERROR:
                icon_label.setText("❌")
                icon_label.setStyleSheet("font-size: 32px;")
            elif friendly_error.severity == ErrorSeverity.WARNING:
                icon_label.setText("⚠️")
                icon_label.setStyleSheet("font-size: 32px;")
            else:
                icon_label.setText("ℹ️")
                icon_label.setStyleSheet("font-size: 32px;")

            header_layout.addWidget(icon_label)

            title_label = QLabel(friendly_error.title)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin-left: 10px;
                }
            """)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # 标签页控件
            tab_widget = QTabWidget()

            # 问题描述标签页
            description_tab = UserFriendlyErrorDialog._create_description_tab(friendly_error)
            tab_widget.addTab(description_tab, "问题描述")

            # 解决方案标签页
            solutions_tab = UserFriendlyErrorDialog._create_solutions_tab(friendly_error)
            tab_widget.addTab(solutions_tab, "解决方案")

            # 技术详情标签页（可选）
            if friendly_error.technical_details:
                technical_tab = UserFriendlyErrorDialog._create_technical_tab(friendly_error)
                tab_widget.addTab(technical_tab, "技术详情")

            layout.addWidget(tab_widget)

            # 按钮区域
            button_layout = QHBoxLayout()

            # 复制错误信息按钮
            copy_button = QPushButton("复制错误信息")
            copy_button.clicked.connect(lambda: UserFriendlyErrorDialog._copy_error_info(friendly_error))
            button_layout.addWidget(copy_button)

            button_layout.addStretch()

            # 关闭按钮
            close_button = QPushButton("确定")
            close_button.clicked.connect(dialog.accept)
            close_button.setDefault(True)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            # 应用样式
            UserFriendlyErrorDialog._apply_dialog_styles(dialog)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            logger.error(f"显示用户友好错误对话框失败: {e}")
            # 备用简单错误提示
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(parent, "错误", f"操作失败: {str(error)}")

    @staticmethod
    def _create_description_tab(friendly_error: UserFriendlyError):
        """创建问题描述标签页"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QTextEdit

        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 问题描述
        desc_label = QLabel("问题描述：")
        desc_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(desc_label)

        desc_text = QLabel(friendly_error.description)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;")
        layout.addWidget(desc_text)

        # 问题原因
        cause_label = QLabel("可能原因：")
        cause_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(cause_label)

        cause_text = QLabel(friendly_error.cause)
        cause_text.setWordWrap(True)
        cause_text.setStyleSheet("margin-bottom: 15px; padding: 10px; background: #fff3cd; border-radius: 4px;")
        layout.addWidget(cause_text)

        layout.addStretch()
        return widget

    @staticmethod
    def _create_solutions_tab(friendly_error: UserFriendlyError):
        """创建解决方案标签页"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QScrollArea, QFrame

        widget = QWidget()
        layout = QVBoxLayout(widget)

        if not friendly_error.solutions:
            no_solution_label = QLabel("暂无具体解决方案，请联系技术支持。")
            no_solution_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_solution_label)
            return widget

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        for i, solution in enumerate(friendly_error.solutions):
            # 解决方案框架
            solution_frame = QFrame()
            solution_frame.setFrameStyle(QFrame.Shape.Box)
            solution_frame.setStyleSheet("""
                QFrame {
                    background: white;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    margin: 5px;
                    padding: 10px;
                }
            """)

            solution_layout = QVBoxLayout(solution_frame)

            # 解决方案标题
            title_label = QLabel(f"{i+1}. {solution.title}")
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2196F3; margin-bottom: 8px;")
            solution_layout.addWidget(title_label)

            # 操作步骤
            for j, step in enumerate(solution.steps):
                step_label = QLabel(f"   {j+1}) {step}")
                step_label.setWordWrap(True)
                step_label.setStyleSheet("margin-bottom: 3px; padding-left: 10px;")
                solution_layout.addWidget(step_label)

            # 预防措施
            if solution.prevention:
                prevention_label = QLabel(f"💡 预防措施: {solution.prevention}")
                prevention_label.setWordWrap(True)
                prevention_label.setStyleSheet("margin-top: 8px; font-style: italic; color: #6c757d;")
                solution_layout.addWidget(prevention_label)

            scroll_layout.addWidget(solution_frame)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget

    @staticmethod
    def _create_technical_tab(friendly_error: UserFriendlyError):
        """创建技术详情标签页"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QTextEdit

        widget = QWidget()
        layout = QVBoxLayout(widget)

        info_label = QLabel("以下是技术详情，可提供给技术支持人员：")
        info_label.setStyleSheet("margin-bottom: 10px; color: #6c757d;")
        layout.addWidget(info_label)

        technical_text = QTextEdit()
        technical_text.setReadOnly(True)
        technical_text.setPlainText(friendly_error.technical_details)
        technical_text.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(technical_text)

        return widget

    @staticmethod
    def _copy_error_info(friendly_error: UserFriendlyError):
        """复制错误信息到剪贴板"""
        from PyQt6.QtWidgets import QApplication

        try:
            error_info = f"""错误报告
================
标题: {friendly_error.title}
描述: {friendly_error.description}
原因: {friendly_error.cause}
严重程度: {friendly_error.severity}
错误代码: {friendly_error.error_code or 'N/A'}

技术详情:
{friendly_error.technical_details or 'N/A'}

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            clipboard = QApplication.clipboard()
            clipboard.setText(error_info)

            logger.info("错误信息已复制到剪贴板")

        except Exception as e:
            logger.error(f"复制错误信息失败: {e}")

    @staticmethod
    def _apply_dialog_styles(dialog):
        """应用对话框样式"""
        dialog.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
