"""
简单的数据库测试
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import tempfile
from src.database.manager import DatabaseManager
from src.database.optimization import DatabaseOptimizer


def test_simple_database_creation():
    """测试简单的数据库创建"""
    # 创建临时数据库文件
    fd, temp_path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    
    try:
        # 创建数据库管理器
        manager = DatabaseManager(temp_path)
        
        # 初始化数据库
        result = manager.initialize()
        print(f"数据库初始化结果: {result}")
        
        if result:
            # 测试基本查询
            with manager.get_connection() as conn:
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"数据库表: {[t[0] for t in tables]}")
        
        manager.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_simple_optimizer_creation():
    """测试简单的优化器创建"""
    # 创建临时数据库文件
    fd, temp_path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    
    try:
        # 创建数据库管理器
        manager = DatabaseManager(temp_path)
        
        # 初始化数据库
        result = manager.initialize()
        print(f"数据库初始化结果: {result}")
        
        if result:
            # 创建优化器
            optimizer = DatabaseOptimizer(manager)
            print(f"优化器创建成功，批量大小: {optimizer._batch_size}")
        
        manager.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)


if __name__ == "__main__":
    print("开始简单数据库测试...")
    test_simple_database_creation()
    print("\n开始简单优化器测试...")
    test_simple_optimizer_creation()
    print("测试完成！")