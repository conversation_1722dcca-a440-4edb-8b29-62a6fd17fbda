#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XtData历史数据获取修复效果

验证修复后的XtData适配器能否正确获取历史数据
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig
from src.config.config_manager import ConfigManager
from src.utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="test_history_fix",
        level="INFO",
        log_to_file=True,
        log_to_console=True
    )


def test_single_stock_history(data_source, symbol):
    """测试单只股票的历史数据获取"""
    try:
        print(f"\n🔍 测试获取 {symbol} 的历史数据...")
        
        # 设置日期范围（最近30天）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"日期范围: {start_date} - {end_date}")
        
        # 获取历史数据
        market_data = data_source.get_market_data(
            symbol=symbol,
            period='1d',
            start_date=start_date,
            end_date=end_date
        )
        
        if market_data and market_data.data is not None and not market_data.data.empty:
            df = market_data.data
            print(f"✅ 成功获取 {symbol} 的历史数据:")
            print(f"   - 数据条数: {len(df)}")
            print(f"   - 数据列: {list(df.columns)}")
            print(f"   - 时间范围: {df.index[0]} - {df.index[-1]}")
            print(f"   - 最新价格: {df['close'].iloc[-1]:.2f}")
            return True
        else:
            print(f"❌ 未获取到 {symbol} 的历史数据")
            return False
            
    except Exception as e:
        print(f"❌ 获取 {symbol} 历史数据失败: {e}")
        return False


def main():
    """主函数"""
    try:
        print("🚀 测试XtData历史数据获取修复效果")
        print("=" * 60)
        
        # 设置日志
        setup_logging()
        
        # 加载配置
        config_manager = ConfigManager()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
        config_data = config_manager.load_config("config", config_path)
        
        # 创建数据源管理器
        data_manager = DataSourceManager()
        
        # 配置XtData数据源
        xtdata_config = config_data.get("data_sources", {}).get("xtdata", {})
        if not xtdata_config.get("enabled", True):
            print("❌ XtData数据源未启用")
            return False
        
        print("📡 正在连接XtData数据源...")
        
        # 创建数据源配置
        config = DataSourceConfig(
            name=xtdata_config.get("name", "XtData"),
            enabled=xtdata_config.get("enabled", True),
            timeout=xtdata_config.get("timeout", 30),
            retry_times=xtdata_config.get("retry_times", 3),
            auto_reconnect=xtdata_config.get("auto_reconnect", True),
            config=xtdata_config.get("config", {})
        )
        
        # 创建XtData数据源
        xtdata_source = XtDataAdapter(config)
        
        # 添加到管理器并测试连接
        data_manager.add_source("xtdata", xtdata_source, priority=10)
        
        if not data_manager.test_connection("xtdata"):
            print("❌ XtData数据源连接失败")
            return False
        
        print("✅ XtData数据源连接成功")
        
        # 获取测试股票列表
        print("\n📋 获取测试股票列表...")
        test_symbols = ['600000.SH', '000001.SZ', '000002.SZ']
        
        # 测试历史数据获取
        success_count = 0
        total_count = len(test_symbols)
        
        for symbol in test_symbols:
            print(f"\n{'='*50}")
            if test_single_stock_history(xtdata_source, symbol):
                success_count += 1
        
        print(f"\n{'='*60}")
        print(f"📊 测试结果统计:")
        print(f"   - 总测试股票: {total_count}")
        print(f"   - 成功获取: {success_count}")
        print(f"   - 失败数量: {total_count - success_count}")
        print(f"   - 成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("🎉 所有测试通过！XtData历史数据获取修复成功！")
            return True
        elif success_count > 0:
            print("⚠️  部分测试通过，可能存在个别股票数据问题")
            return True
        else:
            print("❌ 所有测试失败，需要进一步检查问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logger.error(f"历史数据测试失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 历史数据获取测试完成")
        sys.exit(0)
    else:
        print("\n❌ 历史数据获取测试失败")
        sys.exit(1)
