"""
配置管理器测试用例

测试ConfigManager的核心功能：
- 配置加载和保存
- 配置验证
- 热重载机制
- 错误处理
"""

import pytest
import tempfile
import os
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.config.config_manager import ConfigManager
from src.config.settings import Settings
from src.utils.exceptions import ConfigurationError


class TestConfigManager:
    """配置管理器基础测试"""

    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = self.temp_dir
        self.test_config = {
            'database': {
                'path': 'test.db',
                'pool_size': 10
            },
            'data_sources': {
                'xtdata': {
                    'enabled': True,
                    'timeout': 30
                }
            },
            'system': {
                'log_level': 'INFO',
                'cache_size': 1000
            }
        }

        # 创建测试配置文件
        config_file = os.path.join(self.temp_dir, 'test.yaml')
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.test_config, f, default_flow_style=False)

    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_init_with_valid_config(self):
        """测试使用有效配置初始化"""
        manager = ConfigManager(self.config_dir)
        assert manager.config_dir == Path(self.config_dir)

        # 加载配置
        config = manager.load_config('test')
        assert config['database']['path'] == 'test.db'
    
    def test_load_nonexistent_config(self):
        """测试加载不存在的配置"""
        manager = ConfigManager(self.config_dir)
        config = manager.load_config('nonexistent')
        assert config == {}

    def test_load_config_success(self):
        """测试成功加载配置"""
        manager = ConfigManager(self.config_dir)
        config = manager.load_config('test')
        assert config['database']['pool_size'] == 10
        assert config['data_sources']['xtdata']['enabled'] is True

    def test_load_config_invalid_yaml(self):
        """测试加载无效YAML配置"""
        invalid_yaml_file = os.path.join(self.temp_dir, 'invalid.yaml')
        with open(invalid_yaml_file, 'w') as f:
            f.write("invalid: yaml: content: [")

        manager = ConfigManager(self.config_dir)
        config = manager.load_config('invalid')
        assert config == {}
    
    def test_save_config_success(self):
        """测试成功保存配置"""
        manager = ConfigManager(self.config_dir)
        manager.load_config('test')

        # 修改配置
        new_config = self.test_config.copy()
        new_config['system']['log_level'] = 'DEBUG'

        # 保存配置
        success = manager.save_config('test', new_config)
        assert success is True

        # 重新加载验证
        reloaded_config = manager.load_config('test')
        assert reloaded_config['system']['log_level'] == 'DEBUG'

    def test_get_config_value(self):
        """测试获取配置值"""
        manager = ConfigManager(self.config_dir)
        manager.load_config('test')

        # 测试获取存在的值
        assert manager.get_config('test', 'database.path') == 'test.db'
        assert manager.get_config('test', 'data_sources.xtdata.timeout') == 30

        # 测试获取不存在的值
        assert manager.get_config('test', 'nonexistent.key') is None
        assert manager.get_config('test', 'nonexistent.key', 'default') == 'default'

    def test_set_config_value(self):
        """测试设置配置值"""
        manager = ConfigManager(self.config_dir)
        manager.load_config('test')

        # 设置新值
        manager.set_config('test', 'database.pool_size', 20)
        assert manager.get_config('test', 'database.pool_size') == 20

        # 设置嵌套值
        manager.set_config('test', 'new.nested.value', 'test')
        assert manager.get_config('test', 'new.nested.value') == 'test'


class TestConfigManagerHotReload:
    """配置热重载测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'hot_reload_config.yaml')
        self.initial_config = {
            'system': {'log_level': 'INFO'},
            'database': {'pool_size': 10}
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.initial_config, f)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_enable_hot_reload(self):
        """测试启用热重载"""
        manager = ConfigManager(self.config_file)
        
        # 启用热重载
        manager.enable_hot_reload()
        assert manager._hot_reload_enabled is True
        assert manager._file_watcher is not None
    
    def test_disable_hot_reload(self):
        """测试禁用热重载"""
        manager = ConfigManager(self.config_file)
        manager.enable_hot_reload()
        
        # 禁用热重载
        manager.disable_hot_reload()
        assert manager._hot_reload_enabled is False
        assert manager._file_watcher is None
    
    @patch('src.config.config_manager.FileSystemEventHandler')
    def test_file_change_detection(self, mock_handler):
        """测试文件变化检测"""
        manager = ConfigManager(self.config_file)
        callback = MagicMock()
        
        manager.enable_hot_reload(callback)
        
        # 模拟文件变化
        manager._on_config_changed(None)
        
        # 验证回调被调用
        callback.assert_called_once()


class TestConfigManagerIntegration:
    """配置管理器集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'integration_config.yaml')
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 创建初始配置
        initial_config = {
            'database': {'path': 'initial.db'},
            'system': {'log_level': 'INFO'}
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(initial_config, f)
        
        # 1. 加载配置
        manager = ConfigManager(self.config_file)
        assert manager.get('database.path') == 'initial.db'
        
        # 2. 修改配置
        manager.set('database.path', 'updated.db')
        manager.set('system.log_level', 'DEBUG')
        
        # 3. 保存配置
        manager.save_config()
        
        # 4. 重新加载验证
        new_manager = ConfigManager(self.config_file)
        assert new_manager.get('database.path') == 'updated.db'
        assert new_manager.get('system.log_level') == 'DEBUG'
    
    def test_settings_integration(self):
        """测试与Settings类的集成"""
        config_data = {
            'database': {
                'path': 'test.db',
                'pool_size': 5,
                'timeout': 30
            },
            'data_sources': {
                'xtdata': {
                    'enabled': True,
                    'host': 'localhost',
                    'port': 58610
                }
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f)
        
        manager = ConfigManager(self.config_file)
        settings = Settings.from_dict(manager.config)
        
        assert settings.database.path == 'test.db'
        assert settings.database.pool_size == 5
        assert settings.data_sources['xtdata']['enabled'] is True


if __name__ == '__main__':
    pytest.main([__file__])
