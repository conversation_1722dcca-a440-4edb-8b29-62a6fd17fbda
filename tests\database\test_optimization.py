"""
威科夫相对强弱选股系统 - 数据库优化测试

测试数据库优化功能，包括批量插入、性能监控、索引优化等
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import pytest
import sqlite3
import tempfile
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pandas as pd

from src.database.manager import DatabaseManager
from src.database.optimization import DatabaseOptimizer, PerformanceMonitor


class TestPerformanceMonitor:
    """性能监控器测试"""
    
    def test_performance_monitor_basic(self):
        """测试基本性能监控功能"""
        import time
        
        monitor = PerformanceMonitor()
        
        # 开始监控
        monitor.start("test_operation")
        assert monitor.operation_name == "test_operation"
        assert monitor.start_time is not None
        
        # 添加一个小延迟确保时间差
        time.sleep(0.001)
        
        # 添加记录
        monitor.add_records(100)
        monitor.add_records(200)
        assert monitor.records_processed == 300
        
        # 结束监控
        stats = monitor.finish()
        assert stats['operation_name'] == "test_operation"
        assert stats['records_processed'] == 300
        assert stats['duration_seconds'] >= 0
        assert stats['records_per_second'] >= 0
    
    def test_performance_monitor_empty_finish(self):
        """测试未开始监控时的结束操作"""
        monitor = PerformanceMonitor()
        stats = monitor.finish()
        assert stats == {}


class TestDatabaseOptimizer:
    """数据库优化器测试"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库文件"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
    
    @pytest.fixture
    def db_manager(self, temp_db_path):
        """创建数据库管理器"""
        manager = DatabaseManager(temp_db_path)
        manager.initialize()
        return manager
    
    @pytest.fixture
    def optimizer(self, db_manager):
        """创建数据库优化器"""
        return DatabaseOptimizer(db_manager)
    
    def test_optimizer_initialization(self, optimizer):
        """测试优化器初始化"""
        assert optimizer.db_manager is not None
        assert optimizer.performance_monitor is not None
        assert optimizer._batch_size == 1000
    
    def test_set_batch_size(self, optimizer):
        """测试设置批量大小"""
        optimizer.set_batch_size(500)
        assert optimizer._batch_size == 500
        
        # 测试无效值
        optimizer.set_batch_size(0)
        assert optimizer._batch_size == 500  # 应该保持不变
        
        optimizer.set_batch_size(-100)
        assert optimizer._batch_size == 500  # 应该保持不变
    
    def test_bulk_insert_mode_context(self, optimizer):
        """测试批量插入模式上下文管理器"""
        # 获取原始设置
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("PRAGMA synchronous")
            original_sync = cursor.fetchone()[0]
        
        # 测试批量插入模式
        with optimizer.bulk_insert_mode():
            with optimizer.db_manager.get_connection() as conn:
                cursor = conn.execute("PRAGMA synchronous")
                bulk_sync = cursor.fetchone()[0]
                # 在批量模式下，synchronous应该被设置为OFF (0)
                assert bulk_sync == 0
        
        # 检查设置是否恢复
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("PRAGMA synchronous")
            restored_sync = cursor.fetchone()[0]
            # 设置应该被恢复（可能不完全相同，但不应该是0）
            assert restored_sync != 0
    
    def test_bulk_insert_stock_quotes_empty(self, optimizer):
        """测试空数据的批量插入"""
        result = optimizer.bulk_insert_stock_quotes([])
        assert result == 0
    
    def test_bulk_insert_stock_quotes_valid_data(self, optimizer):
        """测试有效数据的批量插入"""
        # 准备测试数据
        test_data = []
        base_date = datetime.now().date()
        
        for i in range(5):
            test_data.append({
                'stock_code': f'00000{i}',
                'trade_date': base_date - timedelta(days=i),
                'open': 10.0 + i,
                'high': 11.0 + i,
                'low': 9.0 + i,
                'close': 10.5 + i,
                'volume': 1000000 + i * 100000,
                'amount': 10000000 + i * 1000000,
                'adj_factor': 1.0,
                'change_pct': i * 0.5,
                'turnover_rate': i * 0.1,
                'pe_ratio': 15.0 + i,
                'pb_ratio': 1.5 + i * 0.1
            })
        
        # 执行批量插入
        result = optimizer.bulk_insert_stock_quotes(test_data)
        assert result == 5
        
        # 验证数据是否插入成功
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM stock_quotes")
            count = cursor.fetchone()[0]
            assert count == 5
    
    def test_bulk_insert_sector_quotes_valid_data(self, optimizer):
        """测试板块行情数据的批量插入"""
        # 准备测试数据
        test_data = []
        base_date = datetime.now().date()
        
        for i in range(3):
            test_data.append({
                'sector_code': f'BK{i:04d}',
                'trade_date': base_date - timedelta(days=i),
                'open': 1000.0 + i * 10,
                'high': 1100.0 + i * 10,
                'low': 900.0 + i * 10,
                'close': 1050.0 + i * 10,
                'volume': 10000000 + i * 1000000,
                'amount': 100000000 + i * 10000000,
                'change_pct': i * 0.5,
                'turnover_rate': i * 0.05
            })
        
        # 执行批量插入
        result = optimizer.bulk_insert_sector_quotes(test_data)
        assert result == 3
        
        # 验证数据是否插入成功
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM sector_quotes")
            count = cursor.fetchone()[0]
            assert count == 3
    
    def test_batch_iterator(self, optimizer):
        """测试批次迭代器"""
        data = list(range(25))  # 25个元素
        batch_size = 10
        
        batches = list(optimizer._batch_iterator(data, batch_size))
        
        assert len(batches) == 3  # 应该有3个批次
        assert len(batches[0]) == 10
        assert len(batches[1]) == 10
        assert len(batches[2]) == 5  # 最后一个批次只有5个元素
    
    def test_bulk_upsert_from_dataframe(self, optimizer):
        """测试从DataFrame批量插入数据"""
        # 创建测试DataFrame
        data = {
            'stock_code': ['000001', '000002', '000003'],
            'stock_name': ['股票1', '股票2', '股票3'],
            'sector_code': ['BK0001', 'BK0002', 'BK0001'],
            'list_date': ['2020-01-01', '2020-01-02', '2020-01-03'],
            'status': [1, 1, 1]
        }
        df = pd.DataFrame(data)
        
        # 执行批量插入
        result = optimizer.bulk_upsert_from_dataframe(
            df, 'stock_info', ['stock_code']
        )
        
        assert result == 3
        
        # 验证数据
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM stock_info")
            count = cursor.fetchone()[0]
            assert count == 3
    
    def test_bulk_upsert_from_empty_dataframe(self, optimizer):
        """测试空DataFrame的批量插入"""
        df = pd.DataFrame()
        result = optimizer.bulk_upsert_from_dataframe(
            df, 'stock_info', ['stock_code']
        )
        assert result == 0
    
    def test_dataframe_batch_iterator(self, optimizer):
        """测试DataFrame批次迭代器"""
        # 创建测试DataFrame
        data = {'col1': range(25), 'col2': range(25, 50)}
        df = pd.DataFrame(data)
        
        batch_size = 10
        batches = list(optimizer._dataframe_batch_iterator(df, batch_size))
        
        assert len(batches) == 3
        assert len(batches[0]) == 10
        assert len(batches[1]) == 10
        assert len(batches[2]) == 5
    
    def test_optimize_table_indexes_stock_quotes(self, optimizer):
        """测试股票行情表索引优化"""
        result = optimizer.optimize_table_indexes('stock_quotes')
        assert result is True
        
        # 验证索引是否创建
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND tbl_name='stock_quotes' 
                AND name LIKE 'idx_%'
            """)
            indexes = cursor.fetchall()
            assert len(indexes) >= 3  # 应该至少有3个优化索引
    
    def test_optimize_table_indexes_unknown_table(self, optimizer):
        """测试未知表的索引优化"""
        result = optimizer.optimize_table_indexes('unknown_table')
        assert result is False
    
    def test_analyze_table_statistics_stock_quotes(self, optimizer):
        """测试股票行情表统计分析"""
        # 先插入一些测试数据
        test_data = [{
            'stock_code': '000001',
            'trade_date': datetime.now().date(),
            'open': 10.0,
            'high': 11.0,
            'low': 9.0,
            'close': 10.5,
            'volume': 1000000,
            'amount': 10000000
        }]
        optimizer.bulk_insert_stock_quotes(test_data)
        
        # 分析统计信息
        stats = optimizer.analyze_table_statistics('stock_quotes')
        
        assert 'total_records' in stats
        assert stats['total_records'] >= 1
        assert 'table_size_bytes' in stats
        assert 'indexes' in stats
        assert 'date_range' in stats
        assert 'unique_symbols' in stats
    
    def test_analyze_table_statistics_empty_table(self, optimizer):
        """测试空表的统计分析"""
        stats = optimizer.analyze_table_statistics('stock_info')
        
        assert 'total_records' in stats
        assert stats['total_records'] == 0
        assert 'table_size_bytes' in stats
        assert 'indexes' in stats
    
    def test_vacuum_and_analyze(self, optimizer):
        """测试数据库清理和分析"""
        # 先插入一些数据
        test_data = [{
            'stock_code': f'00000{i}',
            'trade_date': datetime.now().date(),
            'open': 10.0,
            'high': 11.0,
            'low': 9.0,
            'close': 10.5,
            'volume': 1000000
        } for i in range(10)]
        
        optimizer.bulk_insert_stock_quotes(test_data)
        
        # 执行清理和分析
        result = optimizer.vacuum_and_analyze()
        assert result is True
    
    def test_get_slow_queries(self, optimizer):
        """测试获取慢查询信息"""
        slow_queries = optimizer.get_slow_queries(5)
        
        assert isinstance(slow_queries, list)
        assert len(slow_queries) <= 5
        
        if slow_queries:
            for query in slow_queries:
                assert 'query_type' in query
                assert 'description' in query
                assert 'suggestion' in query
    
    def test_get_optimization_recommendations(self, optimizer):
        """测试获取优化建议"""
        recommendations = optimizer.get_optimization_recommendations()
        
        assert isinstance(recommendations, list)
        
        for rec in recommendations:
            assert 'type' in rec
            assert 'description' in rec
            
            if rec['type'] != 'error':
                assert 'action' in rec


class TestDatabaseOptimizerIntegration:
    """数据库优化器集成测试"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库文件"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
    
    @pytest.fixture
    def db_manager(self, temp_db_path):
        """创建数据库管理器"""
        manager = DatabaseManager(temp_db_path)
        manager.initialize()
        return manager
    
    @pytest.fixture
    def optimizer(self, db_manager):
        """创建数据库优化器"""
        return DatabaseOptimizer(db_manager)
    
    def test_full_optimization_workflow(self, optimizer):
        """测试完整的优化工作流"""
        # 1. 准备大量测试数据
        stock_data = []
        sector_data = []
        base_date = datetime.now().date()
        
        # 生成1000条股票数据
        for i in range(1000):
            stock_data.append({
                'stock_code': f'{i:06d}',
                'trade_date': base_date - timedelta(days=i % 100),
                'open': 10.0 + (i % 50),
                'high': 11.0 + (i % 50),
                'low': 9.0 + (i % 50),
                'close': 10.5 + (i % 50),
                'volume': 1000000 + i * 1000,
                'amount': 10000000 + i * 10000
            })
        
        # 生成100条板块数据
        for i in range(100):
            sector_data.append({
                'sector_code': f'BK{i:04d}',
                'trade_date': base_date - timedelta(days=i % 30),
                'open': 1000.0 + i,
                'high': 1100.0 + i,
                'low': 900.0 + i,
                'close': 1050.0 + i,
                'volume': 10000000 + i * 100000,
                'amount': 100000000 + i * 1000000
            })
        
        # 2. 批量插入数据
        stock_result = optimizer.bulk_insert_stock_quotes(stock_data)
        assert stock_result == 1000
        
        sector_result = optimizer.bulk_insert_sector_quotes(sector_data)
        assert sector_result == 100
        
        # 3. 优化索引
        stock_index_result = optimizer.optimize_table_indexes('stock_quotes')
        assert stock_index_result is True
        
        sector_index_result = optimizer.optimize_table_indexes('sector_quotes')
        assert sector_index_result is True
        
        # 4. 分析统计信息
        stock_stats = optimizer.analyze_table_statistics('stock_quotes')
        assert stock_stats['total_records'] == 1000
        assert stock_stats['unique_symbols'] > 0
        
        sector_stats = optimizer.analyze_table_statistics('sector_quotes')
        assert sector_stats['total_records'] == 100
        assert sector_stats['unique_symbols'] > 0
        
        # 5. 获取优化建议
        recommendations = optimizer.get_optimization_recommendations()
        assert isinstance(recommendations, list)
        
        # 6. 执行清理和分析
        vacuum_result = optimizer.vacuum_and_analyze()
        assert vacuum_result is True
    
    def test_performance_monitoring_integration(self, optimizer):
        """测试性能监控集成"""
        # 设置较小的批量大小以测试多批次处理
        optimizer.set_batch_size(100)
        
        # 准备测试数据
        test_data = []
        base_date = datetime.now().date()
        
        for i in range(250):  # 250条记录，应该分成3个批次
            test_data.append({
                'stock_code': f'{i:06d}',
                'trade_date': base_date - timedelta(days=i % 10),
                'open': 10.0 + i * 0.01,
                'high': 11.0 + i * 0.01,
                'low': 9.0 + i * 0.01,
                'close': 10.5 + i * 0.01,
                'volume': 1000000 + i * 1000
            })
        
        # 执行批量插入并监控性能
        result = optimizer.bulk_insert_stock_quotes(test_data)
        assert result == 250
        
        # 验证数据插入
        with optimizer.db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM stock_quotes")
            count = cursor.fetchone()[0]
            assert count == 250
    
    def test_error_handling_in_optimization(self, optimizer):
        """测试优化过程中的错误处理"""
        # 测试无效数据的批量插入
        invalid_data = [
            {'stock_code': None, 'trade_date': 'invalid_date'},  # 无效数据
            {'stock_code': '000001'},  # 缺少必要字段
        ]
        
        # 应该能够处理错误而不崩溃
        result = optimizer.bulk_insert_stock_quotes(invalid_data)
        # 结果可能是0（如果所有数据都无效）或部分成功
        assert result >= 0
    
    def test_concurrent_optimization_safety(self, optimizer):
        """测试并发优化的安全性"""
        import threading
        import time
        
        results = []
        errors = []
        
        def insert_data(thread_id):
            try:
                data = [{
                    'stock_code': f'{thread_id:02d}{i:04d}',
                    'trade_date': datetime.now().date(),
                    'open': 10.0,
                    'high': 11.0,
                    'low': 9.0,
                    'close': 10.5,
                    'volume': 1000000
                } for i in range(10)]
                
                result = optimizer.bulk_insert_stock_quotes(data)
                results.append(result)
                
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程同时插入数据
        threads = []
        for i in range(3):
            thread = threading.Thread(target=insert_data, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(errors) == 0, f"并发操作出现错误: {errors}"
        assert len(results) == 3
        assert all(r == 10 for r in results)  # 每个线程应该插入10条记录