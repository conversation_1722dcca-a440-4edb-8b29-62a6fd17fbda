#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版核心功能测试
快速验证新实现的核心筛选功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.manager import DatabaseManager
from src.data_sources.manager import DataSourceManager
from src.services.sector_manager import SectorManager
from src.services.return_calculator import ReturnCalculator
from src.engines.sector_screening import SectorScreeningEngine, SectorScreeningParams
from src.utils.cache import CacheManager


async def test_core_functionality():
    """测试核心功能"""
    print("🧪 开始简化版核心功能测试")
    print("=" * 50)
    
    try:
        # 1. 初始化基础组件
        print("🔧 初始化组件...")
        db_path = "data/test_simple.db"
        db_manager = DatabaseManager(db_path)
        
        if not db_manager.initialize():
            raise Exception("数据库初始化失败")
        
        data_source_manager = DataSourceManager()
        sector_manager = SectorManager(db_manager, data_source_manager)
        cache_manager = CacheManager()
        return_calculator = ReturnCalculator(db_manager, sector_manager, cache_manager)
        
        print("✅ 组件初始化完成")
        
        # 2. 测试板块管理
        print("\n📊 测试板块管理...")
        success = await sector_manager.initialize_sectors()
        if not success:
            raise Exception("板块初始化失败")
        
        # 获取板块列表
        sectors = sector_manager.get_sector_list()
        print(f"  板块总数: {len(sectors)}")
        
        # 下载部分板块数据
        sector_codes = [s['sector_code'] for s in sectors[:3]]
        success = await sector_manager.download_sector_data(sector_codes, days=10)
        print(f"  板块数据下载: {'成功' if success else '失败'}")
        
        # 3. 测试收益率计算
        print("\n📈 测试收益率计算...")
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
        
        # 计算市场指数收益率
        market_returns = await return_calculator.calculate_batch_returns(
            ["000001.SH"], 'market', start_date, end_date
        )
        print(f"  市场收益率: {market_returns}")
        
        # 计算板块收益率
        sector_returns = await return_calculator.calculate_batch_returns(
            sector_codes, 'sector', start_date, end_date
        )
        print(f"  板块收益率数量: {len(sector_returns)}")
        
        # 4. 测试板块筛选
        print("\n🔍 测试板块筛选...")
        sector_engine = SectorScreeningEngine(sector_manager, return_calculator)
        
        params = SectorScreeningParams(
            start_date=start_date,
            end_date=end_date,
            market_index="000001.SH",
            sector_types=["industry"],
            min_relative_strength=0.0,
            max_sectors=5
        )
        
        result = await sector_engine.screen_strong_sectors(params)
        print(f"  市场收益率: {result.market_return:.4f}")
        print(f"  强势板块数: {result.selected_count}")
        
        if result.strong_sectors:
            print("  前3个强势板块:")
            for i, sector in enumerate(result.strong_sectors[:3]):
                print(f"    {i+1}. {sector.sector_name}: {sector.relative_strength:.4f}")
        
        print("\n✅ 所有核心功能测试通过！")
        print("\n🎉 实现的功能:")
        print("  ✓ 板块数据管理和维护")
        print("  ✓ 涨幅批量计算和缓存")
        print("  ✓ 板块相对强弱筛选")
        print("  ✓ 筛选引擎框架")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            if 'db_manager' in locals():
                db_manager.close()
            if 'cache_manager' in locals():
                cache_manager.clear_all()
        except:
            pass


async def main():
    """主函数"""
    success = await test_core_functionality()
    
    if success:
        print("\n🚀 核心功能验证成功！")
        print("📋 下一步工作:")
        print("  1. 完善个股筛选功能")
        print("  2. 集成真实数据源")
        print("  3. 优化用户界面")
        print("  4. 添加多时间段分析")
        return 0
    else:
        print("\n💥 核心功能验证失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
