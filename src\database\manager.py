"""
威科夫相对强弱选股系统 - 数据库管理器

提供数据库连接、初始化、版本管理等核心功能
"""

import sqlite3
import os
import shutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
from contextlib import contextmanager
import pandas as pd

from ..utils.logger import get_logger
from .connection_pool import DatabaseConnectionPool

logger = get_logger(__name__)


class DatabaseError(Exception):
    """数据库操作异常"""
    pass


class DatabaseManager:
    """
    数据库管理器
    
    负责数据库的连接、初始化、版本管理、备份等核心功能
    """
    
    def __init__(self, db_path: str, pool_size: int = 10):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
            pool_size: 连接池大小
        """
        self.db_path = Path(db_path)
        self.pool_size = pool_size
        self._connection_pool: Optional[DatabaseConnectionPool] = None
        self._lock = threading.RLock()
        self._initialized = False
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据库管理器初始化: {self.db_path}")
    
    def initialize(self) -> bool:
        """
        初始化数据库
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            with self._lock:
                if self._initialized:
                    return True
                
                # 创建连接池
                self._connection_pool = DatabaseConnectionPool(
                    str(self.db_path), 
                    self.pool_size
                )
                
                # 检查数据库是否存在或需要初始化
                if not self.db_path.exists():
                    logger.info("数据库文件不存在，开始创建新数据库")
                    self._create_database()
                else:
                    logger.info("数据库文件已存在，检查表结构")
                    # 检查是否需要创建表结构
                    if not self._check_tables_exist():
                        logger.info("表结构不完整，重新创建数据库")
                        self._create_database()
                    else:
                        logger.info("表结构完整，检查版本")
                        self._check_and_upgrade_database()
                
                # 验证数据库完整性
                if self._verify_database_integrity():
                    self._initialized = True
                    logger.info("数据库初始化成功")
                    return True
                else:
                    raise DatabaseError("数据库完整性验证失败")
                    
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False
    
    def _check_tables_exist(self) -> bool:
        """检查所有必要的表是否存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查核心表是否存在
                required_tables = [
                    'sector_info', 'sector_quotes', 'stock_info', 'stock_quotes',
                    'sector_constituents', 'relative_strength_results',
                    'selection_results', 'selection_details', 'system_config', 'sync_logs'
                ]
                
                for table in required_tables:
                    cursor.execute(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                        (table,)
                    )
                    if not cursor.fetchone():
                        logger.warning(f"缺少必要的表: {table}")
                        return False
                
                return True
                
        except sqlite3.Error as e:
            logger.error(f"检查表结构时出错: {e}")
            return False
    
    def _create_database(self) -> None:
        """创建新数据库"""
        try:
            # 读取DDL脚本
            schema_path = Path(__file__).parent / "schema.sql"
            if not schema_path.exists():
                raise DatabaseError(f"数据库架构文件不存在: {schema_path}")
            
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            
            # 执行DDL脚本
            with self.get_connection() as conn:
                # 启用外键约束
                conn.execute("PRAGMA foreign_keys = ON")
                
                # 使用executescript执行整个脚本
                conn.executescript(schema_sql)
                
                logger.info("数据库表结构创建成功")
                
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise DatabaseError(f"创建数据库失败: {e}")
    
    def _check_and_upgrade_database(self) -> None:
        """检查并升级数据库版本"""
        try:
            current_version = self.get_config_value('db_version', '0.0.0')
            target_version = '1.0.0'
            
            if current_version != target_version:
                logger.info(f"数据库版本升级: {current_version} -> {target_version}")
                # 这里可以添加具体的升级逻辑
                self.set_config_value('db_version', target_version)
                
        except Exception as e:
            logger.warning(f"数据库版本检查失败: {e}")
    
    def _verify_database_integrity(self) -> bool:
        """验证数据库完整性"""
        try:
            with self.get_connection() as conn:
                # 检查关键表是否存在
                required_tables = [
                    'sector_info', 'sector_quotes', 'stock_info', 
                    'stock_quotes', 'sector_constituents', 'system_config'
                ]
                
                cursor = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                )
                existing_tables = {row[0] for row in cursor.fetchall()}
                
                missing_tables = set(required_tables) - existing_tables
                if missing_tables:
                    logger.error(f"缺少必要的数据表: {missing_tables}")
                    return False
                
                # 执行PRAGMA integrity_check
                cursor = conn.execute("PRAGMA integrity_check")
                result = cursor.fetchone()[0]
                
                if result != 'ok':
                    logger.error(f"数据库完整性检查失败: {result}")
                    return False
                
                logger.info("数据库完整性验证通过")
                return True
                
        except Exception as e:
            logger.error(f"数据库完整性验证异常: {e}")
            return False
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接
        """
        if not self._connection_pool:
            raise DatabaseError("数据库未初始化")
        
        connection = None
        try:
            connection = self._connection_pool.get_connection()
            yield connection
        finally:
            if connection:
                self._connection_pool.return_connection(connection)
    
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Tuple]:
        """
        执行查询SQL
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            List[Tuple]: 查询结果
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(sql, params or ())
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"执行查询失败: {e}, SQL: {sql}")
            raise DatabaseError(f"查询执行失败: {e}")
    
    def execute_non_query(self, sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行非查询SQL（INSERT, UPDATE, DELETE）
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            int: 影响的行数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(sql, params or ())
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            logger.error(f"执行非查询失败: {e}, SQL: {sql}")
            raise DatabaseError(f"非查询执行失败: {e}")
    
    def execute_many(self, sql: str, params_list: List[Tuple]) -> int:
        """
        批量执行SQL
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 总影响行数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.executemany(sql, params_list)
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            logger.error(f"批量执行失败: {e}, SQL: {sql}")
            raise DatabaseError(f"批量执行失败: {e}")
    
    def query_to_dataframe(self, sql: str, params: Optional[Tuple] = None) -> pd.DataFrame:
        """
        执行查询并返回DataFrame
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            pd.DataFrame: 查询结果
        """
        try:
            with self.get_connection() as conn:
                return pd.read_sql_query(sql, conn, params=params)
                
        except Exception as e:
            logger.error(f"查询转DataFrame失败: {e}, SQL: {sql}")
            raise DatabaseError(f"查询转DataFrame失败: {e}")
    
    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        """
        获取系统配置值
        
        Args:
            key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            sql = "SELECT config_value, config_type FROM system_config WHERE config_key = ?"
            result = self.execute_query(sql, (key,))
            
            if not result:
                return default_value
            
            value, value_type = result[0]
            
            # 根据类型转换值
            if value_type == 'integer':
                return int(value)
            elif value_type == 'float':
                return float(value)
            elif value_type == 'boolean':
                return value.lower() in ('true', '1', 'yes')
            else:
                return value
                
        except Exception as e:
            logger.error(f"获取配置失败: {e}, key: {key}")
            return default_value
    
    def set_config_value(self, key: str, value: Any, value_type: str = 'string') -> bool:
        """
        设置系统配置值
        
        Args:
            key: 配置键
            value: 配置值
            value_type: 值类型
            
        Returns:
            bool: 设置是否成功
        """
        try:
            sql = """
            INSERT OR REPLACE INTO system_config 
            (config_key, config_value, config_type, update_time) 
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """
            self.execute_non_query(sql, (key, str(value), value_type))
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}, key: {key}, value: {value}")
            return False
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径，如果为None则自动生成
            
        Returns:
            bool: 备份是否成功
        """
        try:
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_dir = self.db_path.parent / 'backup'
                backup_dir.mkdir(exist_ok=True)
                backup_path = backup_dir / f'database_backup_{timestamp}.db'
            
            # 使用SQLite的备份API
            with self.get_connection() as source_conn:
                backup_conn = sqlite3.connect(str(backup_path))
                try:
                    source_conn.backup(backup_conn)
                    logger.info(f"数据库备份成功: {backup_path}")
                    return True
                finally:
                    backup_conn.close()
                    
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise DatabaseError(f"备份文件不存在: {backup_path}")
            
            # 关闭当前连接池
            if self._connection_pool:
                self._connection_pool.close_all()
            
            # 备份当前数据库
            current_backup = f"{self.db_path}.restore_backup"
            shutil.copy2(self.db_path, current_backup)
            
            try:
                # 恢复数据库
                shutil.copy2(backup_path, self.db_path)
                
                # 重新初始化连接池
                self._connection_pool = DatabaseConnectionPool(
                    str(self.db_path), 
                    self.pool_size
                )
                
                # 验证恢复的数据库
                if self._verify_database_integrity():
                    logger.info(f"数据库恢复成功: {backup_path}")
                    # 删除临时备份
                    os.remove(current_backup)
                    return True
                else:
                    raise DatabaseError("恢复的数据库完整性验证失败")
                    
            except Exception as e:
                # 恢复失败，回滚到原数据库
                shutil.copy2(current_backup, self.db_path)
                os.remove(current_backup)
                raise e
                
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def vacuum_database(self) -> bool:
        """
        清理数据库（VACUUM操作）
        
        Returns:
            bool: 清理是否成功
        """
        try:
            with self.get_connection() as conn:
                # 获取清理前的文件大小
                old_size = self.db_path.stat().st_size
                
                # 执行VACUUM
                conn.execute("VACUUM")
                
                # 获取清理后的文件大小
                new_size = self.db_path.stat().st_size
                saved_bytes = old_size - new_size
                
                logger.info(f"数据库清理完成，节省空间: {saved_bytes} 字节")
                return True
                
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            Dict[str, Any]: 数据库信息
        """
        try:
            info = {
                'db_path': str(self.db_path),
                'file_size': self.db_path.stat().st_size if self.db_path.exists() else 0,
                'initialized': self._initialized,
                'pool_size': self.pool_size
            }
            
            if self._initialized:
                with self.get_connection() as conn:
                    # 获取表数量
                    cursor = conn.execute(
                        "SELECT COUNT(*) FROM sqlite_master WHERE type='table'"
                    )
                    info['table_count'] = cursor.fetchone()[0]
                    
                    # 获取数据库版本
                    info['db_version'] = self.get_config_value('db_version', 'unknown')
                    
                    # 获取页面数量和页面大小
                    cursor = conn.execute("PRAGMA page_count")
                    info['page_count'] = cursor.fetchone()[0]
                    
                    cursor = conn.execute("PRAGMA page_size")
                    info['page_size'] = cursor.fetchone()[0]
                    
                    # 计算使用率
                    cursor = conn.execute("PRAGMA freelist_count")
                    free_pages = cursor.fetchone()[0]
                    info['usage_ratio'] = (info['page_count'] - free_pages) / info['page_count'] * 100
            
            return info
            
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {'error': str(e)}
    
    def close(self) -> None:
        """关闭数据库管理器"""
        try:
            with self._lock:
                if self._connection_pool:
                    self._connection_pool.close_all()
                    self._connection_pool = None
                
                self._initialized = False
                logger.info("数据库管理器已关闭")
                
        except Exception as e:
            logger.error(f"关闭数据库管理器失败: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self._initialized:
            self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass