"""
修复版本的数据源管理器测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from src.data_sources.manager import DataSourceManager, DataSourceStatus, LoadBalanceStrategy
from src.data_sources.base import IDataSource, DataSourceConfig, DataSourceException
from src.data_sources.data_formatter import MarketData, SectorInfo


class MockDataSource(IDataSource):
    """模拟数据源用于测试"""
    
    def __init__(self, name: str, should_fail: bool = False):
        config = DataSourceConfig(name=name)
        super().__init__(config)
        self.should_fail = should_fail
        self.call_count = 0
        
    def connect(self) -> bool:
        """连接数据源"""
        self.call_count += 1
        if self.should_fail:
            return False
        self.connected = True
        return True
        
    def disconnect(self) -> None:
        """断开数据源"""
        self.connected = False
        
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected
        
    def test_connection(self) -> bool:
        """测试连接"""
        return not self.should_fail
        
    def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, str]]:
        """获取股票列表"""
        if self.should_fail:
            raise DataSourceException("模拟获取股票列表失败")
        return [
            {"code": "000001.SZ", "name": "平安银行", "market": "SZ"},
            {"code": "600036.SH", "name": "招商银行", "market": "SH"}
        ]
        
    def get_sector_list(self, sector_type: str = "industry") -> List[SectorInfo]:
        """获取板块列表"""
        if self.should_fail:
            raise DataSourceException("模拟获取板块列表失败")
        return [SectorInfo(sector_code="BK001", sector_name="测试板块", sector_type=sector_type)]
        
    def get_sector_constituents(self, sector_code: str) -> List[str]:
        """获取板块成分股"""
        if self.should_fail:
            raise DataSourceException("模拟获取板块成分股失败")
        return ["000001.SZ", "600036.SH"]
        
    def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        if self.should_fail:
            raise DataSourceException("模拟获取交易日历失败")
        return ["2024-01-01", "2024-01-02"]
        
    def download_history_data(self, symbols: List[str], 
                            period: str = "1d",
                            start_date: Optional[str] = None) -> bool:
        """下载历史数据"""
        if self.should_fail:
            return False
        return True
        
    def get_market_data(self, symbol: str, 
                       period: str = "1d",
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None,
                       dividend_type: str = "none") -> MarketData:
        """获取市场数据"""
        if self.should_fail:
            raise DataSourceException("模拟获取市场数据失败")
        
        # 创建模拟数据
        import pandas as pd
        data = pd.DataFrame({
            'trade_date': [datetime.now().strftime('%Y-%m-%d')],
            'open': [10.0],
            'high': [11.0],
            'low': [9.0],
            'close': [10.5],
            'volume': [1000000]
        })
        
        return MarketData(
            symbol=symbol,
            data=data,
            source=self.name,
            update_time=datetime.now(),
            data_type="daily"
        )


class TestDataSourceManager:
    """数据源管理器测试类"""
    
    @pytest.fixture
    def manager(self):
        """创建数据源管理器实例"""
        return DataSourceManager()
    
    @pytest.fixture
    def mock_sources(self):
        """创建模拟数据源"""
        return [
            MockDataSource("source1"),
            MockDataSource("source2"),
            MockDataSource("source3")
        ]
    
    def test_init_default(self, manager):
        """测试默认初始化"""
        assert manager._health_check_interval == 60
        assert manager._load_balance_strategy == LoadBalanceStrategy.PRIORITY
        assert len(manager._sources) == 0
    
    def test_init_custom_params(self):
        """测试自定义参数初始化"""
        manager = DataSourceManager(
            health_check_interval=30,
            load_balance_strategy=LoadBalanceStrategy.ROUND_ROBIN
        )
        assert manager._health_check_interval == 30
        assert manager._load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN
    
    def test_register_source_success(self, manager, mock_sources):
        """测试成功注册数据源"""
        source = mock_sources[0]
        result = manager.register_source("test_source", source)
        
        assert result is True
        assert "test_source" in manager._sources
        assert manager._sources["test_source"].source == source
    
    def test_register_multiple_sources(self, manager, mock_sources):
        """测试注册多个数据源"""
        for i, source in enumerate(mock_sources):
            result = manager.register_source(f"source_{i}", source, priority=i+1)
            assert result is True
        
        assert len(manager._sources) == 3
        
        # 检查优先级排序
        available = manager.get_available_sources()
        assert len(available) == 3
    
    def test_register_duplicate_source(self, manager, mock_sources):
        """测试注册重复数据源"""
        source = mock_sources[0]
        
        # 第一次注册成功
        result1 = manager.register_source("test_source", source)
        assert result1 is True
        
        # 第二次注册相同名称会覆盖（根据实际实现）
        result2 = manager.register_source("test_source", source)
        assert result2 is True  # 允许覆盖
    
    def test_unregister_source_success(self, manager, mock_sources):
        """测试成功注销数据源"""
        source = mock_sources[0]
        manager.register_source("test_source", source)
        
        result = manager.unregister_source("test_source")
        assert result is True
        assert "test_source" not in manager._sources
    
    def test_unregister_nonexistent_source(self, manager):
        """测试注销不存在的数据源"""
        result = manager.unregister_source("nonexistent")
        assert result is False
    
    def test_get_available_sources(self, manager, mock_sources):
        """测试获取可用数据源列表"""
        # 注册数据源
        for i, source in enumerate(mock_sources):
            manager.register_source(f"source_{i}", source)
        
        available = manager.get_available_sources()
        assert len(available) == 3
        assert "source_0" in available
        assert "source_1" in available
        assert "source_2" in available
    
    def test_get_source_by_name(self, manager, mock_sources):
        """测试根据名称获取数据源"""
        source = mock_sources[0]
        manager.register_source("test_source", source)
        
        retrieved = manager.get_source_by_name("test_source")
        assert retrieved == source
        
        # 测试不存在的数据源
        nonexistent = manager.get_source_by_name("nonexistent")
        assert nonexistent is None
    
    def test_get_best_source_priority(self, manager, mock_sources):
        """测试按优先级获取最佳数据源"""
        # 注册不同优先级的数据源
        manager.register_source("low_priority", mock_sources[0], priority=1)
        manager.register_source("high_priority", mock_sources[1], priority=5)
        manager.register_source("medium_priority", mock_sources[2], priority=3)
        
        best = manager.get_best_source()
        assert best == mock_sources[1]  # 应该是高优先级的
    
    def test_get_best_source_no_available(self, manager):
        """测试没有可用数据源时获取最佳数据源"""
        best = manager.get_best_source()
        assert best is None
    
    def test_execute_with_failover_success(self, manager, mock_sources):
        """测试故障转移成功执行"""
        manager.register_source("test_source", mock_sources[0])
        
        def test_operation(source):
            return source.get_stock_list()
        
        result = manager.execute_with_failover(test_operation)
        assert len(result) == 2
        assert result[0]["code"] == "000001.SZ"
        assert result[1]["code"] == "600036.SH"
    
    def test_execute_with_failover_all_fail(self, manager):
        """测试所有数据源都失败的故障转移"""
        failing_source = MockDataSource("failing", should_fail=True)
        manager.register_source("failing_source", failing_source)
        
        def test_operation(source):
            return source.get_stock_list()
        
        with pytest.raises(DataSourceException):
            manager.execute_with_failover(test_operation)
    
    def test_get_market_data_with_failover(self, manager, mock_sources):
        """测试故障转移获取市场数据"""
        manager.register_source("test_source", mock_sources[0])
        
        result = manager.get_market_data_with_failover("000001.SZ")
        assert result.symbol == "000001.SZ"
        assert result.data.iloc[0]['close'] == 10.5
    
    def test_get_source_stats(self, manager, mock_sources):
        """测试获取数据源统计信息"""
        manager.register_source("test_source", mock_sources[0])
        
        stats = manager.get_source_stats("test_source")
        assert stats is not None
        assert stats.total_requests == 0  # 初始状态
    
    def test_get_source_stats_nonexistent(self, manager):
        """测试获取不存在数据源的统计信息"""
        stats = manager.get_source_stats("nonexistent")
        assert stats is None
    
    def test_get_all_stats(self, manager, mock_sources):
        """测试获取所有数据源统计信息"""
        for i, source in enumerate(mock_sources):
            manager.register_source(f"source_{i}", source)
        
        all_stats = manager.get_all_stats()
        assert len(all_stats) == 3
        assert "source_0" in all_stats
        assert "source_1" in all_stats
        assert "source_2" in all_stats
    
    def test_health_check_single_source(self, manager, mock_sources):
        """测试单个数据源健康检查"""
        manager.register_source("test_source", mock_sources[0])
        
        result = manager.health_check("test_source")
        assert "test_source" in result
        assert result["test_source"] is True
    
    def test_health_check_all_sources(self, manager, mock_sources):
        """测试所有数据源健康检查"""
        for i, source in enumerate(mock_sources):
            manager.register_source(f"source_{i}", source)
        
        result = manager.health_check()
        assert len(result) == 3
        assert all(status for status in result.values())
    
    def test_shutdown(self, manager, mock_sources):
        """测试关闭管理器"""
        for i, source in enumerate(mock_sources):
            manager.register_source(f"source_{i}", source)
        
        manager.shutdown()
        
        # 检查健康检查是否停止
        assert manager._stop_health_check is True
        
        # 检查所有数据源是否断开
        for source in mock_sources:
            assert not source.is_connected()
    
    def test_context_manager(self, mock_sources):
        """测试上下文管理器"""
        with DataSourceManager() as manager:
            for i, source in enumerate(mock_sources):
                manager.register_source(f"source_{i}", source)
            
            # 在上下文中正常工作
            assert len(manager._sources) == 3
        
        # 退出上下文后应该自动关闭
        assert manager._stop_health_check is True 