"""
缓存管理系统

提供内存缓存、磁盘缓存、过期管理等功能
"""

import time
import pickle
import hashlib
import threading
from typing import Any, Optional, Dict, List, Callable, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import OrderedDict
import json

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: float
    expires_at: Optional[float] = None
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    size_bytes: int = 0


class LRUCache:
    """LRU内存缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: Optional[int] = None):
        """
        初始化LRU缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 生存时间（秒），None表示永不过期
        """
        self.max_size = max_size
        self.ttl = ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_usage': 0
        }
        
        logger.info(f"LRU缓存初始化完成: max_size={max_size}, ttl={ttl}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return default
            
            entry = self._cache[key]
            
            # 检查过期
            if self._is_expired(entry):
                del self._cache[key]
                self._stats['misses'] += 1
                return default
            
            # 更新访问信息
            entry.access_count += 1
            entry.last_accessed = time.time()
            
            # 移动到末尾（最近使用）
            self._cache.move_to_end(key)
            
            self._stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），覆盖默认TTL
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                now = time.time()
                
                # 计算过期时间
                expires_at = None
                if ttl is not None:
                    expires_at = now + ttl
                elif self.ttl is not None:
                    expires_at = now + self.ttl
                
                # 估算大小
                size_bytes = self._estimate_size(value)
                
                # 创建缓存条目
                entry = CacheEntry(
                    value=value,
                    created_at=now,
                    expires_at=expires_at,
                    size_bytes=size_bytes
                )
                
                # 如果键已存在，更新统计
                if key in self._cache:
                    old_entry = self._cache[key]
                    self._stats['memory_usage'] -= old_entry.size_bytes
                
                # 添加/更新缓存
                self._cache[key] = entry
                self._cache.move_to_end(key)
                self._stats['memory_usage'] += size_bytes
                
                # 检查容量限制
                self._evict_if_needed()
                
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, 错误: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        with self._lock:
            if key in self._cache:
                entry = self._cache[key]
                self._stats['memory_usage'] -= entry.size_bytes
                del self._cache[key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats['memory_usage'] = 0
            logger.info("缓存已清空")
    
    def cleanup_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的条目数
        """
        count = 0
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if self._is_expired(entry):
                    expired_keys.append(key)
            
            for key in expired_keys:
                entry = self._cache[key]
                self._stats['memory_usage'] -= entry.size_bytes
                del self._cache[key]
                count += 1
        
        if count > 0:
            logger.info(f"清理了 {count} 个过期缓存项")
        
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': round(hit_rate, 2),
                'evictions': self._stats['evictions'],
                'memory_usage_bytes': self._stats['memory_usage'],
                'memory_usage_mb': round(self._stats['memory_usage'] / (1024 * 1024), 2)
            }
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存项是否过期"""
        if entry.expires_at is None:
            return False
        return time.time() > entry.expires_at
    
    def _estimate_size(self, value: Any) -> int:
        """估算对象大小"""
        try:
            return len(pickle.dumps(value))
        except Exception:
            # 如果无法序列化，返回估算值
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8
            elif isinstance(value, (list, tuple)):
                return sum(self._estimate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) 
                          for k, v in value.items())
            else:
                return 100  # 默认估算值
    
    def _evict_if_needed(self):
        """根据需要驱逐缓存项"""
        while len(self._cache) > self.max_size:
            # 删除最久未使用的项
            key, entry = self._cache.popitem(last=False)
            self._stats['memory_usage'] -= entry.size_bytes
            self._stats['evictions'] += 1


class DiskCache:
    """磁盘缓存"""
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 100):
        """
        初始化磁盘缓存
        
        Args:
            cache_dir: 缓存目录
            max_size_mb: 最大缓存大小（MB）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self._lock = threading.RLock()
        
        # 索引文件
        self.index_file = self.cache_dir / "cache_index.json"
        self._index: Dict[str, Dict[str, Any]] = {}
        self._load_index()
        
        logger.info(f"磁盘缓存初始化完成: {cache_dir}, 最大大小: {max_size_mb}MB")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        try:
            with self._lock:
                key_hash = self._hash_key(key)
                
                if key_hash not in self._index:
                    return default
                
                entry_info = self._index[key_hash]
                
                # 检查过期
                if self._is_expired_entry(entry_info):
                    self._remove_entry(key_hash)
                    return default
                
                # 读取文件
                cache_file = self.cache_dir / f"{key_hash}.cache"
                if not cache_file.exists():
                    self._remove_entry(key_hash)
                    return default
                
                with open(cache_file, 'rb') as f:
                    value = pickle.load(f)
                
                # 更新访问时间
                entry_info['last_accessed'] = time.time()
                entry_info['access_count'] += 1
                self._save_index()
                
                return value
                
        except Exception as e:
            logger.error(f"获取磁盘缓存失败: {key}, 错误: {e}")
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                key_hash = self._hash_key(key)
                cache_file = self.cache_dir / f"{key_hash}.cache"
                
                # 序列化并保存
                with open(cache_file, 'wb') as f:
                    pickle.dump(value, f)
                
                # 获取文件大小
                file_size = cache_file.stat().st_size
                
                # 更新索引
                now = time.time()
                expires_at = (now + ttl) if ttl else None
                
                self._index[key_hash] = {
                    'key': key,
                    'created_at': now,
                    'expires_at': expires_at,
                    'size_bytes': file_size,
                    'access_count': 1,
                    'last_accessed': now
                }
                
                self._save_index()
                
                # 检查磁盘空间
                self._cleanup_if_needed()
                
                return True
                
        except Exception as e:
            logger.error(f"设置磁盘缓存失败: {key}, 错误: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            with self._lock:
                key_hash = self._hash_key(key)
                return self._remove_entry(key_hash)
        except Exception as e:
            logger.error(f"删除磁盘缓存失败: {key}, 错误: {e}")
            return False
    
    def clear(self):
        """清空缓存"""
        try:
            with self._lock:
                # 删除所有缓存文件
                for cache_file in self.cache_dir.glob("*.cache"):
                    cache_file.unlink()
                
                # 清空索引
                self._index.clear()
                self._save_index()
                
                logger.info("磁盘缓存已清空")
        except Exception as e:
            logger.error(f"清空磁盘缓存失败: {e}")
    
    def cleanup_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的条目数
        """
        count = 0
        try:
            with self._lock:
                expired_hashes = []
                for key_hash, entry_info in self._index.items():
                    if self._is_expired_entry(entry_info):
                        expired_hashes.append(key_hash)
                
                for key_hash in expired_hashes:
                    if self._remove_entry(key_hash):
                        count += 1
                
                self._save_index()
        except Exception as e:
            logger.error(f"清理过期磁盘缓存失败: {e}")
        
        if count > 0:
            logger.info(f"清理了 {count} 个过期磁盘缓存项")
        
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_size = sum(entry['size_bytes'] for entry in self._index.values())
            
            return {
                'size': len(self._index),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'max_size_mb': round(self.max_size_bytes / (1024 * 1024), 2),
                'cache_dir': str(self.cache_dir)
            }
    
    def _hash_key(self, key: str) -> str:
        """生成键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _is_expired_entry(self, entry_info: Dict[str, Any]) -> bool:
        """检查缓存项是否过期"""
        expires_at = entry_info.get('expires_at')
        if expires_at is None:
            return False
        return time.time() > expires_at
    
    def _remove_entry(self, key_hash: str) -> bool:
        """删除缓存条目"""
        try:
            if key_hash in self._index:
                # 删除文件
                cache_file = self.cache_dir / f"{key_hash}.cache"
                if cache_file.exists():
                    cache_file.unlink()
                
                # 删除索引
                del self._index[key_hash]
                return True
        except Exception as e:
            logger.error(f"删除缓存条目失败: {key_hash}, 错误: {e}")
        
        return False
    
    def _load_index(self):
        """加载索引文件"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    self._index = json.load(f)
        except Exception as e:
            logger.error(f"加载缓存索引失败: {e}")
            self._index = {}
    
    def _save_index(self):
        """保存索引文件"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self._index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
    
    def _cleanup_if_needed(self):
        """根据需要清理缓存"""
        total_size = sum(entry['size_bytes'] for entry in self._index.values())
        
        if total_size > self.max_size_bytes:
            # 按最后访问时间排序
            entries = list(self._index.items())
            entries.sort(key=lambda x: x[1]['last_accessed'])
            
            # 删除最久未访问的项
            while total_size > self.max_size_bytes and entries:
                key_hash, entry_info = entries.pop(0)
                if self._remove_entry(key_hash):
                    total_size -= entry_info['size_bytes']


class CacheManager:
    """综合缓存管理器"""
    
    def __init__(self, memory_cache_size: int = 1000, 
                 disk_cache_size_mb: int = 100,
                 default_ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            memory_cache_size: 内存缓存大小
            disk_cache_size_mb: 磁盘缓存大小（MB）
            default_ttl: 默认TTL（秒）
        """
        self.default_ttl = default_ttl
        self.memory_cache = LRUCache(memory_cache_size, default_ttl)
        self.disk_cache = DiskCache("cache", disk_cache_size_mb)
        self._cleanup_thread = None
        self._cleanup_interval = 300  # 5分钟
        self._running = True
        
        self._start_cleanup_thread()
        logger.info("缓存管理器初始化完成")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值（先查内存，再查磁盘）
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        # 先查内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 再查磁盘缓存
        value = self.disk_cache.get(key)
        if value is not None:
            # 将热点数据加载到内存
            self.memory_cache.set(key, value)
            return value
        
        return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
           memory_only: bool = False) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
            memory_only: 是否仅存储到内存
            
        Returns:
            是否设置成功
        """
        success = True
        
        # 设置内存缓存
        if not self.memory_cache.set(key, value, ttl):
            success = False
        
        # 设置磁盘缓存（除非指定仅内存）
        if not memory_only:
            if not self.disk_cache.set(key, value, ttl):
                success = False
        
        return success
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        memory_success = self.memory_cache.delete(key)
        disk_success = self.disk_cache.delete(key)
        return memory_success or disk_success
    
    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.disk_cache.clear()
        logger.info("所有缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': self.memory_cache.get_stats(),
            'disk_cache': self.disk_cache.get_stats()
        }
    
    def cleanup_expired(self) -> Dict[str, int]:
        """
        清理过期缓存
        
        Returns:
            清理统计
        """
        memory_cleaned = self.memory_cache.cleanup_expired()
        disk_cleaned = self.disk_cache.cleanup_expired()
        
        return {
            'memory_cleaned': memory_cleaned,
            'disk_cleaned': disk_cleaned,
            'total_cleaned': memory_cleaned + disk_cleaned
        }
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while self._running:
                try:
                    time.sleep(self._cleanup_interval)
                    if self._running:
                        self.cleanup_expired()
                except Exception as e:
                    logger.error(f"缓存清理线程出错: {e}")
        
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        logger.info("缓存清理线程已启动")
    
    def close(self):
        """关闭缓存管理器"""
        self._running = False
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
        logger.info("缓存管理器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 装饰器：缓存函数结果
def cached(ttl: Optional[int] = None, memory_only: bool = False):
    """
    缓存函数结果的装饰器
    
    Args:
        ttl: 生存时间（秒）
        memory_only: 是否仅使用内存缓存
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__module__}.{func.__name__}:{hashlib.md5(str(args).encode() + str(kwargs).encode()).hexdigest()}"
            
            # 尝试从缓存获取
            if hasattr(wrapper, '_cache_manager'):
                result = wrapper._cache_manager.get(cache_key)
                if result is not None:
                    return result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            if hasattr(wrapper, '_cache_manager'):
                wrapper._cache_manager.set(cache_key, result, ttl, memory_only)
            
            return result
        
        # 添加缓存管理器
        wrapper._cache_manager = CacheManager()
        
        return wrapper
    return decorator