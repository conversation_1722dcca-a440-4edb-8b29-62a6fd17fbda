"""
相对强弱计算引擎测试用例

测试相对强弱引擎的核心功能：
- RS值计算
- 多时间周期分析
- 股票排名系统
- RS趋势分析
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.engines.relative_strength import (
    RelativeStrengthEngine, TimeFrame, BenchmarkType,
    RSResult, RSAnalysis
)
from src.utils.exceptions import CalculationError


class TestRelativeStrengthEngine:
    """相对强弱引擎基础测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = RelativeStrengthEngine()
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=300, freq='D')
        
        # 股票数据 - 上涨趋势
        stock_prices = [100 * (1.001 ** i) for i in range(300)]
        self.stock_data = pd.DataFrame({
            'open': stock_prices,
            'high': [p * 1.01 for p in stock_prices],
            'low': [p * 0.99 for p in stock_prices],
            'close': stock_prices,
            'volume': np.random.randint(1000000, 5000000, 300)
        }, index=dates)
        
        # 基准数据 - 平稳趋势
        benchmark_prices = [100 * (1.0005 ** i) for i in range(300)]
        self.benchmark_data = pd.DataFrame({
            'open': benchmark_prices,
            'high': [p * 1.005 for p in benchmark_prices],
            'low': [p * 0.995 for p in benchmark_prices],
            'close': benchmark_prices,
            'volume': np.random.randint(5000000, 10000000, 300)
        }, index=dates)
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        engine = RelativeStrengthEngine(
            default_periods=[30, 60, 120],
            min_data_points=100,
            smoothing_period=5
        )
        
        assert engine.default_periods == [30, 60, 120]
        assert engine.min_data_points == 100
        assert engine.smoothing_period == 5
    
    def test_calculate_rs_value_success(self):
        """测试成功计算RS值"""
        rs_value = self.engine.calculate_rs_value(
            self.stock_data, self.benchmark_data, period=252
        )
        
        assert isinstance(rs_value, float)
        assert rs_value > 0
        # 由于股票涨幅大于基准，RS值应该大于1
        assert rs_value > 1.0
    
    def test_calculate_rs_value_insufficient_data(self):
        """测试数据不足的RS计算"""
        small_stock_data = self.stock_data.head(10)
        small_benchmark_data = self.benchmark_data.head(10)
        
        with pytest.raises(CalculationError):
            self.engine.calculate_rs_value(
                small_stock_data, small_benchmark_data, period=252
            )
    
    def test_calculate_rs_value_equal_performance(self):
        """测试相等表现的RS计算"""
        # 创建相同表现的数据
        equal_data = self.benchmark_data.copy()
        
        rs_value = self.engine.calculate_rs_value(
            equal_data, self.benchmark_data, period=100
        )
        
        # 相等表现的RS值应该接近1
        assert 0.99 <= rs_value <= 1.01
    
    def test_calculate_multi_timeframe_rs(self):
        """测试多时间周期RS计算"""
        rs_results = self.engine.calculate_multi_timeframe_rs(
            self.stock_data, self.benchmark_data
        )
        
        assert isinstance(rs_results, dict)
        
        # 检查各时间周期
        expected_timeframes = [TimeFrame.DAILY, TimeFrame.WEEKLY, 
                             TimeFrame.MONTHLY, TimeFrame.QUARTERLY]
        
        for timeframe in expected_timeframes:
            if timeframe in rs_results:
                assert isinstance(rs_results[timeframe], float)
                assert rs_results[timeframe] > 0
    
    def test_rank_stocks_by_rs(self):
        """测试股票RS排名"""
        # 创建多只股票的数据
        stocks_data = {}
        
        # 强势股票
        strong_stock = self.stock_data.copy()
        strong_stock['close'] = strong_stock['close'] * 1.2
        stocks_data['STRONG'] = strong_stock
        
        # 弱势股票
        weak_stock = self.stock_data.copy()
        weak_stock['close'] = weak_stock['close'] * 0.8
        stocks_data['WEAK'] = weak_stock
        
        # 中性股票
        stocks_data['NEUTRAL'] = self.stock_data.copy()
        
        results = self.engine.rank_stocks_by_rs(
            stocks_data, self.benchmark_data, period=100
        )
        
        assert len(results) == 3
        
        # 检查排名顺序
        assert results[0].symbol == 'STRONG'
        assert results[0].rs_rank == 1
        assert results[0].rs_percentile > results[1].rs_percentile
        
        # 检查结果结构
        for result in results:
            assert isinstance(result, RSResult)
            assert result.rs_value > 0
            assert 1 <= result.rs_rank <= 3
            assert 0 <= result.rs_percentile <= 100
    
    def test_analyze_rs_trend(self):
        """测试RS趋势分析"""
        analysis = self.engine.analyze_rs_trend(
            self.stock_data, self.benchmark_data, lookback_period=60
        )
        
        assert isinstance(analysis, RSAnalysis)
        assert analysis.current_rs > 0
        assert analysis.rs_trend in ["strengthening", "weakening", "stable"]
        assert isinstance(analysis.rs_history, pd.Series)
        assert isinstance(analysis.multi_timeframe_rs, dict)
        assert isinstance(analysis.ranking_info, dict)
    
    def test_select_dynamic_benchmark(self):
        """测试动态基准选择"""
        available_benchmarks = {
            'market_index': self.benchmark_data,
            'sector_index': self.benchmark_data.copy(),
            'custom_basket': self.benchmark_data.copy()
        }
        
        selected = self.engine.select_dynamic_benchmark(
            'TEST_STOCK', available_benchmarks
        )
        
        assert selected in available_benchmarks.keys()
        # 应该优先选择sector_index
        assert selected == 'sector_index'


class TestRSCalculationAccuracy:
    """RS计算准确性测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = RelativeStrengthEngine()
    
    def create_test_data(self, stock_return: float, benchmark_return: float, periods: int = 100):
        """创建指定收益率的测试数据"""
        dates = pd.date_range(start='2023-01-01', periods=periods, freq='D')
        
        # 股票数据
        stock_prices = [100]
        daily_stock_return = (1 + stock_return) ** (1/periods) - 1
        for i in range(1, periods):
            stock_prices.append(stock_prices[-1] * (1 + daily_stock_return))
        
        stock_data = pd.DataFrame({
            'open': stock_prices,
            'high': stock_prices,
            'low': stock_prices,
            'close': stock_prices,
            'volume': [1000000] * periods
        }, index=dates)
        
        # 基准数据
        benchmark_prices = [100]
        daily_benchmark_return = (1 + benchmark_return) ** (1/periods) - 1
        for i in range(1, periods):
            benchmark_prices.append(benchmark_prices[-1] * (1 + daily_benchmark_return))
        
        benchmark_data = pd.DataFrame({
            'open': benchmark_prices,
            'high': benchmark_prices,
            'low': benchmark_prices,
            'close': benchmark_prices,
            'volume': [5000000] * periods
        }, index=dates)
        
        return stock_data, benchmark_data
    
    def test_rs_calculation_outperformance(self):
        """测试超越基准的RS计算"""
        stock_data, benchmark_data = self.create_test_data(0.20, 0.10)  # 20% vs 10%
        
        rs_value = self.engine.calculate_rs_value(stock_data, benchmark_data, period=100)
        
        # RS = (1 + 0.20) / (1 + 0.10) = 1.2 / 1.1 ≈ 1.09
        expected_rs = 1.2 / 1.1
        assert abs(rs_value - expected_rs) < 0.01
    
    def test_rs_calculation_underperformance(self):
        """测试落后基准的RS计算"""
        stock_data, benchmark_data = self.create_test_data(0.05, 0.15)  # 5% vs 15%
        
        rs_value = self.engine.calculate_rs_value(stock_data, benchmark_data, period=100)
        
        # RS = (1 + 0.05) / (1 + 0.15) = 1.05 / 1.15 ≈ 0.91
        expected_rs = 1.05 / 1.15
        assert abs(rs_value - expected_rs) < 0.01
    
    def test_rs_calculation_negative_returns(self):
        """测试负收益的RS计算"""
        stock_data, benchmark_data = self.create_test_data(-0.10, -0.20)  # -10% vs -20%
        
        rs_value = self.engine.calculate_rs_value(stock_data, benchmark_data, period=100)
        
        # RS = (1 - 0.10) / (1 - 0.20) = 0.9 / 0.8 = 1.125
        expected_rs = 0.9 / 0.8
        assert abs(rs_value - expected_rs) < 0.01


class TestMomentumAndTrendCalculation:
    """动量和趋势计算测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = RelativeStrengthEngine()
    
    def test_momentum_score_calculation(self):
        """测试动量分数计算"""
        # 创建上涨趋势数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        prices = [100 * (1.01 ** i) for i in range(100)]
        volumes = [1000000 * (1.005 ** i) for i in range(100)]
        
        data = pd.DataFrame({
            'open': prices,
            'high': prices,
            'low': prices,
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        momentum_score = self.engine._calculate_momentum_score(data, 50)
        
        # 上涨趋势应该有正的动量分数
        assert momentum_score > 0
    
    def test_trend_strength_calculation(self):
        """测试趋势强度计算"""
        # 创建强趋势数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        prices = [100 + i * 0.5 for i in range(100)]  # 线性上涨
        
        data = pd.DataFrame({
            'open': prices,
            'high': prices,
            'low': prices,
            'close': prices,
            'volume': [1000000] * 100
        }, index=dates)
        
        trend_strength = self.engine._calculate_trend_strength(data, 50)
        
        # 强趋势应该有较高的趋势强度
        assert 0 <= trend_strength <= 1
        assert trend_strength > 0.01  # 应该检测到趋势


class TestRSHistoryAndTrend:
    """RS历史和趋势测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = RelativeStrengthEngine()
    
    def test_rs_history_calculation(self):
        """测试RS历史计算"""
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        
        # 创建逐渐改善的相对表现
        stock_prices = [100]
        benchmark_prices = [100]
        
        for i in range(1, 100):
            stock_prices.append(stock_prices[-1] * (1 + 0.001 * i / 50))  # 加速上涨
            benchmark_prices.append(benchmark_prices[-1] * 1.001)  # 稳定上涨
        
        stock_data = pd.DataFrame({
            'open': stock_prices, 'high': stock_prices,
            'low': stock_prices, 'close': stock_prices,
            'volume': [1000000] * 100
        }, index=dates)
        
        benchmark_data = pd.DataFrame({
            'open': benchmark_prices, 'high': benchmark_prices,
            'low': benchmark_prices, 'close': benchmark_prices,
            'volume': [5000000] * 100
        }, index=dates)
        
        rs_history = self.engine._calculate_rs_history(
            stock_data, benchmark_data, lookback_period=60
        )
        
        assert isinstance(rs_history, pd.Series)
        assert len(rs_history) > 0
        
        # RS应该呈上升趋势
        if len(rs_history) > 10:
            recent_avg = rs_history.tail(10).mean()
            early_avg = rs_history.head(10).mean()
            assert recent_avg > early_avg
    
    def test_rs_trend_direction_analysis(self):
        """测试RS趋势方向分析"""
        # 创建上升趋势的RS序列
        dates = pd.date_range(start='2023-01-01', periods=20, freq='D')
        rs_values = [1.0 + i * 0.02 for i in range(20)]  # 从1.0上升到1.38，更明显的趋势
        rs_series = pd.Series(rs_values, index=dates)

        trend = self.engine._analyze_rs_trend_direction(rs_series)
        assert trend == "strengthening"
        
        # 创建下降趋势的RS序列
        rs_values_down = [1.2 - i * 0.01 for i in range(20)]  # 从1.2下降到1.01
        rs_series_down = pd.Series(rs_values_down, index=dates)
        
        trend_down = self.engine._analyze_rs_trend_direction(rs_series_down)
        assert trend_down == "weakening"
        
        # 创建稳定的RS序列
        rs_values_stable = [1.0 + np.random.normal(0, 0.005) for _ in range(20)]
        rs_series_stable = pd.Series(rs_values_stable, index=dates)
        
        trend_stable = self.engine._analyze_rs_trend_direction(rs_series_stable)
        assert trend_stable == "stable"


if __name__ == '__main__':
    pytest.main([__file__])
