#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式标准化工具
统一不同数据源的数据格式，提供数据验证、清洗、转换功能
"""

from typing import Dict, List, Optional, Any, Union, Tuple
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from datetime import datetime, date
import warnings
from enum import Enum

from .base import MarketData, SectorInfo, DataException
from ..utils.logger import get_logger

logger = get_logger(__name__)

# 抑制pandas警告
warnings.filterwarnings('ignore', category=FutureWarning)


class DataQuality(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"  # 优秀
    GOOD = "good"           # 良好
    FAIR = "fair"           # 一般
    POOR = "poor"           # 较差
    INVALID = "invalid"     # 无效


@dataclass
class DataQualityReport:
    """数据质量报告"""
    symbol: str
    total_records: int = 0
    valid_records: int = 0
    missing_values: Dict[str, int] = field(default_factory=dict)
    duplicate_records: int = 0
    outliers: Dict[str, int] = field(default_factory=dict)
    data_quality: DataQuality = DataQuality.INVALID
    quality_score: float = 0.0
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    @property
    def completeness_rate(self) -> float:
        """数据完整性比率"""
        if self.total_records == 0:
            return 0.0
        return self.valid_records / self.total_records
    
    @property
    def duplicate_rate(self) -> float:
        """重复数据比率"""
        if self.total_records == 0:
            return 0.0
        return self.duplicate_records / self.total_records


class DataFormatter:
    """数据格式标准化工具"""
    
    # 标准列名映射
    STANDARD_COLUMNS = {
        'market_data': ['trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount'],
        'stock_info': ['symbol', 'name', 'market', 'list_date', 'industry', 'sector'],
        'sector_info': ['sector_code', 'sector_name', 'sector_type', 'constituents']
    }
    
    # 不同数据源的列名映射
    COLUMN_MAPPINGS = {
        'xtdata': {
            'time': 'trade_date',
            'datetime': 'trade_date',
            'date': 'trade_date',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'vol': 'volume',
            'volume': 'volume',
            'amount': 'amount',
            'turnover': 'amount'
        },
        'tushare': {
            'ts_code': 'symbol',
            'trade_date': 'trade_date',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'vol': 'volume',
            'amount': 'amount'
        },
        'akshare': {
            '日期': 'trade_date',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }
    }
    
    def __init__(self, 
                 enable_data_cleaning: bool = True,
                 enable_outlier_detection: bool = True,
                 outlier_threshold: float = 3.0):
        """
        初始化数据格式化工具
        
        Args:
            enable_data_cleaning: 是否启用数据清洗
            enable_outlier_detection: 是否启用异常值检测
            outlier_threshold: 异常值检测阈值（标准差倍数）
        """
        self.enable_data_cleaning = enable_data_cleaning
        self.enable_outlier_detection = enable_outlier_detection
        self.outlier_threshold = outlier_threshold
        
        logger.info(f"数据格式化工具初始化完成，数据清洗: {enable_data_cleaning}, 异常值检测: {enable_outlier_detection}")
    
    def format_market_data(self, 
                          data: pd.DataFrame, 
                          source_type: str = 'xtdata',
                          symbol: str = '',
                          validate: bool = True) -> Tuple[pd.DataFrame, DataQualityReport]:
        """
        格式化市场数据
        
        Args:
            data: 原始数据
            source_type: 数据源类型
            symbol: 股票代码
            validate: 是否进行数据验证
            
        Returns:
            Tuple[pd.DataFrame, DataQualityReport]: 格式化后的数据和质量报告
        """
        if data.empty:
            logger.warning(f"数据为空: {symbol}")
            return pd.DataFrame(columns=self.STANDARD_COLUMNS['market_data']), \
                   DataQualityReport(symbol=symbol, data_quality=DataQuality.INVALID)
        
        try:
            logger.debug(f"开始格式化市场数据: {symbol}, 源类型: {source_type}, 原始行数: {len(data)}")
            
            # 1. 列名映射
            formatted_data = self._map_columns(data.copy(), source_type, 'market_data')
            
            # 2. 数据类型转换
            formatted_data = self._convert_data_types(formatted_data, 'market_data')
            
            # 3. 数据清洗
            if self.enable_data_cleaning:
                formatted_data = self._clean_market_data(formatted_data)
            
            # 4. 数据验证和质量评估
            quality_report = None
            if validate:
                quality_report = self._validate_market_data(formatted_data, symbol)
            else:
                quality_report = DataQualityReport(symbol=symbol, data_quality=DataQuality.GOOD)
            
            # 5. 确保包含所有标准列
            formatted_data = self._ensure_standard_columns(formatted_data, 'market_data')
            
            # 6. 排序和去重
            formatted_data = self._sort_and_deduplicate(formatted_data, 'trade_date')
            
            logger.debug(f"市场数据格式化完成: {symbol}, 处理后行数: {len(formatted_data)}")
            
            return formatted_data, quality_report
            
        except Exception as e:
            logger.error(f"格式化市场数据失败: {symbol}, 错误: {e}")
            raise DataException(f"数据格式化失败: {e}") from e
    
    def format_stock_list(self, 
                         data: List[Dict[str, Any]], 
                         source_type: str = 'xtdata') -> pd.DataFrame:
        """
        格式化股票列表数据
        
        Args:
            data: 原始股票列表数据
            source_type: 数据源类型
            
        Returns:
            pd.DataFrame: 格式化后的股票列表
        """
        try:
            if not data:
                logger.warning("股票列表数据为空")
                return pd.DataFrame(columns=self.STANDARD_COLUMNS['stock_info'])
            
            logger.debug(f"开始格式化股票列表: 源类型: {source_type}, 原始数量: {len(data)}")
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 列名映射
            formatted_data = self._map_columns(df, source_type, 'stock_info')
            
            # 数据类型转换
            formatted_data = self._convert_data_types(formatted_data, 'stock_info')
            
            # 确保包含所有标准列
            formatted_data = self._ensure_standard_columns(formatted_data, 'stock_info')
            
            # 数据清洗
            if self.enable_data_cleaning:
                formatted_data = self._clean_stock_data(formatted_data)
            
            logger.debug(f"股票列表格式化完成: 处理后数量: {len(formatted_data)}")
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"格式化股票列表失败: {e}")
            raise DataException(f"股票列表格式化失败: {e}") from e
    
    def format_sector_info(self, 
                          sectors: List[SectorInfo], 
                          source_type: str = 'xtdata') -> pd.DataFrame:
        """
        格式化板块信息数据
        
        Args:
            sectors: 板块信息列表
            source_type: 数据源类型
            
        Returns:
            pd.DataFrame: 格式化后的板块信息
        """
        try:
            if not sectors:
                logger.warning("板块信息数据为空")
                return pd.DataFrame(columns=self.STANDARD_COLUMNS['sector_info'])
            
            logger.debug(f"开始格式化板块信息: 源类型: {source_type}, 原始数量: {len(sectors)}")
            
            # 转换为字典列表
            data = []
            for sector in sectors:
                data.append({
                    'sector_code': sector.sector_code,
                    'sector_name': sector.sector_name,
                    'sector_type': sector.sector_type,
                    'constituents': len(sector.constituents) if sector.constituents else 0,
                    'update_time': sector.update_time
                })
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 确保包含所有标准列
            formatted_data = self._ensure_standard_columns(df, 'sector_info')
            
            logger.debug(f"板块信息格式化完成: 处理后数量: {len(formatted_data)}")
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"格式化板块信息失败: {e}")
            raise DataException(f"板块信息格式化失败: {e}") from e
    
    def _map_columns(self, 
                    data: pd.DataFrame, 
                    source_type: str, 
                    data_type: str) -> pd.DataFrame:
        """映射列名到标准格式"""
        if source_type not in self.COLUMN_MAPPINGS:
            logger.warning(f"未知的数据源类型: {source_type}")
            return data
        
        mapping = self.COLUMN_MAPPINGS[source_type]
        
        # 执行列名映射
        renamed_data = data.rename(columns=mapping)
        
        logger.debug(f"列名映射完成: {source_type}, 原始列: {list(data.columns)}, 映射后列: {list(renamed_data.columns)}")
        
        return renamed_data
    
    def _convert_data_types(self, 
                           data: pd.DataFrame, 
                           data_type: str) -> pd.DataFrame:
        """转换数据类型"""
        try:
            if data_type == 'market_data':
                # 处理日期列
                if 'trade_date' in data.columns:
                    data['trade_date'] = pd.to_datetime(data['trade_date'], errors='coerce')
                
                # 处理数值列
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
                for col in numeric_columns:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')
            
            elif data_type == 'stock_info':
                # 处理字符串列
                string_columns = ['symbol', 'name', 'market', 'industry', 'sector']
                for col in string_columns:
                    if col in data.columns:
                        data[col] = data[col].astype(str)
                
                # 处理日期列
                if 'list_date' in data.columns:
                    data['list_date'] = pd.to_datetime(data['list_date'], errors='coerce')
            
            return data
            
        except Exception as e:
            logger.warning(f"数据类型转换失败: {e}")
            return data
    
    def _clean_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗市场数据"""
        try:
            original_rows = len(data)
            
            # 1. 删除空行
            data = data.dropna(how='all')
            
            # 2. 删除关键字段为空的行
            if 'trade_date' in data.columns:
                data = data.dropna(subset=['trade_date'])
            
            # 3. 删除价格为0或负数的行
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in data.columns:
                    data = data[data[col] > 0]
            
            # 4. 检查价格逻辑关系
            if all(col in data.columns for col in price_columns):
                # high >= max(open, close) and low <= min(open, close)
                valid_mask = (
                    (data['high'] >= data[['open', 'close']].max(axis=1)) &
                    (data['low'] <= data[['open', 'close']].min(axis=1))
                )
                data = data[valid_mask]
            
            # 5. 异常值检测和处理
            if self.enable_outlier_detection:
                data = self._detect_and_handle_outliers(data)
            
            # 6. 填充缺失值
            data = self._fill_missing_values(data)
            
            cleaned_rows = len(data)
            if original_rows != cleaned_rows:
                logger.debug(f"数据清洗完成: 原始行数: {original_rows}, 清洗后行数: {cleaned_rows}")
            
            return data
            
        except Exception as e:
            logger.warning(f"数据清洗失败: {e}")
            return data
    
    def _clean_stock_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗股票数据"""
        try:
            original_rows = len(data)
            
            # 删除symbol为空的行
            if 'symbol' in data.columns:
                data = data.dropna(subset=['symbol'])
                data = data[data['symbol'].str.strip() != '']
            
            # 删除重复的股票代码
            if 'symbol' in data.columns:
                data = data.drop_duplicates(subset=['symbol'], keep='first')
            
            # 清理字符串字段
            string_columns = ['symbol', 'name', 'market', 'industry', 'sector']
            for col in string_columns:
                if col in data.columns:
                    data[col] = data[col].astype(str).str.strip()
                    data[col] = data[col].replace('nan', '')
                    data[col] = data[col].replace('None', '')
            
            cleaned_rows = len(data)
            if original_rows != cleaned_rows:
                logger.debug(f"股票数据清洗完成: 原始行数: {original_rows}, 清洗后行数: {cleaned_rows}")
            
            return data
            
        except Exception as e:
            logger.warning(f"股票数据清洗失败: {e}")
            return data
    
    def _detect_and_handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """检测和处理异常值"""
        try:
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            
            for col in numeric_columns:
                if col not in data.columns:
                    continue
                
                # 使用Z-score方法检测异常值
                z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                outlier_mask = z_scores > self.outlier_threshold
                
                outlier_count = outlier_mask.sum()
                if outlier_count > 0:
                    logger.debug(f"检测到 {col} 列异常值: {outlier_count} 个")
                    
                    # 对于价格数据，使用中位数替换异常值
                    if col in ['open', 'high', 'low', 'close']:
                        median_value = data[col].median()
                        data.loc[outlier_mask, col] = median_value
                    
                    # 对于成交量和成交额，删除异常值行
                    elif col in ['volume', 'amount']:
                        data = data[~outlier_mask]
            
            return data
            
        except Exception as e:
            logger.warning(f"异常值处理失败: {e}")
            return data
    
    def _fill_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """填充缺失值"""
        try:
            # 对于数值列，使用前向填充
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = data[col].fillna(method='ffill')
                    data[col] = data[col].fillna(0)  # 如果仍有缺失值，填充为0
            
            return data
            
        except Exception as e:
            logger.warning(f"缺失值填充失败: {e}")
            return data
    
    def _ensure_standard_columns(self, 
                                data: pd.DataFrame, 
                                data_type: str) -> pd.DataFrame:
        """确保包含所有标准列"""
        if data_type not in self.STANDARD_COLUMNS:
            return data
        
        standard_columns = self.STANDARD_COLUMNS[data_type]
        
        for col in standard_columns:
            if col not in data.columns:
                if col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                    data[col] = 0.0
                elif col == 'trade_date':
                    data[col] = pd.NaT
                else:
                    data[col] = ''
        
        # 重新排列列顺序
        return data[standard_columns + [col for col in data.columns if col not in standard_columns]]
    
    def _sort_and_deduplicate(self, 
                             data: pd.DataFrame, 
                             sort_column: str) -> pd.DataFrame:
        """排序和去重"""
        try:
            # 按指定列排序
            if sort_column in data.columns:
                data = data.sort_values(sort_column).reset_index(drop=True)
            
            # 去重（保留第一个）
            if sort_column in data.columns:
                data = data.drop_duplicates(subset=[sort_column], keep='first')
            
            return data
            
        except Exception as e:
            logger.warning(f"排序去重失败: {e}")
            return data
    
    def _validate_market_data(self, 
                             data: pd.DataFrame, 
                             symbol: str) -> DataQualityReport:
        """验证市场数据质量"""
        try:
            report = DataQualityReport(symbol=symbol)
            report.total_records = len(data)
            
            if data.empty:
                report.data_quality = DataQuality.INVALID
                report.issues.append("数据为空")
                return report
            
            # 1. 检查缺失值
            missing_counts = data.isnull().sum()
            report.missing_values = missing_counts.to_dict()
            
            # 2. 检查重复记录
            if 'trade_date' in data.columns:
                report.duplicate_records = data.duplicated(subset=['trade_date']).sum()
            
            # 3. 检查异常值
            if self.enable_outlier_detection:
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
                for col in numeric_columns:
                    if col in data.columns:
                        z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                        outlier_count = (z_scores > self.outlier_threshold).sum()
                        if outlier_count > 0:
                            report.outliers[col] = outlier_count
            
            # 4. 计算有效记录数
            valid_mask = pd.Series(True, index=data.index)
            
            # 检查关键字段
            if 'trade_date' in data.columns:
                valid_mask &= data['trade_date'].notna()
            
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in data.columns:
                    valid_mask &= (data[col] > 0)
            
            report.valid_records = valid_mask.sum()
            
            # 5. 计算质量分数
            completeness_score = report.completeness_rate * 100
            duplicate_penalty = report.duplicate_rate * 20
            outlier_penalty = sum(report.outliers.values()) / report.total_records * 30 if report.total_records > 0 else 0
            
            report.quality_score = max(0, completeness_score - duplicate_penalty - outlier_penalty)
            
            # 6. 确定质量等级
            if report.quality_score >= 90:
                report.data_quality = DataQuality.EXCELLENT
            elif report.quality_score >= 80:
                report.data_quality = DataQuality.GOOD
            elif report.quality_score >= 60:
                report.data_quality = DataQuality.FAIR
            elif report.quality_score >= 30:
                report.data_quality = DataQuality.POOR
            else:
                report.data_quality = DataQuality.INVALID
            
            # 7. 生成问题和建议
            self._generate_quality_issues_and_recommendations(report)
            
            return report
            
        except Exception as e:
            logger.error(f"数据质量验证失败: {symbol}, 错误: {e}")
            report = DataQualityReport(symbol=symbol)
            report.data_quality = DataQuality.INVALID
            report.issues.append(f"质量验证失败: {e}")
            return report
    
    def _generate_quality_issues_and_recommendations(self, report: DataQualityReport) -> None:
        """生成质量问题和建议"""
        # 检查缺失值问题
        for col, count in report.missing_values.items():
            if count > 0:
                rate = count / report.total_records * 100
                if rate > 10:
                    report.issues.append(f"{col} 列缺失值过多: {count} 个 ({rate:.1f}%)")
                    report.recommendations.append(f"检查 {col} 列数据源质量")
        
        # 检查重复记录问题
        if report.duplicate_rate > 0.05:  # 5%
            report.issues.append(f"重复记录过多: {report.duplicate_records} 个 ({report.duplicate_rate*100:.1f}%)")
            report.recommendations.append("清理重复数据")
        
        # 检查异常值问题
        for col, count in report.outliers.items():
            if count > 0:
                rate = count / report.total_records * 100
                if rate > 5:
                    report.issues.append(f"{col} 列异常值过多: {count} 个 ({rate:.1f}%)")
                    report.recommendations.append(f"检查 {col} 列异常值处理策略")
        
        # 检查数据完整性
        if report.completeness_rate < 0.8:
            report.issues.append(f"数据完整性不足: {report.completeness_rate*100:.1f}%")
            report.recommendations.append("提高数据收集质量")
        
        # 根据质量等级给出建议
        if report.data_quality == DataQuality.POOR:
            report.recommendations.append("建议更换数据源或进行深度清洗")
        elif report.data_quality == DataQuality.FAIR:
            report.recommendations.append("建议优化数据处理流程")
        elif report.data_quality == DataQuality.INVALID:
            report.recommendations.append("数据无效，需要重新获取")
    
    def batch_format_market_data(self, 
                                data_dict: Dict[str, pd.DataFrame], 
                                source_type: str = 'xtdata') -> Dict[str, Tuple[pd.DataFrame, DataQualityReport]]:
        """
        批量格式化市场数据
        
        Args:
            data_dict: 股票代码到数据的映射
            source_type: 数据源类型
            
        Returns:
            Dict[str, Tuple[pd.DataFrame, DataQualityReport]]: 格式化结果
        """
        results = {}
        
        logger.info(f"开始批量格式化市场数据: {len(data_dict)} 只股票")
        
        for symbol, data in data_dict.items():
            try:
                formatted_data, quality_report = self.format_market_data(
                    data, source_type, symbol, validate=True
                )
                results[symbol] = (formatted_data, quality_report)
                
            except Exception as e:
                logger.error(f"格式化 {symbol} 数据失败: {e}")
                # 创建空结果
                empty_data = pd.DataFrame(columns=self.STANDARD_COLUMNS['market_data'])
                error_report = DataQualityReport(
                    symbol=symbol, 
                    data_quality=DataQuality.INVALID,
                    issues=[f"格式化失败: {e}"]
                )
                results[symbol] = (empty_data, error_report)
        
        logger.info(f"批量格式化完成: {len(results)} 只股票")
        return results
    
    def get_quality_summary(self, 
                           quality_reports: List[DataQualityReport]) -> Dict[str, Any]:
        """
        获取质量报告汇总
        
        Args:
            quality_reports: 质量报告列表
            
        Returns:
            Dict[str, Any]: 质量汇总信息
        """
        if not quality_reports:
            return {}
        
        summary = {
            'total_symbols': len(quality_reports),
            'quality_distribution': {},
            'avg_quality_score': 0.0,
            'avg_completeness_rate': 0.0,
            'total_issues': 0,
            'common_issues': {},
            'recommendations': []
        }
        
        # 统计质量分布
        quality_counts = {}
        total_score = 0
        total_completeness = 0
        all_issues = []
        
        for report in quality_reports:
            # 质量分布
            quality = report.data_quality.value
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
            
            # 平均分数
            total_score += report.quality_score
            total_completeness += report.completeness_rate
            
            # 收集问题
            all_issues.extend(report.issues)
        
        summary['quality_distribution'] = quality_counts
        summary['avg_quality_score'] = total_score / len(quality_reports)
        summary['avg_completeness_rate'] = total_completeness / len(quality_reports)
        summary['total_issues'] = len(all_issues)
        
        # 统计常见问题
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # 取前5个最常见的问题
        summary['common_issues'] = dict(sorted(issue_counts.items(), 
                                             key=lambda x: x[1], 
                                             reverse=True)[:5])
        
        # 生成建议
        if summary['avg_quality_score'] < 60:
            summary['recommendations'].append("整体数据质量较差，建议检查数据源")
        
        if summary['avg_completeness_rate'] < 0.8:
            summary['recommendations'].append("数据完整性不足，建议优化数据收集流程")
        
        if quality_counts.get('invalid', 0) > len(quality_reports) * 0.1:
            summary['recommendations'].append("无效数据过多，建议更换数据源")
        
        return summary