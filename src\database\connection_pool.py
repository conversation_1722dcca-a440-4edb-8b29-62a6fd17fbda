"""
威科夫相对强弱选股系统 - 数据库连接池管理

提供高效的数据库连接池管理，支持连接复用、健康检查、超时管理等功能
"""

import sqlite3
import threading
import time
from queue import Queue, Empty, Full
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from contextlib import contextmanager

from ..utils.logger import get_logger

logger = get_logger(__name__)


class ConnectionInfo:
    """连接信息"""
    
    def __init__(self, connection: sqlite3.Connection):
        self.connection = connection
        self.created_time = datetime.now()
        self.last_used_time = datetime.now()
        self.usage_count = 0
        self.is_healthy = True
        self.thread_id = threading.get_ident()
    
    def update_usage(self):
        """更新使用信息"""
        self.last_used_time = datetime.now()
        self.usage_count += 1
    
    def is_expired(self, max_lifetime: timedelta) -> bool:
        """检查连接是否过期"""
        return datetime.now() - self.created_time > max_lifetime
    
    def is_idle_too_long(self, max_idle_time: timedelta) -> bool:
        """检查连接是否空闲过久"""
        return datetime.now() - self.last_used_time > max_idle_time


class DatabaseConnectionPool:
    """
    数据库连接池
    
    提供高效的SQLite连接管理，支持：
    - 连接复用和池化管理
    - 连接健康检查
    - 超时和过期管理
    - 并发安全
    """
    
    def __init__(self, 
                 db_path: str,
                 pool_size: int = 10,
                 max_lifetime: int = 3600,  # 连接最大生命周期（秒）
                 max_idle_time: int = 300,  # 最大空闲时间（秒）
                 health_check_interval: int = 60):  # 健康检查间隔（秒）
        """
        初始化连接池
        
        Args:
            db_path: 数据库文件路径
            pool_size: 连接池大小
            max_lifetime: 连接最大生命周期（秒）
            max_idle_time: 最大空闲时间（秒）
            health_check_interval: 健康检查间隔（秒）
        """
        self.db_path = db_path
        self.pool_size = pool_size
        self.max_lifetime = timedelta(seconds=max_lifetime)
        self.max_idle_time = timedelta(seconds=max_idle_time)
        self.health_check_interval = health_check_interval
        
        # 连接池和管理
        self._pool: Queue = Queue(maxsize=pool_size)
        self._connections: Dict[int, ConnectionInfo] = {}
        self._lock = threading.RLock()
        self._closed = False
        
        # 统计信息
        self._stats = {
            'total_created': 0,
            'total_destroyed': 0,
            'current_active': 0,
            'current_idle': 0,
            'get_requests': 0,
            'get_timeouts': 0,
            'health_check_failures': 0
        }
        
        # 启动健康检查线程
        self._health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True
        )
        self._health_check_thread.start()
        
        logger.info(f"数据库连接池初始化: {db_path}, 池大小: {pool_size}")
    
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,  # 允许跨线程使用
                timeout=30.0,  # 连接超时
                isolation_level=None  # 自动提交模式
            )
            
            # 配置连接参数
            conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
            conn.execute("PRAGMA journal_mode = WAL")  # 使用WAL模式
            conn.execute("PRAGMA synchronous = NORMAL")  # 平衡性能和安全性
            conn.execute("PRAGMA cache_size = 10000")  # 设置缓存大小
            conn.execute("PRAGMA temp_store = MEMORY")  # 临时表存储在内存
            
            self._stats['total_created'] += 1
            logger.debug(f"创建新数据库连接，总计: {self._stats['total_created']}")
            
            return conn
            
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            raise
    
    def get_connection(self, timeout: float = 30.0) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Args:
            timeout: 获取连接的超时时间（秒）
            
        Returns:
            sqlite3.Connection: 数据库连接
            
        Raises:
            Exception: 获取连接失败
        """
        if self._closed:
            raise RuntimeError("连接池已关闭")
        
        with self._lock:
            self._stats['get_requests'] += 1
            
            # 尝试从池中获取连接
            try:
                connection_info = self._pool.get(timeout=timeout)
                connection_info.update_usage()
                
                # 验证连接健康性
                if self._is_connection_healthy(connection_info.connection):
                    self._stats['current_active'] += 1
                    self._stats['current_idle'] -= 1
                    logger.debug(f"从池中获取连接，活跃连接数: {self._stats['current_active']}")
                    return connection_info.connection
                else:
                    # 连接不健康，销毁并创建新连接
                    self._destroy_connection(connection_info)
                    
            except Empty:
                self._stats['get_timeouts'] += 1
                logger.warning(f"从连接池获取连接超时: {timeout}秒")
            
            # 池中没有可用连接，创建新连接
            if len(self._connections) < self.pool_size:
                connection = self._create_connection()
                connection_id = id(connection)
                connection_info = ConnectionInfo(connection)
                
                self._connections[connection_id] = connection_info
                self._stats['current_active'] += 1
                
                logger.debug(f"创建新连接，连接池大小: {len(self._connections)}")
                return connection
            else:
                raise RuntimeError("连接池已满且无法获取连接")
    
    def return_connection(self, connection: sqlite3.Connection) -> None:
        """
        归还数据库连接到池中
        
        Args:
            connection: 要归还的数据库连接
        """
        if self._closed:
            return
        
        with self._lock:
            connection_id = id(connection)
            
            if connection_id not in self._connections:
                logger.warning("尝试归还不属于此连接池的连接")
                return
            
            connection_info = self._connections[connection_id]
            
            # 检查连接是否需要销毁
            if (connection_info.is_expired(self.max_lifetime) or 
                not self._is_connection_healthy(connection)):
                
                self._destroy_connection(connection_info)
                return
            
            # 归还到池中
            try:
                self._pool.put_nowait(connection_info)
                self._stats['current_active'] -= 1
                self._stats['current_idle'] += 1
                logger.debug(f"连接归还到池，空闲连接数: {self._stats['current_idle']}")
                
            except Full:
                # 池已满，销毁连接
                logger.debug("连接池已满，销毁多余连接")
                self._destroy_connection(connection_info)
    
    def _is_connection_healthy(self, connection: sqlite3.Connection) -> bool:
        """
        检查连接健康性
        
        Args:
            connection: 数据库连接
            
        Returns:
            bool: 连接是否健康
        """
        try:
            # 执行简单查询验证连接
            cursor = connection.execute("SELECT 1")
            result = cursor.fetchone()
            return result is not None and result[0] == 1
            
        except Exception as e:
            logger.debug(f"连接健康检查失败: {e}")
            return False
    
    def _destroy_connection(self, connection_info: ConnectionInfo) -> None:
        """
        销毁连接
        
        Args:
            connection_info: 连接信息
        """
        try:
            connection_id = id(connection_info.connection)
            
            # 关闭连接
            connection_info.connection.close()
            
            # 从管理字典中移除
            if connection_id in self._connections:
                del self._connections[connection_id]
            
            # 更新统计信息
            self._stats['total_destroyed'] += 1
            if self._stats['current_active'] > 0:
                self._stats['current_active'] -= 1
            elif self._stats['current_idle'] > 0:
                self._stats['current_idle'] -= 1
            
            logger.debug(f"连接已销毁，剩余连接数: {len(self._connections)}")
            
        except Exception as e:
            logger.error(f"销毁连接失败: {e}")
    
    def _health_check_worker(self) -> None:
        """健康检查工作线程"""
        while not self._closed:
            try:
                time.sleep(self.health_check_interval)
                
                if self._closed:
                    break
                
                self._perform_health_check()
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    def _perform_health_check(self) -> None:
        """执行健康检查"""
        with self._lock:
            if self._closed:
                return
            
            unhealthy_connections = []
            expired_connections = []
            idle_too_long_connections = []
            
            # 检查所有连接
            for connection_id, connection_info in list(self._connections.items()):
                # 检查过期
                if connection_info.is_expired(self.max_lifetime):
                    expired_connections.append(connection_info)
                    continue
                
                # 检查空闲时间
                if connection_info.is_idle_too_long(self.max_idle_time):
                    idle_too_long_connections.append(connection_info)
                    continue
                
                # 检查健康性
                if not self._is_connection_healthy(connection_info.connection):
                    unhealthy_connections.append(connection_info)
                    self._stats['health_check_failures'] += 1
            
            # 销毁问题连接
            for connection_info in (unhealthy_connections + expired_connections + idle_too_long_connections):
                self._destroy_connection(connection_info)
            
            # 记录健康检查结果
            if unhealthy_connections or expired_connections or idle_too_long_connections:
                logger.info(f"健康检查完成 - 销毁连接: 不健康={len(unhealthy_connections)}, "
                          f"过期={len(expired_connections)}, 空闲过久={len(idle_too_long_connections)}")
    
    @contextmanager
    def get_connection_context(self, timeout: float = 30.0):
        """
        获取连接的上下文管理器
        
        Args:
            timeout: 获取连接超时时间
            
        Yields:
            sqlite3.Connection: 数据库连接
        """
        connection = None
        try:
            connection = self.get_connection(timeout)
            yield connection
        finally:
            if connection:
                self.return_connection(connection)
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                'pool_size': self.pool_size,
                'current_total': len(self._connections),
                'queue_size': self._pool.qsize(),
                'max_lifetime_seconds': self.max_lifetime.total_seconds(),
                'max_idle_time_seconds': self.max_idle_time.total_seconds(),
                'health_check_interval': self.health_check_interval
            })
            return stats
    
    def resize_pool(self, new_size: int) -> bool:
        """
        调整连接池大小
        
        Args:
            new_size: 新的连接池大小
            
        Returns:
            bool: 调整是否成功
        """
        if new_size <= 0:
            logger.error("连接池大小必须大于0")
            return False
        
        with self._lock:
            old_size = self.pool_size
            self.pool_size = new_size
            
            # 如果缩小池大小，销毁多余连接
            if new_size < old_size:
                excess_count = len(self._connections) - new_size
                if excess_count > 0:
                    connections_to_destroy = []
                    
                    # 优先销毁空闲连接
                    while excess_count > 0 and not self._pool.empty():
                        try:
                            connection_info = self._pool.get_nowait()
                            connections_to_destroy.append(connection_info)
                            excess_count -= 1
                        except Empty:
                            break
                    
                    # 销毁选中的连接
                    for connection_info in connections_to_destroy:
                        self._destroy_connection(connection_info)
            
            logger.info(f"连接池大小调整: {old_size} -> {new_size}")
            return True
    
    def close_all(self) -> None:
        """关闭所有连接并清理资源"""
        with self._lock:
            if self._closed:
                return
            
            self._closed = True
            
            # 销毁所有连接
            for connection_info in list(self._connections.values()):
                self._destroy_connection(connection_info)
            
            # 清空队列
            while not self._pool.empty():
                try:
                    self._pool.get_nowait()
                except Empty:
                    break
            
            logger.info(f"连接池已关闭，共销毁 {self._stats['total_destroyed']} 个连接")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_all()
    
    def __del__(self):
        """析构函数"""
        try:
            self.close_all()
        except:
            pass