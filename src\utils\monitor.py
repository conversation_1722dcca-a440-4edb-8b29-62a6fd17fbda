"""
系统监控模块

提供CPU、内存、磁盘、网络等系统资源监控功能
"""

import os
import time
import threading
import psutil
from typing import Dict, List, Any, Optional, Callable, NamedTuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
import json

from .logger import get_logger

logger = get_logger(__name__)


# ========================= 数据结构定义 =========================

@dataclass
class SystemInfo:
    """系统信息"""
    platform: str
    hostname: str
    cpu_count: int
    memory_total: int  # GB
    disk_total: int   # GB
    python_version: str
    boot_time: datetime


@dataclass
class ResourceUsage:
    """资源使用情况"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int  # MB
    memory_available: int  # MB
    disk_usage: Dict[str, float]  # 各分区使用率
    network_io: Dict[str, int]  # 网络IO
    process_count: int


@dataclass
class ProcessInfo:
    """进程信息"""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    create_time: datetime
    cmdline: List[str] = field(default_factory=list)


@dataclass
class AlertRule:
    """警报规则"""
    name: str
    metric: str  # cpu, memory, disk, network
    threshold: float
    comparison: str  # gt, lt, gte, lte
    duration: int  # 持续时间（秒）
    enabled: bool = True
    callback: Optional[Callable] = None


class AlertStatus(NamedTuple):
    """警报状态"""
    rule_name: str
    triggered: bool
    trigger_time: Optional[datetime]
    current_value: float
    threshold: float


# ========================= 系统监控器 =========================

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, history_size: int = 1000):
        """
        初始化系统监控器
        
        Args:
            history_size: 历史数据保存数量
        """
        self.history_size = history_size
        self.history: deque = deque(maxlen=history_size)
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alert_status: Dict[str, AlertStatus] = {}
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.interval = 5.0  # 监控间隔（秒）
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            'data_collected': [],
            'alert_triggered': [],
            'alert_cleared': []
        }
        
        logger.info("系统监控器初始化完成")
    
    def get_system_info(self) -> SystemInfo:
        """获取系统信息"""
        try:
            import platform
            import sys
            
            # 获取内存信息
            memory = psutil.virtual_memory()
            memory_gb = round(memory.total / (1024**3), 2)
            
            # 获取磁盘信息
            disk_total = 0
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_total += usage.total
                except PermissionError:
                    continue
            disk_gb = round(disk_total / (1024**3), 2)
            
            # 获取启动时间
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            
            return SystemInfo(
                platform=platform.platform(),
                hostname=platform.node(),
                cpu_count=psutil.cpu_count(),
                memory_total=memory_gb,
                disk_total=disk_gb,
                python_version=sys.version,
                boot_time=boot_time
            )
            
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            raise
    
    def collect_current_usage(self) -> ResourceUsage:
        """收集当前资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_used_mb = round((memory.total - memory.available) / (1024**2), 2)
            memory_available_mb = round(memory.available / (1024**2), 2)
            
            # 磁盘使用情况
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.device] = round(usage.percent, 2)
                except PermissionError:
                    continue
            
            # 网络IO
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # 进程数量
            process_count = len(psutil.pids())
            
            return ResourceUsage(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used=memory_used_mb,
                memory_available=memory_available_mb,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count
            )
            
        except Exception as e:
            logger.error(f"收集资源使用情况失败: {e}")
            raise
    
    def get_top_processes(self, count: int = 10, sort_by: str = 'cpu') -> List[ProcessInfo]:
        """
        获取资源使用最多的进程
        
        Args:
            count: 返回进程数量
            sort_by: 排序方式 (cpu, memory)
            
        Returns:
            进程信息列表
        """
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 
                                           'memory_percent', 'memory_info', 
                                           'status', 'create_time', 'cmdline']):
                try:
                    proc_info = proc.info
                    memory_mb = round(proc_info['memory_info'].rss / (1024**2), 2)
                    
                    processes.append(ProcessInfo(
                        pid=proc_info['pid'],
                        name=proc_info['name'] or 'Unknown',
                        cpu_percent=proc_info['cpu_percent'] or 0.0,
                        memory_percent=proc_info['memory_percent'] or 0.0,
                        memory_mb=memory_mb,
                        status=proc_info['status'],
                        create_time=datetime.fromtimestamp(proc_info['create_time']),
                        cmdline=proc_info['cmdline'] or []
                    ))
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 排序
            if sort_by == 'cpu':
                processes.sort(key=lambda x: x.cpu_percent, reverse=True)
            elif sort_by == 'memory':
                processes.sort(key=lambda x: x.memory_percent, reverse=True)
            
            return processes[:count]
            
        except Exception as e:
            logger.error(f"获取进程信息失败: {e}")
            return []
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """
        添加警报规则
        
        Args:
            rule: 警报规则
        """
        self.alert_rules[rule.name] = rule
        self.alert_status[rule.name] = AlertStatus(
            rule_name=rule.name,
            triggered=False,
            trigger_time=None,
            current_value=0.0,
            threshold=rule.threshold
        )
        
        logger.info(f"添加警报规则: {rule.name}")
    
    def remove_alert_rule(self, rule_name: str) -> None:
        """
        移除警报规则
        
        Args:
            rule_name: 规则名称
        """
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            del self.alert_status[rule_name]
            logger.info(f"移除警报规则: {rule_name}")
    
    def check_alerts(self, usage: ResourceUsage) -> None:
        """
        检查警报规则
        
        Args:
            usage: 资源使用情况
        """
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            # 获取当前值
            current_value = self._get_metric_value(usage, rule.metric)
            if current_value is None:
                continue
            
            # 检查阈值
            triggered = self._check_threshold(current_value, rule.threshold, rule.comparison)
            
            # 更新状态
            current_status = self.alert_status[rule_name]
            
            if triggered and not current_status.triggered:
                # 警报触发
                self.alert_status[rule_name] = AlertStatus(
                    rule_name=rule_name,
                    triggered=True,
                    trigger_time=datetime.now(),
                    current_value=current_value,
                    threshold=rule.threshold
                )
                
                self._handle_alert_triggered(rule, current_value)
                
            elif not triggered and current_status.triggered:
                # 警报清除
                self.alert_status[rule_name] = AlertStatus(
                    rule_name=rule_name,
                    triggered=False,
                    trigger_time=None,
                    current_value=current_value,
                    threshold=rule.threshold
                )
                
                self._handle_alert_cleared(rule, current_value)
            
            else:
                # 更新当前值
                self.alert_status[rule_name] = current_status._replace(
                    current_value=current_value
                )
    
    def _get_metric_value(self, usage: ResourceUsage, metric: str) -> Optional[float]:
        """获取指标值"""
        try:
            if metric == 'cpu':
                return usage.cpu_percent
            elif metric == 'memory':
                return usage.memory_percent
            elif metric.startswith('disk:'):
                device = metric.split(':', 1)[1]
                return usage.disk_usage.get(device)
            elif metric.startswith('network:'):
                key = metric.split(':', 1)[1]
                return usage.network_io.get(key)
            else:
                return None
        except Exception:
            return None
    
    def _check_threshold(self, current: float, threshold: float, comparison: str) -> bool:
        """检查阈值"""
        if comparison == 'gt':
            return current > threshold
        elif comparison == 'gte':
            return current >= threshold
        elif comparison == 'lt':
            return current < threshold
        elif comparison == 'lte':
            return current <= threshold
        else:
            return False
    
    def _handle_alert_triggered(self, rule: AlertRule, current_value: float) -> None:
        """处理警报触发"""
        logger.warning(f"警报触发: {rule.name}, 当前值: {current_value}, 阈值: {rule.threshold}")
        
        # 调用规则回调
        if rule.callback:
            try:
                rule.callback(rule, current_value, True)
            except Exception as e:
                logger.error(f"警报回调执行失败: {e}")
        
        # 调用全局回调
        for callback in self.callbacks['alert_triggered']:
            try:
                callback(rule, current_value)
            except Exception as e:
                logger.error(f"警报回调执行失败: {e}")
    
    def _handle_alert_cleared(self, rule: AlertRule, current_value: float) -> None:
        """处理警报清除"""
        logger.info(f"警报清除: {rule.name}, 当前值: {current_value}")
        
        # 调用规则回调
        if rule.callback:
            try:
                rule.callback(rule, current_value, False)
            except Exception as e:
                logger.error(f"警报回调执行失败: {e}")
        
        # 调用全局回调
        for callback in self.callbacks['alert_cleared']:
            try:
                callback(rule, current_value)
            except Exception as e:
                logger.error(f"警报回调执行失败: {e}")
    
    def start_monitoring(self, interval: float = 5.0) -> None:
        """
        开始监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            logger.warning("监控已在运行")
            return
        
        self.interval = interval
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"开始系统监控，间隔 {interval} 秒")
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("系统监控已停止")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                # 收集数据
                usage = self.collect_current_usage()
                self.history.append(usage)
                
                # 检查警报
                self.check_alerts(usage)
                
                # 调用数据收集回调
                for callback in self.callbacks['data_collected']:
                    try:
                        callback(usage)
                    except Exception as e:
                        logger.error(f"数据收集回调执行失败: {e}")
                
                time.sleep(self.interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(self.interval)
    
    def get_history(self, minutes: int = 60) -> List[ResourceUsage]:
        """
        获取历史数据
        
        Args:
            minutes: 获取最近N分钟的数据
            
        Returns:
            历史数据列表
        """
        if not self.history:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [usage for usage in self.history if usage.timestamp >= cutoff_time]
    
    def get_statistics(self, minutes: int = 60) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            minutes: 统计最近N分钟的数据
            
        Returns:
            统计信息
        """
        history = self.get_history(minutes)
        
        if not history:
            return {}
        
        cpu_values = [usage.cpu_percent for usage in history]
        memory_values = [usage.memory_percent for usage in history]
        
        return {
            'period_minutes': minutes,
            'data_points': len(history),
            'cpu': {
                'avg': sum(cpu_values) / len(cpu_values),
                'min': min(cpu_values),
                'max': max(cpu_values)
            },
            'memory': {
                'avg': sum(memory_values) / len(memory_values),
                'min': min(memory_values),
                'max': max(memory_values)
            },
            'latest': history[-1] if history else None
        }
    
    def add_callback(self, event: str, callback: Callable) -> None:
        """
        添加回调函数
        
        Args:
            event: 事件类型 (data_collected, alert_triggered, alert_cleared)
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
            logger.info(f"添加 {event} 回调函数")
    
    def remove_callback(self, event: str, callback: Callable) -> None:
        """
        移除回调函数
        
        Args:
            event: 事件类型
            callback: 回调函数
        """
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
            logger.info(f"移除 {event} 回调函数")
    
    def export_data(self, file_path: str, format: str = 'json', minutes: int = 60) -> bool:
        """
        导出监控数据
        
        Args:
            file_path: 文件路径
            format: 格式 (json, csv)
            minutes: 导出最近N分钟的数据
            
        Returns:
            是否成功
        """
        try:
            history = self.get_history(minutes)
            
            if format.lower() == 'json':
                return self._export_json(file_path, history)
            elif format.lower() == 'csv':
                return self._export_csv(file_path, history)
            else:
                logger.error(f"不支持的导出格式: {format}")
                return False
                
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False
    
    def _export_json(self, file_path: str, history: List[ResourceUsage]) -> bool:
        """导出JSON格式"""
        try:
            data = []
            for usage in history:
                data.append({
                    'timestamp': usage.timestamp.isoformat(),
                    'cpu_percent': usage.cpu_percent,
                    'memory_percent': usage.memory_percent,
                    'memory_used': usage.memory_used,
                    'memory_available': usage.memory_available,
                    'disk_usage': usage.disk_usage,
                    'network_io': usage.network_io,
                    'process_count': usage.process_count
                })
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            return False
    
    def _export_csv(self, file_path: str, history: List[ResourceUsage]) -> bool:
        """导出CSV格式"""
        try:
            import csv
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入标题
                writer.writerow([
                    'timestamp', 'cpu_percent', 'memory_percent', 
                    'memory_used', 'memory_available', 'process_count'
                ])
                
                # 写入数据
                for usage in history:
                    writer.writerow([
                        usage.timestamp.isoformat(),
                        usage.cpu_percent,
                        usage.memory_percent,
                        usage.memory_used,
                        usage.memory_available,
                        usage.process_count
                    ])
            
            logger.info(f"数据已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False


# ========================= 便利函数 =========================

def get_system_summary() -> Dict[str, Any]:
    """获取系统摘要信息"""
    monitor = SystemMonitor()
    system_info = monitor.get_system_info()
    current_usage = monitor.collect_current_usage()
    top_processes = monitor.get_top_processes(5)
    
    return {
        'system_info': {
            'platform': system_info.platform,
            'hostname': system_info.hostname,
            'cpu_count': system_info.cpu_count,
            'memory_total': system_info.memory_total,
            'disk_total': system_info.disk_total,
            'uptime': str(datetime.now() - system_info.boot_time)
        },
        'current_usage': {
            'cpu_percent': current_usage.cpu_percent,
            'memory_percent': current_usage.memory_percent,
            'memory_used': current_usage.memory_used,
            'disk_usage': current_usage.disk_usage,
            'process_count': current_usage.process_count
        },
        'top_processes': [
            {
                'pid': proc.pid,
                'name': proc.name,
                'cpu_percent': proc.cpu_percent,
                'memory_mb': proc.memory_mb
            }
            for proc in top_processes
        ]
    }


def check_system_health() -> Dict[str, str]:
    """检查系统健康状态"""
    try:
        monitor = SystemMonitor()
        usage = monitor.collect_current_usage()
        
        health_status = {
            'overall': 'healthy',
            'cpu': 'healthy',
            'memory': 'healthy',
            'disk': 'healthy'
        }
        
        issues = []
        
        # CPU检查
        if usage.cpu_percent > 90:
            health_status['cpu'] = 'critical'
            issues.append('CPU使用率过高')
        elif usage.cpu_percent > 70:
            health_status['cpu'] = 'warning'
            issues.append('CPU使用率较高')
        
        # 内存检查
        if usage.memory_percent > 90:
            health_status['memory'] = 'critical'
            issues.append('内存使用率过高')
        elif usage.memory_percent > 80:
            health_status['memory'] = 'warning'
            issues.append('内存使用率较高')
        
        # 磁盘检查
        max_disk_usage = max(usage.disk_usage.values()) if usage.disk_usage else 0
        if max_disk_usage > 95:
            health_status['disk'] = 'critical'
            issues.append('磁盘空间不足')
        elif max_disk_usage > 85:
            health_status['disk'] = 'warning'
            issues.append('磁盘空间较少')
        
        # 总体状态
        if any(status == 'critical' for status in health_status.values()):
            health_status['overall'] = 'critical'
        elif any(status == 'warning' for status in health_status.values()):
            health_status['overall'] = 'warning'
        
        health_status['issues'] = issues
        return health_status
        
    except Exception as e:
        logger.error(f"检查系统健康状态失败: {e}")
        return {'overall': 'error', 'message': str(e)}


# ========================= 示例使用 =========================

def example_usage():
    """示例用法"""
    # 创建监控器
    monitor = SystemMonitor(history_size=100)
    
    # 添加警报规则
    cpu_alert = AlertRule(
        name="高CPU使用率",
        metric="cpu",
        threshold=80.0,
        comparison="gt",
        duration=60
    )
    monitor.add_alert_rule(cpu_alert)
    
    memory_alert = AlertRule(
        name="高内存使用率",
        metric="memory",
        threshold=85.0,
        comparison="gt",
        duration=30
    )
    monitor.add_alert_rule(memory_alert)
    
    # 添加回调函数
    def on_data_collected(usage):
        print(f"CPU: {usage.cpu_percent}%, 内存: {usage.memory_percent}%")
    
    def on_alert_triggered(rule, value):
        print(f"警报: {rule.name}, 当前值: {value}")
    
    monitor.add_callback('data_collected', on_data_collected)
    monitor.add_callback('alert_triggered', on_alert_triggered)
    
    # 开始监控
    monitor.start_monitoring(interval=5)
    
    try:
        # 运行一段时间
        time.sleep(30)
        
        # 获取统计信息
        stats = monitor.get_statistics(minutes=5)
        print("统计信息:", stats)
        
        # 导出数据
        monitor.export_data('monitoring_data.json', format='json', minutes=5)
        
    finally:
        # 停止监控
        monitor.stop_monitoring()


if __name__ == "__main__":
    # 运行示例
    print("系统摘要:")
    summary = get_system_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    print("\n健康检查:")
    health = check_system_health()
    print(health)