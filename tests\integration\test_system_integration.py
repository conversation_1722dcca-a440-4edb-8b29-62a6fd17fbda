#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成测试

测试威科夫相对强弱选股系统的各模块集成功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# 模拟xtdata模块
sys.modules['xtdata'] = Mock()

from src.data_sources.base import DataSourceConfig
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.engines.wyckoff import WyckoffAnalysisEngine
from src.engines.relative_strength import RelativeStrengthEngine
from src.engines.selection import StockSelectionEngine, SelectionCriteria
from src.services.data_service import DataService
from src.utils.cache import CacheManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""

    def setUp(self):
        """测试前准备"""
        self.logger = get_logger(self.__class__.__name__)

        # 创建模拟数据
        self.mock_stock_data = self._create_mock_stock_data()
        self.mock_stock_list = [
            {'code': '000001.SZ', 'name': '平安银行', 'market': 'SZ'},
            {'code': '000002.SZ', 'name': '万科A', 'market': 'SZ'},
            {'code': '600000.SH', 'name': '浦发银行', 'market': 'SH'},
            {'code': '600036.SH', 'name': '招商银行', 'market': 'SH'},
            {'code': '600519.SH', 'name': '贵州茅台', 'market': 'SH'}
        ]

    def assertHasAttr(self, obj, attr):
        """检查对象是否有指定属性"""
        self.assertTrue(hasattr(obj, attr), f"对象没有属性 '{attr}'")
        
        # 创建数据源配置
        self.data_source_config = DataSourceConfig(
            name="test_xtdata",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                'type': 'xtdata',
                'ip': '127.0.0.1',
                'port': 58610
            }
        )
    
    def _create_mock_stock_data(self) -> pd.DataFrame:
        """创建模拟股票数据"""
        # 生成足够的数据点（300个交易日）以满足RS计算需求
        dates = pd.date_range(start='2023-01-01', periods=400, freq='D')
        dates = [d for d in dates if d.weekday() < 5][:300]  # 只保留工作日，取前300个
        
        np.random.seed(42)
        n_days = len(dates)
        
        # 生成价格数据
        base_price = 10.0
        price_changes = np.random.normal(0, 0.02, n_days)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 0.1))
        
        # 生成OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            open_price = close * (1 + np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
            volume = int(np.random.uniform(1000000, 10000000))
            amount = volume * close
            
            data.append({
                'trade_date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    @patch('src.data_sources.xtdata_adapter.XtDataAdapter')
    def test_data_service_integration(self, mock_adapter_class):
        """测试数据服务集成"""
        self.logger.info("开始测试数据服务集成...")
        
        # 配置模拟适配器
        mock_adapter = Mock()
        mock_adapter.connect.return_value = True
        mock_adapter.get_stock_list.return_value = self.mock_stock_list
        mock_adapter.get_market_data.return_value = Mock(data=self.mock_stock_data)
        mock_adapter_class.return_value = mock_adapter
        
        # 创建数据服务
        data_service = DataService()
        
        # 测试股票列表加载
        stock_list = data_service.load_stock_list()
        self.assertIsInstance(stock_list, list)
        self.logger.info(f"✅ 股票列表加载成功，共{len(stock_list)}只股票")
        
        # 测试市场数据获取
        if stock_list:
            symbol = stock_list[0].symbol
            market_data = data_service.get_stock_data(symbol)
            self.assertIsNotNone(market_data)
            self.logger.info(f"✅ 市场数据获取成功：{symbol}")
    
    def test_wyckoff_engine_integration(self):
        """测试威科夫分析引擎集成"""
        self.logger.info("开始测试威科夫分析引擎集成...")
        
        # 创建威科夫分析引擎
        wyckoff_engine = WyckoffAnalysisEngine()
        
        # 测试分析功能
        symbol = "000001.SZ"
        analysis_result = wyckoff_engine.analyze_market_structure(self.mock_stock_data)
        
        self.assertIsNotNone(analysis_result)
        self.assertHasAttr(analysis_result, 'phase')
        self.assertHasAttr(analysis_result, 'support_level')
        self.logger.info(f"✅ 威科夫分析完成：{symbol}")
        self.logger.info(f"   市场阶段：{analysis_result.phase.value}")
        self.logger.info(f"   支撑位：{analysis_result.support_level:.2f}")
        self.logger.info(f"   阻力位：{analysis_result.resistance_level:.2f}")
    
    def test_relative_strength_engine_integration(self):
        """测试相对强弱引擎集成"""
        self.logger.info("开始测试相对强弱引擎集成...")
        
        # 创建相对强弱引擎
        rs_engine = RelativeStrengthEngine()
        
        # 创建基准数据（模拟指数数据）
        benchmark_data = self.mock_stock_data.copy()
        benchmark_data['close'] = benchmark_data['close'] * 0.8  # 模拟指数表现稍差
        
        # 测试相对强弱计算
        symbol = "000001.SZ"
        rs_result = rs_engine.calculate_rs_value(
            self.mock_stock_data,
            benchmark_data
        )
        
        self.assertIsNotNone(rs_result)
        self.assertIsInstance(rs_result, float)
        self.assertGreater(rs_result, 0)
        self.logger.info(f"✅ 相对强弱计算完成：{symbol}")
        self.logger.info(f"   RS值：{rs_result:.2f}")
    
    def test_selection_engine_integration(self):
        """测试选股引擎集成"""
        self.logger.info("开始测试选股引擎集成...")
        
        # 创建选股引擎
        selection_engine = StockSelectionEngine()

        # 配置策略
        from src.engines.selection import WyckoffStrategy, RelativeStrengthStrategy, StrategyConfig, StrategyType
        wyckoff_config = StrategyConfig(name="wyckoff", strategy_type=StrategyType.WYCKOFF_ACCUMULATION, weight=0.5)
        rs_config = StrategyConfig(name="rs", strategy_type=StrategyType.RELATIVE_STRENGTH, weight=0.5)
        wyckoff_strategy = WyckoffStrategy(wyckoff_config)
        rs_strategy = RelativeStrengthStrategy(rs_config)
        selection_engine.add_strategy(wyckoff_strategy)
        selection_engine.add_strategy(rs_strategy)

        # 准备股票数据字典
        stock_data_dict = {}
        for stock in self.mock_stock_list:
            # 为每只股票创建略有不同的数据
            data = self.mock_stock_data.copy()
            # 添加一些随机变化
            multiplier = np.random.uniform(0.8, 1.2)
            data['close'] = data['close'] * multiplier
            data['volume'] = data['volume'] * np.random.uniform(0.5, 2.0)
            stock_data_dict[stock['code']] = data

        # 设置基准数据
        selection_engine.set_benchmark(self.mock_stock_data)

        # 测试选股功能
        selection_result = selection_engine.select_stocks(
            stock_data_dict,
            max_selections=3
        )
        
        self.assertIsNotNone(selection_result)
        self.assertIsInstance(selection_result, list)
        self.logger.info(f"✅ 选股完成，选出{len(selection_result)}只股票")
        
        for i, stock in enumerate(selection_result[:3]):
            self.logger.info(f"   {i+1}. {stock.symbol} - 得分: {stock.score:.2f}")
    
    def test_cache_integration(self):
        """测试缓存系统集成"""
        self.logger.info("开始测试缓存系统集成...")
        
        # 创建缓存管理器
        cache_manager = CacheManager()
        
        # 测试缓存存储和读取
        test_key = "test_stock_data"
        test_data = self.mock_stock_data
        
        # 存储数据
        cache_manager.set(test_key, test_data)
        self.logger.info("✅ 数据已存储到缓存")
        
        # 读取数据
        cached_data = cache_manager.get(test_key)
        self.assertIsNotNone(cached_data)
        self.assertTrue(cached_data.equals(test_data))
        self.logger.info("✅ 数据从缓存读取成功")
        
        # 测试缓存过期
        cache_manager.delete(test_key)
        expired_data = cache_manager.get(test_key)
        self.assertIsNone(expired_data)
        self.logger.info("✅ 缓存删除功能正常")
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        self.logger.info("开始测试端到端工作流程...")
        
        try:
            # 1. 数据获取阶段
            self.logger.info("阶段1: 数据获取")
            stock_symbols = [stock['code'] for stock in self.mock_stock_list]
            stock_data_dict = {}
            
            for symbol in stock_symbols:
                # 模拟数据获取
                data = self.mock_stock_data.copy()
                multiplier = np.random.uniform(0.8, 1.2)
                data['close'] = data['close'] * multiplier
                stock_data_dict[symbol] = data
            
            self.logger.info(f"✅ 获取了{len(stock_data_dict)}只股票的数据")
            
            # 2. 威科夫分析阶段
            self.logger.info("阶段2: 威科夫分析")
            wyckoff_engine = WyckoffAnalysisEngine()
            wyckoff_results = {}
            
            for symbol, data in stock_data_dict.items():
                result = wyckoff_engine.analyze_market_structure(data)
                wyckoff_results[symbol] = result
            
            self.logger.info(f"✅ 完成{len(wyckoff_results)}只股票的威科夫分析")
            
            # 3. 相对强弱分析阶段
            self.logger.info("阶段3: 相对强弱分析")
            rs_engine = RelativeStrengthEngine()
            rs_results = {}
            
            # 使用第一只股票作为基准
            benchmark_data = list(stock_data_dict.values())[0]
            
            for symbol, data in stock_data_dict.items():
                result = rs_engine.calculate_rs_value(data, benchmark_data)
                rs_results[symbol] = result
            
            self.logger.info(f"✅ 完成{len(rs_results)}只股票的相对强弱分析")
            
            # 4. 智能选股阶段
            self.logger.info("阶段4: 智能选股")
            selection_engine = StockSelectionEngine()

            # 配置策略
            from src.engines.selection import WyckoffStrategy, RelativeStrengthStrategy, StrategyConfig, StrategyType
            wyckoff_config = StrategyConfig(name="wyckoff", strategy_type=StrategyType.WYCKOFF_ACCUMULATION, weight=0.5)
            rs_config = StrategyConfig(name="rs", strategy_type=StrategyType.RELATIVE_STRENGTH, weight=0.5)
            wyckoff_strategy = WyckoffStrategy(wyckoff_config)
            rs_strategy = RelativeStrengthStrategy(rs_config)
            selection_engine.add_strategy(wyckoff_strategy)
            selection_engine.add_strategy(rs_strategy)

            # 设置基准数据
            selection_engine.set_benchmark(list(stock_data_dict.values())[0])

            final_selections = selection_engine.select_stocks(
                stock_data_dict,
                max_selections=3
            )
            
            self.logger.info(f"✅ 选股完成，最终选出{len(final_selections)}只股票")
            
            # 5. 结果验证
            self.assertIsInstance(final_selections, list)
            self.assertGreater(len(final_selections), 0)
            
            for i, stock in enumerate(final_selections):
                self.assertHasAttr(stock, 'symbol')
                self.assertHasAttr(stock, 'score')
                self.logger.info(f"   选股结果{i+1}: {stock.symbol} (得分: {stock.score:.2f})")
            
            self.logger.info("🎉 端到端工作流程测试完成！")
            
        except Exception as e:
            self.logger.error(f"❌ 端到端测试失败: {e}")
            raise
    
    def test_error_handling(self):
        """测试错误处理机制"""
        self.logger.info("开始测试错误处理机制...")
        
        # 测试空数据处理
        wyckoff_engine = WyckoffAnalysisEngine()
        empty_data = pd.DataFrame()
        
        try:
            result = wyckoff_engine.analyze("TEST.SZ", empty_data)
            # 应该返回默认结果而不是抛出异常
            self.assertIsNotNone(result)
            self.logger.info("✅ 空数据错误处理正常")
        except Exception as e:
            self.logger.warning(f"⚠️ 空数据处理需要改进: {e}")
        
        # 测试无效股票代码处理
        try:
            rs_engine = RelativeStrengthEngine()
            invalid_data = pd.DataFrame({'close': [1, 2, 3]})  # 缺少必要列
            result = rs_engine.calculate_relative_strength("INVALID", invalid_data, invalid_data)
            self.logger.info("✅ 无效数据错误处理正常")
        except Exception as e:
            self.logger.warning(f"⚠️ 无效数据处理需要改进: {e}")


def run_integration_tests():
    """运行集成测试"""
    print("=" * 80)
    print("威科夫相对强弱选股系统 - 集成测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestSystemIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    print(f"运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n总体成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
