# 威科夫相对强弱选股流程详细说明

## 流程概述

本选股系统基于板块相对强弱分析，通过多层筛选机制发现优质个股。核心思路是先从板块层面筛选出强于大盘的板块，再从板块内部筛选出强于板块的个股，实现双重相对强弱筛选。

## 详细执行流程

### 第一步：数据准备与维护

#### 1.1 板块指数数据库建设

- **数据范围**：根据用户选择下载对应板块指数体系
  - 行业板块指数（如申万一级行业、中信行业等）
  - 概念板块指数（如新能源、人工智能、芯片等）
  - 用户可自定义选择单一或多种板块体系
- **存储结构**：建立完整的本地数据库，包含：
  - 板块指数基本信息表
  - 板块指数历史行情数据表
  - 板块成分股对应关系表
- **数据完整性**：确保所有选定板块指数的历史数据完整下载（确保数据库中有近一年数据）

#### 1.2 智能数据更新机制

- **定期更新**：设置自动更新任务，定期同步最新数据
  - 每日收盘后自动更新前一交易日数据
  - 每周或每月更新一次历史数据
  - 按需更新
- **概念板块特殊处理**：
  - 概念板块成分股变动频繁，需要更高频率的同步
  - 新概念板块的自动发现和添加
  - 失效概念板块的标记和处理
- **增量更新**：只更新变化的数据，提高更新效率
- **数据验证**：更新后进行数据完整性和一致性检查

### 第二步：时间区间设定与基础计算

#### 2.1 用户参数设置

- **时间区间**：用户自定义起始日期和结束日期
  - 默认设置为最近八个交易日
  - 支持自定义时间段（用户可以一次选中多个时间段进行并行计算，这样可以并行进行多个时间区间的筛选大大提升效率）
- **板块选择**：用户选择要分析的板块指数体系（主要就是行业板块和概念板块）
- **筛选参数**：用户设定每个板块内选取的个股数量（默认前3只，可自定义），用户可以设定选取涨幅较大盘指数强的排名前多少的行业板块指数或者概念板块指数（默认前5只，可自定义）

#### 2.2 涨幅预计算（核心优化点）

为提高后续计算效率，在开始筛选前一次性计算所有必要的涨幅数据：

**大盘指数涨幅计算**

```
大盘涨幅 = (期末收盘价 - 期初收盘价) / 期初收盘价 × 100%
```

**板块指数涨幅计算**

```
板块涨幅 = (板块期末收盘价 - 板块期初收盘价) / 板块期初收盘价 × 100%
```

**个股涨幅计算**

```
个股涨幅 = (个股期末收盘价 - 个股期初收盘价) / 个股期初收盘价 × 100%
```

#### 2.3 数据缓存策略

- 将计算结果存储在内存或临时数据库中
- 建立涨幅数据索引，支持快速查询
- 为后续多轮筛选提供高效数据支持

### 第三步：板块相对强弱筛选

#### 3.1 板块与大盘比较

- **筛选条件**：板块涨幅 > 大盘涨幅
- **筛选逻辑**：
  ```
  强势板块 = {板块 | 板块涨幅 > 大盘涨幅}
  ```
- **结果排序**：按板块相对强弱程度排序
  ```
  相对强弱度 = 板块涨幅 - 大盘涨幅
  ```

#### 3.2 强势板块列表生成

- 输出所有满足条件的板块清单
- 记录每个板块的具体涨幅和相对强弱度
- 为下一步个股筛选做准备

### 第四步：板块内个股筛选

#### 4.1 成分股获取

- 从板块成分股对应关系表中获取每个强势板块的全部成分股
- 排除异常股票（ST股、停牌股、新股等）
- 确保成分股数据在指定时间区间内完整

#### 4.2 个股与板块比较

- **筛选条件**：个股涨幅 > 所属板块涨幅
- **筛选逻辑**：
  ```
  板块内强势股 = {个股 | 个股涨幅 > 所属板块涨幅}
  ```

#### 4.3 个股排名与筛选

- **排序规则**：按个股相对于板块的强弱程度排序
  ```
  个股相对强弱度 = 个股涨幅 - 板块涨幅
  ```
- **数量控制**：每个板块选取排名前N只个股（用户可配置，默认3只）
- **去重处理**：处理个股在多个板块中都入选的情况

### 第五步：结果汇总与输出

#### 5.1 候选股票池生成

- 汇总所有板块筛选出的个股
- 建立完整的候选股票清单
- 记录每只股票的详细信息：
  - 股票代码和名称
  - 所属板块
  - 个股涨幅
  - 板块涨幅
  - 在板块内的排名

#### 5.2 结果存储与缓存

- 将筛选结果保存到数据库
- 建立结果索引，支持后续查询和分析
- 为进一步的集合运算和多阶段分析做准备

## 技术实现要点

### 数据结构设计

```python
# 涨幅缓存数据结构
price_changes = {
    'market_index': float,  # 大盘涨幅
    'sectors': {
        'sector_code': float,  # 板块涨幅
        ...
    },
    'stocks': {
        'stock_code': float,  # 个股涨幅
        ...
    }
}

# 筛选结果数据结构
selection_result = {
    'timestamp': datetime,
    'time_range': (start_date, end_date),
    'market_index': str,
    'strong_sectors': [
        {
            'sector_code': str,
            'sector_name': str,
            'sector_return': float,
            'relative_strength': float,
            'selected_stocks': [
                {
                    'stock_code': str,
                    'stock_name': str,
                    'stock_return': float,
                    'relative_strength': float,
                    'rank_in_sector': int
                }
            ]
        }
    ]
}
```

### 性能优化策略

1. **批量计算**：一次性计算所有涨幅，避免重复计算
2. **并行处理**：多线程处理不同板块的个股筛选
3. **索引优化**：对常用查询字段建立数据库索引
4. **缓存机制**：缓存计算结果，支持快速重复查询

### 数据质量保证

1. **异常处理**：处理停牌、除权除息等特殊情况
2. **数据验证**：确保价格数据的连续性和准确性
3. **容错机制**：网络异常时的重试和降级策略
4. **日志记录**：详细记录筛选过程和异常情况

## 扩展功能支持

### 多时间段分析

- 支持将时间区间划分为多个阶段
- 每个阶段独立执行筛选流程
- 支持阶段间的集合运算（交集、并集、差集等）

### 高级筛选条件

- 支持添加市值、成交量等额外筛选条件
- 支持自定义筛选公式
- 支持技术指标筛选

### 结果分析工具

- 筛选结果的统计分析
- 历史表现回测
- 板块轮动分析
- 个股表现追踪

## 总结

本选股流程通过双重相对强弱筛选机制，能够有效识别出在特定时间段内表现优异的个股。通过预计算和缓存策略，大幅提升了计算效率。完整的数据维护机制确保了筛选结果的准确性和时效性。该流程为后续的多阶段分析和集合运算提供了坚实的基础。
