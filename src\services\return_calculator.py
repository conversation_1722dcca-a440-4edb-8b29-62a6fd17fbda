#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涨幅计算和缓存系统
实现需求文档第2步的核心优化：批量涨幅计算和缓存策略
"""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import pandas as pd
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..database.manager import DatabaseManager
from ..services.sector_manager import SectorManager
from ..services.market_index_manager import MarketIndexManager
from ..utils.logger import get_logger
from ..utils.cache import CacheManager
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


@dataclass
class ReturnData:
    """收益率数据类"""
    symbol: str
    symbol_type: str  # 'market', 'sector', 'stock'
    start_date: str
    end_date: str
    start_price: float
    end_price: float
    return_rate: float
    calculation_time: datetime = field(default_factory=datetime.now)


@dataclass
class ReturnCacheKey:
    """收益率缓存键"""
    symbol: str
    symbol_type: str
    start_date: str
    end_date: str
    
    def to_string(self) -> str:
        """转换为字符串键"""
        return f"{self.symbol_type}:{self.symbol}:{self.start_date}:{self.end_date}"


class ReturnCalculator:
    """涨幅计算器"""
    
    def __init__(self,
                 db_manager: DatabaseManager,
                 sector_manager: SectorManager,
                 market_index_manager: MarketIndexManager,
                 cache_manager: Optional[CacheManager] = None):
        self.db_manager = db_manager
        self.sector_manager = sector_manager
        self.market_index_manager = market_index_manager
        self.cache_manager = cache_manager or CacheManager()
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 缓存配置
        self.cache_ttl = 3600  # 缓存1小时
        self.batch_size = 100  # 批量处理大小
    
    async def calculate_batch_returns(self, 
                                    symbols: List[str],
                                    symbol_type: str,
                                    start_date: str,
                                    end_date: str,
                                    use_cache: bool = True) -> Dict[str, float]:
        """
        批量计算收益率
        
        Args:
            symbols: 代码列表
            symbol_type: 类型 ('market', 'sector', 'stock')
            start_date: 开始日期
            end_date: 结束日期
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, float]: 收益率字典
        """
        try:
            logger.info(f"开始批量计算{symbol_type}收益率，数量：{len(symbols)}")
            
            results = {}
            cache_misses = []
            
            # 1. 检查缓存
            if use_cache:
                for symbol in symbols:
                    cache_key = ReturnCacheKey(symbol, symbol_type, start_date, end_date)
                    cached_result = self.cache_manager.get(cache_key.to_string())
                    
                    if cached_result is not None:
                        results[symbol] = cached_result
                    else:
                        cache_misses.append(symbol)
            else:
                cache_misses = symbols.copy()
            
            logger.info(f"缓存命中：{len(results)}，需要计算：{len(cache_misses)}")
            
            # 2. 批量计算缓存未命中的数据
            if cache_misses:
                calculated_results = await self._calculate_returns_parallel(
                    cache_misses, symbol_type, start_date, end_date
                )
                
                # 3. 更新缓存和结果
                for symbol, return_rate in calculated_results.items():
                    results[symbol] = return_rate
                    
                    if use_cache:
                        cache_key = ReturnCacheKey(symbol, symbol_type, start_date, end_date)
                        self.cache_manager.set(
                            cache_key.to_string(), 
                            return_rate, 
                            ttl=self.cache_ttl
                        )
            
            logger.info(f"批量收益率计算完成，成功计算{len(results)}个")
            return results
            
        except Exception as e:
            logger.error(f"批量计算收益率失败: {e}")
            raise CalculationError(f"批量计算收益率失败: {e}")
    
    async def _calculate_returns_parallel(self, 
                                        symbols: List[str],
                                        symbol_type: str,
                                        start_date: str,
                                        end_date: str) -> Dict[str, float]:
        """并行计算收益率"""
        try:
            results = {}
            
            # 分批处理
            batches = [symbols[i:i + self.batch_size] 
                      for i in range(0, len(symbols), self.batch_size)]
            
            for batch in batches:
                # 提交并行任务
                futures = []
                for symbol in batch:
                    future = asyncio.get_event_loop().run_in_executor(
                        self.executor,
                        self._calculate_single_return,
                        symbol, symbol_type, start_date, end_date
                    )
                    futures.append((symbol, future))
                
                # 等待批次完成
                for symbol, future in futures:
                    try:
                        return_rate = await future
                        if return_rate is not None:
                            results[symbol] = return_rate
                    except Exception as e:
                        logger.warning(f"计算{symbol}收益率失败: {e}")
                        continue
                
                # 避免过载
                await asyncio.sleep(0.01)
            
            return results
            
        except Exception as e:
            logger.error(f"并行计算收益率失败: {e}")
            return {}
    
    def _calculate_single_return(self, 
                               symbol: str,
                               symbol_type: str,
                               start_date: str,
                               end_date: str) -> Optional[float]:
        """计算单个收益率"""
        try:
            if symbol_type == 'market':
                return self._calculate_market_return(symbol, start_date, end_date)
            elif symbol_type == 'sector':
                return self._calculate_sector_return(symbol, start_date, end_date)
            elif symbol_type == 'stock':
                return self._calculate_stock_return(symbol, start_date, end_date)
            else:
                raise ValueError(f"不支持的类型: {symbol_type}")
                
        except Exception as e:
            logger.warning(f"计算{symbol}收益率失败: {e}")
            return None
    
    def _calculate_market_return(self,
                               index_code: str,
                               start_date: str,
                               end_date: str) -> Optional[float]:
        """计算市场指数收益率 - 使用真实数据"""
        try:
            # 使用市场指数管理器计算真实收益率
            return self.market_index_manager.calculate_index_return(
                index_code, start_date, end_date
            )

        except Exception as e:
            logger.error(f"计算市场指数{index_code}收益率失败: {e}")
            return None
    
    def _calculate_sector_return(self, 
                               sector_code: str,
                               start_date: str,
                               end_date: str) -> Optional[float]:
        """计算板块收益率"""
        try:
            return self.sector_manager.calculate_sector_return(
                sector_code, start_date, end_date
            )
            
        except Exception as e:
            logger.error(f"计算板块{sector_code}收益率失败: {e}")
            return None
    
    def _calculate_stock_return(self, 
                              stock_code: str,
                              start_date: str,
                              end_date: str) -> Optional[float]:
        """计算个股收益率"""
        try:
            sql = """
            SELECT close FROM stock_quotes 
            WHERE stock_code = ? AND trade_date = ?
            ORDER BY trade_date LIMIT 1
            """
            
            # 获取起始价格
            start_results = self.db_manager.execute_query(sql, (stock_code, start_date))
            if not start_results:
                return None
            start_price = start_results[0][0]
            
            # 获取结束价格
            end_results = self.db_manager.execute_query(sql, (stock_code, end_date))
            if not end_results:
                return None
            end_price = end_results[0][0]
            
            if start_price <= 0:
                return None
            
            return (end_price - start_price) / start_price
            
        except Exception as e:
            logger.error(f"计算个股{stock_code}收益率失败: {e}")
            return None
    
    async def precompute_returns(self, 
                               time_ranges: List[Tuple[str, str]],
                               progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        预计算收益率数据
        
        Args:
            time_ranges: 时间区间列表
            progress_callback: 进度回调
            
        Returns:
            Dict: 预计算结果统计
        """
        try:
            logger.info(f"开始预计算收益率，时间区间数量：{len(time_ranges)}")
            
            stats = {
                'total_ranges': len(time_ranges),
                'completed_ranges': 0,
                'market_returns': {},
                'sector_returns': {},
                'stock_returns': {},
                'errors': []
            }
            
            for i, (start_date, end_date) in enumerate(time_ranges):
                try:
                    if progress_callback:
                        progress = (i / len(time_ranges)) * 100
                        progress_callback(f"预计算时间区间: {start_date} - {end_date}", progress)
                    
                    # 1. 计算市场指数收益率
                    market_symbols = list(self.market_indices.keys())
                    market_returns = await self.calculate_batch_returns(
                        market_symbols, 'market', start_date, end_date
                    )
                    stats['market_returns'][f"{start_date}_{end_date}"] = market_returns
                    
                    # 2. 计算板块收益率
                    sector_list = self.sector_manager.get_sector_list()
                    sector_symbols = [s['sector_code'] for s in sector_list]
                    sector_returns = await self.calculate_batch_returns(
                        sector_symbols, 'sector', start_date, end_date
                    )
                    stats['sector_returns'][f"{start_date}_{end_date}"] = sector_returns
                    
                    # 3. 计算个股收益率（限制数量避免过载）
                    stock_symbols = self._get_active_stock_list(limit=1000)
                    stock_returns = await self.calculate_batch_returns(
                        stock_symbols, 'stock', start_date, end_date
                    )
                    stats['stock_returns'][f"{start_date}_{end_date}"] = stock_returns
                    
                    stats['completed_ranges'] += 1
                    
                except Exception as e:
                    error_msg = f"预计算时间区间{start_date}-{end_date}失败: {e}"
                    logger.warning(error_msg)
                    stats['errors'].append(error_msg)
                    continue
            
            logger.info(f"预计算完成，成功{stats['completed_ranges']}/{stats['total_ranges']}个时间区间")
            return stats
            
        except Exception as e:
            logger.error(f"预计算收益率失败: {e}")
            raise CalculationError(f"预计算收益率失败: {e}")
    
    def _get_active_stock_list(self, limit: Optional[int] = None) -> List[str]:
        """获取活跃股票列表"""
        try:
            sql = "SELECT stock_code FROM stock_info WHERE status = 'active' AND is_st = 0"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            results = self.db_manager.execute_query(sql)
            return [row[0] for row in results]
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_cached_returns(self, 
                          symbols: List[str],
                          symbol_type: str,
                          start_date: str,
                          end_date: str) -> Dict[str, Optional[float]]:
        """获取缓存的收益率数据"""
        try:
            results = {}
            
            for symbol in symbols:
                cache_key = ReturnCacheKey(symbol, symbol_type, start_date, end_date)
                cached_result = self.cache_manager.get(cache_key.to_string())
                results[symbol] = cached_result
            
            return results
            
        except Exception as e:
            logger.error(f"获取缓存收益率失败: {e}")
            return {symbol: None for symbol in symbols}
    
    def clear_cache(self, pattern: Optional[str] = None) -> bool:
        """清除缓存"""
        try:
            if pattern:
                # 清除匹配模式的缓存
                return self.cache_manager.clear_pattern(pattern)
            else:
                # 清除所有缓存
                return self.cache_manager.clear_all()
                
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            return False
