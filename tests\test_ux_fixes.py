"""
用户体验修复验证测试

验证数据源连接测试和用户向导内容显示的修复效果
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig, ConnectionException
from src.ui.utils.user_guide import WelcomeGuideDialog, UserGuideManager
from src.ui.components.data_source_config_widget import ConnectionTestThread


class TestXtDataConnectionFixes(unittest.TestCase):
    """XtData连接测试修复验证"""
    
    def setUp(self):
        """设置测试"""
        self.config = DataSourceConfig(
            name="test_xtdata",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                'type': 'xtdata',
                'ip': '127.0.0.1',
                'port': 58610
            }
        )
    
    def test_xtdata_adapter_initialization_without_module(self):
        """测试XtData适配器在没有模块时的初始化"""
        # 模拟xtdata模块不存在的情况
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            adapter = XtDataAdapter(self.config)
            
            # 验证适配器可以正常初始化
            self.assertIsNotNone(adapter)
            self.assertIsNone(adapter.xtdata)
            self.assertFalse(adapter._is_connected)
    
    def test_connection_with_missing_module(self):
        """测试缺少模块时的连接行为"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            adapter = XtDataAdapter(self.config)
            
            # 尝试连接应该抛出详细的错误信息
            with self.assertRaises(ConnectionException) as context:
                adapter.connect()
            
            error_message = str(context.exception)
            self.assertIn("XtData模块不可用", error_message)
            self.assertIn("安装步骤", error_message)
            self.assertIn("MiniQMT", error_message)
    
    def test_installation_guide_content(self):
        """测试安装指南内容"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            adapter = XtDataAdapter(self.config)
            guide = adapter._get_xtdata_installation_guide()
            
            # 验证指南包含必要信息
            self.assertIn("安装步骤", guide)
            self.assertIn("MiniQMT", guide)
            self.assertIn("pip install xtquant", guide)
            self.assertIn("常见问题", guide)
            self.assertIn("技术支持", guide)
    
    def test_connection_troubleshooting_guide(self):
        """测试连接故障排除指南"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            adapter = XtDataAdapter(self.config)
            guide = adapter._get_connection_troubleshooting_guide()
            
            # 验证故障排除指南包含必要信息
            self.assertIn("故障排除步骤", guide)
            self.assertIn("127.0.0.1", guide)  # IP地址
            self.assertIn("58610", guide)      # 端口号
            self.assertIn("防火墙", guide)
            self.assertIn("API服务", guide)


class TestConnectionTestThread(unittest.TestCase):
    """连接测试线程修复验证"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.config = DataSourceConfig(
            name="test_xtdata",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                'type': 'xtdata',
                'ip': '127.0.0.1',
                'port': 58610
            }
        )
    
    def test_connection_test_thread_error_handling(self):
        """测试连接测试线程的错误处理"""
        thread = ConnectionTestThread(self.config)
        
        # 模拟连接失败
        with patch('src.data_sources.xtdata_adapter.XtDataAdapter') as mock_adapter_class:
            mock_adapter = Mock()
            mock_adapter.connect.side_effect = ConnectionException("XtData模块不可用，请检查MiniQMT安装")
            mock_adapter_class.return_value = mock_adapter
            
            # 捕获信号
            test_completed_signal = Mock()
            thread.test_completed.connect(test_completed_signal)
            
            # 运行测试
            thread.run()
            
            # 验证信号被正确发出
            test_completed_signal.assert_called_once()
            args = test_completed_signal.call_args[0]
            
            self.assertEqual(args[0], "test_xtdata")  # 数据源名称
            self.assertFalse(args[1])                 # 连接失败
            self.assertIn("XtData模块不可用", args[2])  # 错误消息


class TestUserGuideContentFixes(unittest.TestCase):
    """用户向导内容显示修复验证"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_welcome_guide_dialog_content_display(self):
        """测试欢迎向导对话框内容显示"""
        dialog = WelcomeGuideDialog()
        
        # 验证对话框创建成功
        self.assertIsNotNone(dialog)
        self.assertIsNotNone(dialog.step_content)
        self.assertIsNotNone(dialog.step_title)
        
        # 验证步骤内容不为空
        self.assertGreater(len(dialog.steps), 0)
        
        # 验证第一步内容
        first_step = dialog.steps[0]
        self.assertIsNotNone(first_step.title)
        self.assertIsNotNone(first_step.content)
        self.assertGreater(len(first_step.content), 0)
        
        # 验证内容显示
        dialog._show_current_step()
        
        # 检查标题和内容是否正确设置
        self.assertEqual(dialog.step_title.text(), first_step.title)
        self.assertEqual(dialog.step_content.toPlainText(), first_step.content)
    
    def test_guide_step_navigation(self):
        """测试向导步骤导航"""
        dialog = WelcomeGuideDialog()
        
        initial_step = dialog.current_step
        
        # 测试下一步
        dialog._next_step()
        self.assertEqual(dialog.current_step, initial_step + 1)
        
        # 测试上一步
        dialog._prev_step()
        self.assertEqual(dialog.current_step, initial_step)
    
    def test_guide_content_quality(self):
        """测试向导内容质量"""
        dialog = WelcomeGuideDialog()
        
        for i, step in enumerate(dialog.steps):
            with self.subTest(step=i):
                # 验证标题不为空
                self.assertIsNotNone(step.title)
                self.assertGreater(len(step.title.strip()), 0)
                
                # 验证内容不为空
                self.assertIsNotNone(step.content)
                self.assertGreater(len(step.content.strip()), 0)
                
                # 验证内容包含有用信息
                if i == 0:  # 欢迎页面
                    self.assertIn("威科夫", step.content)
                    self.assertIn("相对强弱", step.content)
                elif i == 1:  # 股票数据页面
                    self.assertIn("股票数据", step.content)
                    self.assertIn("刷新", step.content)
    
    def test_guide_styling(self):
        """测试向导样式设置"""
        dialog = WelcomeGuideDialog()
        
        # 验证内容区域样式
        content_style = dialog.step_content.styleSheet()
        self.assertIn("background: white", content_style)
        self.assertIn("color: #333", content_style)
        self.assertIn("font-size: 14px", content_style)
        
        # 验证标题样式
        title_style = dialog.step_title.styleSheet()
        self.assertIn("font-size: 18px", title_style)
        self.assertIn("font-weight: bold", title_style)


class TestUserGuideManager(unittest.TestCase):
    """用户向导管理器测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        self.guide_manager = UserGuideManager(self.main_window)
    
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
    
    def test_guide_manager_initialization(self):
        """测试向导管理器初始化"""
        self.assertIsNotNone(self.guide_manager)
        self.assertEqual(self.guide_manager.main_window, self.main_window)
    
    def test_should_show_guide_default(self):
        """测试默认应该显示向导"""
        # 在没有配置文件的情况下，应该显示向导
        should_show = self.guide_manager.should_show_welcome_guide()
        self.assertTrue(should_show)


class TestIntegratedUserExperience(unittest.TestCase):
    """集成用户体验测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_error_message_user_friendliness(self):
        """测试错误消息的用户友好性"""
        config = DataSourceConfig(
            name="test_xtdata",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                'type': 'xtdata',
                'ip': '127.0.0.1',
                'port': 58610
            }
        )
        
        with patch('builtins.__import__', side_effect=ImportError("No module named 'xtdata'")):
            adapter = XtDataAdapter(config)
            
            try:
                adapter.connect()
            except ConnectionException as e:
                error_msg = str(e)
                
                # 验证错误消息的用户友好性
                self.assertNotIn("ImportError", error_msg)  # 不应包含技术性错误
                self.assertNotIn("Traceback", error_msg)    # 不应包含堆栈跟踪
                self.assertIn("安装步骤", error_msg)          # 应包含解决方案
                self.assertIn("MiniQMT", error_msg)         # 应包含具体软件名
    
    def test_guide_content_completeness(self):
        """测试向导内容完整性"""
        dialog = WelcomeGuideDialog()
        
        # 验证向导步骤数量合理
        self.assertGreaterEqual(len(dialog.steps), 5)
        self.assertLessEqual(len(dialog.steps), 10)
        
        # 验证每个步骤都有实质内容
        for step in dialog.steps:
            self.assertGreater(len(step.content), 100)  # 每步至少100字符
            self.assertIn("•", step.content)            # 包含要点列表


if __name__ == '__main__':
    unittest.main()
