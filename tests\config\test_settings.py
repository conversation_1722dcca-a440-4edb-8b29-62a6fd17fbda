"""
设置类测试用例

测试Settings和SystemSettings数据类的功能：
- 数据类初始化
- 配置验证
- 类型转换
- 默认值处理
"""

import pytest
from dataclasses import FrozenInstanceError

from src.config.settings import (
    Settings, SystemSettings, DatabaseSettings, 
    DataSourceSettings, LoggingSettings
)
from src.utils.exceptions import ConfigurationError


class TestDatabaseSettings:
    """数据库设置测试"""

    def test_default_values(self):
        """测试默认值"""
        db_settings = DatabaseSettings()
        assert db_settings.connection_pool_size == 10
        assert db_settings.connection_timeout == 30
        assert db_settings.query_timeout == 60
        assert db_settings.enable_wal_mode is True
        assert db_settings.enable_foreign_keys is True

    def test_custom_values(self):
        """测试自定义值"""
        db_settings = DatabaseSettings(
            connection_pool_size=20,
            connection_timeout=60,
            query_timeout=120
        )
        assert db_settings.connection_pool_size == 20
        assert db_settings.connection_timeout == 60
        assert db_settings.query_timeout == 120


class TestLoggingSettings:
    """日志设置测试"""

    def test_default_values(self):
        """测试默认值"""
        from src.config.settings import LogLevel
        log_settings = LoggingSettings()
        assert log_settings.level == LogLevel.INFO
        assert log_settings.file_logging is True
        assert log_settings.console_logging is True
        assert log_settings.max_file_size_mb == 10
        assert log_settings.backup_count == 5

    def test_valid_log_levels(self):
        """测试有效的日志级别"""
        from src.config.settings import LogLevel
        valid_levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARNING, LogLevel.ERROR]
        for level in valid_levels:
            log_settings = LoggingSettings(level=level)
            assert log_settings.level == level


class TestDataSourceSettings:
    """数据源设置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        ds_settings = DataSourceSettings()
        assert ds_settings.primary == "xtdata"
        assert ds_settings.fallback == []
        assert ds_settings.timeout == 30
        assert ds_settings.retry_count == 3
        assert ds_settings.retry_delay == 1.0
    
    def test_custom_values(self):
        """测试自定义值"""
        ds_settings = DataSourceSettings(
            primary="custom",
            fallback=["backup1", "backup2"],
            timeout=60,
            retry_count=5
        )
        assert ds_settings.primary == "custom"
        assert ds_settings.fallback == ["backup1", "backup2"]
        assert ds_settings.timeout == 60
        assert ds_settings.retry_count == 5


class TestSystemSettings:
    """系统设置测试"""
    
    def test_default_initialization(self):
        """测试默认初始化"""
        sys_settings = SystemSettings()
        
        # 检查子设置对象
        assert isinstance(sys_settings.database, DatabaseSettings)
        assert isinstance(sys_settings.logging, LoggingSettings)
        assert isinstance(sys_settings.data_sources, DataSourceSettings)
        
        # 检查系统级设置
        assert sys_settings.cache_size == 1000
        assert sys_settings.max_workers == 4
        assert sys_settings.debug_mode is False
    
    def test_custom_initialization(self):
        """测试自定义初始化"""
        custom_db = DatabaseSettings(path="custom.db")
        custom_log = LoggingSettings(level="DEBUG")
        
        sys_settings = SystemSettings(
            database=custom_db,
            logging=custom_log,
            cache_size=2000,
            debug_mode=True
        )
        
        assert sys_settings.database.path == "custom.db"
        assert sys_settings.logging.level == "DEBUG"
        assert sys_settings.cache_size == 2000
        assert sys_settings.debug_mode is True
    
    def test_immutability(self):
        """测试不可变性"""
        sys_settings = SystemSettings()
        
        # 尝试修改应该抛出异常
        with pytest.raises(FrozenInstanceError):
            sys_settings.cache_size = 2000


class TestSettings:
    """主设置类测试"""
    
    def test_default_initialization(self):
        """测试默认初始化"""
        settings = Settings()
        
        assert isinstance(settings.system, SystemSettings)
        assert settings.version == "1.0.0"
        assert isinstance(settings.data_sources, dict)
    
    def test_from_dict_method(self):
        """测试从字典创建设置"""
        config_dict = {
            'version': '2.0.0',
            'system': {
                'database': {
                    'path': 'test.db',
                    'pool_size': 15
                },
                'logging': {
                    'level': 'DEBUG',
                    'console_output': False
                },
                'cache_size': 1500,
                'debug_mode': True
            },
            'data_sources': {
                'xtdata': {
                    'enabled': True,
                    'host': 'localhost',
                    'port': 58610
                },
                'tushare': {
                    'enabled': False,
                    'token': 'test_token'
                }
            }
        }
        
        settings = Settings.from_dict(config_dict)
        
        assert settings.version == '2.0.0'
        assert settings.system.database.path == 'test.db'
        assert settings.system.database.pool_size == 15
        assert settings.system.logging.level == 'DEBUG'
        assert settings.system.logging.console_output is False
        assert settings.system.cache_size == 1500
        assert settings.system.debug_mode is True
        assert settings.data_sources['xtdata']['enabled'] is True
        assert settings.data_sources['tushare']['enabled'] is False
    
    def test_to_dict_method(self):
        """测试转换为字典"""
        settings = Settings()
        settings_dict = settings.to_dict()
        
        assert isinstance(settings_dict, dict)
        assert 'version' in settings_dict
        assert 'system' in settings_dict
        assert 'data_sources' in settings_dict
        
        # 检查嵌套结构
        assert 'database' in settings_dict['system']
        assert 'logging' in settings_dict['system']
        assert 'data_sources' in settings_dict['system']
    
    def test_partial_dict_initialization(self):
        """测试部分字典初始化"""
        partial_dict = {
            'system': {
                'database': {
                    'path': 'partial.db'
                },
                'cache_size': 800
            }
        }
        
        settings = Settings.from_dict(partial_dict)
        
        # 指定的值应该被设置
        assert settings.system.database.path == 'partial.db'
        assert settings.system.cache_size == 800
        
        # 未指定的值应该使用默认值
        assert settings.system.database.pool_size == 10  # 默认值
        assert settings.system.logging.level == "INFO"  # 默认值
    
    def test_invalid_dict_structure(self):
        """测试无效的字典结构"""
        invalid_dict = {
            'system': {
                'database': {
                    'pool_size': 'invalid_type'  # 应该是整数
                }
            }
        }
        
        # 应该处理类型错误或使用默认值
        try:
            settings = Settings.from_dict(invalid_dict)
            # 如果没有抛出异常，检查是否使用了默认值
            assert isinstance(settings.system.database.pool_size, int)
        except (ValueError, TypeError, ConfigurationError):
            # 如果抛出异常也是可以接受的
            pass
    
    def test_settings_equality(self):
        """测试设置对象相等性"""
        settings1 = Settings()
        settings2 = Settings()
        
        # 默认设置应该相等
        assert settings1.system.database.path == settings2.system.database.path
        assert settings1.system.cache_size == settings2.system.cache_size
    
    def test_settings_serialization_roundtrip(self):
        """测试设置序列化往返"""
        original_settings = Settings()
        
        # 转换为字典再转换回来
        settings_dict = original_settings.to_dict()
        restored_settings = Settings.from_dict(settings_dict)
        
        # 应该保持一致
        assert original_settings.version == restored_settings.version
        assert original_settings.system.database.path == restored_settings.system.database.path
        assert original_settings.system.cache_size == restored_settings.system.cache_size


if __name__ == '__main__':
    pytest.main([__file__])
