#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 xtquant 连接和基础功能
"""

import sys
import os
from typing import List, Dict, Optional

def test_xtquant_import():
    """测试 xtquant 导入"""
    try:
        import xtquant.xtdata as xt
        print("✅ xtquant.xtdata 导入成功")
        return xt, True
    except ImportError as e:
        print(f"❌ xtquant 导入失败: {e}")
        return None, False

def test_xtquant_connection(xt):
    """测试 xtquant 连接"""
    try:
        print("正在连接 xtdata 服务...")
        result = xt.connect()
        print(f"连接结果: {result}")
        print(f"连接结果类型: {type(result)}")
        
        # 检查连接是否成功
        if hasattr(result, '__class__') and 'Client' in str(result.__class__):
            print("✅ xtdata 连接成功 (返回客户端对象)")
            return True
        elif result == 0:
            print("✅ xtdata 连接成功 (返回码 0)")
            return True
        else:
            print(f"❌ xtdata 连接失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 连接过程中出错: {e}")
        return False

def test_basic_functions(xt):
    """测试基础功能"""
    print("\n正在测试基础功能...")
    
    try:
        # 测试获取股票列表
        print("1. 测试获取股票列表...")
        try:
            stocks = xt.get_stock_list_in_sector('沪深A股')
            if stocks and len(stocks) > 0:
                print(f"✅ 获取股票列表成功，共 {len(stocks)} 只股票")
                print(f"   示例股票: {stocks[:5]}")
            else:
                print("⚠️  获取股票列表为空")
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
        
        # 测试获取板块列表
        print("\n2. 测试获取板块列表...")
        try:
            sectors = xt.get_sector_list()
            if sectors and len(sectors) > 0:
                print(f"✅ 获取板块列表成功，共 {len(sectors)} 个板块")
                print(f"   示例板块: {sectors[:5]}")
            else:
                print("⚠️  获取板块列表为空")
        except Exception as e:
            print(f"❌ 获取板块列表失败: {e}")
        
        # 测试获取股票基本信息
        print("\n3. 测试获取股票基本信息...")
        try:
            # 使用一个常见的股票代码
            test_stock = "000001.SZ"  # 平安银行
            stock_info = xt.get_instrument_detail(test_stock)
            if stock_info:
                print(f"✅ 获取股票信息成功: {test_stock}")
                print(f"   股票信息: {stock_info}")
            else:
                print(f"⚠️  获取股票信息为空: {test_stock}")
        except Exception as e:
            print(f"❌ 获取股票信息失败: {e}")
        
        # 测试获取历史数据
        print("\n4. 测试获取历史数据...")
        try:
            # 获取最近5天的日线数据
            test_stock = "000001.SZ"
            hist_data = xt.get_market_data_ex(
                stock_list=[test_stock],
                period='1d',
                start_time='20241201',
                end_time='20241210',
                count=5
            )
            if hist_data and len(hist_data) > 0:
                print(f"✅ 获取历史数据成功: {test_stock}")
                print(f"   数据条数: {len(hist_data)}")
            else:
                print(f"⚠️  获取历史数据为空: {test_stock}")
        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            
    except Exception as e:
        print(f"❌ 基础功能测试过程中出错: {e}")

def test_disconnect(xt):
    """测试断开连接"""
    try:
        xt.disconnect()
        print("✅ 已断开 xtdata 连接")
    except Exception as e:
        print(f"⚠️  断开连接时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("xtquant 连接和功能测试")
    print("=" * 60)
    
    # 1. 测试导入
    print("\n1. 测试 xtquant 导入...")
    xt, success = test_xtquant_import()
    if not success:
        print("❌ 无法导入 xtquant，请检查安装")
        return
    
    # 2. 测试连接
    print("\n2. 测试 xtdata 连接...")
    if not test_xtquant_connection(xt):
        print("❌ 无法连接 xtdata 服务")
        print("\n可能的原因:")
        print("1. 迅投客户端未启动")
        print("2. xtdata 服务未开启")
        print("3. 端口被占用")
        print("\n解决方案:")
        print("1. 启动迅投客户端 (QMT)")
        print("2. 确保 xtdata 服务正常运行")
        return
    
    # 3. 测试基础功能
    print("\n3. 测试基础功能...")
    test_basic_functions(xt)
    
    # 4. 断开连接
    print("\n4. 断开连接...")
    test_disconnect(xt)
    
    print("\n" + "=" * 60)
    print("🎉 xtquant 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
