#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序打包脚本
第15周：性能优化和部署准备 - PyInstaller打包和部署
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any
import zipfile
import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)


class PackageBuilder:
    """应用程序打包器"""
    
    def __init__(self):
        """初始化打包器"""
        self.project_root = project_root
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.spec_file = self.project_root / "wyckoff_stock_selector.spec"
        
        # 应用程序信息
        self.app_info = {
            "name": "威科夫相对强弱选股系统",
            "version": "1.0.0",
            "description": "基于威科夫理论的智能选股系统",
            "author": "Wyckoff Team",
            "entry_point": "main.py"
        }
        
        logger.info("应用程序打包器初始化完成")
    
    def clean_build_dirs(self):
        """清理构建目录"""
        try:
            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)
                logger.info("清理build目录完成")
            
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                logger.info("清理dist目录完成")
            
            if self.spec_file.exists():
                self.spec_file.unlink()
                logger.info("清理spec文件完成")
                
        except Exception as e:
            logger.error(f"清理构建目录失败: {e}")
            raise
    
    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        try:
            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件
datas = [
    ('config', 'config'),
    ('resources', 'resources'),
    ('src/ui/styles', 'src/ui/styles'),
]

# 隐藏导入
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'pandas',
    'numpy',
    'scipy',
    'matplotlib',
    'seaborn',
    'pyqtgraph',
    'requests',
    'aiohttp',
    'loguru',
    'psutil',
    'scikit-learn',
    'openpyxl',
    'xtquant',
    'sqlite3',
    'json',
    'yaml',
    'configparser',
    'tqdm',
    'dateutil',
]

# 排除的模块
excludes = [
    'tkinter',
    'unittest',
    'test',
    'tests',
    'pytest',
    'IPython',
    'jupyter',
]

a = Analysis(
    ['{self.app_info["entry_point"]}'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_info["name"]}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app_icon.ico' if os.path.exists('resources/icons/app_icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.app_info["name"]}',
)
'''
            
            with open(self.spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)
            
            logger.info(f"创建spec文件完成: {self.spec_file}")
            
        except Exception as e:
            logger.error(f"创建spec文件失败: {e}")
            raise
    
    def install_dependencies(self):
        """安装打包依赖"""
        try:
            # 安装PyInstaller
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "pyinstaller", "auto-py-to-exe"
            ], check=True)
            
            logger.info("打包依赖安装完成")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"安装打包依赖失败: {e}")
            raise
    
    def build_executable(self):
        """构建可执行文件"""
        try:
            # 切换到项目根目录
            os.chdir(self.project_root)
            
            # 运行PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(self.spec_file)
            ]
            
            logger.info(f"开始构建可执行文件: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("可执行文件构建成功")
                logger.info(f"构建输出:\n{result.stdout}")
            else:
                logger.error(f"可执行文件构建失败:\n{result.stderr}")
                raise Exception(f"PyInstaller构建失败: {result.stderr}")
                
        except Exception as e:
            logger.error(f"构建可执行文件失败: {e}")
            raise
    
    def create_installer_script(self):
        """创建安装脚本"""
        try:
            # Windows批处理安装脚本
            install_bat = self.dist_dir / f"{self.app_info['name']}" / "install.bat"
            
            install_content = f'''@echo off
echo 正在安装 {self.app_info["name"]} v{self.app_info["version"]}
echo.

REM 创建程序目录
set INSTALL_DIR=%PROGRAMFILES%\\{self.app_info["name"]}
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM 复制文件
echo 正在复制程序文件...
xcopy /E /I /Y "*" "%INSTALL_DIR%"

REM 创建桌面快捷方式
echo 正在创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\\{self.app_info["name"]}.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\\{self.app_info["name"]}.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "{self.app_info["description"]}" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

REM 创建开始菜单快捷方式
echo 正在创建开始菜单快捷方式...
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\{self.app_info["name"]}" mkdir "%STARTMENU%\\{self.app_info["name"]}"
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%STARTMENU%\\{self.app_info["name"]}\\{self.app_info["name"]}.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%INSTALL_DIR%\\{self.app_info["name"]}.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "{self.app_info["description"]}" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面和开始菜单已创建快捷方式
echo.
pause
'''
            
            with open(install_bat, 'w', encoding='gbk') as f:
                f.write(install_content)
            
            # 卸载脚本
            uninstall_bat = self.dist_dir / f"{self.app_info['name']}" / "uninstall.bat"
            
            uninstall_content = f'''@echo off
echo 正在卸载 {self.app_info["name"]} v{self.app_info["version"]}
echo.

set INSTALL_DIR=%PROGRAMFILES%\\{self.app_info["name"]}
set DESKTOP=%USERPROFILE%\\Desktop
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs

REM 删除快捷方式
echo 正在删除快捷方式...
if exist "%DESKTOP%\\{self.app_info["name"]}.lnk" del "%DESKTOP%\\{self.app_info["name"]}.lnk"
if exist "%STARTMENU%\\{self.app_info["name"]}" rmdir /S /Q "%STARTMENU%\\{self.app_info["name"]}"

REM 删除程序目录
echo 正在删除程序文件...
if exist "%INSTALL_DIR%" rmdir /S /Q "%INSTALL_DIR%"

echo.
echo 卸载完成！
echo.
pause
'''
            
            with open(uninstall_bat, 'w', encoding='gbk') as f:
                f.write(uninstall_content)
            
            logger.info("安装脚本创建完成")
            
        except Exception as e:
            logger.error(f"创建安装脚本失败: {e}")
            raise
    
    def create_readme(self):
        """创建README文件"""
        try:
            readme_file = self.dist_dir / f"{self.app_info['name']}" / "README.txt"
            
            readme_content = f'''{self.app_info["name"]} v{self.app_info["version"]}
{'=' * 50}

{self.app_info["description"]}

系统要求:
- Windows 10 或更高版本
- 4GB 内存（推荐8GB）
- 1GB 可用磁盘空间
- 网络连接（用于数据获取）

安装说明:
1. 双击运行 install.bat 进行自动安装
2. 或者直接运行 {self.app_info["name"]}.exe

使用说明:
1. 首次运行时会显示使用向导
2. 配置数据源连接（XtData）
3. 开始使用威科夫分析和选股功能

技术支持:
- 查看帮助菜单中的使用指南
- 检查日志文件（logs目录）
- 联系技术支持团队

版本信息:
- 版本: {self.app_info["version"]}
- 构建时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- 作者: {self.app_info["author"]}

注意事项:
- 请确保已安装并配置好MiniQMT客户端
- 首次使用建议阅读用户手册
- 定期备份重要配置和数据

卸载说明:
运行 uninstall.bat 进行完全卸载
'''
            
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            logger.info("README文件创建完成")
            
        except Exception as e:
            logger.error(f"创建README文件失败: {e}")
            raise
    
    def create_version_info(self):
        """创建版本信息文件"""
        try:
            version_file = self.dist_dir / f"{self.app_info['name']}" / "version.json"
            
            version_info = {
                "name": self.app_info["name"],
                "version": self.app_info["version"],
                "description": self.app_info["description"],
                "author": self.app_info["author"],
                "build_time": datetime.datetime.now().isoformat(),
                "python_version": sys.version,
                "platform": sys.platform,
                "dependencies": self._get_dependencies()
            }
            
            with open(version_file, 'w', encoding='utf-8') as f:
                json.dump(version_info, f, ensure_ascii=False, indent=2)
            
            logger.info("版本信息文件创建完成")
            
        except Exception as e:
            logger.error(f"创建版本信息文件失败: {e}")
            raise
    
    def _get_dependencies(self) -> List[str]:
        """获取依赖列表"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "freeze"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return result.stdout.strip().split('\n')
            else:
                return []
                
        except Exception:
            return []
    
    def create_distribution_package(self):
        """创建分发包"""
        try:
            # 创建ZIP分发包
            dist_name = f"{self.app_info['name']}_v{self.app_info['version']}_{datetime.datetime.now().strftime('%Y%m%d')}"
            zip_file = self.project_root / f"{dist_name}.zip"
            
            with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
                dist_folder = self.dist_dir / self.app_info['name']
                
                for file_path in dist_folder.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(dist_folder)
                        zf.write(file_path, arcname)
            
            logger.info(f"分发包创建完成: {zip_file}")
            
            # 创建安装包信息
            package_info = {
                "package_name": dist_name,
                "file_size": zip_file.stat().st_size,
                "created_time": datetime.datetime.now().isoformat(),
                "checksum": self._calculate_checksum(zip_file)
            }
            
            info_file = self.project_root / f"{dist_name}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(package_info, f, ensure_ascii=False, indent=2)
            
            return zip_file, info_file
            
        except Exception as e:
            logger.error(f"创建分发包失败: {e}")
            raise
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        import hashlib
        
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
            
        except Exception:
            return ""
    
    def build_complete_package(self):
        """构建完整的应用程序包"""
        try:
            logger.info("开始构建应用程序包...")
            
            # 1. 清理构建目录
            self.clean_build_dirs()
            
            # 2. 安装打包依赖
            self.install_dependencies()
            
            # 3. 创建spec文件
            self.create_spec_file()
            
            # 4. 构建可执行文件
            self.build_executable()
            
            # 5. 创建安装脚本
            self.create_installer_script()
            
            # 6. 创建README
            self.create_readme()
            
            # 7. 创建版本信息
            self.create_version_info()
            
            # 8. 创建分发包
            zip_file, info_file = self.create_distribution_package()
            
            logger.info("应用程序包构建完成！")
            logger.info(f"分发包: {zip_file}")
            logger.info(f"包信息: {info_file}")
            
            return {
                "success": True,
                "package_file": str(zip_file),
                "info_file": str(info_file),
                "build_time": datetime.datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"构建应用程序包失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "build_time": datetime.datetime.now().isoformat()
            }


def main():
    """主函数"""
    try:
        builder = PackageBuilder()
        result = builder.build_complete_package()
        
        if result["success"]:
            print("✅ 应用程序包构建成功！")
            print(f"📦 分发包: {result['package_file']}")
            print(f"📋 包信息: {result['info_file']}")
        else:
            print("❌ 应用程序包构建失败！")
            print(f"错误: {result['error']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 构建过程异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
