"""
系统监控面板控件

提供系统性能监控和状态展示功能
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QProgressBar, QTextEdit, QTabWidget,
    QScrollArea, QFrame, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from ...utils.logger import get_logger
import psutil
import platform
from datetime import datetime

logger = get_logger(__name__)


class SystemMonitorWidget(QWidget):
    """系统监控面板控件"""
    
    # 信号定义
    alert_triggered = pyqtSignal(str, str)  # 警报类型, 消息
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化系统监控控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 监控数据
        self.system_info: Dict[str, Any] = {}
        self.performance_data: List[Dict[str, Any]] = []
        
        # 控件引用
        self.monitor_tabs: Optional[QTabWidget] = None
        self.cpu_progress: Optional[QProgressBar] = None
        self.memory_progress: Optional[QProgressBar] = None
        self.disk_progress: Optional[QProgressBar] = None
        self.network_table: Optional[QTableWidget] = None
        self.process_table: Optional[QTableWidget] = None
        self.system_info_text: Optional[QTextEdit] = None
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._update_monitoring_data)
        self.monitor_timer.start(2000)  # 每2秒更新一次
        
        # 警报阈值
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0
        }
        
        self._init_ui()
        self._load_system_info()
        
        logger.info("系统监控控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📊 系统监控面板")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        self.monitor_tabs = QTabWidget()
        
        # 性能监控标签页
        performance_tab = self._create_performance_tab()
        self.monitor_tabs.addTab(performance_tab, "⚡ 性能监控")
        
        # 进程监控标签页
        process_tab = self._create_process_tab()
        self.monitor_tabs.addTab(process_tab, "🔄 进程监控")
        
        # 网络监控标签页
        network_tab = self._create_network_tab()
        self.monitor_tabs.addTab(network_tab, "🌐 网络监控")
        
        # 系统信息标签页
        system_tab = self._create_system_info_tab()
        self.monitor_tabs.addTab(system_tab, "💻 系统信息")
        
        layout.addWidget(self.monitor_tabs)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self._refresh_all_data)
        button_layout.addWidget(refresh_button)
        
        export_button = QPushButton("📤 导出报告")
        export_button.clicked.connect(self._export_report)
        button_layout.addWidget(export_button)
        
        settings_button = QPushButton("⚙️ 监控设置")
        settings_button.clicked.connect(self._open_monitor_settings)
        button_layout.addWidget(settings_button)
        
        button_layout.addStretch()
        
        # 状态指示
        self.status_label = QLabel("监控运行中...")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        button_layout.addWidget(self.status_label)
        
        layout.addLayout(button_layout)
    
    def _create_performance_tab(self) -> QWidget:
        """创建性能监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # CPU监控
        cpu_group = QGroupBox("CPU使用率")
        cpu_layout = QVBoxLayout(cpu_group)
        
        cpu_info_layout = QHBoxLayout()
        self.cpu_label = QLabel("CPU: 0%")
        self.cpu_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        cpu_info_layout.addWidget(self.cpu_label)
        cpu_info_layout.addStretch()
        
        self.cpu_cores_label = QLabel("核心数: 0")
        cpu_info_layout.addWidget(self.cpu_cores_label)
        cpu_layout.addLayout(cpu_info_layout)
        
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        cpu_layout.addWidget(self.cpu_progress)
        
        layout.addWidget(cpu_group)
        
        # 内存监控
        memory_group = QGroupBox("内存使用率")
        memory_layout = QVBoxLayout(memory_group)
        
        memory_info_layout = QHBoxLayout()
        self.memory_label = QLabel("内存: 0%")
        self.memory_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        memory_info_layout.addWidget(self.memory_label)
        memory_info_layout.addStretch()
        
        self.memory_size_label = QLabel("总内存: 0 GB")
        memory_info_layout.addWidget(self.memory_size_label)
        memory_layout.addLayout(memory_info_layout)
        
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        self.memory_progress.setStyleSheet("""
            QProgressBar::chunk {
                background-color: #2196F3;
            }
        """)
        memory_layout.addWidget(self.memory_progress)
        
        layout.addWidget(memory_group)
        
        # 磁盘监控
        disk_group = QGroupBox("磁盘使用率")
        disk_layout = QVBoxLayout(disk_group)
        
        disk_info_layout = QHBoxLayout()
        self.disk_label = QLabel("磁盘: 0%")
        self.disk_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        disk_info_layout.addWidget(self.disk_label)
        disk_info_layout.addStretch()
        
        self.disk_size_label = QLabel("总容量: 0 GB")
        disk_info_layout.addWidget(self.disk_size_label)
        disk_layout.addLayout(disk_info_layout)
        
        self.disk_progress = QProgressBar()
        self.disk_progress.setRange(0, 100)
        self.disk_progress.setStyleSheet("""
            QProgressBar::chunk {
                background-color: #FF9800;
            }
        """)
        disk_layout.addWidget(self.disk_progress)
        
        layout.addWidget(disk_group)
        
        layout.addStretch()
        
        return widget
    
    def _create_process_tab(self) -> QWidget:
        """创建进程监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 进程表格
        self.process_table = QTableWidget()
        self._setup_process_table()
        layout.addWidget(self.process_table)
        
        # 进程控制按钮
        process_buttons_layout = QHBoxLayout()
        
        refresh_process_button = QPushButton("🔄 刷新进程")
        refresh_process_button.clicked.connect(self._refresh_process_list)
        process_buttons_layout.addWidget(refresh_process_button)
        
        kill_process_button = QPushButton("❌ 结束进程")
        kill_process_button.clicked.connect(self._kill_selected_process)
        process_buttons_layout.addWidget(kill_process_button)
        
        process_buttons_layout.addStretch()
        
        layout.addLayout(process_buttons_layout)
        
        return widget
    
    def _setup_process_table(self):
        """设置进程表格"""
        if not self.process_table:
            return
        
        # 设置列
        columns = ["PID", "进程名", "CPU%", "内存%", "状态"]
        self.process_table.setColumnCount(len(columns))
        self.process_table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.process_table.setAlternatingRowColors(True)
        self.process_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.process_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.process_table.setSortingEnabled(True)
        
        # 列宽设置
        header = self.process_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)   # PID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch) # 进程名
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)   # CPU%
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)   # 内存%
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)   # 状态
        
        self.process_table.setColumnWidth(0, 80)   # PID
        self.process_table.setColumnWidth(2, 80)   # CPU%
        self.process_table.setColumnWidth(3, 80)   # 内存%
        self.process_table.setColumnWidth(4, 100)  # 状态
    
    def _create_network_tab(self) -> QWidget:
        """创建网络监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 网络统计
        network_stats_group = QGroupBox("网络统计")
        stats_layout = QGridLayout(network_stats_group)
        
        stats_layout.addWidget(QLabel("发送字节:"), 0, 0)
        self.bytes_sent_label = QLabel("0 MB")
        stats_layout.addWidget(self.bytes_sent_label, 0, 1)
        
        stats_layout.addWidget(QLabel("接收字节:"), 0, 2)
        self.bytes_recv_label = QLabel("0 MB")
        stats_layout.addWidget(self.bytes_recv_label, 0, 3)
        
        stats_layout.addWidget(QLabel("发送包数:"), 1, 0)
        self.packets_sent_label = QLabel("0")
        stats_layout.addWidget(self.packets_sent_label, 1, 1)
        
        stats_layout.addWidget(QLabel("接收包数:"), 1, 2)
        self.packets_recv_label = QLabel("0")
        stats_layout.addWidget(self.packets_recv_label, 1, 3)
        
        layout.addWidget(network_stats_group)
        
        # 网络连接表格
        connections_group = QGroupBox("网络连接")
        connections_layout = QVBoxLayout(connections_group)
        
        self.network_table = QTableWidget()
        self._setup_network_table()
        connections_layout.addWidget(self.network_table)
        
        layout.addWidget(connections_group)
        
        return widget
    
    def _setup_network_table(self):
        """设置网络表格"""
        if not self.network_table:
            return
        
        # 设置列
        columns = ["协议", "本地地址", "远程地址", "状态"]
        self.network_table.setColumnCount(len(columns))
        self.network_table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.network_table.setAlternatingRowColors(True)
        self.network_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        
        # 列宽设置
        header = self.network_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)   # 协议
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch) # 本地地址
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch) # 远程地址
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)   # 状态
        
        self.network_table.setColumnWidth(0, 80)   # 协议
        self.network_table.setColumnWidth(3, 100)  # 状态

    def _create_system_info_tab(self) -> QWidget:
        """创建系统信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 系统信息文本
        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.system_info_text)

        return widget

    def _load_system_info(self):
        """加载系统信息"""
        try:
            # 获取系统基本信息
            self.system_info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'memory_total': psutil.virtual_memory().total,
                'boot_time': psutil.boot_time()
            }

            self._update_system_info_display()

        except Exception as e:
            logger.error(f"加载系统信息失败: {e}")

    def _update_system_info_display(self):
        """更新系统信息显示"""
        if not self.system_info_text:
            return

        try:
            info_text = "=== 系统信息 ===\n\n"
            info_text += f"操作系统: {self.system_info.get('platform', 'Unknown')}\n"
            info_text += f"系统类型: {self.system_info.get('system', 'Unknown')}\n"
            info_text += f"系统版本: {self.system_info.get('release', 'Unknown')}\n"
            info_text += f"详细版本: {self.system_info.get('version', 'Unknown')}\n"
            info_text += f"架构: {self.system_info.get('machine', 'Unknown')}\n"
            info_text += f"处理器: {self.system_info.get('processor', 'Unknown')}\n\n"

            info_text += "=== 硬件信息 ===\n\n"
            info_text += f"CPU核心数: {self.system_info.get('cpu_count', 0)}\n"
            info_text += f"逻辑处理器: {self.system_info.get('cpu_count_logical', 0)}\n"
            info_text += f"总内存: {self.system_info.get('memory_total', 0) / (1024**3):.2f} GB\n\n"

            # 启动时间
            boot_time = self.system_info.get('boot_time', 0)
            if boot_time:
                boot_datetime = datetime.fromtimestamp(boot_time)
                info_text += f"系统启动时间: {boot_datetime.strftime('%Y-%m-%d %H:%M:%S')}\n"
                uptime = datetime.now() - boot_datetime
                info_text += f"运行时间: {uptime.days}天 {uptime.seconds//3600}小时 {(uptime.seconds//60)%60}分钟\n"

            self.system_info_text.setText(info_text)

        except Exception as e:
            logger.error(f"更新系统信息显示失败: {e}")

    def _update_monitoring_data(self):
        """更新监控数据"""
        try:
            # 更新CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_progress.setValue(int(cpu_percent))
            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")

            # 检查CPU警报
            if cpu_percent > self.alert_thresholds['cpu_usage']:
                self.alert_triggered.emit("CPU", f"CPU使用率过高: {cpu_percent:.1f}%")

            # 更新内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_progress.setValue(int(memory_percent))
            self.memory_label.setText(f"内存: {memory_percent:.1f}%")
            self.memory_size_label.setText(f"总内存: {memory.total / (1024**3):.1f} GB")

            # 检查内存警报
            if memory_percent > self.alert_thresholds['memory_usage']:
                self.alert_triggered.emit("内存", f"内存使用率过高: {memory_percent:.1f}%")

            # 更新磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            self.disk_progress.setValue(int(disk_percent))
            self.disk_label.setText(f"磁盘: {disk_percent:.1f}%")
            self.disk_size_label.setText(f"总容量: {disk.total / (1024**3):.1f} GB")

            # 检查磁盘警报
            if disk_percent > self.alert_thresholds['disk_usage']:
                self.alert_triggered.emit("磁盘", f"磁盘使用率过高: {disk_percent:.1f}%")

            # 更新CPU核心数显示
            self.cpu_cores_label.setText(f"核心数: {psutil.cpu_count()}")

            # 更新网络统计
            self._update_network_stats()

            # 更新进程列表（如果当前在进程标签页）
            if self.monitor_tabs.currentIndex() == 1:  # 进程监控标签页
                self._update_process_list()

            # 更新网络连接（如果当前在网络标签页）
            if self.monitor_tabs.currentIndex() == 2:  # 网络监控标签页
                self._update_network_connections()

        except Exception as e:
            logger.error(f"更新监控数据失败: {e}")

    def _update_network_stats(self):
        """更新网络统计"""
        try:
            net_io = psutil.net_io_counters()

            self.bytes_sent_label.setText(f"{net_io.bytes_sent / (1024**2):.1f} MB")
            self.bytes_recv_label.setText(f"{net_io.bytes_recv / (1024**2):.1f} MB")
            self.packets_sent_label.setText(f"{net_io.packets_sent:,}")
            self.packets_recv_label.setText(f"{net_io.packets_recv:,}")

        except Exception as e:
            logger.error(f"更新网络统计失败: {e}")

    def _update_process_list(self):
        """更新进程列表"""
        try:
            if not self.process_table:
                return

            # 获取进程列表
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按CPU使用率排序
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)

            # 只显示前20个进程
            processes = processes[:20]

            self.process_table.setRowCount(len(processes))

            for row, proc in enumerate(processes):
                # PID
                pid_item = QTableWidgetItem(str(proc.get('pid', 0)))
                self.process_table.setItem(row, 0, pid_item)

                # 进程名
                name_item = QTableWidgetItem(proc.get('name', 'Unknown'))
                self.process_table.setItem(row, 1, name_item)

                # CPU%
                cpu_item = QTableWidgetItem(f"{proc.get('cpu_percent', 0):.1f}%")
                cpu_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.process_table.setItem(row, 2, cpu_item)

                # 内存%
                memory_item = QTableWidgetItem(f"{proc.get('memory_percent', 0):.1f}%")
                memory_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.process_table.setItem(row, 3, memory_item)

                # 状态
                status_item = QTableWidgetItem(proc.get('status', 'unknown'))
                status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.process_table.setItem(row, 4, status_item)

        except Exception as e:
            logger.error(f"更新进程列表失败: {e}")

    def _update_network_connections(self):
        """更新网络连接"""
        try:
            if not self.network_table:
                return

            # 获取网络连接
            connections = psutil.net_connections(kind='inet')

            # 只显示前50个连接
            connections = connections[:50]

            self.network_table.setRowCount(len(connections))

            for row, conn in enumerate(connections):
                # 协议
                family_map = {2: 'IPv4', 10: 'IPv6'}
                type_map = {1: 'TCP', 2: 'UDP'}
                protocol = f"{family_map.get(conn.family, 'Unknown')}/{type_map.get(conn.type, 'Unknown')}"
                protocol_item = QTableWidgetItem(protocol)
                self.network_table.setItem(row, 0, protocol_item)

                # 本地地址
                local_addr = f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "N/A"
                local_item = QTableWidgetItem(local_addr)
                self.network_table.setItem(row, 1, local_item)

                # 远程地址
                remote_addr = f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A"
                remote_item = QTableWidgetItem(remote_addr)
                self.network_table.setItem(row, 2, remote_item)

                # 状态
                status_item = QTableWidgetItem(conn.status if conn.status else "N/A")
                status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.network_table.setItem(row, 3, status_item)

        except Exception as e:
            logger.error(f"更新网络连接失败: {e}")

    def _refresh_all_data(self):
        """刷新所有数据"""
        self._update_monitoring_data()
        self._load_system_info()
        self.status_label.setText("数据已刷新")

    def _refresh_process_list(self):
        """刷新进程列表"""
        self._update_process_list()

    def _kill_selected_process(self):
        """结束选中的进程"""
        current_row = self.process_table.currentRow()
        if current_row >= 0:
            # 这里应该实现进程结束功能，但需要谨慎处理
            logger.info("结束进程功能需要管理员权限，暂未实现")

    def _export_report(self):
        """导出监控报告"""
        logger.info("导出监控报告功能开发中...")

    def _open_monitor_settings(self):
        """打开监控设置"""
        logger.info("监控设置功能开发中...")

    # 公共方法
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return self.system_info.copy()

    def get_current_performance(self) -> Dict[str, float]:
        """获取当前性能数据"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent
            }
        except Exception as e:
            logger.error(f"获取性能数据失败: {e}")
            return {}

    def set_alert_threshold(self, metric: str, threshold: float):
        """设置警报阈值"""
        if metric in self.alert_thresholds:
            self.alert_thresholds[metric] = threshold
            logger.info(f"设置{metric}警报阈值为{threshold}%")

    def start_monitoring(self):
        """开始监控"""
        if not self.monitor_timer.isActive():
            self.monitor_timer.start(2000)
            self.status_label.setText("监控运行中...")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitor_timer.isActive():
            self.monitor_timer.stop()
            self.status_label.setText("监控已停止")
            self.status_label.setStyleSheet("color: #F44336; font-weight: bold;")
