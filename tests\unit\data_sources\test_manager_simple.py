#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器简化测试
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.data_sources.base import (
    IDataSource, DataSourceConfig, MarketData, SectorInfo,
    DataSourceException, ConnectionException, DataException
)
from src.data_sources.manager import (
    DataSourceManager, DataSourceStatus, DataSourceStats, LoadBalanceStrategy
)


class SimpleMockDataSource(IDataSource):
    """简化的模拟数据源"""
    
    def __init__(self, config: DataSourceConfig, fail_connect: bool = False):
        super().__init__(config)
        self.connected = False
        self.fail_connect = fail_connect
        self.call_count = 0
    
    def connect(self) -> bool:
        """连接"""
        if self.fail_connect:
            return False
        self.connected = True
        return True
    
    def disconnect(self) -> bool:
        """断开连接"""
        self.connected = False
        return True
    
    def test_connection(self) -> bool:
        """测试连接"""
        return self.connected
    
    async def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        return ["000001.SZ", "000002.SZ"]
    
    async def get_sector_list(self) -> List[SectorInfo]:
        """获取板块列表"""
        return [SectorInfo(code="BK001", name="银行", stocks=["000001.SZ"])]
    
    async def get_sector_constituents(self, sector_code: str) -> List[str]:
        """获取板块成分股"""
        return ["000001.SZ", "000002.SZ"]
    
    async def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        return ["2024-01-02", "2024-01-03"]
    
    async def download_history_data(self, symbols: List[str], 
                                  start_date: str, end_date: str,
                                  period: str = "1d") -> bool:
        """下载历史数据"""
        return True
    
    def get_market_data(self, symbol: str, period: str = "1d",
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None,
                       dividend_type: str = "none") -> MarketData:
        """获取市场数据"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        self.call_count += 1
        
        if self.config.name == "failing_source":
            raise DataException("数据获取失败")
        
        # 创建正确格式的DataFrame
        import pandas as pd
        data = pd.DataFrame({
            'trade_date': ['2024-01-01'],
            'open': [100.0],
            'high': [110.0],
            'low': [90.0],
            'close': [105.0],
            'volume': [1000000]
        })
        
        return MarketData(
            symbol=symbol,
            data=data,
            source=self.config.name,
            update_time=datetime.now(),
            data_type="daily"
        )
    
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self.connected


class TestDataSourceManagerSimple:
    """数据源管理器简化测试"""
    
    @pytest.fixture
    def manager(self):
        """创建数据源管理器"""
        manager = DataSourceManager(health_check_interval=3600)  # 设置很长的健康检查间隔
        # 立即停止健康检查，避免测试干扰
        manager._stop_health_check = True
        yield manager
        manager.shutdown()  # 清理资源
    
    @pytest.fixture
    def mock_sources(self):
        """创建模拟数据源"""
        return [
            SimpleMockDataSource(DataSourceConfig(name="primary", timeout=30)),
            SimpleMockDataSource(DataSourceConfig(name="secondary", timeout=60)),
            SimpleMockDataSource(DataSourceConfig(name="backup", timeout=45))
        ]
    
    def test_init(self, manager):
        """测试初始化"""
        assert manager._sources == {}
        assert manager._health_check_interval == 3600  # 修正期望值
        assert manager._load_balance_strategy == LoadBalanceStrategy.PRIORITY
    
    def test_register_source(self, manager, mock_sources):
        """测试注册数据源"""
        source = mock_sources[0]
        
        with patch.object(source, 'connect', return_value=True):
            result = manager.register_source("test_source", source)
            assert result is True
            assert "test_source" in manager._sources
            assert manager._sources["test_source"].source == source
    
    def test_unregister_source(self, manager, mock_sources):
        """测试注销数据源"""
        source = mock_sources[0]
        
        with patch.object(source, 'connect', return_value=True):
            manager.register_source("test_source", source)
            result = manager.unregister_source("test_source")
            assert result is True
            assert "test_source" not in manager._sources
    
    def test_get_available_sources(self, manager, mock_sources):
        """测试获取可用数据源"""
        from src.data_sources.manager import DataSourceStatus
        
        # 注册一个成功的数据源，同时模拟test_connection
        with patch.object(mock_sources[0], 'connect', return_value=True), \
             patch.object(mock_sources[0], 'test_connection', return_value=True):
            manager.register_source("active_source", mock_sources[0])
        
        # 手动设置状态为ACTIVE
        manager._sources["active_source"].status = DataSourceStatus.ACTIVE
        
        # 注册一个失败的数据源
        with patch.object(mock_sources[1], 'connect', return_value=False):
            manager.register_source("inactive_source", mock_sources[1])
        
        available = manager.get_available_sources()
        assert "active_source" in available
        assert "inactive_source" not in available
    
    def test_get_best_source_priority(self, manager, mock_sources):
        """测试按优先级获取最佳数据源"""
        # 注册不同优先级的数据源
        with patch.object(mock_sources[0], 'connect', return_value=True):
            manager.register_source("low_priority", mock_sources[0], priority=1)
        
        with patch.object(mock_sources[1], 'connect', return_value=True):
            manager.register_source("high_priority", mock_sources[1], priority=3)
        
        best_source = manager.get_best_source()
        assert best_source == mock_sources[1]  # 高优先级的数据源
    
    def test_health_check(self, manager, mock_sources):
        """测试健康检查"""
        source = mock_sources[0]
        
        with patch.object(source, 'connect', return_value=True):
            manager.register_source("test_source", source)
        
        with patch.object(source, 'test_connection', return_value=True):
            results = manager.health_check("test_source")
            assert results["test_source"] is True
    
    def test_get_market_data_with_failover(self, manager, mock_sources):
        """测试故障转移获取市场数据"""
        from src.data_sources.manager import DataSourceStatus
        source = mock_sources[0]
        
        # 注册数据源并模拟连接和健康检查
        with patch.object(source, 'connect', return_value=True), \
             patch.object(source, 'test_connection', return_value=True):
            manager.register_source("test_source", source)
            # 确保连接状态正确
            source.connected = True
        
        # 手动设置为活跃状态
        manager._sources["test_source"].status = DataSourceStatus.ACTIVE
        
        # 直接调用源的get_market_data方法，因为它已经返回正确格式
        result = manager.get_market_data_with_failover("000001.SZ")
        assert result.symbol == "000001.SZ"
        assert result.data.iloc[0]['close'] == 105.0
    
    def test_get_source_stats(self, manager, mock_sources):
        """测试获取数据源统计信息"""
        source = mock_sources[0]
        
        with patch.object(source, 'connect', return_value=True):
            manager.register_source("test_source", source)
        
        stats = manager.get_source_stats("test_source")
        assert isinstance(stats, DataSourceStats)
        assert stats.total_requests == 0
    
    def test_get_all_stats(self, manager, mock_sources):
        """测试获取所有统计信息"""
        with patch.object(mock_sources[0], 'connect', return_value=True):
            manager.register_source("source1", mock_sources[0])
        
        with patch.object(mock_sources[1], 'connect', return_value=True):
            manager.register_source("source2", mock_sources[1])
        
        all_stats = manager.get_all_stats()
        assert len(all_stats) == 2
        assert "source1" in all_stats
        assert "source2" in all_stats
    
    def test_shutdown(self, manager, mock_sources):
        """测试关闭管理器"""
        source = mock_sources[0]
        
        with patch.object(source, 'connect', return_value=True):
            manager.register_source("test_source", source)
        
        with patch.object(source, 'disconnect', return_value=True) as mock_disconnect:
            manager.shutdown()
            mock_disconnect.assert_called_once()
            assert len(manager._sources) == 0