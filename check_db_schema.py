#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构

查看实际的数据库表结构和列名
"""

import sqlite3
import os

def check_database_schema():
    """检查数据库表结构"""
    db_path = "data/stock_analysis.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print("📊 数据库表结构:")
            print("=" * 60)
            
            for table in tables:
                table_name = table[0]
                print(f"\n🔍 表: {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                print("列信息:")
                for col in columns:
                    cid, name, type_, notnull, default, pk = col
                    print(f"  - {name} ({type_}) {'NOT NULL' if notnull else ''} {'PRIMARY KEY' if pk else ''}")
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"记录数: {count:,}")
                
                print("-" * 40)
    
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")


if __name__ == "__main__":
    check_database_schema()
