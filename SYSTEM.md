# 威科夫相对强弱选股系统 - 系统需求设计文档

## 项目概述

### 项目名称
威科夫相对强弱选股系统 (Wyckoff Relative Strength Stock Selection System)

### 项目目标
基于威科夫理论和相对强弱分析，构建一个智能化的股票筛选系统，通过板块-个股双重相对强弱筛选机制，为投资者提供高质量的投资标的。

### 核心价值主张
- **双重筛选机制**：板块相对大盘 + 个股相对板块的两层筛选
- **多时间段分析**：支持并行多时间区间分析，提升筛选效率
- **智能数据管理**：自动化数据更新和完整性验证
- **可扩展架构**：支持多数据源集成和自定义筛选策略

## 功能需求规格

### 核心功能模块

#### 1. 数据源管理模块
**功能描述**：统一管理多个数据源，提供标准化数据接口

**主要功能**：
- 支持XtData（MiniQMT）作为主数据源
- 预留TuShare、AkShare、Baostock等数据源扩展接口
- 数据源自动切换和故障转移
- 数据质量验证和异常处理

**接口规范**：
```python
class DataSourceInterface:
    def get_market_data(self, symbol: str, period: str, start_date: str, end_date: str) -> pd.DataFrame
    def get_sector_data(self, sector_code: str, start_date: str, end_date: str) -> pd.DataFrame
    def get_sector_constituents(self, sector_code: str) -> List[str]
    def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]
    def download_history_data(self, symbols: List[str], period: str, start_date: str) -> bool
```

**数据标准化**：
- 统一时间格式：YYYY-MM-DD
- 统一代码格式：code.market（如000001.SZ）
- 统一数据结构：pandas.DataFrame
- 统一字段命名：open, high, low, close, volume, amount

#### 2. 数据库管理模块
**功能描述**：本地数据存储和管理

**数据表设计**：
- **板块基础信息表**：sector_info (sector_code, sector_name, sector_type, update_time)
- **板块历史行情表**：sector_quotes (sector_code, trade_date, open, high, low, close, volume, amount)
- **个股基础信息表**：stock_info (stock_code, stock_name, sector_codes, list_date, delist_date)
- **个股历史行情表**：stock_quotes (stock_code, trade_date, open, high, low, close, volume, amount, adj_factor)
- **板块成分股关系表**：sector_stocks (sector_code, stock_code, weight, update_time)

**存储策略**：
- 使用SQLite作为本地数据库
- 支持数据压缩和分区存储
- 建立适当索引优化查询性能
- 实现数据备份和恢复机制

#### 3. 数据更新模块
**功能描述**：智能化数据同步和更新

**更新策略**：
- **实时更新**：交易时间内实时同步最新数据
- **定时更新**：每日收盘后自动更新历史数据
- **增量更新**：只同步变化的数据，提高效率
- **按需更新**：用户手动触发特定数据更新

**更新机制**：
- 支持断点续传和错误重试
- 数据完整性验证和修复
- 更新进度显示和日志记录
- 多线程并行更新提升速度

#### 4. 相对强弱计算引擎
**功能描述**：核心的相对强弱计算逻辑

**计算流程**：
1. **预计算阶段**：
   - 批量计算大盘指数涨幅
   - 批量计算所有板块指数涨幅
   - 批量计算所有个股涨幅
   - 建立涨幅数据缓存

2. **板块筛选阶段**：
   - 筛选板块涨幅 > 大盘涨幅的板块
   - 计算板块相对强弱度 = 板块涨幅 - 大盘涨幅
   - 按相对强弱度排序，选取前N个板块

3. **个股筛选阶段**：
   - 获取强势板块的成分股
   - 筛选个股涨幅 > 板块涨幅的个股
   - 计算个股相对强弱度 = 个股涨幅 - 板块涨幅
   - 每个板块选取前M只个股

**性能优化**：
- 使用NumPy向量化计算
- 多进程并行处理不同板块
- 内存映射处理大数据集
- 结果缓存避免重复计算

#### 5. 筛选策略模块
**功能描述**：灵活的筛选策略配置

**策略参数**：
- **时间参数**：起始日期、结束日期、多时间段设置
- **板块参数**：板块类型选择、强势板块数量
- **个股参数**：每板块选股数量、排除条件
- **高级参数**：市值范围、成交量阈值、技术指标条件

**策略模板**：
- 短期强势策略（近5-20个交易日）
- 中期趋势策略（近1-3个月）
- 长期价值策略（近6-12个月）
- 自定义策略（用户自定义参数）

#### 6. 结果分析模块
**功能描述**：筛选结果的深度分析

**分析功能**：
- **结果统计**：入选个股数量、板块分布、平均涨幅
- **历史回测**：筛选结果的历史表现分析
- **风险评估**：集中度分析、相关性分析
- **趋势分析**：板块轮动、强弱变化趋势

**可视化展示**：
- 板块相对强弱热力图
- 个股表现散点图
- 时间序列趋势图
- 统计分布直方图

#### 7. 用户界面模块
**功能描述**：直观易用的图形界面

**界面设计**：
- **主界面**：参数设置、一键筛选、结果展示
- **数据管理界面**：数据源配置、更新管理、存储状态
- **分析界面**：深度分析、图表展示、报告导出
- **设置界面**：系统配置、策略管理、日志查看

**交互特性**：
- 拖拽式参数设置
- 实时预览和调整
- 批量操作支持
- 快捷键操作

### 非功能需求

#### 性能需求
- **响应时间**：单次筛选操作 < 30秒
- **并发处理**：支持多时间段并行计算
- **内存使用**：峰值内存使用 < 2GB
- **存储效率**：数据压缩率 > 50%

#### 可靠性需求
- **数据完整性**：99.9%数据准确性
- **系统稳定性**：连续运行无崩溃
- **错误恢复**：自动重试和故障转移
- **数据备份**：自动备份和恢复机制

#### 可用性需求
- **界面友好**：符合Windows界面设计规范
- **操作简便**：3步内完成基本筛选
- **帮助系统**：完整的用户手册和在线帮助
- **多语言支持**：中英文界面切换

#### 扩展性需求
- **数据源扩展**：插件式数据源集成
- **策略扩展**：自定义筛选策略支持
- **指标扩展**：技术指标计算模块
- **导出扩展**：多格式结果导出

## 技术约束与限制

### 系统环境约束
- **操作系统**：Windows 10/11 (64位)
- **Python版本**：Python 3.8+
- **内存要求**：最低4GB，推荐8GB
- **存储空间**：至少10GB可用空间

### 技术栈约束
- **GUI框架**：PyQt6（优先）或PyQt5
- **数据处理**：Pandas、NumPy
- **数据库**：SQLite（本地）
- **网络请求**：Requests、aiohttp
- **数据可视化**：PyQtGraph、Matplotlib

### 数据源约束
- **主数据源**：依赖MiniQMT客户端
- **网络依赖**：需要稳定的网络连接
- **API限制**：遵循各数据源的使用限制
- **数据延迟**：接受分钟级数据延迟

### 法规约束
- **数据使用**：仅用于个人投资参考
- **版权保护**：遵循数据源版权协议
- **隐私保护**：不收集用户个人信息
- **免责声明**：投资风险用户自担

## 质量保证要求

### 代码质量标准
- **代码覆盖率**：单元测试覆盖率 > 80%
- **代码规范**：遵循PEP 8编码规范
- **文档完整性**：所有公共接口有完整文档
- **错误处理**：全面的异常处理机制

### 测试要求
- **单元测试**：核心算法和数据处理模块
- **集成测试**：数据源集成和界面交互
- **性能测试**：大数据量处理性能验证
- **用户测试**：真实用户场景测试

### 部署要求
- **打包方式**：PyInstaller单文件打包
- **安装程序**：NSIS安装包制作
- **版本管理**：语义化版本号管理
- **更新机制**：自动检查和更新功能

## 项目范围与边界

### 包含功能
- 威科夫相对强弱选股核心算法
- 多数据源集成和管理
- 本地数据库存储和查询
- 图形用户界面
- 结果分析和可视化
- 基础的回测功能

### 不包含功能
- 实盘交易功能
- 复杂的量化策略回测
- 机器学习预测模型
- 实时推送和预警
- 多用户和云端同步

### 未来扩展方向
- 更多技术指标集成
- 高级回测和风险管理
- 移动端应用开发
- 云端数据同步
- 社区功能和策略分享

## 成功标准

### 功能完整性
- 所有核心功能模块正常运行
- 数据源集成稳定可靠
- 筛选结果准确有效
- 用户界面操作流畅

### 性能指标
- 筛选速度满足性能要求
- 内存和存储使用合理
- 系统稳定性达标
- 数据准确性验证通过

### 用户满意度
- 界面设计用户友好
- 操作流程简单直观
- 功能满足实际需求
- 技术支持及时有效

## 项目里程碑

### 第一阶段：基础架构（4周）
- 数据源接口设计和实现
- 本地数据库设计和创建
- 核心计算引擎开发
- 基础单元测试

### 第二阶段：核心功能（6周）
- 相对强弱筛选算法实现
- 数据更新和管理功能
- 筛选策略配置模块
- 集成测试和优化

### 第三阶段：用户界面（4周）
- GUI界面设计和开发
- 用户交互逻辑实现
- 结果展示和可视化
- 界面测试和优化

### 第四阶段：完善优化（2周）
- 性能优化和bug修复
- 用户文档编写
- 打包部署测试
- 最终验收测试

## 风险管理

### 技术风险
- **数据源不稳定**：准备多个备用数据源
- **性能瓶颈**：提前进行性能测试和优化
- **兼容性问题**：在多个环境中测试验证

### 业务风险
- **需求变更**：采用敏捷开发方法应对
- **数据质量**：建立数据验证和清洗机制
- **用户接受度**：持续收集用户反馈优化

### 项目风险
- **时间延期**：合理规划缓冲时间
- **资源不足**：优先实现核心功能
- **技术债务**：定期代码审查和重构

## 总结

本系统需求设计文档全面定义了威科夫相对强弱选股系统的功能需求、技术约束、质量标准和项目计划。通过模块化设计和可扩展架构，确保系统既能满足当前需求，又具备良好的扩展性。严格的质量保证措施和风险管理策略将确保项目的成功交付。