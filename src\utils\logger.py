#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
基于loguru的日志管理系统
"""

import os
import sys
from pathlib import Path
from typing import Optional
from loguru import logger

# 日志配置
LOG_DIR = Path("logs")
LOG_DIR.mkdir(exist_ok=True)

# 日志格式
LOG_FORMAT = (
    "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
    "<level>{level: <8}</level> | "
    "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
    "<level>{message}</level>"
)

# 文件日志格式
FILE_FORMAT = (
    "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
    "{level: <8} | "
    "{name}:{function}:{line} - "
    "{message}"
)

def setup_logger(
    name: Optional[str] = None,
    level: str = "INFO",
    log_to_file: bool = True,
    log_to_console: bool = True,
    file_rotation: str = "10 MB",
    file_retention: str = "30 days"
) -> None:
    """
    设置日志配置
    
    Args:
        name: 日志名称
        level: 日志级别
        log_to_file: 是否记录到文件
        log_to_console: 是否输出到控制台
        file_rotation: 文件轮转大小
        file_retention: 文件保留时间
    """
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出
    if log_to_console:
        logger.add(
            sys.stdout,
            format=LOG_FORMAT,
            level=level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # 文件输出
    if log_to_file:
        log_file = LOG_DIR / f"{name or 'app'}.log"
        logger.add(
            log_file,
            format=FILE_FORMAT,
            level=level,
            rotation=file_rotation,
            retention=file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        # 错误日志单独文件
        error_log_file = LOG_DIR / f"{name or 'app'}_error.log"
        logger.add(
            error_log_file,
            format=FILE_FORMAT,
            level="ERROR",
            rotation=file_rotation,
            retention=file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

def get_logger(name: str = None) -> object:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if name:
        return logger.bind(name=name)
    return logger

# 默认设置
setup_logger(
    name="xdqr",
    level=os.getenv("LOG_LEVEL", "INFO"),
    log_to_file=True,
    log_to_console=True
)