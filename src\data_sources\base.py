#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源抽象接口
定义统一的数据源接口，支持多种数据源的接入
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import pandas as pd
from dataclasses import dataclass
from datetime import datetime


@dataclass
class DataSourceConfig:
    """数据源配置"""
    name: str
    enabled: bool = True
    timeout: int = 30
    retry_times: int = 3
    auto_reconnect: bool = True
    config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.config is None:
            self.config = {}


@dataclass
class MarketData:
    """市场数据标准格式"""
    symbol: str
    data: pd.DataFrame
    source: str
    update_time: datetime
    data_type: str = "daily"  # daily, minute, tick
    
    def __post_init__(self):
        """验证数据格式"""
        required_columns = ['trade_date', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in self.data.columns for col in required_columns):
            missing = [col for col in required_columns if col not in self.data.columns]
            raise ValueError(f"缺少必要的数据列: {missing}")


@dataclass
class SectorInfo:
    """板块信息"""
    sector_code: str
    sector_name: str
    sector_type: str  # industry, concept, theme
    constituents: List[str] = None
    update_time: datetime = None
    
    def __post_init__(self):
        if self.constituents is None:
            self.constituents = []
        if self.update_time is None:
            self.update_time = datetime.now()


@dataclass
class StockInfo:
    """股票基本信息"""
    symbol: str
    name: str
    market: str
    list_date: Optional[str] = None
    industry: Optional[str] = None
    sector: Optional[str] = None
    is_active: bool = True
    update_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.update_time is None:
            self.update_time = datetime.now()


class DataSourceException(Exception):
    """数据源异常基类"""
    pass


class ConnectionException(DataSourceException):
    """连接异常"""
    pass


class DataException(DataSourceException):
    """数据异常"""
    pass


class AuthenticationException(DataSourceException):
    """认证异常"""
    pass


class IDataSource(ABC):
    """数据源抽象接口"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化数据源
        
        Args:
            config: 数据源配置
        """
        self.config = config
        self.connected = False
        self._last_error = None
    
    @property
    def name(self) -> str:
        """数据源名称"""
        return self.config.name
    
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self.connected
    
    @property
    def last_error(self) -> Optional[Exception]:
        """最后一次错误"""
        return self._last_error
    
    @abstractmethod
    def connect(self) -> bool:
        """
        建立连接
        
        Returns:
            bool: 连接是否成功
            
        Raises:
            ConnectionError: 连接失败
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开连接"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """
        测试连接状态
        
        Returns:
            bool: 连接是否正常
        """
        pass
    
    @abstractmethod
    def get_market_data(
        self, 
        symbol: str, 
        period: str = "1d",
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        dividend_type: str = "none"
    ) -> MarketData:
        """
        获取市场数据
        
        Args:
            symbol: 股票代码，格式如 "000001.SZ"
            period: 数据周期，支持 "1d", "1m", "5m", "15m", "30m", "60m"
            start_date: 开始日期，格式 "YYYY-MM-DD"
            end_date: 结束日期，格式 "YYYY-MM-DD"
            dividend_type: 复权类型，"none"不复权, "front"前复权, "back"后复权
            
        Returns:
            MarketData: 标准化的市场数据
            
        Raises:
            DataError: 数据获取失败
        """
        pass
    
    @abstractmethod
    def get_sector_list(self, sector_type: str = "industry") -> List[SectorInfo]:
        """
        获取板块列表
        
        Args:
            sector_type: 板块类型，"industry"行业板块, "concept"概念板块, "theme"主题板块
            
        Returns:
            List[SectorInfo]: 板块信息列表
            
        Raises:
            DataError: 数据获取失败
        """
        pass
    
    @abstractmethod
    def get_sector_constituents(self, sector_code: str) -> List[str]:
        """
        获取板块成分股
        
        Args:
            sector_code: 板块代码
            
        Returns:
            List[str]: 成分股代码列表
            
        Raises:
            DataError: 数据获取失败
        """
        pass
    
    @abstractmethod
    def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, str]]:
        """
        获取股票列表
        
        Args:
            market: 市场代码，None表示全部，"SH"上海，"SZ"深圳，"BJ"北京
            
        Returns:
            List[Dict]: 股票信息列表，包含code, name, market等字段
            
        Raises:
            DataError: 数据获取失败
        """
        pass
    
    @abstractmethod
    def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """
        获取交易日历
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[str]: 交易日期列表
            
        Raises:
            DataError: 数据获取失败
        """
        pass
    
    @abstractmethod
    def download_history_data(
        self, 
        symbols: List[str], 
        period: str = "1d",
        start_date: Optional[str] = None
    ) -> bool:
        """
        批量下载历史数据
        
        Args:
            symbols: 股票代码列表
            period: 数据周期
            start_date: 开始日期
            
        Returns:
            bool: 下载是否成功
            
        Raises:
            DataError: 下载失败
        """
        pass
    
    def _format_symbol(self, symbol: str) -> str:
        """
        格式化股票代码为标准格式
        
        Args:
            symbol: 原始股票代码
            
        Returns:
            str: 标准格式的股票代码 (code.market)
        """
        # 如果已经是标准格式，直接返回
        if '.' in symbol:
            return symbol.upper()
        
        # 根据代码判断市场
        if symbol.startswith(('60', '68', '11', '12', '13')):
            return f"{symbol}.SH"
        elif symbol.startswith(('00', '30', '12', '20')):
            return f"{symbol}.SZ"
        elif symbol.startswith(('8', '4')):
            return f"{symbol}.BJ"
        else:
            # 默认深圳市场
            return f"{symbol}.SZ"
    
    def _validate_date(self, date_str: str) -> bool:
        """
        验证日期格式
        
        Args:
            date_str: 日期字符串
            
        Returns:
            bool: 格式是否正确
        """
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def _handle_error(self, error: Exception, operation: str) -> None:
        """
        统一错误处理
        
        Args:
            error: 异常对象
            operation: 操作描述
        """
        self._last_error = error
        error_msg = f"{self.name} {operation} 失败: {str(error)}"
        
        # 根据错误类型抛出相应异常
        if "连接" in str(error) or "网络" in str(error):
            raise ConnectionException(error_msg) from error
        elif "认证" in str(error) or "授权" in str(error):
            raise AuthenticationException(error_msg) from error
        else:
            raise DataException(error_msg) from error