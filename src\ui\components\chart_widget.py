"""
图表展示控件

显示K线图、RS曲线等技术分析图表
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QFrame, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPainter, QPen, QColor, QBrush
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import pandas as pd
import numpy as np

from ...utils.logger import get_logger
from ...services.data_service import DataService

logger = get_logger(__name__)


class ChartWidget(QWidget):
    """图表展示控件"""
    
    # 信号定义
    chart_updated = pyqtSignal(str, str)  # 股票代码, 图表类型
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化图表控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)

        # 数据服务
        self.data_service = DataService()

        # 当前显示的股票和数据
        self.current_stock: Optional[str] = None
        self.stock_data: Optional[pd.DataFrame] = None
        self.rs_data: Optional[pd.DataFrame] = None

        # 图表控件
        self.figure: Optional[Figure] = None
        self.canvas: Optional[FigureCanvas] = None

        # 控件引用
        self.chart_type_combo: Optional[QComboBox] = None
        self.timeframe_combo: Optional[QComboBox] = None
        self.refresh_button: Optional[QPushButton] = None
        self.chart_type_combo: Optional[QComboBox] = None
        self.timeframe_combo: Optional[QComboBox] = None
        
        self._init_ui()
        self._setup_matplotlib()
        self._show_welcome_chart()
        
        logger.info("图表控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 控制面板
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("📈 技术分析图表")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        control_layout.addWidget(title_label)
        
        control_layout.addStretch()
        
        # 图表类型选择
        control_layout.addWidget(QLabel("图表类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "K线图",
            "RS曲线图",
            "成交量图",
            "威科夫分析图"
        ])
        self.chart_type_combo.currentTextChanged.connect(self._on_chart_type_changed)
        control_layout.addWidget(self.chart_type_combo)
        
        # 时间周期选择
        control_layout.addWidget(QLabel("时间周期:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems([
            "日线",
            "周线", 
            "月线"
        ])
        self.timeframe_combo.currentTextChanged.connect(self._on_timeframe_changed)
        control_layout.addWidget(self.timeframe_combo)
        
        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.clicked.connect(self._refresh_chart)
        self.refresh_button.setEnabled(False)
        control_layout.addWidget(self.refresh_button)
        
        layout.addWidget(control_frame)
        
        # 图表区域
        self.chart_frame = QFrame()
        self.chart_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.chart_frame.setMinimumHeight(350)
        
        chart_layout = QVBoxLayout(self.chart_frame)
        chart_layout.setContentsMargins(0, 0, 0, 0)
        
        layout.addWidget(self.chart_frame)
    
    def _setup_matplotlib(self):
        """设置matplotlib"""
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        self.figure = Figure(figsize=(12, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        
        # 添加到布局
        if self.chart_frame:
            layout = self.chart_frame.layout()
            if layout:
                layout.addWidget(self.canvas)
    
    def _show_welcome_chart(self):
        """显示欢迎图表"""
        if not self.figure:
            return
        
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # 显示欢迎信息
        ax.text(0.5, 0.5, '请选择股票查看技术分析图表\n\n支持的图表类型：\n• K线图\n• RS曲线图\n• 成交量分析\n• 威科夫分析',
                horizontalalignment='center',
                verticalalignment='center',
                transform=ax.transAxes,
                fontsize=14,
                color='gray')
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        self.canvas.draw()
    
    def _on_chart_type_changed(self, chart_type: str):
        """图表类型变化"""
        if self.current_stock and self.stock_data is not None:
            self._update_chart()
        logger.debug(f"图表类型变化：{chart_type}")
    
    def _on_timeframe_changed(self, timeframe: str):
        """时间周期变化"""
        if self.current_stock and self.stock_data is not None:
            self._update_chart()
        logger.debug(f"时间周期变化：{timeframe}")
    
    def _refresh_chart(self):
        """刷新图表"""
        if self.current_stock:
            # 重新获取数据
            self.stock_data = self.data_service.get_stock_data(self.current_stock)
            self._update_chart()

    def _update_chart(self):
        """更新图表显示"""
        if not self.figure or not self.stock_data is not None:
            return

        chart_type = self.chart_type_combo.currentText()

        try:
            if chart_type == "K线图":
                self._draw_candlestick_chart()
            elif chart_type == "RS曲线图":
                self._draw_rs_chart()
            elif chart_type == "成交量图":
                self._draw_volume_chart()
            elif chart_type == "威科夫分析图":
                self._draw_wyckoff_chart()

            self.canvas.draw()

        except Exception as e:
            logger.error(f"绘制图表失败: {e}")
            self._show_error_chart(str(e))

    def _draw_candlestick_chart(self):
        """绘制K线图"""
        if self.stock_data is None or self.stock_data.empty:
            return

        self.figure.clear()

        # 创建子图
        ax1 = self.figure.add_subplot(211)  # K线图
        ax2 = self.figure.add_subplot(212)  # 成交量图

        # 准备数据
        data = self.stock_data.tail(100)  # 显示最近100个交易日
        dates = data.index

        # 绘制K线
        for i, (date, row) in enumerate(data.iterrows()):
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']

            # 确定颜色
            color = '#F44336' if close_price >= open_price else '#4CAF50'  # 红涨绿跌

            # 绘制影线
            ax1.plot([i, i], [low_price, high_price], color='black', linewidth=0.8)

            # 绘制实体
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)

            rect = Rectangle((i-0.3, body_bottom), 0.6, body_height,
                           facecolor=color, edgecolor='black', linewidth=0.5)
            ax1.add_patch(rect)

        # 设置K线图
        ax1.set_title(f'{self.current_stock} - K线图', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价格', fontsize=12)
        ax1.grid(True, alpha=0.3)

        # 绘制成交量
        volumes = data['volume'].values
        colors = ['#F44336' if data.iloc[i]['close'] >= data.iloc[i]['open'] else '#4CAF50'
                 for i in range(len(data))]

        ax2.bar(range(len(volumes)), volumes, color=colors, alpha=0.7)
        ax2.set_title('成交量', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=10)
        ax2.grid(True, alpha=0.3)

        # 设置x轴标签
        step = max(1, len(data) // 10)
        x_ticks = range(0, len(data), step)
        x_labels = [dates[i].strftime('%m-%d') for i in x_ticks]

        ax1.set_xticks(x_ticks)
        ax1.set_xticklabels(x_labels, rotation=45)
        ax2.set_xticks(x_ticks)
        ax2.set_xticklabels(x_labels, rotation=45)

        self.figure.tight_layout()

    def _draw_rs_chart(self):
        """绘制RS曲线图"""
        if self.stock_data is None or self.stock_data.empty:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        # 计算简化的RS值（相对于自身的移动平均）
        data = self.stock_data.tail(100)
        prices = data['close']
        ma20 = prices.rolling(20).mean()
        rs_values = prices / ma20

        # 绘制RS曲线
        dates = range(len(rs_values))
        ax.plot(dates, rs_values, color='#2196F3', linewidth=2, label='RS值')

        # 添加基准线
        ax.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7, label='基准线(1.0)')
        ax.axhline(y=1.2, color='red', linestyle='--', alpha=0.5, label='强势线(1.2)')
        ax.axhline(y=0.8, color='green', linestyle='--', alpha=0.5, label='弱势线(0.8)')

        # 填充区域
        ax.fill_between(dates, rs_values, 1.0,
                       where=(rs_values >= 1.0), color='red', alpha=0.1)
        ax.fill_between(dates, rs_values, 1.0,
                       where=(rs_values < 1.0), color='green', alpha=0.1)

        ax.set_title(f'{self.current_stock} - 相对强弱曲线', fontsize=14, fontweight='bold')
        ax.set_ylabel('RS值', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 设置x轴
        step = max(1, len(data) // 10)
        x_ticks = range(0, len(data), step)
        x_labels = [data.index[i].strftime('%m-%d') for i in x_ticks]
        ax.set_xticks(x_ticks)
        ax.set_xticklabels(x_labels, rotation=45)

        self.figure.tight_layout()

    def _draw_volume_chart(self):
        """绘制成交量图"""
        if self.stock_data is None or self.stock_data.empty:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        # 准备数据
        data = self.stock_data.tail(100)
        volumes = data['volume'].values

        # 计算成交量移动平均
        volume_ma5 = data['volume'].rolling(5).mean()
        volume_ma20 = data['volume'].rolling(20).mean()

        # 绘制成交量柱状图
        colors = ['#F44336' if data.iloc[i]['close'] >= data.iloc[i]['open'] else '#4CAF50'
                 for i in range(len(data))]

        bars = ax.bar(range(len(volumes)), volumes, color=colors, alpha=0.7, label='成交量')

        # 绘制移动平均线
        ax.plot(range(len(volume_ma5)), volume_ma5, color='orange', linewidth=1.5, label='MA5')
        ax.plot(range(len(volume_ma20)), volume_ma20, color='purple', linewidth=1.5, label='MA20')

        ax.set_title(f'{self.current_stock} - 成交量分析', fontsize=14, fontweight='bold')
        ax.set_ylabel('成交量', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 设置x轴
        step = max(1, len(data) // 10)
        x_ticks = range(0, len(data), step)
        x_labels = [data.index[i].strftime('%m-%d') for i in x_ticks]
        ax.set_xticks(x_ticks)
        ax.set_xticklabels(x_labels, rotation=45)

        self.figure.tight_layout()

    def _draw_wyckoff_chart(self):
        """绘制威科夫分析图"""
        if self.stock_data is None or self.stock_data.empty:
            return

        self.figure.clear()

        # 创建子图
        ax1 = self.figure.add_subplot(211)  # 价格图
        ax2 = self.figure.add_subplot(212)  # 成交量图

        # 准备数据
        data = self.stock_data.tail(100)
        dates = range(len(data))
        prices = data['close']
        volumes = data['volume']

        # 绘制价格线
        ax1.plot(dates, prices, color='#2196F3', linewidth=2, label='收盘价')

        # 计算支撑阻力位（简化版本）
        high_20 = data['high'].rolling(20).max()
        low_20 = data['low'].rolling(20).min()

        ax1.plot(dates, high_20, color='red', linestyle='--', alpha=0.7, label='阻力位')
        ax1.plot(dates, low_20, color='green', linestyle='--', alpha=0.7, label='支撑位')

        # 标注威科夫阶段（简化版本）
        # 这里可以根据实际的威科夫分析结果来标注
        ax1.text(0.02, 0.95, '威科夫分析阶段：累积期', transform=ax1.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                fontsize=10)

        ax1.set_title(f'{self.current_stock} - 威科夫分析', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价格', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 绘制成交量分析
        colors = ['#F44336' if data.iloc[i]['close'] >= data.iloc[i]['open'] else '#4CAF50'
                 for i in range(len(data))]

        ax2.bar(dates, volumes, color=colors, alpha=0.7)

        # 成交量移动平均
        volume_ma = volumes.rolling(20).mean()
        ax2.plot(dates, volume_ma, color='orange', linewidth=2, label='成交量MA20')

        ax2.set_title('成交量分析', fontsize=12)
        ax2.set_ylabel('成交量', fontsize=10)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 设置x轴
        step = max(1, len(data) // 10)
        x_ticks = range(0, len(data), step)
        x_labels = [data.index[i].strftime('%m-%d') for i in x_ticks]

        ax1.set_xticks(x_ticks)
        ax1.set_xticklabels(x_labels, rotation=45)
        ax2.set_xticks(x_ticks)
        ax2.set_xticklabels(x_labels, rotation=45)

        self.figure.tight_layout()

    def _show_error_chart(self, error_message: str):
        """显示错误信息图表"""
        if not self.figure:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        ax.text(0.5, 0.5, f'图表绘制失败\n\n错误信息：{error_message}',
                horizontalalignment='center',
                verticalalignment='center',
                transform=ax.transAxes,
                fontsize=12,
                color='red')

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        self.canvas.draw()

    # 公共方法
    def set_current_stock(self, symbol: str):
        """设置当前显示的股票"""
        self.current_stock = symbol
        self.refresh_button.setEnabled(True)

        # 获取股票数据
        self.stock_data = self.data_service.get_stock_data(symbol)

        # 更新图表
        self._update_chart()

        logger.debug(f"设置当前股票: {symbol}")

    def refresh_data(self):
        """刷新数据"""
        if self.current_stock:
            self.stock_data = self.data_service.get_stock_data(self.current_stock)
            self._update_chart()

    def get_current_chart_type(self) -> str:
        """获取当前图表类型"""
        return self.chart_type_combo.currentText() if self.chart_type_combo else "K线图"
