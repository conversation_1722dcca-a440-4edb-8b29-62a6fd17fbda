#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源配置管理
扩展配置系统以支持数据源配置、验证和模板管理
"""

import os
import yaml
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from ..data_sources.base import DataSourceConfig
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class DataSourceTemplate:
    """数据源配置模板"""
    name: str
    type: str
    description: str
    required_config: Dict[str, Any]
    optional_config: Dict[str, Any]
    example_config: Dict[str, Any]


class DataSourceConfigManager:
    """数据源配置管理器"""
    
    # 预定义的数据源模板
    TEMPLATES = {
        'xtdata': DataSourceTemplate(
            name='xtdata',
            type='market_data',
            description='XtData数据源（基于MiniQMT）',
            required_config={
                'ip': '127.0.0.1',
                'port': 58610
            },
            optional_config={
                'timeout': 30,
                'retry_times': 3,
                'auto_reconnect': True,
                'max_connections': 5
            },
            example_config={
                'name': 'xtdata_main',
                'enabled': True,
                'timeout': 30,
                'retry_times': 3,
                'auto_reconnect': True,
                'config': {
                    'ip': '127.0.0.1',
                    'port': 58610,
                    'max_connections': 5
                }
            }
        ),
        'tushare': DataSourceTemplate(
            name='tushare',
            type='market_data',
            description='Tushare数据源',
            required_config={
                'token': 'your_tushare_token'
            },
            optional_config={
                'timeout': 30,
                'retry_times': 3,
                'pro_api': True
            },
            example_config={
                'name': 'tushare_main',
                'enabled': True,
                'timeout': 30,
                'retry_times': 3,
                'auto_reconnect': True,
                'config': {
                    'token': 'your_tushare_token',
                    'pro_api': True
                }
            }
        ),
        'akshare': DataSourceTemplate(
            name='akshare',
            type='market_data',
            description='AkShare数据源',
            required_config={},
            optional_config={
                'timeout': 30,
                'retry_times': 3,
                'rate_limit': 1.0
            },
            example_config={
                'name': 'akshare_main',
                'enabled': True,
                'timeout': 30,
                'retry_times': 3,
                'auto_reconnect': True,
                'config': {
                    'rate_limit': 1.0
                }
            }
        )
    }
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认使用项目根目录的config.yaml
        """
        if config_file is None:
            # 自动查找配置文件
            project_root = Path(__file__).parent.parent.parent
            config_file = project_root / 'config.yaml'
        
        self.config_file = Path(config_file)
        self._config_data: Dict[str, Any] = {}
        self._data_sources: Dict[str, DataSourceConfig] = {}
        
        # 加载配置
        self.load_config()
        
        logger.info(f"数据源配置管理器初始化完成，配置文件: {self.config_file}")
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_data = yaml.safe_load(f) or {}
                    
                # 解析数据源配置
                self._parse_data_sources()
                
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                logger.warning(f"配置文件不存在，使用默认配置: {self.config_file}")
                self._create_default_config()
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 更新配置数据
            self._update_config_data()
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(
                    self._config_data, 
                    f, 
                    default_flow_style=False, 
                    allow_unicode=True,
                    indent=2
                )
            
            logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_data_source_config(self, name: str) -> Optional[DataSourceConfig]:
        """
        获取数据源配置
        
        Args:
            name: 数据源名称
            
        Returns:
            Optional[DataSourceConfig]: 数据源配置
        """
        return self._data_sources.get(name)
    
    def add_data_source_config(self, config: DataSourceConfig) -> bool:
        """
        添加数据源配置
        
        Args:
            config: 数据源配置
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 验证配置
            self.validate_config(config)
            
            # 添加到内存
            self._data_sources[config.name] = config
            
            logger.info(f"添加数据源配置: {config.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加数据源配置失败: {config.name}, {e}")
            return False
    
    def update_data_source_config(self, name: str, config: DataSourceConfig) -> bool:
        """
        更新数据源配置
        
        Args:
            name: 原数据源名称
            config: 新的数据源配置
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if name not in self._data_sources:
                logger.warning(f"数据源配置不存在: {name}")
                return False
            
            # 验证配置
            self.validate_config(config)
            
            # 更新配置
            if name != config.name:
                # 名称变更，删除旧配置
                del self._data_sources[name]
            
            self._data_sources[config.name] = config
            
            logger.info(f"更新数据源配置: {name} -> {config.name}")
            return True
            
        except Exception as e:
            logger.error(f"更新数据源配置失败: {name}, {e}")
            return False
    
    def remove_data_source_config(self, name: str) -> bool:
        """
        移除数据源配置
        
        Args:
            name: 数据源名称
            
        Returns:
            bool: 移除是否成功
        """
        if name in self._data_sources:
            del self._data_sources[name]
            logger.info(f"移除数据源配置: {name}")
            return True
        else:
            logger.warning(f"数据源配置不存在: {name}")
            return False
    
    def list_data_sources(self) -> List[str]:
        """
        获取所有数据源名称列表
        
        Returns:
            List[str]: 数据源名称列表
        """
        return list(self._data_sources.keys())
    
    def get_enabled_data_sources(self) -> List[DataSourceConfig]:
        """
        获取启用的数据源配置列表
        
        Returns:
            List[DataSourceConfig]: 启用的数据源配置列表
        """
        return [config for config in self._data_sources.values() if config.enabled]
    
    def create_from_template(self, template_name: str, name: str, **kwargs) -> Optional[DataSourceConfig]:
        """
        从模板创建数据源配置
        
        Args:
            template_name: 模板名称
            name: 数据源名称
            **kwargs: 配置参数
            
        Returns:
            Optional[DataSourceConfig]: 创建的数据源配置
        """
        try:
            if template_name not in self.TEMPLATES:
                logger.error(f"模板不存在: {template_name}")
                return None
            
            template = self.TEMPLATES[template_name]
            
            # 合并配置
            config_dict = template.example_config.copy()
            config_dict['name'] = name
            
            # 更新配置参数
            if 'config' not in config_dict:
                config_dict['config'] = {}
            
            for key, value in kwargs.items():
                if key in ['enabled', 'timeout', 'retry_times', 'auto_reconnect']:
                    config_dict[key] = value
                else:
                    config_dict['config'][key] = value
            
            # 创建配置对象
            config = DataSourceConfig(**config_dict)
            
            # 验证配置
            self.validate_config(config, template_name)
            
            logger.info(f"从模板创建数据源配置: {template_name} -> {name}")
            return config
            
        except Exception as e:
            logger.error(f"从模板创建配置失败: {template_name}, {e}")
            return None
    
    def validate_config(self, config: DataSourceConfig, template_name: Optional[str] = None) -> bool:
        """
        验证数据源配置
        
        Args:
            config: 数据源配置
            template_name: 模板名称（可选）
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValueError: 配置验证失败
        """
        # 基本验证
        if not config.name:
            raise ValueError("数据源名称不能为空")
        
        if config.timeout <= 0:
            raise ValueError("超时时间必须大于0")
        
        if config.retry_times < 0:
            raise ValueError("重试次数不能小于0")
        
        # 模板特定验证
        if template_name and template_name in self.TEMPLATES:
            template = self.TEMPLATES[template_name]
            
            # 检查必需配置
            for key, default_value in template.required_config.items():
                if key not in config.config:
                    if default_value is not None:
                        config.config[key] = default_value
                    else:
                        raise ValueError(f"缺少必需配置项: {key}")
        
        logger.debug(f"配置验证通过: {config.name}")
        return True
    
    def get_template_list(self) -> List[str]:
        """
        获取可用模板列表
        
        Returns:
            List[str]: 模板名称列表
        """
        return list(self.TEMPLATES.keys())
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            Optional[Dict[str, Any]]: 模板信息
        """
        if template_name not in self.TEMPLATES:
            return None
        
        template = self.TEMPLATES[template_name]
        return {
            'name': template.name,
            'type': template.type,
            'description': template.description,
            'required_config': template.required_config,
            'optional_config': template.optional_config,
            'example_config': template.example_config
        }
    
    def _parse_data_sources(self) -> None:
        """解析数据源配置"""
        data_sources_config = self._config_data.get('data_sources', {})
        
        for name, config_dict in data_sources_config.items():
            try:
                # 确保配置字典有必要的字段
                if 'name' not in config_dict:
                    config_dict['name'] = name
                
                config = DataSourceConfig(**config_dict)
                self._data_sources[name] = config
                
                logger.debug(f"解析数据源配置: {name}")
                
            except Exception as e:
                logger.error(f"解析数据源配置失败: {name}, {e}")
    
    def _update_config_data(self) -> None:
        """更新配置数据"""
        if 'data_sources' not in self._config_data:
            self._config_data['data_sources'] = {}
        
        # 清空现有数据源配置
        self._config_data['data_sources'].clear()
        
        # 添加当前数据源配置
        for name, config in self._data_sources.items():
            config_dict = asdict(config)
            self._config_data['data_sources'][name] = config_dict
    
    def _create_default_config(self) -> None:
        """创建默认配置"""
        self._config_data = {
            'app': {
                'name': '威科夫相对强弱选股系统',
                'version': '1.0.0',
                'debug': False
            },
            'data_sources': {
                'xtdata_main': {
                    'name': 'xtdata_main',
                    'enabled': True,
                    'timeout': 30,
                    'retry_times': 3,
                    'auto_reconnect': True,
                    'config': {
                        'ip': '127.0.0.1',
                        'port': 58610
                    }
                }
            },
            'database': {
                'type': 'sqlite',
                'path': 'data/wyckoff.db',
                'pool_size': 10,
                'echo': False
            },
            'ui': {
                'theme': 'light',
                'language': 'zh_CN',
                'window_size': [1200, 800],
                'auto_save': True
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/app.log',
                'max_size': '10MB',
                'backup_count': 5
            }
        }
        
        # 解析默认数据源配置
        self._parse_data_sources()
        
        logger.info("创建默认配置")
    
    def export_config(self, export_path: str, include_sensitive: bool = False) -> bool:
        """
        导出配置到指定路径
        
        Args:
            export_path: 导出路径
            include_sensitive: 是否包含敏感信息（如token）
            
        Returns:
            bool: 导出是否成功
        """
        try:
            export_data = self._config_data.copy()
            
            # 如果不包含敏感信息，移除敏感字段
            if not include_sensitive:
                for ds_name, ds_config in export_data.get('data_sources', {}).items():
                    config_section = ds_config.get('config', {})
                    sensitive_keys = ['token', 'password', 'secret', 'key']
                    
                    for key in sensitive_keys:
                        if key in config_section:
                            config_section[key] = '***HIDDEN***'
            
            # 添加导出信息
            export_data['export_info'] = {
                'exported_at': datetime.now().isoformat(),
                'include_sensitive': include_sensitive,
                'version': export_data.get('app', {}).get('version', '1.0.0')
            }
            
            # 写入文件
            with open(export_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    export_data,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2
                )
            
            logger.info(f"配置导出成功: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导出失败: {e}")
            return False
    
    def import_config(self, import_path: str, merge: bool = True) -> bool:
        """
        从指定路径导入配置
        
        Args:
            import_path: 导入路径
            merge: 是否与现有配置合并
            
        Returns:
            bool: 导入是否成功
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = yaml.safe_load(f)
            
            if not import_data:
                logger.error("导入的配置文件为空")
                return False
            
            if merge:
                # 合并配置
                self._merge_config(self._config_data, import_data)
            else:
                # 替换配置
                self._config_data = import_data
            
            # 重新解析数据源配置
            self._data_sources.clear()
            self._parse_data_sources()
            
            logger.info(f"配置导入成功: {import_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            return False
    
    def _merge_config(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        递归合并配置字典
        
        Args:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            Dict[str, Any]: 配置摘要
        """
        return {
            'config_file': str(self.config_file),
            'total_data_sources': len(self._data_sources),
            'enabled_data_sources': len(self.get_enabled_data_sources()),
            'available_templates': len(self.TEMPLATES),
            'data_source_names': list(self._data_sources.keys()),
            'template_names': list(self.TEMPLATES.keys())
        } 