#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询性能优化器
第15周：性能优化和部署准备 - 数据库查询性能优化
"""

import sqlite3
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import threading
from contextlib import contextmanager
from ..utils.logger import get_logger
from ..utils.performance_optimizer import profile

logger = get_logger(__name__)


@dataclass
class QueryStats:
    """查询统计信息"""
    query: str
    execution_time: float
    rows_affected: int
    timestamp: float
    cache_hit: bool = False


class QueryCache:
    """查询缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        """
        初始化查询缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.access_order: List[str] = []
        self.lock = threading.RLock()
        logger.info(f"查询缓存初始化完成，最大大小: {max_size}, TTL: {ttl}秒")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                value, timestamp = self.cache[key]
                
                # 检查是否过期
                if time.time() - timestamp > self.ttl:
                    del self.cache[key]
                    if key in self.access_order:
                        self.access_order.remove(key)
                    return None
                
                # 更新访问顺序
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
                
                return value
            
            return None
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self.lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size and key not in self.cache:
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            # 添加新条目
            self.cache[key] = (value, time.time())
            
            # 更新访问顺序
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
    
    def clear(self):
        """清除所有缓存"""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
            logger.info("查询缓存已清除")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                "cache_size": len(self.cache),
                "max_size": self.max_size,
                "usage_percent": (len(self.cache) / self.max_size) * 100,
                "ttl": self.ttl
            }


class ConnectionPool:
    """数据库连接池"""
    
    def __init__(self, database_path: str, max_connections: int = 10):
        """
        初始化连接池
        
        Args:
            database_path: 数据库路径
            max_connections: 最大连接数
        """
        self.database_path = database_path
        self.max_connections = max_connections
        self.connections: List[sqlite3.Connection] = []
        self.available_connections: List[sqlite3.Connection] = []
        self.lock = threading.RLock()
        
        # 预创建连接
        self._create_initial_connections()
        logger.info(f"数据库连接池初始化完成，最大连接数: {max_connections}")
    
    def _create_initial_connections(self):
        """创建初始连接"""
        for _ in range(min(3, self.max_connections)):  # 预创建3个连接
            conn = self._create_connection()
            if conn:
                self.connections.append(conn)
                self.available_connections.append(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新连接"""
        try:
            conn = sqlite3.connect(
                self.database_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 优化连接设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            return conn
            
        except Exception as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            with self.lock:
                if self.available_connections:
                    conn = self.available_connections.pop()
                elif len(self.connections) < self.max_connections:
                    conn = self._create_connection()
                    if conn:
                        self.connections.append(conn)
                else:
                    # 等待可用连接
                    logger.warning("连接池已满，等待可用连接...")
                    time.sleep(0.1)
                    if self.available_connections:
                        conn = self.available_connections.pop()
            
            if conn is None:
                raise Exception("无法获取数据库连接")
            
            yield conn
            
        finally:
            if conn:
                with self.lock:
                    self.available_connections.append(conn)
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            for conn in self.connections:
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接失败: {e}")
            
            self.connections.clear()
            self.available_connections.clear()
            logger.info("所有数据库连接已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self.lock:
            return {
                "total_connections": len(self.connections),
                "available_connections": len(self.available_connections),
                "active_connections": len(self.connections) - len(self.available_connections),
                "max_connections": self.max_connections,
                "usage_percent": ((len(self.connections) - len(self.available_connections)) / self.max_connections) * 100
            }


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self, database_path: str):
        """初始化查询优化器"""
        self.database_path = database_path
        self.connection_pool = ConnectionPool(database_path)
        self.query_cache = QueryCache()
        self.query_stats: List[QueryStats] = []
        self.slow_query_threshold = 1.0  # 1秒
        logger.info("查询优化器初始化完成")
    
    @profile("database_query")
    def execute_query(self, query: str, params: Tuple = None, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        执行查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            use_cache: 是否使用缓存
            
        Returns:
            查询结果列表
        """
        start_time = time.time()
        cache_key = f"{query}:{params}" if params else query
        
        # 尝试从缓存获取
        if use_cache:
            cached_result = self.query_cache.get(cache_key)
            if cached_result is not None:
                self._record_query_stats(query, time.time() - start_time, len(cached_result), True)
                return cached_result
        
        # 执行查询
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                # 获取结果
                columns = [description[0] for description in cursor.description] if cursor.description else []
                rows = cursor.fetchall()
                
                # 转换为字典列表
                result = [dict(zip(columns, row)) for row in rows]
                
                # 缓存结果（只缓存SELECT查询）
                if use_cache and query.strip().upper().startswith('SELECT'):
                    self.query_cache.set(cache_key, result)
                
                # 记录统计
                execution_time = time.time() - start_time
                self._record_query_stats(query, execution_time, len(result), False)
                
                return result
                
        except Exception as e:
            logger.error(f"查询执行失败: {query} - {e}")
            raise
    
    @profile("database_execute")
    def execute_command(self, command: str, params: Tuple = None) -> int:
        """
        执行命令（INSERT, UPDATE, DELETE）
        
        Args:
            command: SQL命令
            params: 命令参数
            
        Returns:
            影响的行数
        """
        start_time = time.time()
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(command, params)
                else:
                    cursor.execute(command)
                
                conn.commit()
                rows_affected = cursor.rowcount
                
                # 记录统计
                execution_time = time.time() - start_time
                self._record_query_stats(command, execution_time, rows_affected, False)
                
                # 清除相关缓存
                self._invalidate_cache_for_command(command)
                
                return rows_affected
                
        except Exception as e:
            logger.error(f"命令执行失败: {command} - {e}")
            raise
    
    def execute_batch(self, commands: List[Tuple[str, Tuple]]) -> List[int]:
        """
        批量执行命令
        
        Args:
            commands: 命令列表，每个元素为(command, params)
            
        Returns:
            每个命令影响的行数列表
        """
        start_time = time.time()
        results = []
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for command, params in commands:
                    if params:
                        cursor.execute(command, params)
                    else:
                        cursor.execute(command)
                    
                    results.append(cursor.rowcount)
                
                conn.commit()
                
                # 记录统计
                execution_time = time.time() - start_time
                total_rows = sum(results)
                self._record_query_stats(f"BATCH({len(commands)} commands)", execution_time, total_rows, False)
                
                # 清除缓存
                self.query_cache.clear()
                
                return results
                
        except Exception as e:
            logger.error(f"批量命令执行失败: {e}")
            raise
    
    def _record_query_stats(self, query: str, execution_time: float, rows_affected: int, cache_hit: bool):
        """记录查询统计"""
        stats = QueryStats(
            query=query[:100] + "..." if len(query) > 100 else query,  # 截断长查询
            execution_time=execution_time,
            rows_affected=rows_affected,
            timestamp=time.time(),
            cache_hit=cache_hit
        )
        
        self.query_stats.append(stats)
        
        # 检查慢查询
        if execution_time > self.slow_query_threshold and not cache_hit:
            logger.warning(f"慢查询检测: {stats.query} - {execution_time:.2f}s")
        
        # 限制统计记录数量
        if len(self.query_stats) > 1000:
            self.query_stats = self.query_stats[-500:]  # 保留最近500条
    
    def _invalidate_cache_for_command(self, command: str):
        """根据命令类型清除相关缓存"""
        command_upper = command.strip().upper()
        
        if any(cmd in command_upper for cmd in ['INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER']):
            # 对于修改操作，清除所有缓存
            self.query_cache.clear()
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析查询性能"""
        if not self.query_stats:
            return {"message": "暂无查询统计数据"}
        
        # 计算统计指标
        total_queries = len(self.query_stats)
        cache_hits = sum(1 for stat in self.query_stats if stat.cache_hit)
        cache_hit_rate = (cache_hits / total_queries) * 100
        
        # 执行时间统计
        execution_times = [stat.execution_time for stat in self.query_stats if not stat.cache_hit]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        max_execution_time = max(execution_times) if execution_times else 0
        
        # 慢查询统计
        slow_queries = [stat for stat in self.query_stats if stat.execution_time > self.slow_query_threshold and not stat.cache_hit]
        
        # 最频繁的查询
        query_counts = {}
        for stat in self.query_stats:
            query_counts[stat.query] = query_counts.get(stat.query, 0) + 1
        
        most_frequent = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "total_queries": total_queries,
            "cache_hit_rate": cache_hit_rate,
            "avg_execution_time": avg_execution_time,
            "max_execution_time": max_execution_time,
            "slow_queries_count": len(slow_queries),
            "slow_queries": [
                {
                    "query": sq.query,
                    "execution_time": sq.execution_time,
                    "timestamp": sq.timestamp
                }
                for sq in slow_queries[-5:]  # 最近5个慢查询
            ],
            "most_frequent_queries": most_frequent,
            "connection_pool_stats": self.connection_pool.get_stats(),
            "cache_stats": self.query_cache.get_stats()
        }
    
    def optimize_database(self):
        """优化数据库"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 分析数据库
                cursor.execute("ANALYZE")
                
                # 重建索引
                cursor.execute("REINDEX")
                
                # 清理数据库
                cursor.execute("VACUUM")
                
                conn.commit()
                
                logger.info("数据库优化完成")
                
        except Exception as e:
            logger.error(f"数据库优化失败: {e}")
            raise
    
    def get_table_stats(self) -> Dict[str, Any]:
        """获取表统计信息"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                table_stats = {}
                for table in tables:
                    # 获取行数
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    row_count = cursor.fetchone()[0]
                    
                    # 获取表大小（近似）
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    
                    table_stats[table] = {
                        "row_count": row_count,
                        "column_count": len(columns),
                        "columns": [col[1] for col in columns]
                    }
                
                return table_stats
                
        except Exception as e:
            logger.error(f"获取表统计失败: {e}")
            return {}
    
    def shutdown(self):
        """关闭查询优化器"""
        try:
            self.connection_pool.close_all()
            self.query_cache.clear()
            logger.info("查询优化器已关闭")
            
        except Exception as e:
            logger.error(f"关闭查询优化器失败: {e}")


# 全局查询优化器实例（延迟初始化）
_query_optimizer: Optional[QueryOptimizer] = None

def get_query_optimizer(database_path: str = None) -> QueryOptimizer:
    """获取查询优化器实例"""
    global _query_optimizer
    
    if _query_optimizer is None and database_path:
        _query_optimizer = QueryOptimizer(database_path)
    
    return _query_optimizer
