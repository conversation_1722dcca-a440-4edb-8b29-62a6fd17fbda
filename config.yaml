# 威科夫相对强弱选股系统 - 配置文件
application:
  name: "威科夫相对强弱选股系统"
  version: "1.0.0"
  debug: false
  language: "zh_CN"

database:
  path: "./data/database.db"
  backup_path: "./data/backup/"
  auto_backup: true
  backup_interval: 24  # hours
  connection_pool_size: 5

# 数据下载配置
data_download:
  enabled: true
  use_real_data: true  # 是否使用真实数据
  auto_download_on_startup: false  # 启动时自动下载
  include_history: true  # 是否包含历史数据
  history_days: 365  # 历史数据天数
  batch_size: 50  # 批量下载大小
  request_delay: 0.1  # 请求间隔（秒）
  max_retries: 3  # 最大重试次数
  update_interval: 24  # 自动更新间隔（小时）
  progress_update_interval: 1  # 进度更新间隔（秒）

data_sources:
  primary: "xtdata"
  fallback: ["tushare", "akshare"]
  
  # 连接池配置
  connection_pool:
    enabled: true
    max_size: 10
    min_size: 2
    timeout: 30
    health_check_interval: 60
  
  # 数据格式化配置
  data_formatter:
    enabled: true
    auto_clean: true
    quality_check: true
    fill_missing: "forward"
    remove_outliers: true
    outlier_threshold: 3.0
  
  # 数据源管理配置
  manager:
    health_check_interval: 300  # 健康检查间隔(秒)
    retry_backoff_factor: 2.0   # 重试退避因子
    max_retry_delay: 60         # 最大重试延迟(秒)
    
  xtdata:
    name: "XtData"
    enabled: true
    timeout: 30
    retry_times: 3
    auto_reconnect: true
    config:
      host: "127.0.0.1"
      port: 58610
      username: ""
      password: ""
      connect_timeout: 10
      data_timeout: 30
      supported_periods: ["1d", "1m", "5m", "15m", "30m", "60m"]
      supported_dividend_types: ["none", "front", "back"]
    
  tushare:
    name: "Tushare"
    enabled: false
    token: ""
    pro_api: true
    max_requests_per_minute: 200
    timeout: 30
    retry_times: 3
    auto_reconnect: true
    
  akshare:
    name: "AkShare"
    enabled: false
    timeout: 30
    retry_times: 3
    auto_reconnect: true
    config:
      api_timeout: 30

ui:
  theme: "light"  # light/dark
  window_size: [1200, 800]
  window_position: [100, 100]
  auto_save_layout: true
  font_family: "Microsoft YaHei"
  font_size: 9

selection:
  default_params:
    market_index: "000300.SH"  # 沪深300
    top_sectors: 5
    stocks_per_sector: 3
    exclude_st: true
    exclude_new_stock_days: 60
    min_market_cap: 0  # 亿元
    
  time_periods:
    short_term: 5    # 5个交易日
    medium_term: 20  # 20个交易日
    long_term: 60    # 60个交易日

performance:
  max_workers: 4  # 并行处理线程数
  cache_size: 1000  # 缓存条目数
  batch_size: 100   # 批处理大小
  
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file_path: "./logs/app.log"
  max_file_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
update:
  auto_update_data: true
  update_time: "15:30"  # 每日更新时间
  update_on_startup: true
  check_data_integrity: true

export:
  default_format: "excel"  # excel, csv, pdf
  include_charts: true
  output_path: "./output/"