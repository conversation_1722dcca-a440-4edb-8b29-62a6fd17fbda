#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI错误处理器
提供用户友好的错误消息和解决方案
"""

from typing import Dict, Optional, Callable, Any
from PyQt6.QtWidgets import QMessageBox, QWidget
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QIcon

from ...utils.logger import get_logger

logger = get_logger(__name__)


class UIErrorHandler(QObject):
    """UI错误处理器"""
    
    # 信号定义
    error_occurred = pyqtSignal(str, str)  # 错误类型, 错误消息
    warning_occurred = pyqtSignal(str)     # 警告消息
    info_occurred = pyqtSignal(str)        # 信息消息
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.parent_widget = parent
        
        # 错误消息映射
        self.error_messages = {
            # 数据相关错误
            "data_connection_failed": {
                "title": "数据连接失败",
                "message": "无法连接到数据源，请检查网络连接和数据源配置。",
                "solutions": [
                    "检查网络连接是否正常",
                    "确认数据源服务是否可用",
                    "重新配置数据源参数",
                    "联系技术支持"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            "data_not_found": {
                "title": "数据未找到",
                "message": "未找到指定的股票或板块数据。",
                "solutions": [
                    "检查股票代码是否正确",
                    "确认数据日期范围是否有效",
                    "尝试刷新数据",
                    "选择其他时间范围"
                ],
                "icon": QMessageBox.Icon.Warning
            },
            "data_format_error": {
                "title": "数据格式错误",
                "message": "数据格式不正确，无法进行分析。",
                "solutions": [
                    "检查数据源格式",
                    "重新下载数据",
                    "联系数据提供商",
                    "使用备用数据源"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            
            # 计算相关错误
            "calculation_failed": {
                "title": "计算失败",
                "message": "分析计算过程中出现错误。",
                "solutions": [
                    "检查输入参数是否有效",
                    "确认数据完整性",
                    "重新执行计算",
                    "调整计算参数"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            "insufficient_data": {
                "title": "数据不足",
                "message": "数据量不足以进行有效分析。",
                "solutions": [
                    "扩大时间范围",
                    "选择更多股票",
                    "降低筛选条件",
                    "使用更长的历史数据"
                ],
                "icon": QMessageBox.Icon.Warning
            },
            
            # 配置相关错误
            "invalid_parameters": {
                "title": "参数无效",
                "message": "输入的参数不符合要求。",
                "solutions": [
                    "检查参数范围是否正确",
                    "确认日期格式是否有效",
                    "重置为默认参数",
                    "参考参数说明文档"
                ],
                "icon": QMessageBox.Icon.Warning
            },
            "config_load_failed": {
                "title": "配置加载失败",
                "message": "无法加载配置文件。",
                "solutions": [
                    "检查配置文件是否存在",
                    "确认文件权限",
                    "重置为默认配置",
                    "重新安装应用程序"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            
            # 系统相关错误
            "memory_insufficient": {
                "title": "内存不足",
                "message": "系统内存不足，无法完成操作。",
                "solutions": [
                    "关闭其他应用程序",
                    "减少数据处理量",
                    "重启应用程序",
                    "升级系统内存"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            "file_access_denied": {
                "title": "文件访问被拒绝",
                "message": "无法访问指定文件。",
                "solutions": [
                    "检查文件权限",
                    "以管理员身份运行",
                    "检查文件是否被占用",
                    "更改文件保存位置"
                ],
                "icon": QMessageBox.Icon.Critical
            },
            
            # 网络相关错误
            "network_timeout": {
                "title": "网络超时",
                "message": "网络请求超时，请稍后重试。",
                "solutions": [
                    "检查网络连接",
                    "稍后重试",
                    "更换网络环境",
                    "联系网络管理员"
                ],
                "icon": QMessageBox.Icon.Warning
            },
            "api_rate_limit": {
                "title": "API调用频率限制",
                "message": "API调用过于频繁，请稍后重试。",
                "solutions": [
                    "等待一段时间后重试",
                    "减少请求频率",
                    "升级API服务等级",
                    "使用缓存数据"
                ],
                "icon": QMessageBox.Icon.Warning
            }
        }
        
        logger.info("UI错误处理器初始化完成")
    
    def handle_error(self, error_type: str, details: Optional[str] = None, 
                    show_dialog: bool = True) -> bool:
        """
        处理错误
        
        Args:
            error_type: 错误类型
            details: 错误详情
            show_dialog: 是否显示对话框
            
        Returns:
            bool: 是否成功处理
        """
        try:
            if error_type not in self.error_messages:
                return self._handle_unknown_error(error_type, details, show_dialog)
            
            error_info = self.error_messages[error_type]
            
            # 记录错误日志
            logger.error(f"UI错误: {error_type} - {error_info['message']}")
            if details:
                logger.error(f"错误详情: {details}")
            
            # 发送错误信号
            self.error_occurred.emit(error_type, error_info['message'])
            
            # 显示错误对话框
            if show_dialog:
                self._show_error_dialog(error_info, details)
            
            return True
            
        except Exception as e:
            logger.error(f"错误处理器异常: {e}")
            return False
    
    def handle_warning(self, message: str, show_dialog: bool = True) -> bool:
        """
        处理警告
        
        Args:
            message: 警告消息
            show_dialog: 是否显示对话框
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.warning(f"UI警告: {message}")
            self.warning_occurred.emit(message)
            
            if show_dialog:
                self._show_warning_dialog(message)
            
            return True
            
        except Exception as e:
            logger.error(f"警告处理异常: {e}")
            return False
    
    def handle_info(self, message: str, show_dialog: bool = True) -> bool:
        """
        处理信息
        
        Args:
            message: 信息消息
            show_dialog: 是否显示对话框
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.info(f"UI信息: {message}")
            self.info_occurred.emit(message)
            
            if show_dialog:
                self._show_info_dialog(message)
            
            return True
            
        except Exception as e:
            logger.error(f"信息处理异常: {e}")
            return False
    
    def _handle_unknown_error(self, error_type: str, details: Optional[str], 
                             show_dialog: bool) -> bool:
        """处理未知错误"""
        message = f"发生未知错误: {error_type}"
        if details:
            message += f"\n详情: {details}"
        
        logger.error(f"未知错误: {error_type}")
        self.error_occurred.emit(error_type, message)
        
        if show_dialog:
            self._show_generic_error_dialog(message)
        
        return True
    
    def _show_error_dialog(self, error_info: Dict[str, Any], details: Optional[str]):
        """显示错误对话框"""
        msg_box = QMessageBox(self.parent_widget)
        msg_box.setIcon(error_info['icon'])
        msg_box.setWindowTitle(error_info['title'])
        msg_box.setText(error_info['message'])
        
        # 添加详细信息
        detailed_text = "建议解决方案:\n"
        for i, solution in enumerate(error_info['solutions'], 1):
            detailed_text += f"{i}. {solution}\n"
        
        if details:
            detailed_text += f"\n技术详情:\n{details}"
        
        msg_box.setDetailedText(detailed_text)
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg_box.exec()
    
    def _show_warning_dialog(self, message: str):
        """显示警告对话框"""
        QMessageBox.warning(self.parent_widget, "警告", message)
    
    def _show_info_dialog(self, message: str):
        """显示信息对话框"""
        QMessageBox.information(self.parent_widget, "信息", message)
    
    def _show_generic_error_dialog(self, message: str):
        """显示通用错误对话框"""
        QMessageBox.critical(self.parent_widget, "错误", message)
    
    def add_custom_error(self, error_type: str, title: str, message: str, 
                        solutions: list, icon: QMessageBox.Icon = QMessageBox.Icon.Critical):
        """
        添加自定义错误类型
        
        Args:
            error_type: 错误类型标识
            title: 错误标题
            message: 错误消息
            solutions: 解决方案列表
            icon: 图标类型
        """
        self.error_messages[error_type] = {
            "title": title,
            "message": message,
            "solutions": solutions,
            "icon": icon
        }
        
        logger.info(f"添加自定义错误类型: {error_type}")
    
    def get_error_info(self, error_type: str) -> Optional[Dict[str, Any]]:
        """获取错误信息"""
        return self.error_messages.get(error_type)
    
    def clear_custom_errors(self):
        """清除自定义错误"""
        # 保留预定义错误，清除自定义添加的错误
        predefined_errors = set(self.error_messages.keys())
        # 这里可以根据需要实现清除逻辑
        logger.info("清除自定义错误类型")
