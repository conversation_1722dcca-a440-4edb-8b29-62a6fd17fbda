"""
系统设置控件

提供系统配置和管理功能
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QSpinBox, QDoubleSpinBox, QComboBox,
    QCheckBox, QSlider, QLineEdit, QTabWidget, QScrollArea,
    QTextEdit, QProgressBar, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from ...utils.logger import get_logger
from ...config.environment import Environment

logger = get_logger(__name__)


class SystemSettingsWidget(QWidget):
    """系统设置控件"""
    
    # 信号定义
    settings_changed = pyqtSignal(dict)  # 设置变更
    theme_changed = pyqtSignal(str)      # 主题变更
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化系统设置控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 环境管理器
        self.env_manager = Environment()
        
        # 当前设置
        self.current_settings: Dict[str, Any] = {}
        
        # 控件引用
        self.settings_tabs: Optional[QTabWidget] = None
        self.apply_button: Optional[QPushButton] = None
        self.reset_button: Optional[QPushButton] = None
        self.status_label: Optional[QLabel] = None
        
        # 设置控件引用
        self.theme_settings = {}
        self.performance_settings = {}
        self.cache_settings = {}
        self.logging_settings = {}
        
        self._init_ui()
        self._load_current_settings()
        
        logger.info("系统设置控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("⚙️ 系统设置")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        self.settings_tabs = QTabWidget()
        
        # 界面主题设置标签页
        theme_tab = self._create_theme_settings_tab()
        self.settings_tabs.addTab(theme_tab, "🎨 界面主题")
        
        # 性能设置标签页
        performance_tab = self._create_performance_settings_tab()
        self.settings_tabs.addTab(performance_tab, "⚡ 性能设置")
        
        # 缓存管理标签页
        cache_tab = self._create_cache_settings_tab()
        self.settings_tabs.addTab(cache_tab, "💾 缓存管理")
        
        # 日志设置标签页
        logging_tab = self._create_logging_settings_tab()
        self.settings_tabs.addTab(logging_tab, "📝 日志设置")
        
        layout.addWidget(self.settings_tabs)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("✅ 应用设置")
        self.apply_button.clicked.connect(self._apply_settings)
        self.apply_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px 16px; }")
        button_layout.addWidget(self.apply_button)
        
        self.reset_button = QPushButton("🔄 重置默认")
        self.reset_button.clicked.connect(self._reset_settings)
        button_layout.addWidget(self.reset_button)
        
        export_button = QPushButton("📤 导出设置")
        export_button.clicked.connect(self._export_settings)
        button_layout.addWidget(export_button)
        
        import_button = QPushButton("📥 导入设置")
        import_button.clicked.connect(self._import_settings)
        button_layout.addWidget(import_button)
        
        button_layout.addStretch()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 9pt;")
        button_layout.addWidget(self.status_label)
        
        layout.addLayout(button_layout)
    
    def _create_theme_settings_tab(self) -> QWidget:
        """创建界面主题设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 主题选择
        theme_group = QGroupBox("主题选择")
        theme_layout = QGridLayout(theme_group)
        
        theme_layout.addWidget(QLabel("当前主题:"), 0, 0)
        self.theme_settings['current_theme'] = QComboBox()
        self.theme_settings['current_theme'].addItems([
            "modern_light", "modern_dark", "classic_light", "classic_dark"
        ])
        self.theme_settings['current_theme'].setCurrentText("modern_light")
        theme_layout.addWidget(self.theme_settings['current_theme'], 0, 1)
        
        theme_layout.addWidget(QLabel("自动切换主题:"), 1, 0)
        self.theme_settings['auto_theme'] = QCheckBox("根据系统时间自动切换")
        theme_layout.addWidget(self.theme_settings['auto_theme'], 1, 1)
        
        scroll_layout.addWidget(theme_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout(font_group)
        
        font_layout.addWidget(QLabel("界面字体:"), 0, 0)
        self.theme_settings['ui_font'] = QComboBox()
        self.theme_settings['ui_font'].addItems([
            "Microsoft YaHei UI", "SimHei", "Arial", "Segoe UI"
        ])
        font_layout.addWidget(self.theme_settings['ui_font'], 0, 1)
        
        font_layout.addWidget(QLabel("字体大小:"), 0, 2)
        self.theme_settings['font_size'] = QSpinBox()
        self.theme_settings['font_size'].setRange(8, 16)
        self.theme_settings['font_size'].setValue(9)
        font_layout.addWidget(self.theme_settings['font_size'], 0, 3)
        
        font_layout.addWidget(QLabel("代码字体:"), 1, 0)
        self.theme_settings['code_font'] = QComboBox()
        self.theme_settings['code_font'].addItems([
            "Consolas", "Courier New", "Monaco", "Source Code Pro"
        ])
        font_layout.addWidget(self.theme_settings['code_font'], 1, 1)
        
        scroll_layout.addWidget(font_group)
        
        # 界面布局
        layout_group = QGroupBox("界面布局")
        layout_layout = QGridLayout(layout_group)
        
        layout_layout.addWidget(QLabel("窗口透明度:"), 0, 0)
        self.theme_settings['window_opacity'] = QSlider(Qt.Orientation.Horizontal)
        self.theme_settings['window_opacity'].setRange(70, 100)
        self.theme_settings['window_opacity'].setValue(100)
        layout_layout.addWidget(self.theme_settings['window_opacity'], 0, 1)
        
        self.theme_settings['opacity_label'] = QLabel("100%")
        layout_layout.addWidget(self.theme_settings['opacity_label'], 0, 2)
        
        layout_layout.addWidget(QLabel("动画效果:"), 1, 0)
        self.theme_settings['enable_animations'] = QCheckBox("启用界面动画")
        self.theme_settings['enable_animations'].setChecked(True)
        layout_layout.addWidget(self.theme_settings['enable_animations'], 1, 1)
        
        # 连接透明度滑块
        self.theme_settings['window_opacity'].valueChanged.connect(
            lambda v: self.theme_settings['opacity_label'].setText(f"{v}%")
        )
        
        scroll_layout.addWidget(layout_group)
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        
        return widget

    def _create_performance_settings_tab(self) -> QWidget:
        """创建性能设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 计算性能
        compute_group = QGroupBox("计算性能")
        compute_layout = QGridLayout(compute_group)

        compute_layout.addWidget(QLabel("线程池大小:"), 0, 0)
        self.performance_settings['thread_pool_size'] = QSpinBox()
        self.performance_settings['thread_pool_size'].setRange(1, 16)
        self.performance_settings['thread_pool_size'].setValue(4)
        compute_layout.addWidget(self.performance_settings['thread_pool_size'], 0, 1)

        compute_layout.addWidget(QLabel("批处理大小:"), 0, 2)
        self.performance_settings['batch_size'] = QSpinBox()
        self.performance_settings['batch_size'].setRange(10, 1000)
        self.performance_settings['batch_size'].setValue(100)
        compute_layout.addWidget(self.performance_settings['batch_size'], 0, 3)

        compute_layout.addWidget(QLabel("计算超时(秒):"), 1, 0)
        self.performance_settings['compute_timeout'] = QSpinBox()
        self.performance_settings['compute_timeout'].setRange(10, 300)
        self.performance_settings['compute_timeout'].setValue(60)
        compute_layout.addWidget(self.performance_settings['compute_timeout'], 1, 1)

        scroll_layout.addWidget(compute_group)

        # 内存管理
        memory_group = QGroupBox("内存管理")
        memory_layout = QGridLayout(memory_group)

        memory_layout.addWidget(QLabel("最大内存使用(MB):"), 0, 0)
        self.performance_settings['max_memory'] = QSpinBox()
        self.performance_settings['max_memory'].setRange(512, 8192)
        self.performance_settings['max_memory'].setValue(2048)
        memory_layout.addWidget(self.performance_settings['max_memory'], 0, 1)

        memory_layout.addWidget(QLabel("垃圾回收频率:"), 0, 2)
        self.performance_settings['gc_frequency'] = QComboBox()
        self.performance_settings['gc_frequency'].addItems([
            "低频率", "正常频率", "高频率", "自动"
        ])
        self.performance_settings['gc_frequency'].setCurrentText("正常频率")
        memory_layout.addWidget(self.performance_settings['gc_frequency'], 0, 3)

        memory_layout.addWidget(QLabel("启用内存监控:"), 1, 0)
        self.performance_settings['enable_memory_monitor'] = QCheckBox()
        self.performance_settings['enable_memory_monitor'].setChecked(True)
        memory_layout.addWidget(self.performance_settings['enable_memory_monitor'], 1, 1)

        scroll_layout.addWidget(memory_group)

        # 数据处理
        data_group = QGroupBox("数据处理")
        data_layout = QGridLayout(data_group)

        data_layout.addWidget(QLabel("数据预加载:"), 0, 0)
        self.performance_settings['enable_preload'] = QCheckBox("启用数据预加载")
        self.performance_settings['enable_preload'].setChecked(True)
        data_layout.addWidget(self.performance_settings['enable_preload'], 0, 1)

        data_layout.addWidget(QLabel("异步处理:"), 1, 0)
        self.performance_settings['enable_async'] = QCheckBox("启用异步数据处理")
        self.performance_settings['enable_async'].setChecked(True)
        data_layout.addWidget(self.performance_settings['enable_async'], 1, 1)

        scroll_layout.addWidget(data_group)
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return widget

    def _create_cache_settings_tab(self) -> QWidget:
        """创建缓存管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 缓存配置
        cache_config_group = QGroupBox("缓存配置")
        cache_config_layout = QGridLayout(cache_config_group)

        cache_config_layout.addWidget(QLabel("启用缓存:"), 0, 0)
        self.cache_settings['enable_cache'] = QCheckBox()
        self.cache_settings['enable_cache'].setChecked(True)
        cache_config_layout.addWidget(self.cache_settings['enable_cache'], 0, 1)

        cache_config_layout.addWidget(QLabel("缓存大小(MB):"), 1, 0)
        self.cache_settings['cache_size'] = QSpinBox()
        self.cache_settings['cache_size'].setRange(50, 1000)
        self.cache_settings['cache_size'].setValue(100)
        cache_config_layout.addWidget(self.cache_settings['cache_size'], 1, 1)

        cache_config_layout.addWidget(QLabel("缓存过期时间(小时):"), 1, 2)
        self.cache_settings['cache_ttl'] = QSpinBox()
        self.cache_settings['cache_ttl'].setRange(1, 168)
        self.cache_settings['cache_ttl'].setValue(24)
        cache_config_layout.addWidget(self.cache_settings['cache_ttl'], 1, 3)

        layout.addWidget(cache_config_group)

        # 缓存管理
        cache_manage_group = QGroupBox("缓存管理")
        cache_manage_layout = QVBoxLayout(cache_manage_group)

        # 缓存状态
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("缓存状态:"))
        self.cache_status_label = QLabel("正常")
        self.cache_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        status_layout.addWidget(self.cache_status_label)
        status_layout.addStretch()
        cache_manage_layout.addLayout(status_layout)

        # 缓存操作按钮
        cache_buttons_layout = QHBoxLayout()

        clear_cache_button = QPushButton("🗑️ 清空缓存")
        clear_cache_button.clicked.connect(self._clear_cache)
        cache_buttons_layout.addWidget(clear_cache_button)

        refresh_cache_button = QPushButton("🔄 刷新缓存")
        refresh_cache_button.clicked.connect(self._refresh_cache)
        cache_buttons_layout.addWidget(refresh_cache_button)

        optimize_cache_button = QPushButton("⚡ 优化缓存")
        optimize_cache_button.clicked.connect(self._optimize_cache)
        cache_buttons_layout.addWidget(optimize_cache_button)

        cache_buttons_layout.addStretch()
        cache_manage_layout.addLayout(cache_buttons_layout)

        layout.addWidget(cache_manage_group)
        layout.addStretch()

        return widget

    def _create_logging_settings_tab(self) -> QWidget:
        """创建日志设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 日志配置
        log_config_group = QGroupBox("日志配置")
        log_config_layout = QGridLayout(log_config_group)

        log_config_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.logging_settings['log_level'] = QComboBox()
        self.logging_settings['log_level'].addItems([
            "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
        ])
        self.logging_settings['log_level'].setCurrentText("INFO")
        log_config_layout.addWidget(self.logging_settings['log_level'], 0, 1)

        log_config_layout.addWidget(QLabel("启用文件日志:"), 0, 2)
        self.logging_settings['enable_file_log'] = QCheckBox()
        self.logging_settings['enable_file_log'].setChecked(True)
        log_config_layout.addWidget(self.logging_settings['enable_file_log'], 0, 3)

        log_config_layout.addWidget(QLabel("日志文件大小(MB):"), 1, 0)
        self.logging_settings['log_file_size'] = QSpinBox()
        self.logging_settings['log_file_size'].setRange(1, 100)
        self.logging_settings['log_file_size'].setValue(10)
        log_config_layout.addWidget(self.logging_settings['log_file_size'], 1, 1)

        log_config_layout.addWidget(QLabel("保留日志天数:"), 1, 2)
        self.logging_settings['log_retention_days'] = QSpinBox()
        self.logging_settings['log_retention_days'].setRange(1, 365)
        self.logging_settings['log_retention_days'].setValue(30)
        log_config_layout.addWidget(self.logging_settings['log_retention_days'], 1, 3)

        layout.addWidget(log_config_group)

        # 日志管理
        log_manage_group = QGroupBox("日志管理")
        log_manage_layout = QVBoxLayout(log_manage_group)

        # 日志操作按钮
        log_buttons_layout = QHBoxLayout()

        view_logs_button = QPushButton("📖 查看日志")
        view_logs_button.clicked.connect(self._view_logs)
        log_buttons_layout.addWidget(view_logs_button)

        clear_logs_button = QPushButton("🗑️ 清空日志")
        clear_logs_button.clicked.connect(self._clear_logs)
        log_buttons_layout.addWidget(clear_logs_button)

        export_logs_button = QPushButton("📤 导出日志")
        export_logs_button.clicked.connect(self._export_logs)
        log_buttons_layout.addWidget(export_logs_button)

        log_buttons_layout.addStretch()
        log_manage_layout.addLayout(log_buttons_layout)

        layout.addWidget(log_manage_group)
        layout.addStretch()

        return widget

    def _load_current_settings(self):
        """加载当前设置"""
        try:
            # 这里应该从配置文件或数据库加载设置
            # 现在使用默认值
            self.current_settings = {
                'theme': {
                    'current_theme': 'modern_light',
                    'auto_theme': False,
                    'ui_font': 'Microsoft YaHei UI',
                    'font_size': 9,
                    'code_font': 'Consolas',
                    'window_opacity': 100,
                    'enable_animations': True
                },
                'performance': {
                    'thread_pool_size': 4,
                    'batch_size': 100,
                    'compute_timeout': 60,
                    'max_memory': 2048,
                    'gc_frequency': '正常频率',
                    'enable_memory_monitor': True,
                    'enable_preload': True,
                    'enable_async': True
                },
                'cache': {
                    'enable_cache': True,
                    'cache_size': 100,
                    'cache_ttl': 24
                },
                'logging': {
                    'log_level': 'INFO',
                    'enable_file_log': True,
                    'log_file_size': 10,
                    'log_retention_days': 30
                }
            }

            self._apply_settings_to_ui()
            self.status_label.setText("设置已加载")

        except Exception as e:
            logger.error(f"加载设置失败: {e}")
            self.status_label.setText(f"加载设置失败: {e}")

    def _apply_settings_to_ui(self):
        """将设置应用到UI控件"""
        try:
            # 应用主题设置
            theme_settings = self.current_settings.get('theme', {})
            for key, value in theme_settings.items():
                if key in self.theme_settings:
                    widget = self.theme_settings[key]
                    if isinstance(widget, QComboBox):
                        index = widget.findText(str(value))
                        if index >= 0:
                            widget.setCurrentIndex(index)
                    elif isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value))
                    elif isinstance(widget, QSpinBox):
                        widget.setValue(int(value))
                    elif isinstance(widget, QSlider):
                        widget.setValue(int(value))

            # 应用性能设置
            performance_settings = self.current_settings.get('performance', {})
            for key, value in performance_settings.items():
                if key in self.performance_settings:
                    widget = self.performance_settings[key]
                    if isinstance(widget, QComboBox):
                        index = widget.findText(str(value))
                        if index >= 0:
                            widget.setCurrentIndex(index)
                    elif isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value))
                    elif isinstance(widget, QSpinBox):
                        widget.setValue(int(value))

            # 应用缓存设置
            cache_settings = self.current_settings.get('cache', {})
            for key, value in cache_settings.items():
                if key in self.cache_settings:
                    widget = self.cache_settings[key]
                    if isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value))
                    elif isinstance(widget, QSpinBox):
                        widget.setValue(int(value))

            # 应用日志设置
            logging_settings = self.current_settings.get('logging', {})
            for key, value in logging_settings.items():
                if key in self.logging_settings:
                    widget = self.logging_settings[key]
                    if isinstance(widget, QComboBox):
                        index = widget.findText(str(value))
                        if index >= 0:
                            widget.setCurrentIndex(index)
                    elif isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value))
                    elif isinstance(widget, QSpinBox):
                        widget.setValue(int(value))

        except Exception as e:
            logger.error(f"应用设置到UI失败: {e}")

    def _apply_settings(self):
        """应用设置"""
        try:
            # 收集当前UI设置
            new_settings = self._collect_settings_from_ui()

            # 检查主题是否变更
            old_theme = self.current_settings.get('theme', {}).get('current_theme')
            new_theme = new_settings.get('theme', {}).get('current_theme')

            if old_theme != new_theme:
                self.theme_changed.emit(new_theme)

            # 保存设置
            self.current_settings = new_settings
            self._save_settings()

            # 发送设置变更信号
            self.settings_changed.emit(self.current_settings)

            self.status_label.setText("设置已应用")
            logger.info("系统设置已应用")

        except Exception as e:
            logger.error(f"应用设置失败: {e}")
            self.status_label.setText(f"应用设置失败: {e}")

    def _collect_settings_from_ui(self) -> Dict[str, Any]:
        """从UI收集设置"""
        settings = {
            'theme': {},
            'performance': {},
            'cache': {},
            'logging': {}
        }

        # 收集主题设置
        for key, widget in self.theme_settings.items():
            if key == 'opacity_label':
                continue
            if isinstance(widget, QComboBox):
                settings['theme'][key] = widget.currentText()
            elif isinstance(widget, QCheckBox):
                settings['theme'][key] = widget.isChecked()
            elif isinstance(widget, QSpinBox):
                settings['theme'][key] = widget.value()
            elif isinstance(widget, QSlider):
                settings['theme'][key] = widget.value()

        # 收集性能设置
        for key, widget in self.performance_settings.items():
            if isinstance(widget, QComboBox):
                settings['performance'][key] = widget.currentText()
            elif isinstance(widget, QCheckBox):
                settings['performance'][key] = widget.isChecked()
            elif isinstance(widget, QSpinBox):
                settings['performance'][key] = widget.value()

        # 收集缓存设置
        for key, widget in self.cache_settings.items():
            if isinstance(widget, QCheckBox):
                settings['cache'][key] = widget.isChecked()
            elif isinstance(widget, QSpinBox):
                settings['cache'][key] = widget.value()

        # 收集日志设置
        for key, widget in self.logging_settings.items():
            if isinstance(widget, QComboBox):
                settings['logging'][key] = widget.currentText()
            elif isinstance(widget, QCheckBox):
                settings['logging'][key] = widget.isChecked()
            elif isinstance(widget, QSpinBox):
                settings['logging'][key] = widget.value()

        return settings

    def _save_settings(self):
        """保存设置"""
        # 这里应该保存到配置文件
        logger.info("设置已保存到配置文件")

    def _reset_settings(self):
        """重置设置"""
        self._load_current_settings()
        self.status_label.setText("设置已重置为默认值")
        logger.info("系统设置已重置")

    def _export_settings(self):
        """导出设置"""
        self.status_label.setText("导出设置功能开发中...")
        logger.info("导出设置请求")

    def _import_settings(self):
        """导入设置"""
        self.status_label.setText("导入设置功能开发中...")
        logger.info("导入设置请求")

    def _clear_cache(self):
        """清空缓存"""
        self.cache_status_label.setText("缓存已清空")
        self.cache_status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
        self.status_label.setText("缓存已清空")
        logger.info("缓存已清空")

    def _refresh_cache(self):
        """刷新缓存"""
        self.cache_status_label.setText("正常")
        self.cache_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        self.status_label.setText("缓存已刷新")
        logger.info("缓存已刷新")

    def _optimize_cache(self):
        """优化缓存"""
        self.cache_status_label.setText("已优化")
        self.cache_status_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        self.status_label.setText("缓存已优化")
        logger.info("缓存已优化")

    def _view_logs(self):
        """查看日志"""
        self.status_label.setText("日志查看器功能开发中...")
        logger.info("查看日志请求")

    def _clear_logs(self):
        """清空日志"""
        self.status_label.setText("日志已清空")
        logger.info("日志已清空")

    def _export_logs(self):
        """导出日志"""
        self.status_label.setText("导出日志功能开发中...")
        logger.info("导出日志请求")

    # 公共方法
    def get_current_settings(self) -> Dict[str, Any]:
        """获取当前设置"""
        return self.current_settings.copy()

    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name in ["modern_light", "modern_dark", "classic_light", "classic_dark"]:
            self.theme_settings['current_theme'].setCurrentText(theme_name)
            self._apply_settings()

    def get_performance_settings(self) -> Dict[str, Any]:
        """获取性能设置"""
        return self.current_settings.get('performance', {})
