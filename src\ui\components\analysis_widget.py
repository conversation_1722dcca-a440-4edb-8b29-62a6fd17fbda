"""
分析结果展示控件

显示威科夫分析和相对强弱分析的结果
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QGridLayout, QProgressBar, QGroupBox, QScrollArea, QPushButton
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor

from ...utils.logger import get_logger
from ...services.data_service import DataService, AnalysisResult

logger = get_logger(__name__)


class AnalysisWidget(QWidget):
    """分析结果展示控件"""
    
    # 信号定义
    analysis_updated = pyqtSignal(str, dict)  # 股票代码, 分析结果
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化分析结果控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)

        # 数据服务
        self.data_service = DataService()

        # 当前分析的股票
        self.current_stock: Optional[str] = None
        self.analysis_result: Optional[AnalysisResult] = None

        # 控件引用
        self.stock_info_frame: Optional[QGroupBox] = None
        self.wyckoff_frame: Optional[QGroupBox] = None
        self.rs_frame: Optional[QGroupBox] = None
        self.analyze_button: Optional[QPushButton] = None
        self.status_label: Optional[QLabel] = None

        # 分析定时器
        self.analysis_timer = QTimer()
        self.analysis_timer.setSingleShot(True)
        self.analysis_timer.timeout.connect(self._perform_analysis)

        self._init_ui()
        self._show_welcome_message()

        logger.info("分析结果控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📊 分析结果")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 分析按钮和状态
        button_layout = QHBoxLayout()
        self.analyze_button = QPushButton("🔍 开始分析")
        self.analyze_button.clicked.connect(self._on_analyze_clicked)
        self.analyze_button.setEnabled(False)

        self.status_label = QLabel("请选择股票进行分析")
        self.status_label.setStyleSheet("color: #666; font-size: 9pt;")

        button_layout.addWidget(self.analyze_button)
        button_layout.addWidget(self.status_label)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(10)

        # 股票基本信息
        self.stock_info_frame = self._create_stock_info_frame()
        content_layout.addWidget(self.stock_info_frame)

        # 威科夫分析结果
        self.wyckoff_frame = self._create_wyckoff_frame()
        content_layout.addWidget(self.wyckoff_frame)

        # 相对强弱分析结果
        self.rs_frame = self._create_rs_frame()
        content_layout.addWidget(self.rs_frame)

        # 弹性空间
        content_layout.addStretch()

        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def _create_stock_info_frame(self) -> QGroupBox:
        """创建股票信息框架"""
        frame = QGroupBox("📈 股票信息")
        layout = QGridLayout(frame)

        # 股票代码和名称
        self.stock_code_label = QLabel("--")
        self.stock_name_label = QLabel("--")
        self.stock_sector_label = QLabel("--")
        self.stock_price_label = QLabel("--")
        self.stock_change_label = QLabel("--")
        self.stock_volume_label = QLabel("--")
        self.stock_market_cap_label = QLabel("--")

        # 设置字体
        code_font = QFont("Microsoft YaHei UI", 14, QFont.Weight.Bold)
        self.stock_code_label.setFont(code_font)

        name_font = QFont("Microsoft YaHei UI", 12)
        self.stock_name_label.setFont(name_font)
        self.stock_sector_label.setFont(name_font)

        price_font = QFont("Consolas", 12, QFont.Weight.Bold)
        self.stock_price_label.setFont(price_font)
        self.stock_change_label.setFont(price_font)

        # 布局
        layout.addWidget(QLabel("代码:"), 0, 0)
        layout.addWidget(self.stock_code_label, 0, 1)
        layout.addWidget(QLabel("名称:"), 0, 2)
        layout.addWidget(self.stock_name_label, 0, 3)

        layout.addWidget(QLabel("板块:"), 1, 0)
        layout.addWidget(self.stock_sector_label, 1, 1)
        layout.addWidget(QLabel("最新价:"), 1, 2)
        layout.addWidget(self.stock_price_label, 1, 3)

        layout.addWidget(QLabel("涨跌幅:"), 2, 0)
        layout.addWidget(self.stock_change_label, 2, 1)
        layout.addWidget(QLabel("成交量:"), 2, 2)
        layout.addWidget(self.stock_volume_label, 2, 3)

        layout.addWidget(QLabel("市值(亿):"), 3, 0)
        layout.addWidget(self.stock_market_cap_label, 3, 1)

        return frame
        
        # 布局
        layout.addWidget(QLabel("代码:"), 0, 0)
        layout.addWidget(self.stock_code_label, 0, 1)
        layout.addWidget(QLabel("名称:"), 0, 2)
        layout.addWidget(self.stock_name_label, 0, 3)
        
        layout.addWidget(QLabel("板块:"), 1, 0)
        layout.addWidget(self.stock_sector_label, 1, 1)
        layout.addWidget(QLabel("最新价:"), 1, 2)
        layout.addWidget(self.stock_price_label, 1, 3)

        layout.addWidget(QLabel("涨跌幅:"), 2, 0)
        layout.addWidget(self.stock_change_label, 2, 1)
        layout.addWidget(QLabel("成交量:"), 2, 2)
        layout.addWidget(self.stock_volume_label, 2, 3)

        layout.addWidget(QLabel("市值(亿):"), 3, 0)
        layout.addWidget(self.stock_market_cap_label, 3, 1)

        return frame
    
    def _create_wyckoff_frame(self) -> QGroupBox:
        """创建威科夫分析框架"""
        frame = QGroupBox("🔍 威科夫分析")
        layout = QVBoxLayout(frame)

        # 市场阶段
        phase_layout = QHBoxLayout()
        phase_layout.addWidget(QLabel("市场阶段:"))
        self.wyckoff_phase_label = QLabel("--")
        self.wyckoff_phase_label.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        phase_layout.addWidget(self.wyckoff_phase_label)
        phase_layout.addStretch()
        layout.addLayout(phase_layout)

        # 置信度
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(QLabel("置信度:"))
        self.wyckoff_confidence_bar = QProgressBar()
        self.wyckoff_confidence_bar.setMaximum(100)
        self.wyckoff_confidence_bar.setTextVisible(True)
        self.wyckoff_confidence_bar.setFormat("%p%")
        confidence_layout.addWidget(self.wyckoff_confidence_bar)
        layout.addLayout(confidence_layout)

        # 详细信息网格
        details_layout = QGridLayout()

        # 价格趋势
        self.wyckoff_price_trend_label = QLabel("--")
        self.wyckoff_volume_trend_label = QLabel("--")
        self.wyckoff_support_label = QLabel("--")
        self.wyckoff_resistance_label = QLabel("--")
        self.wyckoff_signals_label = QLabel("--")

        details_layout.addWidget(QLabel("价格趋势:"), 0, 0)
        details_layout.addWidget(self.wyckoff_price_trend_label, 0, 1)
        details_layout.addWidget(QLabel("成交量趋势:"), 0, 2)
        details_layout.addWidget(self.wyckoff_volume_trend_label, 0, 3)

        details_layout.addWidget(QLabel("支撑位:"), 1, 0)
        details_layout.addWidget(self.wyckoff_support_label, 1, 1)
        details_layout.addWidget(QLabel("阻力位:"), 1, 2)
        details_layout.addWidget(self.wyckoff_resistance_label, 1, 3)

        details_layout.addWidget(QLabel("威科夫信号:"), 2, 0)
        details_layout.addWidget(self.wyckoff_signals_label, 2, 1, 1, 3)

        layout.addLayout(details_layout)
        return frame

    
    def _create_rs_frame(self) -> QGroupBox:
        """创建相对强弱分析框架"""
        frame = QGroupBox("📊 相对强弱分析")
        layout = QVBoxLayout(frame)

        # RS值显示
        rs_layout = QHBoxLayout()
        rs_layout.addWidget(QLabel("RS值:"))
        self.rs_value_label = QLabel("--")
        self.rs_value_label.setFont(QFont("Consolas", 12, QFont.Weight.Bold))
        rs_layout.addWidget(self.rs_value_label)

        # RS排名
        rs_layout.addWidget(QLabel("排名:"))
        self.rs_rank_label = QLabel("--")
        rs_layout.addWidget(self.rs_rank_label)

        # RS百分位
        rs_layout.addWidget(QLabel("百分位:"))
        self.rs_percentile_label = QLabel("--")
        rs_layout.addWidget(self.rs_percentile_label)
        rs_layout.addStretch()
        layout.addLayout(rs_layout)

        # 详细信息网格
        details_layout = QGridLayout()

        self.rs_momentum_label = QLabel("--")
        self.rs_trend_strength_label = QLabel("--")
        self.rs_benchmark_label = QLabel("--")
        self.rs_timeframe_label = QLabel("--")

        details_layout.addWidget(QLabel("动量分数:"), 0, 0)
        details_layout.addWidget(self.rs_momentum_label, 0, 1)
        details_layout.addWidget(QLabel("趋势强度:"), 0, 2)
        details_layout.addWidget(self.rs_trend_strength_label, 0, 3)

        details_layout.addWidget(QLabel("基准:"), 1, 0)
        details_layout.addWidget(self.rs_benchmark_label, 1, 1)
        details_layout.addWidget(QLabel("时间周期:"), 1, 2)
        details_layout.addWidget(self.rs_timeframe_label, 1, 3)

        layout.addLayout(details_layout)
        return frame
    
    def _show_welcome_message(self):
        """显示欢迎信息"""
        self.stock_code_label.setText("请选择股票")
        self.stock_name_label.setText("从左侧列表中选择要分析的股票")
        self.stock_sector_label.setText("--")
        self.stock_price_label.setText("--")
        self.stock_change_label.setText("--")
        self.stock_volume_label.setText("--")
        self.stock_market_cap_label.setText("--")

        # 清空分析结果
        self._clear_analysis_results()

    def _on_analyze_clicked(self):
        """分析按钮点击事件"""
        if not self.current_stock:
            return

        self.analyze_button.setEnabled(False)
        self.analyze_button.setText("分析中...")
        self.status_label.setText("正在进行分析，请稍候...")

        # 延迟执行分析，避免界面卡顿
        self.analysis_timer.start(100)

    def _perform_analysis(self):
        """执行分析"""
        try:
            if not self.current_stock:
                return

            # 执行分析
            self.analysis_result = self.data_service.analyze_stock(self.current_stock)

            if self.analysis_result:
                self._update_analysis_display()
                self.status_label.setText("分析完成")

                # 发送信号
                self.analysis_updated.emit(self.current_stock, {
                    'wyckoff': self.analysis_result.wyckoff_analysis,
                    'rs': self.analysis_result.rs_analysis
                })
            else:
                self.status_label.setText("分析失败")

        except Exception as e:
            logger.error(f"分析失败: {e}")
            self.status_label.setText(f"分析失败: {e}")
        finally:
            self.analyze_button.setEnabled(True)
            self.analyze_button.setText("🔍 开始分析")

    def _update_analysis_display(self):
        """更新分析结果显示"""
        if not self.analysis_result:
            return

        # 更新股票信息
        stock_info = self.analysis_result.stock_info
        self.stock_code_label.setText(stock_info.symbol)
        self.stock_name_label.setText(stock_info.name)
        self.stock_sector_label.setText(stock_info.sector)
        self.stock_price_label.setText(f"{stock_info.price:.2f}")

        # 设置涨跌幅颜色
        change_text = f"{stock_info.change_percent:+.2f}%"
        self.stock_change_label.setText(change_text)
        if stock_info.change_percent > 0:
            self.stock_change_label.setStyleSheet("color: #F44336; font-weight: bold;")
        elif stock_info.change_percent < 0:
            self.stock_change_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        else:
            self.stock_change_label.setStyleSheet("color: #666;")

        # 格式化成交量和市值
        volume_text = self._format_volume(stock_info.volume)
        self.stock_volume_label.setText(volume_text)
        self.stock_market_cap_label.setText(f"{stock_info.market_cap:.0f}")

        # 更新威科夫分析
        self._update_wyckoff_display()

        # 更新相对强弱分析
        self._update_rs_display()

    def _format_volume(self, volume: int) -> str:
        """格式化成交量显示"""
        if volume >= 100000000:  # 1亿
            return f"{volume/100000000:.1f}亿"
        elif volume >= 10000:  # 1万
            return f"{volume/10000:.1f}万"
        else:
            return str(volume)
    
    def _update_wyckoff_display(self):
        """更新威科夫分析显示"""
        if not self.analysis_result or not self.analysis_result.wyckoff_analysis:
            self._clear_wyckoff_display()
            return

        wyckoff = self.analysis_result.wyckoff_analysis

        # 市场阶段
        phase_text = wyckoff.phase.value if wyckoff.phase else "未知"
        self.wyckoff_phase_label.setText(phase_text)

        # 设置阶段颜色
        phase_colors = {
            "累积": "#4CAF50",
            "上涨": "#2196F3",
            "派发": "#FF9800",
            "下跌": "#F44336"
        }
        color = phase_colors.get(phase_text, "#666")
        self.wyckoff_phase_label.setStyleSheet(f"color: {color}; font-weight: bold;")

        # 置信度
        confidence_percent = int(wyckoff.confidence * 100)
        self.wyckoff_confidence_bar.setValue(confidence_percent)

        # 详细信息
        self.wyckoff_price_trend_label.setText(wyckoff.price_trend or "--")
        self.wyckoff_volume_trend_label.setText(wyckoff.volume_trend or "--")
        self.wyckoff_support_label.setText(f"{wyckoff.support_level:.2f}" if wyckoff.support_level else "--")
        self.wyckoff_resistance_label.setText(f"{wyckoff.resistance_level:.2f}" if wyckoff.resistance_level else "--")

        # 威科夫信号
        signals_count = len(self.analysis_result.wyckoff_signals)
        self.wyckoff_signals_label.setText(f"检测到 {signals_count} 个信号")

    def _update_rs_display(self):
        """更新相对强弱分析显示"""
        if not self.analysis_result or not self.analysis_result.rs_ranking:
            self._clear_rs_display()
            return

        rs_result = self.analysis_result.rs_ranking

        # RS值
        rs_value_text = f"{rs_result.rs_value:.3f}"
        self.rs_value_label.setText(rs_value_text)

        # 设置RS值颜色
        if rs_result.rs_value > 1.2:
            self.rs_value_label.setStyleSheet("color: #F44336; font-weight: bold;")  # 强势红色
        elif rs_result.rs_value < 0.8:
            self.rs_value_label.setStyleSheet("color: #4CAF50; font-weight: bold;")  # 弱势绿色
        else:
            self.rs_value_label.setStyleSheet("color: #666; font-weight: bold;")

        # 排名和百分位
        self.rs_rank_label.setText(str(rs_result.rs_rank) if rs_result.rs_rank else "--")
        self.rs_percentile_label.setText(f"{rs_result.rs_percentile:.1f}%" if rs_result.rs_percentile else "--")

        # 详细信息
        self.rs_momentum_label.setText(f"{rs_result.momentum_score:.2f}" if rs_result.momentum_score else "--")
        self.rs_trend_strength_label.setText(f"{rs_result.trend_strength:.2f}" if rs_result.trend_strength else "--")
        self.rs_benchmark_label.setText(rs_result.benchmark or "--")
        self.rs_timeframe_label.setText(rs_result.timeframe.value if rs_result.timeframe else "--")

    def _clear_analysis_results(self):
        """清空分析结果"""
        self._clear_wyckoff_display()
        self._clear_rs_display()

    def _clear_wyckoff_display(self):
        """清空威科夫分析显示"""
        self.wyckoff_phase_label.setText("--")
        self.wyckoff_phase_label.setStyleSheet("color: #666;")
        self.wyckoff_confidence_bar.setValue(0)
        self.wyckoff_price_trend_label.setText("--")
        self.wyckoff_volume_trend_label.setText("--")
        self.wyckoff_support_label.setText("--")
        self.wyckoff_resistance_label.setText("--")
        self.wyckoff_signals_label.setText("--")

    def _clear_rs_display(self):
        """清空相对强弱分析显示"""
        self.rs_value_label.setText("--")
        self.rs_value_label.setStyleSheet("color: #666;")
        self.rs_rank_label.setText("--")
        self.rs_percentile_label.setText("--")
        self.rs_momentum_label.setText("--")
        self.rs_trend_strength_label.setText("--")
        self.rs_benchmark_label.setText("--")
        self.rs_timeframe_label.setText("--")

    # 公共方法
    def set_current_stock(self, symbol: str):
        """设置当前分析的股票"""
        self.current_stock = symbol
        self.analyze_button.setEnabled(True)
        self.status_label.setText(f"已选择股票 {symbol}，点击分析按钮开始分析")

        # 清空之前的分析结果
        self._clear_analysis_results()

        logger.debug(f"设置当前股票: {symbol}")

    def get_current_analysis(self) -> Optional[AnalysisResult]:
        """获取当前分析结果"""
        return self.analysis_result

    def refresh_analysis(self):
        """刷新当前股票的分析"""
        if self.current_stock:
            self._on_analyze_clicked()
