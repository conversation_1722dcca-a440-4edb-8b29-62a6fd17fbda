#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源基础接口测试
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

from src.data_sources.base import (
    IDataSource, DataSourceConfig, MarketData, SectorInfo, StockInfo,
    DataSourceException, ConnectionException, DataException, AuthenticationException
)


class TestDataSourceConfig:
    """测试数据源配置类"""
    
    def test_init_with_defaults(self):
        """测试使用默认值初始化"""
        config = DataSourceConfig(name="test_source")
        
        assert config.name == "test_source"
        assert config.enabled is True
        assert config.timeout == 30
        assert config.retry_times == 3
        assert config.auto_reconnect is True
        assert config.config == {}
    
    def test_init_with_custom_values(self):
        """测试使用自定义值初始化"""
        custom_config = {"api_key": "test_key", "base_url": "http://test.com"}
        
        config = DataSourceConfig(
            name="custom_source",
            enabled=False,
            timeout=60,
            retry_times=5,
            auto_reconnect=False,
            config=custom_config
        )
        
        assert config.name == "custom_source"
        assert config.enabled is False
        assert config.timeout == 60
        assert config.retry_times == 5
        assert config.auto_reconnect is False
        assert config.config == custom_config
    
    def test_post_init_config_none(self):
        """测试config为None时的初始化"""
        config = DataSourceConfig(name="test", config=None)
        assert config.config == {}


class TestMarketData:
    """测试市场数据类"""
    
    def test_init_with_valid_data(self):
        """测试使用有效数据初始化"""
        # 创建有效的DataFrame
        df = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02'],
            'open': [10.0, 10.5],
            'high': [10.5, 11.0],
            'low': [9.5, 10.0],
            'close': [10.2, 10.8],
            'volume': [1000000, 1200000]
        })
        
        data = MarketData(
            symbol="000001.SZ",
            data=df,
            source="test_source",
            update_time=datetime.now(),
            data_type="daily"
        )
        
        assert data.symbol == "000001.SZ"
        assert isinstance(data.data, pd.DataFrame)
        assert data.source == "test_source"
        assert isinstance(data.update_time, datetime)
        assert data.data_type == "daily"
    
    def test_init_with_missing_columns(self):
        """测试缺少必要列时的初始化"""
        # 创建缺少必要列的DataFrame
        df = pd.DataFrame({
            'trade_date': ['2024-01-01'],
            'open': [10.0],
            'high': [10.5]
            # 缺少 low, close, volume
        })
        
        with pytest.raises(ValueError) as exc_info:
            MarketData(
                symbol="000001.SZ",
                data=df,
                source="test_source",
                update_time=datetime.now()
            )
        
        assert "缺少必要的数据列" in str(exc_info.value)
    
    def test_init_with_default_data_type(self):
        """测试默认数据类型"""
        df = pd.DataFrame({
            'trade_date': ['2024-01-01'],
            'open': [10.0],
            'high': [10.5],
            'low': [9.5],
            'close': [10.2],
            'volume': [1000000]
        })
        
        data = MarketData(
            symbol="000001.SZ",
            data=df,
            source="test_source",
            update_time=datetime.now()
        )
        
        assert data.data_type == "daily"


class TestSectorInfo:
    """测试板块信息类"""
    
    def test_init_with_required_fields(self):
        """测试使用必需字段初始化"""
        sector = SectorInfo(
            sector_code="BK001",
            sector_name="银行",
            sector_type="industry"
        )
        
        assert sector.sector_code == "BK001"
        assert sector.sector_name == "银行"
        assert sector.sector_type == "industry"
        assert sector.constituents == []
        assert isinstance(sector.update_time, datetime)
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        constituents = ["000001.SZ", "600036.SH"]
        update_time = datetime.now()
        
        sector = SectorInfo(
            sector_code="BK001",
            sector_name="银行",
            sector_type="industry",
            constituents=constituents,
            update_time=update_time
        )
        
        assert sector.sector_code == "BK001"
        assert sector.sector_name == "银行"
        assert sector.sector_type == "industry"
        assert sector.constituents == constituents
        assert sector.update_time == update_time
    
    def test_post_init_defaults(self):
        """测试初始化时的默认值设置"""
        sector = SectorInfo(
            sector_code="BK001",
            sector_name="银行",
            sector_type="industry",
            constituents=None,
            update_time=None
        )
        
        assert sector.constituents == []
        assert isinstance(sector.update_time, datetime)


class TestStockInfo:
    """测试股票信息类"""
    
    def test_init_with_required_fields(self):
        """测试使用必需字段初始化"""
        stock = StockInfo(
            symbol="000001.SZ",
            name="平安银行",
            market="SZ"
        )
        
        assert stock.symbol == "000001.SZ"
        assert stock.name == "平安银行"
        assert stock.market == "SZ"
        assert stock.list_date is None
        assert stock.industry is None
        assert stock.sector is None
        assert stock.is_active is True
        assert isinstance(stock.update_time, datetime)
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        update_time = datetime.now()
        
        stock = StockInfo(
            symbol="000001.SZ",
            name="平安银行",
            market="SZ",
            list_date="1991-04-03",
            industry="银行",
            sector="金融",
            is_active=True,
            update_time=update_time
        )
        
        assert stock.symbol == "000001.SZ"
        assert stock.name == "平安银行"
        assert stock.market == "SZ"
        assert stock.list_date == "1991-04-03"
        assert stock.industry == "银行"
        assert stock.sector == "金融"
        assert stock.is_active is True
        assert stock.update_time == update_time


class TestDataSourceExceptions:
    """测试数据源异常类"""
    
    def test_data_source_exception(self):
        """测试数据源异常"""
        with pytest.raises(DataSourceException) as exc_info:
            raise DataSourceException("测试异常")
        
        assert str(exc_info.value) == "测试异常"
        assert isinstance(exc_info.value, Exception)
    
    def test_connection_exception(self):
        """测试连接异常"""
        with pytest.raises(ConnectionException) as exc_info:
            raise ConnectionException("连接失败")
        
        assert str(exc_info.value) == "连接失败"
        assert isinstance(exc_info.value, DataSourceException)
    
    def test_data_exception(self):
        """测试数据异常"""
        with pytest.raises(DataException) as exc_info:
            raise DataException("数据错误")
        
        assert str(exc_info.value) == "数据错误"
        assert isinstance(exc_info.value, DataSourceException)
    
    def test_authentication_exception(self):
        """测试认证异常"""
        with pytest.raises(AuthenticationException) as exc_info:
            raise AuthenticationException("认证失败")
        
        assert str(exc_info.value) == "认证失败"
        assert isinstance(exc_info.value, DataSourceException)


class MockDataSource(IDataSource):
    """模拟数据源实现，用于测试抽象接口"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.test_connected = False
    
    def connect(self) -> bool:
        """模拟连接"""
        self.connected = True
        self.test_connected = True
        return True
    
    def disconnect(self) -> None:
        """模拟断开连接"""
        self.connected = False
        self.test_connected = False
    
    def test_connection(self) -> bool:
        """模拟测试连接"""
        return self.test_connected
    
    def get_market_data(self, symbol: str, period: str = "1d", 
                       start_date: str = None, end_date: str = None,
                       dividend_type: str = "none") -> MarketData:
        """模拟获取市场数据"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        # 创建模拟数据
        df = pd.DataFrame({
            'trade_date': ['2024-01-01', '2024-01-02'],
            'open': [10.0, 10.5],
            'high': [10.5, 11.0],
            'low': [9.5, 10.0],
            'close': [10.2, 10.8],
            'volume': [1000000, 1200000]
        })
        
        return MarketData(
            symbol=symbol,
            data=df,
            source=self.name,
            update_time=datetime.now(),
            data_type=period
        )
    
    def get_sector_list(self, sector_type: str = "industry") -> List[SectorInfo]:
        """模拟获取板块列表"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        return [
            SectorInfo(
                sector_code="BK001",
                sector_name="银行",
                sector_type=sector_type,
                constituents=["000001.SZ", "600036.SH"]
            )
        ]
    
    def get_sector_constituents(self, sector_code: str) -> List[str]:
        """模拟获取板块成分股"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        if sector_code == "BK001":
            return ["000001.SZ", "600036.SH"]
        return []
    
    def get_stock_list(self, market: str = None) -> List[Dict[str, str]]:
        """模拟获取股票列表"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        stocks = [
            {"code": "000001.SZ", "name": "平安银行", "market": "SZ"},
            {"code": "600036.SH", "name": "招商银行", "market": "SH"}
        ]
        
        if market:
            stocks = [s for s in stocks if s["market"] == market]
        
        return stocks
    
    def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """模拟获取交易日历"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        return ["2024-01-02", "2024-01-03", "2024-01-04"]
    
    def download_history_data(self, symbols: List[str], period: str = "1d",
                             start_date: str = None) -> bool:
        """模拟下载历史数据"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        return True


class TestIDataSource:
    """测试数据源抽象接口"""
    
    @pytest.fixture
    def mock_data_source(self):
        """创建模拟数据源"""
        config = DataSourceConfig(name="test_source")
        return MockDataSource(config)
    
    def test_init(self, mock_data_source):
        """测试初始化"""
        assert mock_data_source.name == "test_source"
        assert mock_data_source.is_connected is False
        assert mock_data_source.last_error is None
    
    def test_connect(self, mock_data_source):
        """测试连接"""
        result = mock_data_source.connect()
        assert result is True
        assert mock_data_source.is_connected is True
    
    def test_disconnect(self, mock_data_source):
        """测试断开连接"""
        mock_data_source.connect()
        mock_data_source.disconnect()
        assert mock_data_source.is_connected is False
    
    def test_test_connection(self, mock_data_source):
        """测试连接状态检查"""
        # 未连接时
        assert mock_data_source.test_connection() is False
        
        # 连接后
        mock_data_source.connect()
        assert mock_data_source.test_connection() is True
        
        # 断开后
        mock_data_source.disconnect()
        assert mock_data_source.test_connection() is False
    
    def test_get_market_data_success(self, mock_data_source):
        """测试成功获取市场数据"""
        mock_data_source.connect()
        
        result = mock_data_source.get_market_data("000001.SZ")
        
        assert isinstance(result, MarketData)
        assert result.symbol == "000001.SZ"
        assert result.source == "test_source"
        assert isinstance(result.data, pd.DataFrame)
        assert len(result.data) == 2
    
    def test_get_market_data_not_connected(self, mock_data_source):
        """测试未连接时获取市场数据"""
        with pytest.raises(ConnectionException):
            mock_data_source.get_market_data("000001.SZ")
    
    def test_get_sector_list_success(self, mock_data_source):
        """测试成功获取板块列表"""
        mock_data_source.connect()
        
        result = mock_data_source.get_sector_list("industry")
        
        assert len(result) == 1
        assert isinstance(result[0], SectorInfo)
        assert result[0].sector_code == "BK001"
        assert result[0].sector_name == "银行"
        assert result[0].sector_type == "industry"
    
    def test_get_sector_list_not_connected(self, mock_data_source):
        """测试未连接时获取板块列表"""
        with pytest.raises(ConnectionException):
            mock_data_source.get_sector_list()
    
    def test_get_sector_constituents_success(self, mock_data_source):
        """测试成功获取板块成分股"""
        mock_data_source.connect()
        
        result = mock_data_source.get_sector_constituents("BK001")
        
        assert result == ["000001.SZ", "600036.SH"]
    
    def test_get_sector_constituents_not_found(self, mock_data_source):
        """测试获取不存在的板块成分股"""
        mock_data_source.connect()
        
        result = mock_data_source.get_sector_constituents("BK999")
        
        assert result == []
    
    def test_get_stock_list_all(self, mock_data_source):
        """测试获取所有股票列表"""
        mock_data_source.connect()
        
        result = mock_data_source.get_stock_list()
        
        assert len(result) == 2
        assert all("code" in stock and "name" in stock and "market" in stock 
                  for stock in result)
    
    def test_get_stock_list_by_market(self, mock_data_source):
        """测试按市场获取股票列表"""
        mock_data_source.connect()
        
        result = mock_data_source.get_stock_list("SZ")
        
        assert len(result) == 1
        assert result[0]["code"] == "000001.SZ"
        assert result[0]["market"] == "SZ"
    
    def test_get_trading_calendar(self, mock_data_source):
        """测试获取交易日历"""
        mock_data_source.connect()
        
        result = mock_data_source.get_trading_calendar("2024-01-01", "2024-01-05")
        
        assert isinstance(result, list)
        assert len(result) == 3
        assert all(isinstance(date, str) for date in result)
    
    def test_download_history_data(self, mock_data_source):
        """测试下载历史数据"""
        mock_data_source.connect()
        
        symbols = ["000001.SZ", "600036.SH"]
        result = mock_data_source.download_history_data(symbols)
        
        assert result is True
    
    def test_format_symbol(self, mock_data_source):
        """测试股票代码格式化"""
        # 上海股票
        assert mock_data_source._format_symbol("600000") == "600000.SH"
        assert mock_data_source._format_symbol("688000") == "688000.SH"
        
        # 深圳股票
        assert mock_data_source._format_symbol("000001") == "000001.SZ"
        assert mock_data_source._format_symbol("300001") == "300001.SZ"
        
        # 北京股票
        assert mock_data_source._format_symbol("830001") == "830001.BJ"
        assert mock_data_source._format_symbol("430001") == "430001.BJ"
        
        # 已经格式化的代码
        assert mock_data_source._format_symbol("000001.SZ") == "000001.SZ"
        assert mock_data_source._format_symbol("600000.sh") == "600000.SH"
    
    def test_validate_date(self, mock_data_source):
        """测试日期验证"""
        # 有效日期
        assert mock_data_source._validate_date("2024-01-01") is True
        assert mock_data_source._validate_date("2024-12-31") is True
        
        # 无效日期
        assert mock_data_source._validate_date("2024-13-01") is False
        assert mock_data_source._validate_date("2024/01/01") is False
        assert mock_data_source._validate_date("invalid") is False
    
    def test_handle_error(self, mock_data_source):
        """测试错误处理"""
        # 连接错误
        with pytest.raises(ConnectionException):
            mock_data_source._handle_error(Exception("连接失败"), "连接")
        
        # 认证错误
        with pytest.raises(AuthenticationException):
            mock_data_source._handle_error(Exception("认证失败"), "认证")
        
        # 一般数据错误
        with pytest.raises(DataException):
            mock_data_source._handle_error(Exception("数据错误"), "获取数据")
        
        # 检查最后错误记录
        try:
            mock_data_source._handle_error(Exception("测试错误"), "测试")
        except:
            pass
        
        assert mock_data_source.last_error is not None
        assert "测试错误" in str(mock_data_source.last_error)