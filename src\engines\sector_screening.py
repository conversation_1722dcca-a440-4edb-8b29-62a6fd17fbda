#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块相对强弱筛选引擎
实现需求文档第3步：板块相对强弱筛选
"""

import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass, field
import pandas as pd

from ..services.sector_manager import SectorManager
from ..services.return_calculator import ReturnCalculator
from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


@dataclass
class SectorStrength:
    """板块强弱数据类"""
    sector_code: str
    sector_name: str
    sector_type: str
    sector_return: float
    market_return: float
    relative_strength: float  # 相对强弱度 = 板块涨幅 - 大盘涨幅
    rank: int = 0
    percentile: float = 0.0
    calculation_time: datetime = field(default_factory=datetime.now)


@dataclass
class SectorScreeningParams:
    """板块筛选参数"""
    start_date: str
    end_date: str
    market_index: str = "000001.SH"  # 默认使用上证指数
    sector_types: List[str] = field(default_factory=lambda: ["industry", "concept"])
    min_relative_strength: float = 0.0  # 最小相对强弱度
    max_sectors: int = 10  # 最大选择板块数
    sort_by: str = "relative_strength"  # 排序字段


@dataclass
class SectorScreeningResult:
    """板块筛选结果"""
    params: SectorScreeningParams
    market_return: float
    strong_sectors: List[SectorStrength]
    total_sectors: int
    selected_count: int
    calculation_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'params': {
                'start_date': self.params.start_date,
                'end_date': self.params.end_date,
                'market_index': self.params.market_index,
                'sector_types': self.params.sector_types,
                'min_relative_strength': self.params.min_relative_strength,
                'max_sectors': self.params.max_sectors
            },
            'market_return': self.market_return,
            'strong_sectors': [
                {
                    'sector_code': s.sector_code,
                    'sector_name': s.sector_name,
                    'sector_type': s.sector_type,
                    'sector_return': s.sector_return,
                    'relative_strength': s.relative_strength,
                    'rank': s.rank,
                    'percentile': s.percentile
                }
                for s in self.strong_sectors
            ],
            'total_sectors': self.total_sectors,
            'selected_count': self.selected_count,
            'calculation_time': self.calculation_time.isoformat()
        }


class SectorScreeningEngine:
    """板块筛选引擎"""
    
    def __init__(self, 
                 sector_manager: SectorManager,
                 return_calculator: ReturnCalculator):
        self.sector_manager = sector_manager
        self.return_calculator = return_calculator
    
    async def screen_strong_sectors(self, 
                                  params: SectorScreeningParams,
                                  progress_callback: Optional[callable] = None) -> SectorScreeningResult:
        """
        筛选强势板块
        
        Args:
            params: 筛选参数
            progress_callback: 进度回调函数
            
        Returns:
            SectorScreeningResult: 筛选结果
        """
        try:
            logger.info(f"开始板块筛选: {params.start_date} -> {params.end_date}")
            
            # 1. 计算市场指数收益率
            if progress_callback:
                progress_callback("计算市场指数收益率...", 10)
            
            market_return = await self._calculate_market_return(
                params.market_index, params.start_date, params.end_date
            )
            
            if market_return is None:
                raise CalculationError(f"无法获取市场指数{params.market_index}的收益率")
            
            logger.info(f"市场指数收益率: {market_return:.4f}")
            
            # 2. 获取板块列表
            if progress_callback:
                progress_callback("获取板块列表...", 20)
            
            all_sectors = []
            for sector_type in params.sector_types:
                sectors = self.sector_manager.get_sector_list(sector_type)
                all_sectors.extend(sectors)
            
            logger.info(f"获取到{len(all_sectors)}个板块")
            
            # 3. 批量计算板块收益率
            if progress_callback:
                progress_callback("计算板块收益率...", 40)
            
            sector_codes = [s['sector_code'] for s in all_sectors]
            sector_returns = await self.return_calculator.calculate_batch_returns(
                sector_codes, 'sector', params.start_date, params.end_date
            )
            
            # 4. 计算相对强弱度
            if progress_callback:
                progress_callback("计算相对强弱度...", 60)
            
            sector_strengths = []
            for sector in all_sectors:
                sector_code = sector['sector_code']
                sector_return = sector_returns.get(sector_code)
                
                if sector_return is not None:
                    relative_strength = sector_return - market_return
                    
                    strength = SectorStrength(
                        sector_code=sector_code,
                        sector_name=sector['sector_name'],
                        sector_type=sector['sector_type'],
                        sector_return=sector_return,
                        market_return=market_return,
                        relative_strength=relative_strength
                    )
                    sector_strengths.append(strength)
            
            # 5. 筛选和排序
            if progress_callback:
                progress_callback("筛选强势板块...", 80)
            
            strong_sectors = self._filter_and_rank_sectors(
                sector_strengths, params
            )
            
            # 6. 生成结果
            if progress_callback:
                progress_callback("生成筛选结果...", 100)
            
            result = SectorScreeningResult(
                params=params,
                market_return=market_return,
                strong_sectors=strong_sectors,
                total_sectors=len(sector_strengths),
                selected_count=len(strong_sectors)
            )
            
            logger.info(f"板块筛选完成，从{len(sector_strengths)}个板块中选出{len(strong_sectors)}个强势板块")
            return result
            
        except Exception as e:
            logger.error(f"板块筛选失败: {e}")
            raise CalculationError(f"板块筛选失败: {e}")
    
    async def _calculate_market_return(self, 
                                     market_index: str,
                                     start_date: str,
                                     end_date: str) -> Optional[float]:
        """计算市场指数收益率"""
        try:
            market_returns = await self.return_calculator.calculate_batch_returns(
                [market_index], 'market', start_date, end_date
            )
            return market_returns.get(market_index)
            
        except Exception as e:
            logger.error(f"计算市场指数收益率失败: {e}")
            return None
    
    def _filter_and_rank_sectors(self, 
                                sector_strengths: List[SectorStrength],
                                params: SectorScreeningParams) -> List[SectorStrength]:
        """筛选和排名板块"""
        try:
            # 1. 筛选条件：板块涨幅 > 大盘涨幅
            filtered_sectors = [
                s for s in sector_strengths 
                if s.relative_strength >= params.min_relative_strength
            ]
            
            logger.info(f"相对强弱筛选：{len(filtered_sectors)}/{len(sector_strengths)}个板块通过")
            
            # 2. 按相对强弱度排序
            if params.sort_by == "relative_strength":
                filtered_sectors.sort(key=lambda x: x.relative_strength, reverse=True)
            elif params.sort_by == "sector_return":
                filtered_sectors.sort(key=lambda x: x.sector_return, reverse=True)
            else:
                filtered_sectors.sort(key=lambda x: x.relative_strength, reverse=True)
            
            # 3. 限制数量
            selected_sectors = filtered_sectors[:params.max_sectors]
            
            # 4. 填充排名和百分位
            for i, sector in enumerate(selected_sectors):
                sector.rank = i + 1
                sector.percentile = (len(filtered_sectors) - i) / len(filtered_sectors) * 100
            
            return selected_sectors
            
        except Exception as e:
            logger.error(f"筛选和排名板块失败: {e}")
            return []
    
    async def screen_multiple_timeframes(self, 
                                       time_ranges: List[Tuple[str, str]],
                                       base_params: SectorScreeningParams,
                                       progress_callback: Optional[callable] = None) -> List[SectorScreeningResult]:
        """
        多时间段板块筛选
        
        Args:
            time_ranges: 时间区间列表
            base_params: 基础参数
            progress_callback: 进度回调
            
        Returns:
            List[SectorScreeningResult]: 多时间段筛选结果
        """
        try:
            logger.info(f"开始多时间段板块筛选，时间区间数量：{len(time_ranges)}")
            
            results = []
            
            for i, (start_date, end_date) in enumerate(time_ranges):
                try:
                    if progress_callback:
                        progress = (i / len(time_ranges)) * 100
                        progress_callback(f"筛选时间区间: {start_date} - {end_date}", progress)
                    
                    # 创建当前时间段的参数
                    current_params = SectorScreeningParams(
                        start_date=start_date,
                        end_date=end_date,
                        market_index=base_params.market_index,
                        sector_types=base_params.sector_types,
                        min_relative_strength=base_params.min_relative_strength,
                        max_sectors=base_params.max_sectors,
                        sort_by=base_params.sort_by
                    )
                    
                    # 执行筛选
                    result = await self.screen_strong_sectors(current_params)
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"时间区间{start_date}-{end_date}筛选失败: {e}")
                    continue
            
            logger.info(f"多时间段筛选完成，成功{len(results)}/{len(time_ranges)}个时间区间")
            return results
            
        except Exception as e:
            logger.error(f"多时间段板块筛选失败: {e}")
            raise CalculationError(f"多时间段板块筛选失败: {e}")
    
    def analyze_sector_intersection(self, 
                                  results: List[SectorScreeningResult],
                                  min_appearances: int = 2) -> List[Dict[str, Any]]:
        """
        分析板块交集
        
        Args:
            results: 多时间段筛选结果
            min_appearances: 最小出现次数
            
        Returns:
            List[Dict]: 交集分析结果
        """
        try:
            # 统计板块出现次数
            sector_counts = {}
            sector_info = {}
            
            for result in results:
                for sector in result.strong_sectors:
                    code = sector.sector_code
                    
                    if code not in sector_counts:
                        sector_counts[code] = 0
                        sector_info[code] = {
                            'sector_name': sector.sector_name,
                            'sector_type': sector.sector_type,
                            'appearances': [],
                            'avg_relative_strength': 0.0,
                            'avg_rank': 0.0
                        }
                    
                    sector_counts[code] += 1
                    sector_info[code]['appearances'].append({
                        'time_range': f"{result.params.start_date}_{result.params.end_date}",
                        'relative_strength': sector.relative_strength,
                        'rank': sector.rank
                    })
            
            # 筛选频繁出现的板块
            frequent_sectors = []
            for sector_code, count in sector_counts.items():
                if count >= min_appearances:
                    info = sector_info[sector_code]
                    
                    # 计算平均值
                    appearances = info['appearances']
                    avg_strength = sum(a['relative_strength'] for a in appearances) / len(appearances)
                    avg_rank = sum(a['rank'] for a in appearances) / len(appearances)
                    
                    frequent_sectors.append({
                        'sector_code': sector_code,
                        'sector_name': info['sector_name'],
                        'sector_type': info['sector_type'],
                        'appearance_count': count,
                        'appearance_rate': count / len(results),
                        'avg_relative_strength': avg_strength,
                        'avg_rank': avg_rank,
                        'appearances': appearances
                    })
            
            # 按出现次数和平均相对强弱度排序
            frequent_sectors.sort(
                key=lambda x: (x['appearance_count'], x['avg_relative_strength']), 
                reverse=True
            )
            
            logger.info(f"交集分析完成，发现{len(frequent_sectors)}个频繁强势板块")
            return frequent_sectors
            
        except Exception as e:
            logger.error(f"板块交集分析失败: {e}")
            return []
