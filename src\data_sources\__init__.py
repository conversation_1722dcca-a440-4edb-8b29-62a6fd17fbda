#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源模块
提供统一的数据源接口和多种数据源实现
"""

# 核心接口和数据结构
from .base import (
    IDataSource,
    DataSourceConfig,
    MarketData,
    SectorInfo,
    StockInfo,
    DataSourceException,
    ConnectionException,
    DataException,
    AuthenticationException
)

# 数据源适配器
from .xtdata_adapter import XtDataAdapter

# 数据源管理
from .manager import DataSourceManager, DataSourceStatus

# 连接池管理
from .connection_pool import ConnectionPool, ConnectionStatus, ConnectionInfo

# 数据格式化工具
from .data_formatter import DataFormatter, DataQualityReport

# 配置管理
from .data_source_config import (
    DataSourceConfigManager,
    ConfigValidator,
    ConfigEncryption,
    ConfigTemplate
)

__all__ = [
    # 核心接口
    'IDataSource',
    'DataSourceConfig',
    'MarketData',
    'SectorInfo',
    'StockInfo',
    
    # 异常类
    'DataSourceException',
    'ConnectionException',
    'DataException',
    'AuthenticationException',
    
    # 适配器
    'XtDataAdapter',
    
    # 管理器
    'DataSourceManager',
    'DataSourceStatus',
    
    # 连接池
    'ConnectionPool',
    'ConnectionStatus',
    'ConnectionInfo',
    
    # 数据格式化
    'DataFormatter',
    'DataQualityReport',
    
    # 配置管理
    'DataSourceConfigManager',
    'ConfigValidator',
    'ConfigEncryption',
    'ConfigTemplate'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Wyckoff RS System Team'
__description__ = '威科夫相对强弱选股系统 - 数据源模块'