"""
威科夫分析引擎测试用例

测试威科夫分析引擎的核心功能：
- 市场结构分析
- 威科夫信号检测
- 量价关系分析
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.engines.wyckoff import (
    WyckoffAnalysisEngine, MarketPhase, VolumePattern, 
    PriceAction, WyckoffSignal, MarketStructure
)
from src.utils.exceptions import CalculationError


class TestWyckoffAnalysisEngine:
    """威科夫分析引擎基础测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = WyckoffAnalysisEngine()
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        
        # 模拟股价数据
        base_price = 100
        price_changes = np.random.normal(0, 0.02, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            prices.append(prices[-1] * (1 + change))
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        engine = WyckoffAnalysisEngine(
            volume_ma_period=30,
            price_ma_period=30,
            volume_threshold=2.0,
            min_phase_duration=15
        )
        
        assert engine.volume_ma_period == 30
        assert engine.price_ma_period == 30
        assert engine.volume_threshold == 2.0
        assert engine.min_phase_duration == 15
    
    def test_analyze_market_structure_success(self):
        """测试成功的市场结构分析"""
        result = self.engine.analyze_market_structure(self.test_data)
        
        assert isinstance(result, MarketStructure)
        assert isinstance(result.phase, MarketPhase)
        assert result.support_level > 0
        assert result.resistance_level > result.support_level
        assert result.volume_trend in ["increasing", "decreasing", "stable"]
        assert result.price_trend in ["up", "down", "sideways"]
        assert 0 <= result.confidence <= 1
    
    def test_analyze_market_structure_insufficient_data(self):
        """测试数据不足的情况"""
        small_data = self.test_data.head(5)
        
        with pytest.raises(CalculationError):
            self.engine.analyze_market_structure(small_data)
    
    def test_analyze_market_structure_empty_data(self):
        """测试空数据的情况"""
        empty_data = pd.DataFrame()
        
        with pytest.raises(CalculationError):
            self.engine.analyze_market_structure(empty_data)
    
    def test_detect_wyckoff_signals(self):
        """测试威科夫信号检测"""
        signals = self.engine.detect_wyckoff_signals(self.test_data)
        
        assert isinstance(signals, list)
        # 信号可能为空，这是正常的
        for signal in signals:
            assert isinstance(signal, WyckoffSignal)
            assert isinstance(signal.signal_type, PriceAction)
            assert 0 <= signal.strength <= 1
            assert 0 <= signal.confidence <= 1
    
    def test_detect_wyckoff_signals_insufficient_data(self):
        """测试信号检测数据不足的情况"""
        small_data = self.test_data.head(10)
        signals = self.engine.detect_wyckoff_signals(small_data)
        
        # 数据不足时应该返回空列表
        assert signals == []
    
    def test_analyze_volume_price_relationship(self):
        """测试量价关系分析"""
        result = self.engine.analyze_volume_price_relationship(self.test_data)
        
        assert isinstance(result, dict)
        assert 'volume_price_divergence' in result
        assert 'volume_patterns' in result
        assert 'volume_price_correlation' in result
        assert 'average_volume_ratio' in result
        assert 'volume_volatility' in result
        
        # 检查相关性值的合理性
        correlation = result['volume_price_correlation']
        assert -1 <= correlation <= 1 or pd.isna(correlation)
    
    def test_analyze_volume_price_relationship_empty_data(self):
        """测试空数据的量价关系分析"""
        empty_data = pd.DataFrame()
        result = self.engine.analyze_volume_price_relationship(empty_data)
        
        assert result == {}


class TestMarketPhaseIdentification:
    """市场阶段识别测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = WyckoffAnalysisEngine()
    
    def create_phase_data(self, phase_type: str) -> pd.DataFrame:
        """创建特定阶段的测试数据"""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        base_price = 100
        
        if phase_type == "accumulation":
            # 累积阶段：价格横盘，成交量递减
            prices = [base_price + np.random.normal(0, 1) for _ in range(50)]
            volumes = [2000000 - i * 20000 + np.random.randint(-100000, 100000) for i in range(50)]
        
        elif phase_type == "markup":
            # 上涨阶段：价格上涨，成交量增加
            prices = [base_price + i * 0.5 + np.random.normal(0, 0.5) for i in range(50)]
            volumes = [1000000 + i * 30000 + np.random.randint(-50000, 50000) for i in range(50)]
        
        elif phase_type == "distribution":
            # 派发阶段：价格横盘，成交量较高
            prices = [base_price + 20 + np.random.normal(0, 1) for _ in range(50)]
            volumes = [3000000 + np.random.randint(-200000, 200000) for _ in range(50)]
        
        else:  # markdown
            # 下跌阶段：价格下跌，成交量增加
            prices = [base_price + 20 - i * 0.4 + np.random.normal(0, 0.5) for i in range(50)]
            volumes = [1500000 + i * 25000 + np.random.randint(-50000, 50000) for i in range(50)]
        
        return pd.DataFrame({
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': volumes
        }, index=dates)
    
    def test_identify_accumulation_phase(self):
        """测试累积阶段识别"""
        data = self.create_phase_data("accumulation")
        structure = self.engine.analyze_market_structure(data)
        
        # 累积阶段的特征
        assert structure.volume_trend in ["decreasing", "stable"]
        assert structure.price_trend in ["sideways", "down"]
    
    def test_identify_markup_phase(self):
        """测试上涨阶段识别"""
        data = self.create_phase_data("markup")
        structure = self.engine.analyze_market_structure(data)
        
        # 上涨阶段的特征
        assert structure.volume_trend in ["increasing", "stable"]
        assert structure.price_trend in ["up", "sideways"]
    
    def test_identify_distribution_phase(self):
        """测试派发阶段识别"""
        data = self.create_phase_data("distribution")
        structure = self.engine.analyze_market_structure(data)
        
        # 派发阶段的特征
        assert structure.volume_trend in ["increasing", "stable"]
        assert structure.price_trend in ["sideways", "up"]
    
    def test_identify_markdown_phase(self):
        """测试下跌阶段识别"""
        data = self.create_phase_data("markdown")
        structure = self.engine.analyze_market_structure(data)
        
        # 下跌阶段的特征
        assert structure.volume_trend in ["increasing", "stable"]
        assert structure.price_trend in ["down", "sideways"]


class TestVolumeAnalysis:
    """成交量分析测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = WyckoffAnalysisEngine()
    
    def test_volume_trend_analysis(self):
        """测试成交量趋势分析"""
        # 创建成交量递增的数据
        dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
        increasing_volumes = [1000000 + i * 50000 for i in range(30)]
        
        data = pd.DataFrame({
            'open': [100] * 30,
            'high': [101] * 30,
            'low': [99] * 30,
            'close': [100] * 30,
            'volume': increasing_volumes
        }, index=dates)
        
        result = self.engine.analyze_volume_price_relationship(data)
        
        # 应该检测到成交量增加趋势
        assert 'average_volume_ratio' in result
        assert result['average_volume_ratio'] > 1.0
    
    def test_volume_patterns_detection(self):
        """测试成交量模式检测"""
        dates = pd.date_range(start='2023-01-01', periods=20, freq='D')
        
        # 创建包含异常成交量的数据
        normal_volume = 1000000
        volumes = [normal_volume] * 20
        volumes[10] = normal_volume * 3  # 异常高成交量
        
        data = pd.DataFrame({
            'open': [100] * 20,
            'high': [101] * 20,
            'low': [99] * 20,
            'close': [100] * 20,
            'volume': volumes
        }, index=dates)
        
        result = self.engine.analyze_volume_price_relationship(data)
        
        # 应该检测到成交量异常
        assert 'volume_volatility' in result
        # 由于价格没有变化，相关性可能为NaN，这是正常的
        volatility = result['volume_volatility']
        assert volatility > 0 or pd.isna(volatility)


class TestWyckoffSignals:
    """威科夫信号测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = WyckoffAnalysisEngine()
    
    def test_signal_data_structure(self):
        """测试信号数据结构"""
        # 创建测试信号
        signal = WyckoffSignal(
            timestamp=pd.Timestamp('2023-01-01'),
            signal_type=PriceAction.SPRING,
            strength=0.8,
            price=100.0,
            volume=1000000,
            description="测试弹簧信号",
            confidence=0.7
        )
        
        assert signal.timestamp == pd.Timestamp('2023-01-01')
        assert signal.signal_type == PriceAction.SPRING
        assert signal.strength == 0.8
        assert signal.confidence == 0.7
    
    def test_signal_detection_with_valid_data(self):
        """测试有效数据的信号检测"""
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        
        # 创建包含潜在信号的数据
        prices = [100 + np.sin(i * 0.1) * 5 for i in range(50)]
        volumes = [1000000 + np.random.randint(-200000, 200000) for _ in range(50)]
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        signals = self.engine.detect_wyckoff_signals(data)
        
        # 验证信号格式
        for signal in signals:
            assert isinstance(signal.timestamp, pd.Timestamp)
            assert isinstance(signal.signal_type, PriceAction)
            assert 0 <= signal.strength <= 1
            assert 0 <= signal.confidence <= 1
            assert signal.price > 0
            assert signal.volume > 0


if __name__ == '__main__':
    pytest.main([__file__])
