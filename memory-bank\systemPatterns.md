# 威科夫相对强弱选股系统 - 系统架构和设计模式

## 系统架构概览

### 整体架构原则
系统采用**分层架构模式**，遵循**关注点分离**原则，确保各层职责清晰、松耦合、高内聚。架构设计支持可扩展性、可维护性和可测试性。

### 架构层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   主界面模块     │  │   设置界面模块   │  │   分析界面模块   │ │
│  │   (PyQt6 GUI)   │  │   (Configuration)│  │   (Analytics)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                  应用服务层 (Application Layer)             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   任务调度器     │  │   缓存管理器     │  │   配置管理器     │ │
│  │  (Task Scheduler)│  │ (Cache Manager) │  │(Config Manager) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  相对强弱计算    │  │   筛选策略引擎   │  │   结果分析器     │ │
│  │     引擎        │  │ (Strategy Engine)│  │ (Result Analyzer)│ │
│  │ (RS Calculator) │  └─────────────────┘  └─────────────────┘ │
│  └─────────────────┘  ┌─────────────────┐  ┌─────────────────┐ │
│  ┌─────────────────┐  │   数据更新器     │  │   报告生成器     │ │
│  │  数据验证器      │  │ (Data Updater)  │  │(Report Generator)│ │
│  │(Data Validator) │  └─────────────────┘  └─────────────────┘ │
│  └─────────────────┘                                          │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                  数据访问层 (Data Access Layer)             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  数据源管理器    │  │   数据库管理器   │  │   文件管理器     │ │
│  │(DataSource Mgr) │  │(Database Manager)│  │ (File Manager)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           ▼                     ▼                     ▼        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   XtData适配器   │  │   SQLite数据库   │  │   CSV/Excel     │ │
│  │  (XtDataAdapter)│  │   (Local DB)    │  │   (Export)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心设计模式

### 1. 适配器模式 (Adapter Pattern)

**应用场景**：数据源接口统一化

**实现方式**：
```python
# 抽象数据源接口
class IDataSource(ABC):
    @abstractmethod
    def get_market_data(self, symbol: str, period: str, 
                       start_date: str, end_date: str) -> pd.DataFrame:
        pass

# XtData适配器
class XtDataAdapter(IDataSource):
    def get_market_data(self, symbol: str, period: str, 
                       start_date: str, end_date: str) -> pd.DataFrame:
        # 调用XtData原始API并转换为标准格式
        raw_data = xtdata.get_market_data_ex(...)
        return self._convert_to_standard_format(raw_data)

# TuShare适配器（预留扩展）
class TuShareAdapter(IDataSource):
    def get_market_data(self, symbol: str, period: str, 
                       start_date: str, end_date: str) -> pd.DataFrame:
        # 调用TuShare API并转换为标准格式
        pass
```

**优势**：
- 统一不同数据源的接口
- 支持数据源的无缝切换
- 便于新数据源的集成

### 2. 策略模式 (Strategy Pattern)

**应用场景**：筛选策略的灵活配置

**实现方式**：
```python
# 筛选策略接口
class ISelectionStrategy(ABC):
    @abstractmethod
    def filter_sectors(self, sectors: pd.DataFrame, 
                      market_return: float) -> pd.DataFrame:
        pass
    
    @abstractmethod
    def filter_stocks(self, stocks: pd.DataFrame, 
                     sector_return: float) -> pd.DataFrame:
        pass

# 威科夫相对强弱策略
class WyckoffRelativeStrengthStrategy(ISelectionStrategy):
    def __init__(self, sector_count: int = 5, stock_per_sector: int = 3):
        self.sector_count = sector_count
        self.stock_per_sector = stock_per_sector
    
    def filter_sectors(self, sectors: pd.DataFrame, 
                      market_return: float) -> pd.DataFrame:
        # 筛选强于大盘的板块
        strong_sectors = sectors[sectors['return'] > market_return]
        return strong_sectors.nlargest(self.sector_count, 'relative_strength')

# 策略上下文
class SelectionContext:
    def __init__(self, strategy: ISelectionStrategy):
        self._strategy = strategy
    
    def set_strategy(self, strategy: ISelectionStrategy):
        self._strategy = strategy
    
    def execute_selection(self, data: dict) -> dict:
        return self._strategy.execute(data)
```

**优势**：
- 支持多种筛选策略
- 运行时策略切换
- 便于新策略的添加

### 3. 观察者模式 (Observer Pattern)

**应用场景**：数据更新和界面刷新

**实现方式**：
```python
# 观察者接口
class IObserver(ABC):
    @abstractmethod
    def update(self, event_type: str, data: Any):
        pass

# 被观察者（数据更新器）
class DataUpdater:
    def __init__(self):
        self._observers: List[IObserver] = []
        self._update_progress = 0
    
    def attach(self, observer: IObserver):
        self._observers.append(observer)
    
    def detach(self, observer: IObserver):
        self._observers.remove(observer)
    
    def notify(self, event_type: str, data: Any):
        for observer in self._observers:
            observer.update(event_type, data)
    
    def update_data(self):
        # 数据更新逻辑
        self.notify("update_started", None)
        # ... 更新过程 ...
        self.notify("progress_changed", self._update_progress)
        # ... 更新完成 ...
        self.notify("update_completed", updated_data)

# 观察者（界面更新器）
class UIProgressObserver(IObserver):
    def __init__(self, progress_bar):
        self.progress_bar = progress_bar
    
    def update(self, event_type: str, data: Any):
        if event_type == "progress_changed":
            self.progress_bar.setValue(data)
        elif event_type == "update_completed":
            self.progress_bar.hide()
```

**优势**：
- 解耦数据层和界面层
- 支持多个界面组件同时更新
- 便于添加新的事件监听器

### 4. 工厂模式 (Factory Pattern)

**应用场景**：数据源和策略对象的创建

**实现方式**：
```python
# 数据源工厂
class DataSourceFactory:
    _sources = {
        'xtdata': XtDataAdapter,
        'tushare': TuShareAdapter,
        'akshare': AkShareAdapter,
        'baostock': BaostockAdapter
    }
    
    @classmethod
    def create_source(cls, source_type: str, config: dict) -> IDataSource:
        if source_type not in cls._sources:
            raise ValueError(f"不支持的数据源类型: {source_type}")
        
        source_class = cls._sources[source_type]
        return source_class(config)
    
    @classmethod
    def register_source(cls, source_type: str, source_class: type):
        cls._sources[source_type] = source_class

# 策略工厂
class StrategyFactory:
    _strategies = {
        'wyckoff_rs': WyckoffRelativeStrengthStrategy,
        'momentum': MomentumStrategy,
        'custom': CustomStrategy
    }
    
    @classmethod
    def create_strategy(cls, strategy_type: str, params: dict) -> ISelectionStrategy:
        if strategy_type not in cls._strategies:
            raise ValueError(f"不支持的策略类型: {strategy_type}")
        
        strategy_class = cls._strategies[strategy_type]
        return strategy_class(**params)
```

**优势**：
- 集中管理对象创建逻辑
- 支持动态注册新类型
- 便于配置和扩展

### 5. 单例模式 (Singleton Pattern)

**应用场景**：全局配置管理和缓存管理

**实现方式**：
```python
# 配置管理器单例
class ConfigManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._config = {}
            self._load_config()
            self._initialized = True
    
    def get(self, key: str, default=None):
        return self._config.get(key, default)
    
    def set(self, key: str, value):
        self._config[key] = value
        self._save_config()

# 缓存管理器单例
class CacheManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._cache = {}
            self._cache_size_limit = 1000
            self._initialized = True
```

**优势**：
- 确保全局唯一实例
- 避免重复初始化
- 提供全局访问点

## 组件关系和数据流

### 数据流向图

```
用户操作 → 表示层 → 应用服务层 → 业务逻辑层 → 数据访问层
    ↓         ↓         ↓           ↓           ↓
  界面事件   任务调度   算法计算     数据查询    数据源API
    ↓         ↓         ↓           ↓           ↓
  参数设置   缓存管理   结果生成     数据存储    外部数据
    ↓         ↓         ↓           ↓           ↓
  结果展示 ← 配置管理 ← 业务处理 ← 数据处理 ← 数据获取
```

### 核心组件交互

#### 1. 筛选流程组件交互
```
SelectionController → StrategyEngine → RelativeStrengthCalculator
        ↓                   ↓                    ↓
    UIController ←    DataSourceManager ←   DatabaseManager
        ↓                   ↓                    ↓
   ResultDisplay ←      CacheManager    ←   DataValidator
```

#### 2. 数据更新组件交互
```
DataUpdateScheduler → DataUpdater → DataSourceManager
        ↓                 ↓              ↓
   ProgressObserver ←  DatabaseManager ← XtDataAdapter
        ↓                 ↓              ↓
     UIProgress    ←  DataValidator  ←  ExternalAPI
```

### 依赖关系管理

#### 依赖注入原则
- **构造函数注入**：主要依赖通过构造函数传入
- **接口依赖**：依赖抽象接口而非具体实现
- **配置驱动**：通过配置文件管理依赖关系

#### 示例实现
```python
class SelectionService:
    def __init__(self, 
                 data_source: IDataSource,
                 strategy: ISelectionStrategy,
                 cache_manager: CacheManager,
                 database_manager: DatabaseManager):
        self._data_source = data_source
        self._strategy = strategy
        self._cache = cache_manager
        self._db = database_manager
    
    def execute_selection(self, params: dict) -> dict:
        # 使用注入的依赖执行选股逻辑
        pass

# 依赖注入容器
class DIContainer:
    def __init__(self):
        self._services = {}
        self._singletons = {}
    
    def register_singleton(self, interface: type, implementation: type):
        self._singletons[interface] = implementation
    
    def register_transient(self, interface: type, implementation: type):
        self._services[interface] = implementation
    
    def resolve(self, interface: type):
        if interface in self._singletons:
            if interface not in self._instances:
                self._instances[interface] = self._singletons[interface]()
            return self._instances[interface]
        elif interface in self._services:
            return self._services[interface]()
        else:
            raise ValueError(f"未注册的服务: {interface}")
```

## 架构决策记录

### ADR-001: 分层架构选择
**决策**：采用4层架构（表示层、应用服务层、业务逻辑层、数据访问层）
**理由**：
- 职责分离清晰
- 支持独立测试
- 便于维护和扩展
- 符合领域驱动设计原则

**后果**：
- 增加了一定的复杂性
- 需要更多的接口定义
- 但提高了代码质量和可维护性

### ADR-002: PyQt6 GUI框架选择
**决策**：选择PyQt6作为主要GUI框架，PyQt5作为备选
**理由**：
- 成熟稳定的跨平台GUI框架
- 丰富的控件和功能
- 良好的性能表现
- 支持现代化界面设计

**后果**：
- 需要商业许可或遵循GPL许可
- 学习曲线相对较陡
- 但提供了专业级的界面开发能力

### ADR-003: SQLite数据库选择
**决策**：使用SQLite作为本地数据存储
**理由**：
- 轻量级，无需额外安装
- 支持标准SQL语法
- 性能满足单用户需求
- 便于数据备份和迁移

**后果**：
- 不支持并发写入
- 不适合大规模数据处理
- 但满足当前项目需求

### ADR-004: 适配器模式数据源设计
**决策**：使用适配器模式统一数据源接口
**理由**：
- 支持多数据源无缝切换
- 便于新数据源集成
- 降低业务逻辑对数据源的依赖
- 提高系统的可扩展性

**后果**：
- 增加了抽象层的复杂性
- 需要为每个数据源开发适配器
- 但大大提高了系统的灵活性

## 性能优化策略

### 1. 计算性能优化
- **向量化计算**：使用NumPy和Pandas的向量化操作
- **并行处理**：多进程处理不同板块的计算
- **内存映射**：大数据集使用内存映射文件
- **算法优化**：优化关键路径的算法复杂度

### 2. 数据访问优化
- **连接池**：数据库连接池管理
- **批量操作**：批量插入和更新操作
- **索引优化**：关键查询字段建立索引
- **查询优化**：SQL查询语句优化

### 3. 缓存策略
- **多级缓存**：内存缓存 + 文件缓存 + 数据库缓存
- **智能失效**：基于时间和数据变化的缓存失效
- **预加载**：预测性数据预加载
- **压缩存储**：缓存数据压缩存储

### 4. 界面性能优化
- **异步操作**：长时间操作使用异步处理
- **虚拟滚动**：大数据列表使用虚拟滚动
- **延迟加载**：按需加载界面组件
- **渲染优化**：减少不必要的界面重绘

## 可扩展性设计

### 1. 插件架构
- **插件接口**：定义标准的插件接口
- **动态加载**：运行时动态加载插件
- **配置管理**：插件配置和管理机制
- **版本兼容**：插件版本兼容性管理

### 2. 微服务化准备
- **服务边界**：清晰的服务边界定义
- **API设计**：RESTful API接口设计
- **消息队列**：异步消息处理机制
- **配置中心**：集中化配置管理

### 3. 数据源扩展
- **标准接口**：统一的数据源接口规范
- **配置驱动**：配置文件驱动的数据源管理
- **故障转移**：自动故障转移机制
- **负载均衡**：多数据源负载均衡

## 安全性考虑

### 1. 数据安全
- **本地存储**：敏感数据本地存储
- **加密传输**：网络传输数据加密
- **访问控制**：数据访问权限控制
- **审计日志**：操作审计日志记录

### 2. 系统安全
- **输入验证**：所有输入数据验证
- **异常处理**：安全的异常处理机制
- **资源限制**：系统资源使用限制
- **更新机制**：安全的系统更新机制

## 质量保证

### 1. 代码质量
- **代码规范**：统一的代码风格规范
- **静态分析**：代码静态分析工具
- **代码审查**：同行代码审查机制
- **重构策略**：持续重构改进策略

### 2. 测试策略
- **单元测试**：核心逻辑单元测试
- **集成测试**：组件集成测试
- **性能测试**：关键功能性能测试
- **用户测试**：真实用户场景测试

### 3. 监控和诊断
- **性能监控**：系统性能实时监控
- **错误追踪**：错误日志和追踪
- **健康检查**：系统健康状态检查
- **诊断工具**：问题诊断和调试工具 