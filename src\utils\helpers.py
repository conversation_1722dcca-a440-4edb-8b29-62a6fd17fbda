"""
通用工具函数库

提供时间处理、数据验证、格式化等常用工具函数
"""

import re
import time
import hashlib
import uuid
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from datetime import datetime, timedelta, date
from decimal import Decimal, ROUND_HALF_UP
from pathlib import Path
import json
import csv
from dataclasses import is_dataclass, asdict

from .logger import get_logger

logger = get_logger(__name__)


# ========================= 时间处理工具 =========================

def get_current_timestamp() -> int:
    """获取当前时间戳（秒）"""
    return int(time.time())


def get_current_datetime() -> datetime:
    """获取当前日期时间"""
    return datetime.now()


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象
        format_str: 格式字符串
        
    Returns:
        格式化的日期时间字符串
    """
    return dt.strftime(format_str)


def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """
    解析日期时间字符串
    
    Args:
        dt_str: 日期时间字符串
        format_str: 格式字符串
        
    Returns:
        日期时间对象或None
    """
    try:
        return datetime.strptime(dt_str, format_str)
    except ValueError:
        logger.warning(f"无法解析日期时间: {dt_str}")
        return None


def get_trading_day(target_date: Optional[date] = None) -> date:
    """
    获取交易日（排除周末）
    
    Args:
        target_date: 目标日期，默认为今天
        
    Returns:
        交易日日期
    """
    if target_date is None:
        target_date = date.today()
    
    # 如果是周末，返回上一个周五
    weekday = target_date.weekday()
    if weekday == 5:  # 周六
        return target_date - timedelta(days=1)
    elif weekday == 6:  # 周日
        return target_date - timedelta(days=2)
    else:
        return target_date


def get_previous_trading_day(current_date: date, days: int = 1) -> date:
    """
    获取前N个交易日
    
    Args:
        current_date: 当前日期
        days: 往前的交易日数量
        
    Returns:
        前N个交易日的日期
    """
    result_date = current_date
    remaining_days = days
    
    while remaining_days > 0:
        result_date -= timedelta(days=1)
        # 跳过周末
        if result_date.weekday() < 5:  # 周一到周五
            remaining_days -= 1
    
    return result_date


def get_date_range(start_date: date, end_date: date, trading_days_only: bool = True) -> List[date]:
    """
    获取日期范围内的所有日期
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        trading_days_only: 是否仅包含交易日
        
    Returns:
        日期列表
    """
    dates = []
    current_date = start_date
    
    while current_date <= end_date:
        if not trading_days_only or current_date.weekday() < 5:
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    return dates


def time_elapsed(start_time: float) -> str:
    """
    计算经过的时间
    
    Args:
        start_time: 开始时间戳
        
    Returns:
        经过时间的描述
    """
    elapsed = time.time() - start_time
    
    if elapsed < 60:
        return f"{elapsed:.1f}秒"
    elif elapsed < 3600:
        return f"{elapsed/60:.1f}分钟"
    else:
        return f"{elapsed/3600:.1f}小时"


# ========================= 数据验证工具 =========================

def is_valid_stock_code(code: str) -> bool:
    """
    验证股票代码格式
    
    Args:
        code: 股票代码
        
    Returns:
        是否有效
    """
    if not isinstance(code, str) or len(code) != 6:
        return False
    
    return code.isdigit()


def is_valid_email(email: str) -> bool:
    """
    验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否有效
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def is_valid_phone(phone: str) -> bool:
    """
    验证手机号格式
    
    Args:
        phone: 手机号
        
    Returns:
        是否有效
    """
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None


def is_valid_url(url: str) -> bool:
    """
    验证URL格式
    
    Args:
        url: URL地址
        
    Returns:
        是否有效
    """
    pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return re.match(pattern, url) is not None


def validate_numeric_range(value: Union[int, float], min_val: Optional[Union[int, float]] = None,
                          max_val: Optional[Union[int, float]] = None) -> bool:
    """
    验证数值范围
    
    Args:
        value: 数值
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        是否在范围内
    """
    if min_val is not None and value < min_val:
        return False
    if max_val is not None and value > max_val:
        return False
    return True


# ========================= 数据格式化工具 =========================

def format_number(value: Union[int, float], precision: int = 2, 
                 thousands_separator: str = ",") -> str:
    """
    格式化数字
    
    Args:
        value: 数值
        precision: 小数位数
        thousands_separator: 千位分隔符
        
    Returns:
        格式化的数字字符串
    """
    if isinstance(value, (int, float)):
        # 使用Decimal进行精确计算
        decimal_value = Decimal(str(value))
        rounded_value = decimal_value.quantize(
            Decimal('0.' + '0' * precision), 
            rounding=ROUND_HALF_UP
        )
        
        # 格式化
        if thousands_separator:
            return f"{rounded_value:,}".replace(',', thousands_separator)
        else:
            return str(rounded_value)
    else:
        return str(value)


def format_percentage(value: Union[int, float], precision: int = 2) -> str:
    """
    格式化百分比
    
    Args:
        value: 数值（小数形式，如0.05表示5%）
        precision: 小数位数
        
    Returns:
        格式化的百分比字符串
    """
    percentage = value * 100
    return f"{format_number(percentage, precision)}%"


def format_currency(value: Union[int, float], currency: str = "¥", 
                   precision: int = 2) -> str:
    """
    格式化货币
    
    Args:
        value: 数值
        currency: 货币符号
        precision: 小数位数
        
    Returns:
        格式化的货币字符串
    """
    return f"{currency}{format_number(value, precision)}"


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        格式化的文件大小字符串
    """
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(size_bytes)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        截断后的字符串
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


# ========================= 数据转换工具 =========================

def safe_int(value: Any, default: int = 0) -> int:
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        整数值
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        浮点数值
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_str(value: Any, default: str = "") -> str:
    """
    安全转换为字符串
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        字符串值
    """
    try:
        if value is None:
            return default
        return str(value)
    except Exception:
        return default


def dict_to_obj(data: Dict[str, Any]) -> object:
    """
    将字典转换为对象（支持嵌套）
    
    Args:
        data: 字典数据
        
    Returns:
        对象
    """
    if not isinstance(data, dict):
        return data
    
    class DictObject:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                if isinstance(value, dict):
                    setattr(self, key, dict_to_obj(value))
                elif isinstance(value, list):
                    setattr(self, key, [dict_to_obj(item) if isinstance(item, dict) else item for item in value])
                else:
                    setattr(self, key, value)
    
    return DictObject(**data)


def obj_to_dict(obj: Any) -> Dict[str, Any]:
    """
    将对象转换为字典
    
    Args:
        obj: 对象
        
    Returns:
        字典数据
    """
    if is_dataclass(obj):
        return asdict(obj)
    elif hasattr(obj, '__dict__'):
        result = {}
        for key, value in obj.__dict__.items():
            if not key.startswith('_'):
                if hasattr(value, '__dict__') or is_dataclass(value):
                    result[key] = obj_to_dict(value)
                else:
                    result[key] = value
        return result
    else:
        return obj


# ========================= 文件处理工具 =========================

def ensure_dir_exists(dir_path: Union[str, Path]) -> bool:
    """
    确保目录存在
    
    Args:
        dir_path: 目录路径
        
    Returns:
        是否成功
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {dir_path}, 错误: {e}")
        return False


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    获取文件扩展名
    
    Args:
        file_path: 文件路径
        
    Returns:
        扩展名（小写，不含点）
    """
    return Path(file_path).suffix.lower().lstrip('.')


def is_file_older_than(file_path: Union[str, Path], hours: int) -> bool:
    """
    检查文件是否超过指定时间
    
    Args:
        file_path: 文件路径
        hours: 小时数
        
    Returns:
        是否超时
    """
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return True
        
        file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
        threshold = datetime.now() - timedelta(hours=hours)
        
        return file_time < threshold
        
    except Exception as e:
        logger.error(f"检查文件时间失败: {file_path}, 错误: {e}")
        return True


def read_json_file(file_path: Union[str, Path], default: Any = None) -> Any:
    """
    读取JSON文件
    
    Args:
        file_path: 文件路径
        default: 默认值
        
    Returns:
        JSON数据或默认值
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"读取JSON文件失败: {file_path}, 错误: {e}")
        return default


def write_json_file(file_path: Union[str, Path], data: Any, indent: int = 2) -> bool:
    """
    写入JSON文件
    
    Args:
        file_path: 文件路径
        data: 数据
        indent: 缩进
        
    Returns:
        是否成功
    """
    try:
        file_path = Path(file_path)
        ensure_dir_exists(file_path.parent)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        
        return True
        
    except Exception as e:
        logger.error(f"写入JSON文件失败: {file_path}, 错误: {e}")
        return False


def read_csv_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> List[Dict[str, str]]:
    """
    读取CSV文件
    
    Args:
        file_path: 文件路径
        encoding: 编码
        
    Returns:
        数据列表
    """
    try:
        data = []
        with open(file_path, 'r', encoding=encoding, newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        return data
        
    except Exception as e:
        logger.error(f"读取CSV文件失败: {file_path}, 错误: {e}")
        return []


def write_csv_file(file_path: Union[str, Path], data: List[Dict[str, Any]], 
                  encoding: str = 'utf-8') -> bool:
    """
    写入CSV文件
    
    Args:
        file_path: 文件路径
        data: 数据列表
        encoding: 编码
        
    Returns:
        是否成功
    """
    try:
        if not data:
            return True
        
        file_path = Path(file_path)
        ensure_dir_exists(file_path.parent)
        
        fieldnames = data[0].keys()
        
        with open(file_path, 'w', encoding=encoding, newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        return True
        
    except Exception as e:
        logger.error(f"写入CSV文件失败: {file_path}, 错误: {e}")
        return False


# ========================= 加密和哈希工具 =========================

def generate_uuid() -> str:
    """生成UUID"""
    return str(uuid.uuid4())


def generate_short_id(length: int = 8) -> str:
    """
    生成短ID
    
    Args:
        length: ID长度
        
    Returns:
        短ID字符串
    """
    return str(uuid.uuid4()).replace('-', '')[:length]


def calculate_md5(text: str) -> str:
    """
    计算MD5哈希
    
    Args:
        text: 文本
        
    Returns:
        MD5哈希值
    """
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def calculate_sha256(text: str) -> str:
    """
    计算SHA256哈希
    
    Args:
        text: 文本
        
    Returns:
        SHA256哈希值
    """
    return hashlib.sha256(text.encode('utf-8')).hexdigest()


def calculate_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> Optional[str]:
    """
    计算文件哈希
    
    Args:
        file_path: 文件路径
        algorithm: 算法（md5, sha256）
        
    Returns:
        哈希值或None
    """
    try:
        hash_func = hashlib.md5() if algorithm == 'md5' else hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()
        
    except Exception as e:
        logger.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
        return None


# ========================= 缓存和性能工具 =========================

def memoize(func: Callable) -> Callable:
    """
    简单的函数结果缓存装饰器
    
    Args:
        func: 要缓存的函数
        
    Returns:
        装饰后的函数
    """
    cache = {}
    
    def wrapper(*args, **kwargs):
        # 创建缓存键
        key = str(args) + str(sorted(kwargs.items()))
        
        if key not in cache:
            cache[key] = func(*args, **kwargs)
        
        return cache[key]
    
    wrapper.cache = cache
    wrapper.cache_clear = lambda: cache.clear()
    
    return wrapper


def timing(func: Callable) -> Callable:
    """
    函数执行时间装饰器
    
    Args:
        func: 要测量的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"函数 {func.__name__} 执行时间: {end_time - start_time:.3f}秒")
        return result
    
    return wrapper


def batch_process(items: List[Any], batch_size: int, 
                 process_func: Callable[[List[Any]], Any]) -> List[Any]:
    """
    批量处理数据
    
    Args:
        items: 要处理的项目列表
        batch_size: 批次大小
        process_func: 处理函数
        
    Returns:
        处理结果列表
    """
    results = []
    
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        batch_result = process_func(batch)
        
        if isinstance(batch_result, list):
            results.extend(batch_result)
        else:
            results.append(batch_result)
    
    return results


# ========================= 其他工具函数 =========================

def flatten_dict(data: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    展平嵌套字典
    
    Args:
        data: 嵌套字典
        parent_key: 父键
        sep: 分隔符
        
    Returns:
        展平后的字典
    """
    items = []
    
    for key, value in data.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key
        
        if isinstance(value, dict):
            items.extend(flatten_dict(value, new_key, sep).items())
        else:
            items.append((new_key, value))
    
    return dict(items)


def get_nested_value(data: Dict[str, Any], key_path: str, default: Any = None) -> Any:
    """
    获取嵌套字典的值
    
    Args:
        data: 字典数据
        key_path: 键路径，如 'a.b.c'
        default: 默认值
        
    Returns:
        值或默认值
    """
    try:
        keys = key_path.split('.')
        current = data
        
        for key in keys:
            current = current[key]
        
        return current
        
    except (KeyError, TypeError):
        return default


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 列表
        chunk_size: 块大小
        
    Returns:
        分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def remove_duplicates(lst: List[Any], key_func: Optional[Callable] = None) -> List[Any]:
    """
    去除列表重复项
    
    Args:
        lst: 列表
        key_func: 键函数
        
    Returns:
        去重后的列表
    """
    if key_func is None:
        return list(dict.fromkeys(lst))
    
    seen = set()
    result = []
    
    for item in lst:
        key = key_func(item)
        if key not in seen:
            seen.add(key)
            result.append(item)
    
    return result


def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并多个字典
    
    Args:
        *dicts: 字典列表
        
    Returns:
        合并后的字典
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def retry(max_attempts: int = 3, delay: float = 1.0, 
         exceptions: Tuple[type, ...] = (Exception,)) -> Callable:
    """
    重试装饰器
    
    Args:
        max_attempts: 最大尝试次数
        delay: 延迟时间（秒）
        exceptions: 要捕获的异常类型
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        time.sleep(delay)
                    else:
                        logger.error(f"函数 {func.__name__} 所有尝试都失败了")
            
            if last_exception:
                raise last_exception
        
        return wrapper
    return decorator