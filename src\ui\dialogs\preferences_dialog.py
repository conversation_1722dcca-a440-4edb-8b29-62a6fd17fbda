"""
用户偏好设置对话框

提供用户友好的设置界面
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QSlider, QGroupBox, QFormLayout, QLineEdit,
    QFileDialog, QMessageBox, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from ..utils.user_preferences import user_preferences, UIPreferences, DataPreferences
from ..utils.theme_manager import theme_manager, ThemeType
from ...utils.logger import get_logger

logger = get_logger(__name__)


class PreferencesDialog(QDialog):
    """偏好设置对话框"""
    
    preferences_applied = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("偏好设置")
        self.setFixedSize(700, 600)
        self.setModal(True)
        
        # 保存原始设置用于取消操作
        self.original_preferences = user_preferences.preferences
        
        self._init_ui()
        self._load_current_settings()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标签页控件
        self.tab_widget = QTabWidget()
        
        # 界面设置标签页
        self.ui_tab = self._create_ui_tab()
        self.tab_widget.addTab(self.ui_tab, "界面设置")
        
        # 数据设置标签页
        self.data_tab = self._create_data_tab()
        self.tab_widget.addTab(self.data_tab, "数据设置")
        
        # 分析设置标签页
        self.analysis_tab = self._create_analysis_tab()
        self.tab_widget.addTab(self.analysis_tab, "分析设置")
        
        # 选股设置标签页
        self.selection_tab = self._create_selection_tab()
        self.tab_widget.addTab(self.selection_tab, "选股设置")
        
        # 通知设置标签页
        self.notification_tab = self._create_notification_tab()
        self.tab_widget.addTab(self.notification_tab, "通知设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 导入导出按钮
        self.import_button = QPushButton("导入设置")
        self.export_button = QPushButton("导出设置")
        button_layout.addWidget(self.import_button)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        
        # 重置按钮
        self.reset_button = QPushButton("重置默认")
        button_layout.addWidget(self.reset_button)
        
        # 确定取消按钮
        self.cancel_button = QPushButton("取消")
        self.apply_button = QPushButton("应用")
        self.ok_button = QPushButton("确定")
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
        
    def _create_ui_tab(self) -> QWidget:
        """创建界面设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置组
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["明亮主题", "深色主题"])
        theme_layout.addRow("界面主题:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 布局设置组
        layout_group = QGroupBox("布局设置")
        layout_form = QFormLayout(layout_group)
        
        self.layout_mode_combo = QComboBox()
        self.layout_mode_combo.addItems(["紧凑", "标准", "宽松"])
        layout_form.addRow("布局模式:", self.layout_mode_combo)
        
        self.show_toolbar_check = QCheckBox("显示工具栏")
        self.show_statusbar_check = QCheckBox("显示状态栏")
        layout_form.addRow("", self.show_toolbar_check)
        layout_form.addRow("", self.show_statusbar_check)
        
        layout.addWidget(layout_group)
        
        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setSuffix(" pt")
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["Microsoft YaHei", "SimSun", "Arial", "Consolas"])
        font_layout.addRow("字体族:", self.font_family_combo)
        
        layout.addWidget(font_group)
        
        # 其他设置组
        other_group = QGroupBox("其他设置")
        other_layout = QFormLayout(other_group)
        
        self.show_tooltips_check = QCheckBox("显示工具提示")
        self.animation_enabled_check = QCheckBox("启用动画效果")
        other_layout.addRow("", self.show_tooltips_check)
        other_layout.addRow("", self.animation_enabled_check)
        
        layout.addWidget(other_group)
        
        layout.addStretch()
        return widget
        
    def _create_data_tab(self) -> QWidget:
        """创建数据设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 刷新设置组
        refresh_group = QGroupBox("数据刷新")
        refresh_layout = QFormLayout(refresh_group)
        
        self.refresh_mode_combo = QComboBox()
        self.refresh_mode_combo.addItems(["手动刷新", "自动刷新", "定时刷新"])
        refresh_layout.addRow("刷新模式:", self.refresh_mode_combo)
        
        self.auto_refresh_spin = QSpinBox()
        self.auto_refresh_spin.setRange(60, 3600)
        self.auto_refresh_spin.setSuffix(" 秒")
        refresh_layout.addRow("自动刷新间隔:", self.auto_refresh_spin)
        
        layout.addWidget(refresh_group)
        
        # 缓存设置组
        cache_group = QGroupBox("数据缓存")
        cache_layout = QFormLayout(cache_group)
        
        self.cache_enabled_check = QCheckBox("启用数据缓存")
        cache_layout.addRow("", self.cache_enabled_check)
        
        self.cache_duration_spin = QSpinBox()
        self.cache_duration_spin.setRange(300, 86400)
        self.cache_duration_spin.setSuffix(" 秒")
        cache_layout.addRow("缓存有效期:", self.cache_duration_spin)
        
        layout.addWidget(cache_group)
        
        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout(connection_group)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 120)
        self.timeout_spin.setSuffix(" 秒")
        connection_layout.addRow("连接超时:", self.timeout_spin)
        
        self.retry_times_spin = QSpinBox()
        self.retry_times_spin.setRange(1, 10)
        connection_layout.addRow("重试次数:", self.retry_times_spin)
        
        layout.addWidget(connection_group)
        
        # 筛选设置组
        filter_group = QGroupBox("数据筛选")
        filter_layout = QFormLayout(filter_group)
        
        self.default_market_combo = QComboBox()
        self.default_market_combo.addItems(["全部市场", "沪市", "深市"])
        filter_layout.addRow("默认市场:", self.default_market_combo)
        
        self.show_suspended_check = QCheckBox("显示停牌股票")
        filter_layout.addRow("", self.show_suspended_check)
        
        layout.addWidget(filter_group)
        
        layout.addStretch()
        return widget
        
    def _create_analysis_tab(self) -> QWidget:
        """创建分析设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本参数组
        basic_group = QGroupBox("基本参数")
        basic_layout = QFormLayout(basic_group)
        
        self.default_period_spin = QSpinBox()
        self.default_period_spin.setRange(50, 1000)
        basic_layout.addRow("默认分析周期:", self.default_period_spin)
        
        self.confidence_threshold_spin = QDoubleSpinBox()
        self.confidence_threshold_spin.setRange(0.1, 1.0)
        self.confidence_threshold_spin.setDecimals(2)
        self.confidence_threshold_spin.setSingleStep(0.05)
        basic_layout.addRow("最小置信度:", self.confidence_threshold_spin)
        
        layout.addWidget(basic_group)
        
        # 威科夫分析组
        wyckoff_group = QGroupBox("威科夫分析")
        wyckoff_layout = QFormLayout(wyckoff_group)
        
        self.wyckoff_sensitivity_spin = QDoubleSpinBox()
        self.wyckoff_sensitivity_spin.setRange(0.1, 1.0)
        self.wyckoff_sensitivity_spin.setDecimals(2)
        self.wyckoff_sensitivity_spin.setSingleStep(0.05)
        wyckoff_layout.addRow("分析敏感度:", self.wyckoff_sensitivity_spin)
        
        layout.addWidget(wyckoff_group)
        
        # 相对强弱组
        rs_group = QGroupBox("相对强弱分析")
        rs_layout = QFormLayout(rs_group)
        
        self.rs_period_spin = QSpinBox()
        self.rs_period_spin.setRange(5, 100)
        rs_layout.addRow("计算周期:", self.rs_period_spin)
        
        layout.addWidget(rs_group)
        
        # 其他选项组
        other_analysis_group = QGroupBox("其他选项")
        other_analysis_layout = QFormLayout(other_analysis_group)
        
        self.auto_save_results_check = QCheckBox("自动保存分析结果")
        self.show_analysis_details_check = QCheckBox("显示分析详情")
        self.enable_background_analysis_check = QCheckBox("启用后台分析")
        
        other_analysis_layout.addRow("", self.auto_save_results_check)
        other_analysis_layout.addRow("", self.show_analysis_details_check)
        other_analysis_layout.addRow("", self.enable_background_analysis_check)
        
        layout.addWidget(other_analysis_group)
        
        layout.addStretch()
        return widget
        
    def _create_selection_tab(self) -> QWidget:
        """创建选股设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本设置组
        basic_selection_group = QGroupBox("基本设置")
        basic_selection_layout = QFormLayout(basic_selection_group)
        
        self.max_selection_spin = QSpinBox()
        self.max_selection_spin.setRange(10, 200)
        basic_selection_layout.addRow("最大选股数量:", self.max_selection_spin)
        
        self.default_strategy_combo = QComboBox()
        self.default_strategy_combo.addItems(["保守", "平衡", "激进"])
        basic_selection_layout.addRow("默认策略:", self.default_strategy_combo)
        
        layout.addWidget(basic_selection_group)
        
        # 市值筛选组
        market_cap_group = QGroupBox("市值筛选")
        market_cap_layout = QFormLayout(market_cap_group)
        
        self.min_market_cap_spin = QDoubleSpinBox()
        self.min_market_cap_spin.setRange(1.0, 10000.0)
        self.min_market_cap_spin.setSuffix(" 亿")
        market_cap_layout.addRow("最小市值:", self.min_market_cap_spin)
        
        self.max_market_cap_spin = QDoubleSpinBox()
        self.max_market_cap_spin.setRange(10.0, 100000.0)
        self.max_market_cap_spin.setSuffix(" 亿")
        market_cap_layout.addRow("最大市值:", self.max_market_cap_spin)
        
        layout.addWidget(market_cap_group)
        
        # 排除设置组
        exclude_group = QGroupBox("排除设置")
        exclude_layout = QFormLayout(exclude_group)
        
        self.exclude_st_check = QCheckBox("排除ST股票")
        self.exclude_new_stocks_check = QCheckBox("排除新股")
        exclude_layout.addRow("", self.exclude_st_check)
        exclude_layout.addRow("", self.exclude_new_stocks_check)
        
        self.new_stock_days_spin = QSpinBox()
        self.new_stock_days_spin.setRange(30, 365)
        self.new_stock_days_spin.setSuffix(" 天")
        exclude_layout.addRow("新股天数阈值:", self.new_stock_days_spin)
        
        layout.addWidget(exclude_group)
        
        layout.addStretch()
        return widget
        
    def _create_notification_tab(self) -> QWidget:
        """创建通知设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 通知开关组
        notification_group = QGroupBox("通知设置")
        notification_layout = QFormLayout(notification_group)
        
        self.enable_notifications_check = QCheckBox("启用通知")
        notification_layout.addRow("", self.enable_notifications_check)
        
        self.show_success_check = QCheckBox("显示成功通知")
        self.show_warning_check = QCheckBox("显示警告通知")
        self.show_error_check = QCheckBox("显示错误通知")
        
        notification_layout.addRow("", self.show_success_check)
        notification_layout.addRow("", self.show_warning_check)
        notification_layout.addRow("", self.show_error_check)
        
        layout.addWidget(notification_group)
        
        # 通知行为组
        behavior_group = QGroupBox("通知行为")
        behavior_layout = QFormLayout(behavior_group)
        
        self.notification_duration_spin = QSpinBox()
        self.notification_duration_spin.setRange(1000, 30000)
        self.notification_duration_spin.setSuffix(" 毫秒")
        behavior_layout.addRow("显示时长:", self.notification_duration_spin)
        
        self.sound_enabled_check = QCheckBox("启用声音提示")
        self.desktop_notifications_check = QCheckBox("启用桌面通知")
        
        behavior_layout.addRow("", self.sound_enabled_check)
        behavior_layout.addRow("", self.desktop_notifications_check)
        
        layout.addWidget(behavior_group)
        
        layout.addStretch()
        return widget

    def _load_current_settings(self):
        """加载当前设置"""
        ui_prefs = user_preferences.get_ui_preferences()
        data_prefs = user_preferences.get_data_preferences()
        analysis_prefs = user_preferences.get_analysis_preferences()
        selection_prefs = user_preferences.get_selection_preferences()
        notification_prefs = user_preferences.get_notification_preferences()

        # 界面设置
        theme_index = 0 if ui_prefs.theme == "light" else 1
        self.theme_combo.setCurrentIndex(theme_index)

        layout_modes = {"compact": 0, "standard": 1, "expanded": 2}
        self.layout_mode_combo.setCurrentIndex(layout_modes.get(ui_prefs.layout_mode, 1))

        self.show_toolbar_check.setChecked(ui_prefs.show_toolbar)
        self.show_statusbar_check.setChecked(ui_prefs.show_statusbar)
        self.font_size_spin.setValue(ui_prefs.font_size)

        font_index = self.font_family_combo.findText(ui_prefs.font_family)
        if font_index >= 0:
            self.font_family_combo.setCurrentIndex(font_index)

        self.show_tooltips_check.setChecked(ui_prefs.show_tooltips)
        self.animation_enabled_check.setChecked(ui_prefs.animation_enabled)

        # 数据设置
        refresh_modes = {"manual": 0, "auto": 1, "scheduled": 2}
        self.refresh_mode_combo.setCurrentIndex(refresh_modes.get(data_prefs.refresh_mode, 0))

        self.auto_refresh_spin.setValue(data_prefs.auto_refresh_interval)
        self.cache_enabled_check.setChecked(data_prefs.cache_enabled)
        self.cache_duration_spin.setValue(data_prefs.cache_duration)
        self.timeout_spin.setValue(data_prefs.data_source_timeout)
        self.retry_times_spin.setValue(data_prefs.retry_times)

        markets = {"all": 0, "sh": 1, "sz": 2}
        self.default_market_combo.setCurrentIndex(markets.get(data_prefs.default_market, 0))
        self.show_suspended_check.setChecked(data_prefs.show_suspended_stocks)

        # 分析设置
        self.default_period_spin.setValue(analysis_prefs.default_period)
        self.confidence_threshold_spin.setValue(analysis_prefs.min_confidence_threshold)
        self.wyckoff_sensitivity_spin.setValue(analysis_prefs.wyckoff_sensitivity)
        self.rs_period_spin.setValue(analysis_prefs.rs_calculation_period)
        self.auto_save_results_check.setChecked(analysis_prefs.auto_save_results)
        self.show_analysis_details_check.setChecked(analysis_prefs.show_analysis_details)
        self.enable_background_analysis_check.setChecked(analysis_prefs.enable_background_analysis)

        # 选股设置
        self.max_selection_spin.setValue(selection_prefs.max_selection_count)

        strategies = {"conservative": 0, "balanced": 1, "aggressive": 2}
        self.default_strategy_combo.setCurrentIndex(strategies.get(selection_prefs.default_strategy, 1))

        self.min_market_cap_spin.setValue(selection_prefs.min_market_cap)
        self.max_market_cap_spin.setValue(selection_prefs.max_market_cap)
        self.exclude_st_check.setChecked(selection_prefs.exclude_st_stocks)
        self.exclude_new_stocks_check.setChecked(selection_prefs.exclude_new_stocks)
        self.new_stock_days_spin.setValue(selection_prefs.new_stock_days_threshold)

        # 通知设置
        self.enable_notifications_check.setChecked(notification_prefs.enable_notifications)
        self.show_success_check.setChecked(notification_prefs.show_success_notifications)
        self.show_warning_check.setChecked(notification_prefs.show_warning_notifications)
        self.show_error_check.setChecked(notification_prefs.show_error_notifications)
        self.notification_duration_spin.setValue(notification_prefs.notification_duration)
        self.sound_enabled_check.setChecked(notification_prefs.sound_enabled)
        self.desktop_notifications_check.setChecked(notification_prefs.desktop_notifications)

    def _connect_signals(self):
        """连接信号"""
        self.ok_button.clicked.connect(self._on_ok_clicked)
        self.apply_button.clicked.connect(self._on_apply_clicked)
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        self.reset_button.clicked.connect(self._on_reset_clicked)
        self.import_button.clicked.connect(self._on_import_clicked)
        self.export_button.clicked.connect(self._on_export_clicked)

        # 主题变化立即应用
        self.theme_combo.currentTextChanged.connect(self._on_theme_changed)

    def _on_theme_changed(self, theme_text: str):
        """主题变化处理"""
        theme_type = ThemeType.LIGHT if theme_text == "明亮主题" else ThemeType.DARK
        theme_manager.set_theme(theme_type)

    def _on_ok_clicked(self):
        """确定按钮点击"""
        if self._apply_settings():
            self.accept()

    def _on_apply_clicked(self):
        """应用按钮点击"""
        self._apply_settings()

    def _on_cancel_clicked(self):
        """取消按钮点击"""
        # 恢复原始设置
        user_preferences.preferences = self.original_preferences
        self.reject()

    def _on_reset_clicked(self):
        """重置按钮点击"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            user_preferences.reset_to_defaults()
            self._load_current_settings()

    def _on_import_clicked(self):
        """导入按钮点击"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入设置", "", "JSON文件 (*.json)"
        )

        if file_path:
            if user_preferences.import_preferences(file_path):
                QMessageBox.information(self, "导入成功", "设置已成功导入")
                self._load_current_settings()
            else:
                QMessageBox.warning(self, "导入失败", "导入设置文件失败")

    def _on_export_clicked(self):
        """导出按钮点击"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出设置", "preferences.json", "JSON文件 (*.json)"
        )

        if file_path:
            if user_preferences.export_preferences(file_path):
                QMessageBox.information(self, "导出成功", f"设置已导出到 {file_path}")
            else:
                QMessageBox.warning(self, "导出失败", "导出设置文件失败")

    def _apply_settings(self) -> bool:
        """应用设置"""
        try:
            # 界面设置
            theme = "light" if self.theme_combo.currentIndex() == 0 else "dark"
            user_preferences.set_preference("ui", "theme", theme)

            layout_modes = ["compact", "standard", "expanded"]
            layout_mode = layout_modes[self.layout_mode_combo.currentIndex()]
            user_preferences.set_preference("ui", "layout_mode", layout_mode)

            user_preferences.set_preference("ui", "show_toolbar", self.show_toolbar_check.isChecked())
            user_preferences.set_preference("ui", "show_statusbar", self.show_statusbar_check.isChecked())
            user_preferences.set_preference("ui", "font_size", self.font_size_spin.value())
            user_preferences.set_preference("ui", "font_family", self.font_family_combo.currentText())
            user_preferences.set_preference("ui", "show_tooltips", self.show_tooltips_check.isChecked())
            user_preferences.set_preference("ui", "animation_enabled", self.animation_enabled_check.isChecked())

            # 数据设置
            refresh_modes = ["manual", "auto", "scheduled"]
            refresh_mode = refresh_modes[self.refresh_mode_combo.currentIndex()]
            user_preferences.set_preference("data", "refresh_mode", refresh_mode)

            user_preferences.set_preference("data", "auto_refresh_interval", self.auto_refresh_spin.value())
            user_preferences.set_preference("data", "cache_enabled", self.cache_enabled_check.isChecked())
            user_preferences.set_preference("data", "cache_duration", self.cache_duration_spin.value())
            user_preferences.set_preference("data", "data_source_timeout", self.timeout_spin.value())
            user_preferences.set_preference("data", "retry_times", self.retry_times_spin.value())

            markets = ["all", "sh", "sz"]
            market = markets[self.default_market_combo.currentIndex()]
            user_preferences.set_preference("data", "default_market", market)
            user_preferences.set_preference("data", "show_suspended_stocks", self.show_suspended_check.isChecked())

            # 分析设置
            user_preferences.set_preference("analysis", "default_period", self.default_period_spin.value())
            user_preferences.set_preference("analysis", "min_confidence_threshold", self.confidence_threshold_spin.value())
            user_preferences.set_preference("analysis", "wyckoff_sensitivity", self.wyckoff_sensitivity_spin.value())
            user_preferences.set_preference("analysis", "rs_calculation_period", self.rs_period_spin.value())
            user_preferences.set_preference("analysis", "auto_save_results", self.auto_save_results_check.isChecked())
            user_preferences.set_preference("analysis", "show_analysis_details", self.show_analysis_details_check.isChecked())
            user_preferences.set_preference("analysis", "enable_background_analysis", self.enable_background_analysis_check.isChecked())

            # 选股设置
            user_preferences.set_preference("selection", "max_selection_count", self.max_selection_spin.value())

            strategies = ["conservative", "balanced", "aggressive"]
            strategy = strategies[self.default_strategy_combo.currentIndex()]
            user_preferences.set_preference("selection", "default_strategy", strategy)

            user_preferences.set_preference("selection", "min_market_cap", self.min_market_cap_spin.value())
            user_preferences.set_preference("selection", "max_market_cap", self.max_market_cap_spin.value())
            user_preferences.set_preference("selection", "exclude_st_stocks", self.exclude_st_check.isChecked())
            user_preferences.set_preference("selection", "exclude_new_stocks", self.exclude_new_stocks_check.isChecked())
            user_preferences.set_preference("selection", "new_stock_days_threshold", self.new_stock_days_spin.value())

            # 通知设置
            user_preferences.set_preference("notifications", "enable_notifications", self.enable_notifications_check.isChecked())
            user_preferences.set_preference("notifications", "show_success_notifications", self.show_success_check.isChecked())
            user_preferences.set_preference("notifications", "show_warning_notifications", self.show_warning_check.isChecked())
            user_preferences.set_preference("notifications", "show_error_notifications", self.show_error_check.isChecked())
            user_preferences.set_preference("notifications", "notification_duration", self.notification_duration_spin.value())
            user_preferences.set_preference("notifications", "sound_enabled", self.sound_enabled_check.isChecked())
            user_preferences.set_preference("notifications", "desktop_notifications", self.desktop_notifications_check.isChecked())

            # 保存设置
            user_preferences.save_preferences()

            # 发出信号
            self.preferences_applied.emit()

            logger.info("用户偏好设置已应用")
            return True

        except Exception as e:
            logger.error(f"应用设置失败: {e}")
            QMessageBox.warning(self, "应用失败", f"应用设置时发生错误: {e}")
            return False
