#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接池管理器
管理数据源连接的生命周期，提供连接复用、自动重连、健康检查等功能
"""

import time
import threading
from typing import Dict, Optional, Any, Callable, Type
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from queue import Queue, Empty, Full
import weakref

from .base import IDataSource, DataSourceConfig, ConnectionException
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ConnectionStatus(Enum):
    """连接状态枚举"""
    IDLE = "idle"              # 空闲状态
    ACTIVE = "active"          # 活跃状态
    ERROR = "error"           # 错误状态
    EXPIRED = "expired"       # 过期状态
    CLOSED = "closed"         # 已关闭状态


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    source: IDataSource
    status: ConnectionStatus = ConnectionStatus.IDLE
    created_time: datetime = field(default_factory=datetime.now)
    last_used_time: datetime = field(default_factory=datetime.now)
    last_error_time: Optional[datetime] = None
    error_count: int = 0
    max_error_count: int = 3
    max_idle_time: int = 300  # 最大空闲时间（秒）
    max_lifetime: int = 3600  # 最大生存时间（秒）
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        now = datetime.now()
        return (now - self.created_time).seconds > self.max_lifetime
    
    @property
    def is_idle_timeout(self) -> bool:
        """是否空闲超时"""
        now = datetime.now()
        return (now - self.last_used_time).seconds > self.max_idle_time
    
    @property
    def is_error_threshold_exceeded(self) -> bool:
        """是否超过错误阈值"""
        return self.error_count >= self.max_error_count


class ConnectionFactory:
    """连接工厂"""
    
    def __init__(self):
        self._creators: Dict[str, Callable[[DataSourceConfig], IDataSource]] = {}
    
    def register_creator(self, 
                        source_type: str, 
                        creator: Callable[[DataSourceConfig], IDataSource]) -> None:
        """
        注册连接创建器
        
        Args:
            source_type: 数据源类型
            creator: 创建器函数
        """
        self._creators[source_type] = creator
        logger.debug(f"注册连接创建器: {source_type}")
    
    def create_connection(self, source_type: str, config: DataSourceConfig) -> IDataSource:
        """
        创建连接
        
        Args:
            source_type: 数据源类型
            config: 数据源配置
            
        Returns:
            IDataSource: 数据源实例
            
        Raises:
            ValueError: 不支持的数据源类型
        """
        if source_type not in self._creators:
            raise ValueError(f"不支持的数据源类型: {source_type}")
        
        creator = self._creators[source_type]
        return creator(config)


class ConnectionPool:
    """连接池"""
    
    def __init__(self,
                 source_type: str,
                 config: DataSourceConfig,
                 min_connections: int = 1,
                 max_connections: int = 10,
                 connection_timeout: int = 30,
                 health_check_interval: int = 60):
        """
        初始化连接池
        
        Args:
            source_type: 数据源类型
            config: 数据源配置
            min_connections: 最小连接数
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
            health_check_interval: 健康检查间隔（秒）
        """
        self.source_type = source_type
        self.config = config
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.health_check_interval = health_check_interval
        
        # 连接池和管理
        self._connections: Dict[str, ConnectionInfo] = {}
        self._idle_connections: Queue = Queue(maxsize=max_connections)
        self._lock = threading.RLock()
        self._connection_counter = 0
        
        # 连接工厂
        self._factory = ConnectionFactory()
        self._register_default_creators()
        
        # 后台线程
        self._maintenance_thread = None
        self._stop_maintenance = False
        
        # 统计信息
        self._stats = {
            'total_created': 0,
            'total_destroyed': 0,
            'current_active': 0,
            'current_idle': 0,
            'total_requests': 0,
            'total_timeouts': 0,
            'total_errors': 0
        }
        
        # 初始化连接池
        self._initialize_pool()
        
        logger.info(f"连接池初始化完成: {source_type}, 最小连接数: {min_connections}, 最大连接数: {max_connections}")
    
    def _register_default_creators(self) -> None:
        """注册默认的连接创建器"""
        def create_xtdata_connection(config: DataSourceConfig) -> IDataSource:
            from .xtdata_adapter import XtDataAdapter
            return XtDataAdapter(config)
        
        self._factory.register_creator('xtdata', create_xtdata_connection)
    
    def _initialize_pool(self) -> None:
        """初始化连接池"""
        try:
            # 创建最小数量的连接
            for _ in range(self.min_connections):
                conn_info = self._create_connection()
                if conn_info:
                    self._idle_connections.put(conn_info.connection_id, block=False)
            
            # 启动维护线程
            self._start_maintenance_thread()
            
        except Exception as e:
            logger.error(f"初始化连接池失败: {e}")
            raise
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """创建新连接"""
        try:
            with self._lock:
                if len(self._connections) >= self.max_connections:
                    logger.warning("连接池已达到最大连接数")
                    return None
                
                # 生成连接ID
                self._connection_counter += 1
                connection_id = f"{self.source_type}_{self._connection_counter}_{int(time.time())}"
                
                # 创建数据源实例
                source = self._factory.create_connection(self.source_type, self.config)
                
                # 尝试连接
                if not source.connect():
                    logger.error(f"创建连接 {connection_id} 失败：无法连接到数据源")
                    return None
                
                # 创建连接信息
                conn_info = ConnectionInfo(
                    connection_id=connection_id,
                    source=source,
                    status=ConnectionStatus.IDLE
                )
                
                self._connections[connection_id] = conn_info
                self._stats['total_created'] += 1
                self._stats['current_idle'] += 1
                
                logger.debug(f"创建连接成功: {connection_id}")
                return conn_info
                
        except Exception as e:
            logger.error(f"创建连接失败: {e}")
            return None
    
    def get_connection(self, timeout: int = None) -> Optional[IDataSource]:
        """
        获取连接
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            Optional[IDataSource]: 数据源连接
        """
        if timeout is None:
            timeout = self.connection_timeout
        
        start_time = time.time()
        
        try:
            with self._lock:
                self._stats['total_requests'] += 1
            
            while time.time() - start_time < timeout:
                try:
                    # 从空闲连接队列获取连接
                    connection_id = self._idle_connections.get(timeout=1)
                    
                    with self._lock:
                        if connection_id not in self._connections:
                            continue
                        
                        conn_info = self._connections[connection_id]
                        
                        # 检查连接状态
                        if not self._is_connection_valid(conn_info):
                            self._destroy_connection(connection_id)
                            continue
                        
                        # 标记为活跃状态
                        conn_info.status = ConnectionStatus.ACTIVE
                        conn_info.last_used_time = datetime.now()
                        
                        self._stats['current_idle'] -= 1
                        self._stats['current_active'] += 1
                        
                        logger.debug(f"获取连接: {connection_id}")
                        return conn_info.source
                        
                except Empty:
                    # 队列为空，尝试创建新连接
                    conn_info = self._create_connection()
                    if conn_info:
                        with self._lock:
                            conn_info.status = ConnectionStatus.ACTIVE
                            conn_info.last_used_time = datetime.now()
                            self._stats['current_active'] += 1
                            self._stats['current_idle'] -= 1
                        
                        logger.debug(f"创建并获取新连接: {conn_info.connection_id}")
                        return conn_info.source
                    
                    # 无法创建新连接，短暂等待
                    time.sleep(0.1)
            
            # 超时
            with self._lock:
                self._stats['total_timeouts'] += 1
            
            logger.warning(f"获取连接超时: {timeout}秒")
            return None
            
        except Exception as e:
            with self._lock:
                self._stats['total_errors'] += 1
            
            logger.error(f"获取连接失败: {e}")
            return None
    
    def return_connection(self, source: IDataSource, has_error: bool = False) -> None:
        """
        归还连接
        
        Args:
            source: 数据源连接
            has_error: 是否有错误
        """
        try:
            with self._lock:
                # 查找连接信息
                conn_info = None
                for connection_id, info in self._connections.items():
                    if info.source is source:
                        conn_info = info
                        break
                
                if not conn_info:
                    logger.warning("归还的连接不在连接池中")
                    return
                
                # 更新连接状态
                if has_error:
                    conn_info.error_count += 1
                    conn_info.last_error_time = datetime.now()
                    
                    if conn_info.is_error_threshold_exceeded:
                        logger.warning(f"连接 {conn_info.connection_id} 错误次数过多，销毁连接")
                        self._destroy_connection(conn_info.connection_id)
                        return
                
                # 检查连接是否仍然有效
                if not self._is_connection_valid(conn_info):
                    self._destroy_connection(conn_info.connection_id)
                    return
                
                # 归还到空闲队列
                conn_info.status = ConnectionStatus.IDLE
                conn_info.last_used_time = datetime.now()
                
                self._stats['current_active'] -= 1
                self._stats['current_idle'] += 1
                
                try:
                    self._idle_connections.put(conn_info.connection_id, block=False)
                    logger.debug(f"归还连接: {conn_info.connection_id}")
                except Full:
                    # 队列已满，销毁连接
                    logger.debug(f"空闲队列已满，销毁连接: {conn_info.connection_id}")
                    self._destroy_connection(conn_info.connection_id)
                    
        except Exception as e:
            logger.error(f"归还连接失败: {e}")
    
    def _is_connection_valid(self, conn_info: ConnectionInfo) -> bool:
        """检查连接是否有效"""
        try:
            # 检查是否过期
            if conn_info.is_expired:
                logger.debug(f"连接 {conn_info.connection_id} 已过期")
                return False
            
            # 检查是否超过错误阈值
            if conn_info.is_error_threshold_exceeded:
                logger.debug(f"连接 {conn_info.connection_id} 错误次数过多")
                return False
            
            # 测试连接是否正常
            if not conn_info.source.test_connection():
                logger.debug(f"连接 {conn_info.connection_id} 测试失败")
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"连接 {conn_info.connection_id} 验证异常: {e}")
            return False
    
    def _destroy_connection(self, connection_id: str) -> None:
        """销毁连接"""
        try:
            with self._lock:
                if connection_id not in self._connections:
                    return
                
                conn_info = self._connections[connection_id]
                
                # 断开连接
                try:
                    conn_info.source.disconnect()
                except Exception as e:
                    logger.debug(f"断开连接 {connection_id} 时发生错误: {e}")
                
                # 更新状态
                conn_info.status = ConnectionStatus.CLOSED
                
                # 从连接池移除
                del self._connections[connection_id]
                
                # 更新统计
                self._stats['total_destroyed'] += 1
                if conn_info.status == ConnectionStatus.ACTIVE:
                    self._stats['current_active'] -= 1
                else:
                    self._stats['current_idle'] -= 1
                
                logger.debug(f"销毁连接: {connection_id}")
                
        except Exception as e:
            logger.error(f"销毁连接 {connection_id} 失败: {e}")
    
    def _start_maintenance_thread(self) -> None:
        """启动维护线程"""
        if self._maintenance_thread and self._maintenance_thread.is_alive():
            return
        
        self._stop_maintenance = False
        self._maintenance_thread = threading.Thread(
            target=self._maintenance_worker,
            daemon=True,
            name=f"ConnectionPool-{self.source_type}-Maintenance"
        )
        self._maintenance_thread.start()
        logger.info(f"连接池维护线程已启动: {self.source_type}")
    
    def _maintenance_worker(self) -> None:
        """维护工作线程"""
        while not self._stop_maintenance:
            try:
                # 清理过期和无效连接
                self._cleanup_connections()
                
                # 确保最小连接数
                self._ensure_min_connections()
                
                # 健康检查
                self._health_check()
                
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"连接池维护线程异常: {e}")
                time.sleep(10)
    
    def _cleanup_connections(self) -> None:
        """清理无效连接"""
        with self._lock:
            connections_to_destroy = []
            
            for connection_id, conn_info in self._connections.items():
                if (conn_info.status == ConnectionStatus.IDLE and 
                    (conn_info.is_expired or conn_info.is_idle_timeout or 
                     conn_info.is_error_threshold_exceeded)):
                    connections_to_destroy.append(connection_id)
            
            for connection_id in connections_to_destroy:
                self._destroy_connection(connection_id)
                
                # 从空闲队列中移除
                try:
                    temp_queue = Queue()
                    while not self._idle_connections.empty():
                        idle_id = self._idle_connections.get_nowait()
                        if idle_id != connection_id:
                            temp_queue.put(idle_id)
                    
                    while not temp_queue.empty():
                        self._idle_connections.put(temp_queue.get_nowait())
                        
                except Empty:
                    pass
    
    def _ensure_min_connections(self) -> None:
        """确保最小连接数"""
        with self._lock:
            current_connections = len(self._connections)
            if current_connections < self.min_connections:
                needed = self.min_connections - current_connections
                
                for _ in range(needed):
                    conn_info = self._create_connection()
                    if conn_info:
                        try:
                            self._idle_connections.put(conn_info.connection_id, block=False)
                        except Full:
                            break
    
    def _health_check(self) -> None:
        """健康检查"""
        with self._lock:
            for connection_id, conn_info in list(self._connections.items()):
                if conn_info.status == ConnectionStatus.IDLE:
                    try:
                        if not conn_info.source.test_connection():
                            logger.warning(f"连接 {connection_id} 健康检查失败")
                            conn_info.error_count += 1
                            conn_info.last_error_time = datetime.now()
                    except Exception as e:
                        logger.warning(f"连接 {connection_id} 健康检查异常: {e}")
                        conn_info.error_count += 1
                        conn_info.last_error_time = datetime.now()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                'source_type': self.source_type,
                'min_connections': self.min_connections,
                'max_connections': self.max_connections,
                'current_total': len(self._connections),
                'idle_queue_size': self._idle_connections.qsize()
            })
            return stats
    
    def close(self) -> None:
        """关闭连接池"""
        logger.info(f"开始关闭连接池: {self.source_type}")
        
        # 停止维护线程
        self._stop_maintenance = True
        if self._maintenance_thread:
            self._maintenance_thread.join(timeout=5)
        
        # 关闭所有连接
        with self._lock:
            for connection_id in list(self._connections.keys()):
                self._destroy_connection(connection_id)
            
            # 清空队列
            while not self._idle_connections.empty():
                try:
                    self._idle_connections.get_nowait()
                except Empty:
                    break
        
        logger.info(f"连接池已关闭: {self.source_type}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        self._pools: Dict[str, ConnectionPool] = {}
        self._lock = threading.Lock()
        logger.info("连接池管理器初始化完成")
    
    def create_pool(self,
                   pool_name: str,
                   source_type: str,
                   config: DataSourceConfig,
                   min_connections: int = 1,
                   max_connections: int = 10,
                   **kwargs) -> ConnectionPool:
        """
        创建连接池
        
        Args:
            pool_name: 连接池名称
            source_type: 数据源类型
            config: 数据源配置
            min_connections: 最小连接数
            max_connections: 最大连接数
            **kwargs: 其他参数
            
        Returns:
            ConnectionPool: 连接池实例
        """
        with self._lock:
            if pool_name in self._pools:
                logger.warning(f"连接池 {pool_name} 已存在，将关闭旧连接池")
                self._pools[pool_name].close()
            
            pool = ConnectionPool(
                source_type=source_type,
                config=config,
                min_connections=min_connections,
                max_connections=max_connections,
                **kwargs
            )
            
            self._pools[pool_name] = pool
            logger.info(f"创建连接池: {pool_name}")
            return pool
    
    def get_pool(self, pool_name: str) -> Optional[ConnectionPool]:
        """
        获取连接池
        
        Args:
            pool_name: 连接池名称
            
        Returns:
            Optional[ConnectionPool]: 连接池实例
        """
        with self._lock:
            return self._pools.get(pool_name)
    
    def close_pool(self, pool_name: str) -> bool:
        """
        关闭连接池
        
        Args:
            pool_name: 连接池名称
            
        Returns:
            bool: 是否成功关闭
        """
        with self._lock:
            if pool_name not in self._pools:
                logger.warning(f"连接池 {pool_name} 不存在")
                return False
            
            self._pools[pool_name].close()
            del self._pools[pool_name]
            logger.info(f"关闭连接池: {pool_name}")
            return True
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池统计信息"""
        with self._lock:
            return {name: pool.get_stats() for name, pool in self._pools.items()}
    
    def close_all(self) -> None:
        """关闭所有连接池"""
        logger.info("开始关闭所有连接池")
        
        with self._lock:
            for pool_name, pool in self._pools.items():
                try:
                    pool.close()
                    logger.debug(f"连接池 {pool_name} 已关闭")
                except Exception as e:
                    logger.error(f"关闭连接池 {pool_name} 失败: {e}")
            
            self._pools.clear()
        
        logger.info("所有连接池已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_all()