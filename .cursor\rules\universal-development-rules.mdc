---
description: 通用开发规则 - 整合Python开发规范与桌面应用开发最佳实践
globs:
alwaysApply: true
---

# 通用开发规则

## 项目偏好设置
- 优先使用成熟稳定的技术栈，避免重复造轮子
- 代码质量优于开发速度，注重长期可维护性
- 遵循语言特定的最佳实践（Python遵循PEP 8，JavaScript遵循ESLint等）
- 模块化设计，单一职责原则，高内聚低耦合
- 优先使用包管理器进行依赖管理，避免手动编辑配置文件
- 智能调用AI辅助工具，减少重复性工作
- 主动承担代码编写任务，无需用户反复督促
- **UI设计理念**：协调性优先，创意无限制 - 充分发挥AI设计优势，创造炫酷现代的视觉效果
- **设计自由度**：在整体协调基础上，给予最大创意空间，不受传统色彩束缚

## 核心开发原则

### 代码质量标准
- 所有函数和类必须有清晰的文档字符串
- 实现全面的错误处理和用户友好的错误信息
- 使用类型提示（适用的语言）
- 保持一致的命名约定：
  * Python: snake_case（函数、变量）、PascalCase（类）、UPPER_SNAKE_CASE（常量）
  * JavaScript: camelCase（函数、变量）、PascalCase（类、组件）
  * 文件名：小写+下划线或连字符
- 单个文件不超过1000行，超出时按功能模块拆分

### 项目结构规范示例
```
project_name/              # 项目根目录（用户自定义的项目名称）
├── src/                   # 源代码目录
│   ├── core/              # 核心业务逻辑
│   ├── ui/                # 用户界面（如适用）
│   ├── data/              # 数据处理层
│   ├── utils/             # 工具函数
│   ├── config/            # 配置管理
│   └── resources/         # 静态资源
├── tests/                 # 测试代码
│   ├── unit/              # 单元测试
│   └── integration/       # 集成测试
├── docs/                  # 文档目录
├── scripts/               # 构建和工具脚本
├── config/                # 配置文件
├── logs/                  # 日志文件（不纳入版本控制）
├── README.md              # 项目说明
├── .gitignore             # 版本控制忽略规则
└── requirements.txt       # 依赖列表（语言特定）
```

## Python项目特定规则

### 环境管理
- 必须使用虚拟环境，目录名为 `.venv`
- 创建命令：`python -m venv .venv`
- 激活命令：Windows `venv\Scripts\activate`，Linux/macOS `source venv/bin/activate`
- 升级pip：`python -m pip install --upgrade pip`

### 依赖管理
- 生产依赖：`requirements.txt`
- 开发依赖：`requirements-dev.txt`
- 精确版本锁定：`package==1.2.3`
- 兼容版本范围：`package>=1.2.0,<2.0.0`
- 使用pip安装：`pip install -r requirements.txt`

### 代码规范示例
```python
def process_data(data: list, options: dict = None) -> dict:
    """
    处理数据的主要函数

    Args:
        data (list): 待处理的数据列表
        options (dict, optional): 处理选项. Defaults to None.

    Returns:
        dict: 处理结果，包含状态和数据

    Raises:
        ValueError: 当数据格式不正确时抛出

    Example:
        >>> result = process_data([1, 2, 3], {'method': 'sum'})
        >>> print(result['status'])
        'success'
    """
    if options is None:
        options = {}

    try:
        # 业务逻辑实现
        return {"status": "success", "data": processed_data}
    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        raise ValueError(f"无效的数据格式: {e}")
```

### 测试规范
- 使用pytest框架
- 测试文件以 `test_` 开头
- 测试函数以 `test_` 开头
- 测试类以 `Test` 开头
- 测试文件智能放至于 `tests/unit` 或 `tests/integration`
- 目标覆盖率：80%以上
- 运行命令：`pytest --cov=src --cov-report=html`

## 桌面应用开发规则

### UI设计原则
- **设计理念**：协调性优先，创意无限制 - 在整体视觉协调的基础上充分发挥设计创意
- **色彩基调**：天空蓝 (#87CEEB) 作为整体协调的参考色调，但不限制具体设计发挥
- **设计自由度**：界面设计、图标设计等享有最大创意自由，鼓励丰富多元、绚丽多彩的表现
- **视觉效果**：追求炫酷、现代化、高度专业的视觉冲击力，展现最佳设计水准
- **用户体验**：适合中国用户习惯，支持明暗模式切换和高DPI适配
- **创新鼓励**：不受传统设计束缚，充分发挥AI设计优势，创造独特视觉体验

### 技术栈选择
- Python桌面应用：PyQt/PySide + QTabWidget、Tkinter或其他适合Windows平台的GUI框架开发图形化操作界面
- Web支持：PyQt-WebEngine (QWebEngineView)
- 数据库：SQLAlchemy + SQLite
- 打包工具：PyInstaller
- 网络请求：requests
- GUI 框架：推荐使用 PyQt，也可使用 Tkinter、PySide 等，按照需求智能选择
备注：涉及技术栈PyQt的情况优先考虑PyQt5，因为兼容性更好。

### 界面组件规范
- **布局设计**：元素间距设置合理，组件之间设计紧凑方便小尺寸屏幕显示，响应式布局适配不同屏幕尺寸
- **组件尺寸**：按钮可根据内容和设计需要灵活调整，按钮上必须添加图标或者文字符合描述
- **视觉风格**：现代化圆角设计，支持渐变、阴影、透明度等丰富视觉效果
- **视觉风格**：现代化圆角设计（建议根据界面尺寸和内容密度智能调整），支持渐变、阴影、透明度等丰富视觉效果，确保在不同分辨率和设备上保持视觉协调性
- **字体系统**：思源黑体变体或其他现代字体，支持多层级字体设计，字体大小应根据不同平台和显示设备智能调整，确保最佳可读性和视觉效果
- **交互动效**：涟漪效果、悬停微光、流畅动画，鼓励创新的交互反馈设计
- **交互设计**：提供清晰的操作指引，支持数据预览、结果导出（如导出为 CSV 文件）等功能
- **色彩运用**：在整体协调基础上，充分发挥色彩创意，支持渐变、多色搭配、动态色彩变化

### 图标与视觉资源设计
- **设计自由度**：图标设计享有最大创意自由，不受天空蓝主题色限制
- **色彩表现**：鼓励丰富多彩、绚丽多元的图标设计，追求视觉冲击力和现代感
- **设计风格**：支持渐变色、3D立体感、发光效果、金属质感、玻璃质感等现代设计元素
- **创新鼓励**：充分发挥AI设计优势，创造独特、炫酷、专业的视觉效果
- **技术规范**：图标路径 `resources/icons/`，支持SVG、PNG等多种格式，优先使用矢量图标
- **样式管理**：样式文件 `resources/themes/style.qss`，支持动态主题切换和自定义样式
- **资源加载**：使用相对路径或动态路径，确保跨平台兼容性和资源的正确加载

## 版本控制规范

### Git工作流
- 主分支：main（生产）、develop（开发）
- 功能分支：feature/功能名称
- 修复分支：hotfix/问题描述、bugfix/问题描述

### 提交信息格式
```
<类型>(<范围>): <描述>

类型：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- build: 构建相关
- chore: 其他杂项

示例：
feat(user): 添加用户注册功能
fix(ui): 修复按钮点击无响应问题
docs(readme): 更新安装指南
```

### .gitignore配置
```
# Python
__pycache__/
*.py[cod]
*.so
.Python
build/
dist/
*.egg-info/

# 虚拟环境
.venv/
venv/
ENV/

# IDE
.vscode/
.idea/
*.swp

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 日志和临时文件
*.log
logs/
temp/
cache/
```

## Web应用开发规则

### 前端开发
- 使用组件化架构（React、Vue、Angular）
- 实现响应式设计原则
- 确保跨浏览器兼容性
- 性能优化：懒加载、代码分割
- 遵循无障碍设计指南（WCAG）

### 后端开发
- 实现RESTful API设计或GraphQL
- 使用适当的数据库技术
- 实现正确的身份验证和授权
- 为可扩展性和高可用性设计
- 适当时遵循微服务模式

### API设计规范
```
GET    /api/users          # 获取用户列表
GET    /api/users/{id}     # 获取特定用户
POST   /api/users          # 创建新用户
PUT    /api/users/{id}     # 更新用户
DELETE /api/users/{id}     # 删除用户

响应格式：
{
  "status": "success|error",
  "data": {...},
  "message": "操作结果描述",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 数据库设计规范

### 命名约定
- 表名：复数形式，小写+下划线（users, user_profiles）
- 字段名：小写+下划线（user_name, created_at）
- 主键：统一使用 `id`
- 外键：`表名_id`（user_id, order_id）
- 索引：`idx_表名_字段名`

### 设计原则
- 遵循第三范式，避免数据冗余
- 合理使用索引，平衡查询性能和写入性能
- 设置适当的约束和默认值
- 考虑数据迁移和版本控制
- 实现软删除（deleted_at字段）

## 错误处理和日志

### 错误处理策略
```python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class BusinessError(Exception):
    """业务逻辑错误"""
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(message)

def handle_operation(data: dict) -> dict:
    """标准错误处理模式"""
    try:
        # 输入验证
        if not data:
            raise BusinessError("输入数据不能为空", "INVALID_INPUT")

        # 业务逻辑
        result = process_business_logic(data)

        logger.info(f"操作成功完成: {result.get('id')}")
        return {"status": "success", "data": result}

    except BusinessError as e:
        logger.warning(f"业务错误: {e.message}")
        return {"status": "error", "message": e.message, "code": e.code}
    except Exception as e:
        logger.error(f"系统错误: {str(e)}", exc_info=True)
        return {"status": "error", "message": "系统内部错误"}
```

## 测试和质量保证

### 测试策略
- 单元测试：测试独立功能模块
- 集成测试：测试模块间交互
- 端到端测试：测试完整用户流程
- 性能测试：关键功能性能验证

### 代码质量检查
- 自动化代码格式化（Black for Python）
- 代码风格检查（flake8, pylint）
- 类型检查（mypy for Python）
- 安全漏洞扫描
- 依赖项安全检查

### 文档要求
- README.md：项目概述、安装、使用指南
- API文档：详细的接口说明和示例
- 开发文档：环境设置、贡献指南
- 用户手册：功能说明和操作指导
- 更新日志：版本变更记录

## 性能和安全

### 性能优化
- 懒加载非关键组件
- 异步处理长时间运行的任务
- 实现适当的缓存策略
- 监控内存使用，防止内存泄漏
- 数据库查询优化

### 安全最佳实践
- 输入验证和数据清理
- 安全的数据存储和传输
- 适当的身份验证和授权
- 定期安全审计和漏洞评估
- 依赖项安全扫描

## 部署和维护

### 构建和部署
- 自动化构建流程
- 环境特定的配置管理
- 版本控制和发布管理
- 回滚策略和灾难恢复

### 日常维护
- 定期依赖项更新
- 代码质量审查和重构
- 性能监控和优化
- 文档更新和维护
- 用户反馈收集和处理

## 团队协作

### 代码审查
- 所有代码变更需要同行审查
- 关注代码正确性、可维护性和设计
- 提供建设性反馈和具体建议
- 主分支合并前必须通过审查

### 知识管理
- 定期技术分享和代码审查会议
- 维护项目wiki和知识库
- 新成员入职指导
- 技术决策文档化

## 配置管理

### 环境配置
```python
import os
from typing import Optional

class Config:
    """应用配置管理"""

    # 数据库配置
    DATABASE_URL: str = os.getenv('DATABASE_URL', 'sqlite:///app.db')

    # 应用配置
    DEBUG: bool = os.getenv('DEBUG', 'False').lower() == 'true'
    SECRET_KEY: str = os.getenv('SECRET_KEY', 'dev-secret-key')

    # 第三方服务
    API_KEY: Optional[str] = os.getenv('API_KEY')

    # 日志配置
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')

    @classmethod
    def validate(cls):
        """验证必要的配置项"""
        required_vars = ['SECRET_KEY']
        missing = [var for var in required_vars if not getattr(cls, var)]
        if missing:
            raise ValueError(f"缺少必要的环境变量: {missing}")
```

## 常用工具和命令

### 开发环境快速设置
```bash
# 创建并激活虚拟环境
python -m venv .venv
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# 升级pip并安装依赖
python -m pip install --upgrade pip
pip install -r requirements-dev.txt

# 运行质量检查
python -m black src/
python -m flake8 src/
python -m pytest --cov=src

# 生成依赖文件
pip freeze > requirements.txt
```

### Git工作流命令
```bash
# 创建功能分支
git checkout -b feature/新功能名称

# 提交代码
git add .
git commit -m "feat(模块): 添加新功能描述"

# 推送并创建PR
git push origin feature/新功能名称

# 合并到主分支
git checkout main
git merge feature/新功能名称
git push origin main
```

## 自动化脚本

### 项目初始化脚本
```python
#!/usr/bin/env python3
"""项目初始化脚本"""

import os
import subprocess
import sys
from pathlib import Path

def create_project_structure():
    """创建项目目录结构"""
    directories = [
        'src/core', 'src/ui', 'src/data', 'src/utils', 'src/config', 'src/resources',
        'tests/unit', 'tests/integration', 'docs', 'scripts', 'config', 'logs'
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")

def setup_virtual_environment():
    """设置虚拟环境"""
    if not Path('.venv').exists():
        subprocess.run([sys.executable, '-m', 'venv', '.venv'])
        print("虚拟环境创建完成")
    else:
        print("虚拟环境已存在")

def install_dependencies():
    """安装依赖"""
    if Path('requirements.txt').exists():
        subprocess.run(['.venv/Scripts/python', '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("依赖安装完成")

if __name__ == "__main__":
    print("开始项目初始化...")
    create_project_structure()
    setup_virtual_environment()
    install_dependencies()
    print("项目初始化完成！")
```

### 代码质量检查脚本
```python
#!/usr/bin/env python3
"""代码质量检查脚本"""

import subprocess
import sys

def run_command(command: list, description: str) -> bool:
    """运行命令并返回是否成功"""
    print(f"执行: {description}")
    result = subprocess.run(command, capture_output=True, text=True)

    if result.returncode == 0:
        print(f"✓ {description} 通过")
        return True
    else:
        print(f"✗ {description} 失败")
        print(result.stdout)
        print(result.stderr)
        return False

def main():
    """主函数"""
    checks = [
        (['python', '-m', 'black', '--check', 'src/'], "代码格式检查"),
        (['python', '-m', 'flake8', 'src/'], "代码风格检查"),
        (['python', '-m', 'mypy', 'src/'], "类型检查"),
        (['python', '-m', 'pytest', '--cov=src'], "测试覆盖率检查"),
    ]

    all_passed = True
    for command, description in checks:
        if not run_command(command, description):
            all_passed = False

    if all_passed:
        print("\n🎉 所有质量检查通过！")
        sys.exit(0)
    else:
        print("\n❌ 存在质量问题，请修复后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

## 质量检查清单

### 代码提交前检查
- [ ] 所有函数有完整的文档字符串
- [ ] 错误处理已实现
- [ ] 代码遵循风格指南
- [ ] 无明显安全漏洞
- [ ] 性能考虑已评估

### 测试和文档检查
- [ ] 单元测试已编写并通过（80%+覆盖率）
- [ ] 关键工作流的集成测试
- [ ] README包含清晰的设置说明
- [ ] API文档完整
- [ ] 用户指南和故障排除文档

### 项目结构检查
- [ ] 逻辑目录组织
- [ ] 适当的关注点分离
- [ ] 配置外部化
- [ ] 资源正确组织
- [ ] 构建和部署脚本

### 部署就绪检查
- [ ] 环境设置已文档化
- [ ] 依赖正确管理
- [ ] CI/CD管道已配置
- [ ] 安全扫描已实施
- [ ] 性能监控已就位
