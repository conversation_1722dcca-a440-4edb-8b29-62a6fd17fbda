#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
威科夫相对强弱选股系统 - 核心引擎演示

演示威科夫分析、相对强弱计算和选股策略引擎的核心功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.engines.wyckoff import WyckoffAnalysisEngine, MarketPhase
from src.engines.relative_strength import RelativeStrengthEngine, TimeFrame
from src.engines.selection import (
    StockSelectionEngine, StrategyConfig, StrategyType,
    WyckoffStrategy, RelativeStrengthStrategy, SelectionCriteria
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


def create_sample_data():
    """创建示例数据"""
    print("📊 创建示例股票数据...")
    
    dates = pd.date_range(start='2023-01-01', periods=300, freq='D')
    
    # 创建不同表现的股票数据
    stocks_data = {}
    
    # 强势股票 - 持续上涨
    strong_prices = [100 * (1.002 ** i) for i in range(300)]
    stocks_data['强势股A'] = pd.DataFrame({
        'open': strong_prices,
        'high': [p * 1.015 for p in strong_prices],
        'low': [p * 0.985 for p in strong_prices],
        'close': strong_prices,
        'volume': np.random.randint(2000000, 8000000, 300)
    }, index=dates)
    
    # 弱势股票 - 下跌趋势
    weak_prices = [100 * (0.9995 ** i) for i in range(300)]
    stocks_data['弱势股B'] = pd.DataFrame({
        'open': weak_prices,
        'high': [p * 1.01 for p in weak_prices],
        'low': [p * 0.99 for p in weak_prices],
        'close': weak_prices,
        'volume': np.random.randint(1000000, 4000000, 300)
    }, index=dates)
    
    # 震荡股票 - 横盘整理
    oscillating_prices = [100 + 10 * np.sin(i * 0.1) for i in range(300)]
    stocks_data['震荡股C'] = pd.DataFrame({
        'open': oscillating_prices,
        'high': [p * 1.02 for p in oscillating_prices],
        'low': [p * 0.98 for p in oscillating_prices],
        'close': oscillating_prices,
        'volume': np.random.randint(1500000, 6000000, 300)
    }, index=dates)
    
    # 基准指数 - 稳定上涨
    benchmark_prices = [100 * (1.001 ** i) for i in range(300)]
    benchmark_data = pd.DataFrame({
        'open': benchmark_prices,
        'high': [p * 1.01 for p in benchmark_prices],
        'low': [p * 0.99 for p in benchmark_prices],
        'close': benchmark_prices,
        'volume': np.random.randint(10000000, 50000000, 300)
    }, index=dates)
    
    print(f"✅ 创建了 {len(stocks_data)} 只股票的数据，时间跨度：{len(dates)} 天")
    return stocks_data, benchmark_data


def demo_wyckoff_analysis(stocks_data):
    """演示威科夫分析"""
    print("\n🔍 威科夫分析引擎演示")
    print("=" * 50)
    
    engine = WyckoffAnalysisEngine()
    
    for symbol, data in stocks_data.items():
        print(f"\n📈 分析股票：{symbol}")
        
        try:
            # 市场结构分析
            structure = engine.analyze_market_structure(data)
            print(f"  市场阶段：{structure.phase.value}")
            print(f"  价格趋势：{structure.price_trend}")
            print(f"  成交量趋势：{structure.volume_trend}")
            print(f"  支撑位：{structure.support_level:.2f}")
            print(f"  阻力位：{structure.resistance_level:.2f}")
            print(f"  置信度：{structure.confidence:.2%}")
            
            # 威科夫信号检测
            signals = engine.detect_wyckoff_signals(data)
            print(f"  检测到信号数量：{len(signals)}")
            
            # 量价关系分析
            vp_analysis = engine.analyze_volume_price_relationship(data)
            correlation = vp_analysis.get('volume_price_correlation', 0)
            if not pd.isna(correlation):
                print(f"  量价相关性：{correlation:.3f}")
            
        except Exception as e:
            print(f"  ❌ 分析失败：{e}")


def demo_relative_strength(stocks_data, benchmark_data):
    """演示相对强弱分析"""
    print("\n📊 相对强弱分析引擎演示")
    print("=" * 50)
    
    engine = RelativeStrengthEngine()
    
    # 计算各股票的RS值
    print("\n🏆 相对强弱排名：")
    rs_results = engine.rank_stocks_by_rs(stocks_data, benchmark_data, period=100)
    
    for i, result in enumerate(rs_results, 1):
        print(f"  {i}. {result.symbol}")
        print(f"     RS值：{result.rs_value:.3f}")
        print(f"     排名：第{result.rs_rank}名 (前{result.rs_percentile:.1f}%)")
        print(f"     动量分数：{result.momentum_score:.3f}")
        print(f"     趋势强度：{result.trend_strength:.3f}")
    
    # 多时间周期分析
    print(f"\n📅 多时间周期RS分析 (以{list(stocks_data.keys())[0]}为例)：")
    first_stock_data = list(stocks_data.values())[0]
    multi_rs = engine.calculate_multi_timeframe_rs(first_stock_data, benchmark_data)
    
    for timeframe, rs_value in multi_rs.items():
        print(f"  {timeframe.value}：{rs_value:.3f}")


def demo_stock_selection(stocks_data, benchmark_data):
    """演示选股策略"""
    print("\n🎯 选股策略引擎演示")
    print("=" * 50)
    
    # 创建选股引擎
    engine = StockSelectionEngine()
    engine.set_benchmark(benchmark_data)
    
    # 添加威科夫策略
    wyckoff_config = StrategyConfig(
        name="威科夫策略",
        strategy_type=StrategyType.WYCKOFF_ACCUMULATION,
        weight=0.6,
        parameters={'min_score': 0.3}
    )
    wyckoff_strategy = WyckoffStrategy(wyckoff_config)
    engine.add_strategy(wyckoff_strategy)
    
    # 添加相对强弱策略
    rs_config = StrategyConfig(
        name="相对强弱策略",
        strategy_type=StrategyType.RELATIVE_STRENGTH,
        weight=0.4,
        parameters={'min_rs_score': 0.4}
    )
    rs_strategy = RelativeStrengthStrategy(rs_config)
    engine.add_strategy(rs_strategy)
    
    print(f"📋 已配置策略：")
    print(f"  - 威科夫策略 (权重: 60%)")
    print(f"  - 相对强弱策略 (权重: 40%)")
    
    # 执行选股
    print(f"\n🔍 执行选股分析...")
    try:
        results = engine.select_stocks(
            stocks_data,
            selection_criteria=SelectionCriteria.TOP_PERCENTILE,
            max_selections=3
        )
        
        print(f"\n🏅 选股结果 (共选出{len(results)}只股票)：")
        for i, result in enumerate(results, 1):
            print(f"\n  {i}. {result.symbol}")
            print(f"     综合得分：{result.score:.3f}")
            print(f"     排名：第{result.rank}名")
            print(f"     策略得分：")
            for strategy_name, score in result.strategy_scores.items():
                print(f"       - {strategy_name}：{score:.3f}")
            print(f"     选股理由：{result.selection_reason}")
        
        # 回测演示
        print(f"\n📈 策略回测演示...")
        metrics = engine.backtest_strategy(
            stocks_data,
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
        
        print(f"  总收益率：{metrics.total_return:.2%}")
        print(f"  夏普比率：{metrics.sharpe_ratio:.2f}")
        print(f"  最大回撤：{metrics.max_drawdown:.2%}")
        print(f"  胜率：{metrics.win_rate:.2%}")
        
    except Exception as e:
        print(f"  ❌ 选股失败：{e}")


def main():
    """主演示函数"""
    print("🚀 威科夫相对强弱选股系统 - 核心引擎演示")
    print("=" * 60)
    print("本演示展示系统的三大核心引擎：")
    print("1. 威科夫分析引擎 - 市场结构和信号分析")
    print("2. 相对强弱引擎 - RS值计算和排名")
    print("3. 选股策略引擎 - 多策略组合选股")
    print("=" * 60)
    
    try:
        # 创建示例数据
        stocks_data, benchmark_data = create_sample_data()
        
        # 演示各个引擎
        demo_wyckoff_analysis(stocks_data)
        demo_relative_strength(stocks_data, benchmark_data)
        demo_stock_selection(stocks_data, benchmark_data)
        
        print("\n✅ 演示完成！")
        print("\n📝 总结：")
        print("- 威科夫分析引擎成功识别了市场阶段和趋势")
        print("- 相对强弱引擎准确计算了RS值和排名")
        print("- 选股策略引擎整合多个策略进行智能选股")
        print("- 系统具备完整的分析、计算和决策能力")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"\n❌ 演示失败：{e}")


if __name__ == "__main__":
    main()
