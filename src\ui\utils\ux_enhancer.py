"""
用户体验增强工具

提供用户界面交互优化功能，包括：
- 操作反馈增强
- 加载状态管理
- 错误提示优化
- 操作向导系统
"""

from typing import Optional, Callable, Any, Dict
from PyQt6.QtWidgets import (
    QWidget, QMessageBox, QProgressDialog, QLabel, QVBoxLayout,
    QHBoxLayout, QPushButton, QFrame, QApplication, QToolTip
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap, QIcon
import time
from ...utils.logger import get_logger
from .user_friendly_errors import UserFriendlyErrorDialog
from .operation_feedback import OperationFeedbackManager

logger = get_logger(__name__)


class NotificationLevel:
    """通知级别"""
    SUCCESS = "success"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class UXNotification(QFrame):
    """用户体验通知控件"""
    
    def __init__(self, message: str, level: str = NotificationLevel.INFO, parent: Optional[QWidget] = None):
        """
        初始化通知控件
        
        Args:
            message: 通知消息
            level: 通知级别
            parent: 父控件
        """
        super().__init__(parent)
        self.message = message
        self.level = level
        
        self._init_ui()
        self._apply_styles()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(16, 16)
        
        # 根据级别设置图标
        if self.level == NotificationLevel.SUCCESS:
            icon_label.setText("✓")
            icon_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        elif self.level == NotificationLevel.WARNING:
            icon_label.setText("⚠")
            icon_label.setStyleSheet("color: #FF9800; font-weight: bold;")
        elif self.level == NotificationLevel.ERROR:
            icon_label.setText("✗")
            icon_label.setStyleSheet("color: #F44336; font-weight: bold;")
        else:
            icon_label.setText("ℹ")
            icon_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        
        layout.addWidget(icon_label)
        
        # 消息文本
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label, 1)
        
        # 关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                border: none;
                background: transparent;
                font-weight: bold;
                color: #666;
            }
            QPushButton:hover {
                background: rgba(0, 0, 0, 0.1);
                border-radius: 10px;
            }
        """)
        close_button.clicked.connect(self.hide)
        layout.addWidget(close_button)
        
    def _apply_styles(self):
        """应用样式"""
        base_style = """
            QFrame {
                border-radius: 6px;
                border: 1px solid;
                margin: 2px;
            }
        """
        
        if self.level == NotificationLevel.SUCCESS:
            self.setStyleSheet(base_style + """
                QFrame {
                    background-color: #E8F5E8;
                    border-color: #4CAF50;
                }
            """)
        elif self.level == NotificationLevel.WARNING:
            self.setStyleSheet(base_style + """
                QFrame {
                    background-color: #FFF3E0;
                    border-color: #FF9800;
                }
            """)
        elif self.level == NotificationLevel.ERROR:
            self.setStyleSheet(base_style + """
                QFrame {
                    background-color: #FFEBEE;
                    border-color: #F44336;
                }
            """)
        else:
            self.setStyleSheet(base_style + """
                QFrame {
                    background-color: #E3F2FD;
                    border-color: #2196F3;
                }
            """)


class LoadingOverlay(QFrame):
    """加载遮罩层"""
    
    def __init__(self, parent: Optional[QWidget] = None, message: str = "加载中..."):
        """
        初始化加载遮罩
        
        Args:
            parent: 父控件
            message: 加载消息
        """
        super().__init__(parent)
        self.message = message
        
        self._init_ui()
        self._apply_styles()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 加载动画（简单的文本动画）
        self.loading_label = QLabel("●●●")
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #2196F3;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.loading_label)
        
        # 消息标签
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        message_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin-top: 10px;
            }
        """)
        layout.addWidget(message_label)
        
        # 动画定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._animate_loading)
        self.animation_timer.start(500)
        self.animation_state = 0
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 8px;
            }
        """)
        
    def _animate_loading(self):
        """加载动画"""
        animations = ["●○○", "○●○", "○○●", "●●○", "○●●", "●○●"]
        self.loading_label.setText(animations[self.animation_state])
        self.animation_state = (self.animation_state + 1) % len(animations)
        
    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)
        if self.parent():
            # 调整大小和位置
            parent_rect = self.parent().rect()
            self.resize(200, 100)
            self.move(
                parent_rect.center().x() - self.width() // 2,
                parent_rect.center().y() - self.height() // 2
            )
            
    def hideEvent(self, event):
        """隐藏事件"""
        super().hideEvent(event)
        self.animation_timer.stop()


class UXEnhancer(QObject):
    """用户体验增强器"""
    
    def __init__(self, main_window):
        """
        初始化UX增强器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.notifications: list[UXNotification] = []
        self.loading_overlay: Optional[LoadingOverlay] = None

        # 操作反馈管理器
        self.operation_feedback = OperationFeedbackManager(main_window)
        
        logger.info("用户体验增强器初始化完成")
        
    def show_notification(self, message: str, level: str = NotificationLevel.INFO, duration: int = 3000):
        """
        显示通知
        
        Args:
            message: 通知消息
            level: 通知级别
            duration: 显示时长（毫秒）
        """
        try:
            # 创建通知控件
            notification = UXNotification(message, level, self.main_window)
            
            # 添加到主窗口
            if hasattr(self.main_window, 'centralWidget'):
                central_widget = self.main_window.centralWidget()
                if central_widget:
                    # 计算位置
                    notification.setParent(central_widget)
                    notification.resize(300, 50)
                    notification.move(
                        central_widget.width() - notification.width() - 20,
                        20 + len(self.notifications) * 60
                    )
                    notification.show()
                    
                    # 添加到列表
                    self.notifications.append(notification)
                    
                    # 自动隐藏
                    if duration > 0:
                        QTimer.singleShot(duration, lambda: self._remove_notification(notification))
                        
            logger.debug(f"显示通知: {message} ({level})")
            
        except Exception as e:
            logger.error(f"显示通知失败: {e}")
            
    def _remove_notification(self, notification: UXNotification):
        """移除通知"""
        try:
            if notification in self.notifications:
                self.notifications.remove(notification)
                notification.hide()
                notification.deleteLater()
                
                # 重新排列剩余通知
                for i, notif in enumerate(self.notifications):
                    notif.move(notif.x(), 20 + i * 60)
                    
        except Exception as e:
            logger.error(f"移除通知失败: {e}")
            
    def show_loading(self, message: str = "加载中..."):
        """显示加载遮罩"""
        try:
            if self.loading_overlay:
                self.hide_loading()
                
            self.loading_overlay = LoadingOverlay(self.main_window.centralWidget(), message)
            self.loading_overlay.show()
            
            logger.debug(f"显示加载遮罩: {message}")
            
        except Exception as e:
            logger.error(f"显示加载遮罩失败: {e}")
            
    def hide_loading(self):
        """隐藏加载遮罩"""
        try:
            if self.loading_overlay:
                self.loading_overlay.hide()
                self.loading_overlay.deleteLater()
                self.loading_overlay = None
                
            logger.debug("隐藏加载遮罩")
            
        except Exception as e:
            logger.error(f"隐藏加载遮罩失败: {e}")
            
    def show_success(self, message: str):
        """显示成功消息"""
        self.show_notification(message, NotificationLevel.SUCCESS)
        
    def show_info(self, message: str):
        """显示信息消息"""
        self.show_notification(message, NotificationLevel.INFO)
        
    def show_warning(self, message: str):
        """显示警告消息"""
        self.show_notification(message, NotificationLevel.WARNING)
        
    def show_error(self, message: str):
        """显示错误消息"""
        self.show_notification(message, NotificationLevel.ERROR)

    def show_user_friendly_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """
        显示用户友好错误对话框

        Args:
            error: 原始错误
            context: 错误上下文
        """
        try:
            UserFriendlyErrorDialog.show_error(self.main_window, error, context)
            logger.info(f"显示用户友好错误对话框: {type(error).__name__}")
        except Exception as e:
            logger.error(f"显示用户友好错误对话框失败: {e}")
            # 备用简单错误提示
            self.show_error(f"操作失败: {str(error)}")
        
    def enhance_button(self, button: QPushButton, loading_text: str = "处理中..."):
        """
        增强按钮交互
        
        Args:
            button: 要增强的按钮
            loading_text: 加载时显示的文本
        """
        original_text = button.text()
        original_enabled = button.isEnabled()
        
        def start_loading():
            button.setText(loading_text)
            button.setEnabled(False)
            
        def stop_loading():
            button.setText(original_text)
            button.setEnabled(original_enabled)
            
        # 为按钮添加加载状态方法
        button.start_loading = start_loading
        button.stop_loading = stop_loading
        
        logger.debug(f"按钮增强完成: {original_text}")
