"""
环境变量管理

管理系统环境变量、应用环境检测等
"""

import os
import sys
import platform
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger

logger = get_logger(__name__)


class EnvironmentType(Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"


@dataclass
class SystemInfo:
    """系统信息"""
    platform: str
    platform_version: str
    architecture: str
    python_version: str
    python_executable: str
    current_user: str
    hostname: str
    cpu_count: int
    total_memory_gb: float


class Environment:
    """环境管理器"""
    
    def __init__(self):
        """初始化环境管理器"""
        self._env_vars: Dict[str, str] = {}
        self._system_info: Optional[SystemInfo] = None
        self._app_dir: Optional[Path] = None
        self._data_dir: Optional[Path] = None
        self._config_dir: Optional[Path] = None
        self._log_dir: Optional[Path] = None
        
        self._load_environment()
        self._detect_system_info()
        self._setup_directories()
        
        logger.info("环境管理器初始化完成")
    
    def _load_environment(self):
        """加载环境变量"""
        # 加载系统环境变量
        self._env_vars.update(os.environ)
        
        # 加载.env文件（如果存在）
        env_file = Path(".env")
        if env_file.exists():
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                self._env_vars[key.strip()] = value.strip()
                
                logger.info(f"从.env文件加载了环境变量: {env_file}")
            except Exception as e:
                logger.error(f"加载.env文件失败: {e}")
    
    def _detect_system_info(self):
        """检测系统信息"""
        try:
            # 获取内存信息
            try:
                import psutil
                total_memory_gb = psutil.virtual_memory().total / (1024**3)
            except ImportError:
                total_memory_gb = 0.0
            
            self._system_info = SystemInfo(
                platform=platform.system(),
                platform_version=platform.version(),
                architecture=platform.machine(),
                python_version=platform.python_version(),
                python_executable=sys.executable,
                current_user=os.getenv('USERNAME', os.getenv('USER', 'unknown')),
                hostname=platform.node(),
                cpu_count=os.cpu_count() or 1,
                total_memory_gb=total_memory_gb
            )
            
            logger.info(f"系统信息检测完成: {self._system_info.platform} {self._system_info.platform_version}")
            
        except Exception as e:
            logger.error(f"检测系统信息失败: {e}")
    
    def _setup_directories(self):
        """设置应用目录"""
        try:
            # 确定应用根目录
            if getattr(sys, 'frozen', False):
                # 打包后的应用
                self._app_dir = Path(sys.executable).parent
            else:
                # 开发环境
                self._app_dir = Path(__file__).parent.parent.parent
            
            # 设置各种目录
            self._data_dir = self._app_dir / "data"
            self._config_dir = self._app_dir / "config"
            self._log_dir = self._app_dir / "logs"
            
            # 创建目录
            for directory in [self._data_dir, self._config_dir, self._log_dir]:
                directory.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"应用目录设置完成: {self._app_dir}")
            
        except Exception as e:
            logger.error(f"设置应用目录失败: {e}")

    @property
    def data_path(self) -> str:
        """获取数据目录路径"""
        return str(self._data_dir)

    @property
    def config_path(self) -> str:
        """获取配置文件路径"""
        return str(self._config_dir / "config.yaml")

    @property
    def log_path(self) -> str:
        """获取日志目录路径"""
        return str(self._log_dir)

    @property
    def app_path(self) -> str:
        """获取应用根目录路径"""
        return str(self._app_dir)

    def get_env(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """
        获取环境变量
        
        Args:
            key: 环境变量键
            default: 默认值
            
        Returns:
            环境变量值
        """
        return self._env_vars.get(key, default)
    
    def set_env(self, key: str, value: str, persist: bool = False):
        """
        设置环境变量
        
        Args:
            key: 环境变量键
            value: 环境变量值
            persist: 是否持久化到.env文件
        """
        self._env_vars[key] = value
        os.environ[key] = value
        
        if persist:
            self._save_to_env_file(key, value)
        
        logger.info(f"环境变量已设置: {key} = {value}")
    
    def get_env_int(self, key: str, default: int = 0) -> int:
        """
        获取整数型环境变量
        
        Args:
            key: 环境变量键
            default: 默认值
            
        Returns:
            整数值
        """
        try:
            value = self.get_env(key)
            return int(value) if value is not None else default
        except ValueError:
            logger.warning(f"环境变量类型转换失败: {key}, 使用默认值: {default}")
            return default
    
    def get_env_bool(self, key: str, default: bool = False) -> bool:
        """
        获取布尔型环境变量
        
        Args:
            key: 环境变量键
            default: 默认值
            
        Returns:
            布尔值
        """
        value = self.get_env(key)
        if value is None:
            return default
        
        return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
    
    def get_env_list(self, key: str, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
        """
        获取列表型环境变量
        
        Args:
            key: 环境变量键
            separator: 分隔符
            default: 默认值
            
        Returns:
            字符串列表
        """
        value = self.get_env(key)
        if value is None:
            return default or []
        
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def get_environment_type(self) -> EnvironmentType:
        """
        获取当前环境类型
        
        Returns:
            环境类型
        """
        env_type = self.get_env('ENVIRONMENT', 'development').lower()
        
        try:
            return EnvironmentType(env_type)
        except ValueError:
            logger.warning(f"未知的环境类型: {env_type}, 使用默认值: development")
            return EnvironmentType.DEVELOPMENT
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.get_environment_type() == EnvironmentType.DEVELOPMENT
    
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.get_environment_type() == EnvironmentType.TESTING
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.get_environment_type() == EnvironmentType.PRODUCTION
    
    def is_frozen(self) -> bool:
        """是否为打包后的应用"""
        return getattr(sys, 'frozen', False)
    
    def get_system_info(self) -> Optional[SystemInfo]:
        """
        获取系统信息
        
        Returns:
            系统信息
        """
        return self._system_info
    
    def get_app_dir(self) -> Optional[Path]:
        """获取应用根目录"""
        return self._app_dir
    
    def get_data_dir(self) -> Optional[Path]:
        """获取数据目录"""
        return self._data_dir
    
    def get_config_dir(self) -> Optional[Path]:
        """获取配置目录"""
        return self._config_dir
    
    def get_log_dir(self) -> Optional[Path]:
        """获取日志目录"""
        return self._log_dir
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        import tempfile
        return Path(tempfile.gettempdir())
    
    def expand_path(self, path: str) -> Path:
        """
        展开路径（支持环境变量和相对路径）
        
        Args:
            path: 路径字符串
            
        Returns:
            展开后的路径
        """
        # 展开环境变量
        expanded = os.path.expandvars(path)
        
        # 展开用户目录
        expanded = os.path.expanduser(expanded)
        
        # 转换为绝对路径
        path_obj = Path(expanded)
        if not path_obj.is_absolute() and self._app_dir:
            path_obj = self._app_dir / path_obj
        
        return path_obj.resolve()
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """
        验证依赖项
        
        Returns:
            依赖项验证结果
        """
        dependencies = {
            'PyQt6': False,
            'pandas': False,
            'numpy': False,
            'pyyaml': False,
            'loguru': False,
            'psutil': False,
        }
        
        for package in dependencies:
            try:
                __import__(package.lower())
                dependencies[package] = True
            except ImportError:
                logger.warning(f"依赖项未安装: {package}")
        
        return dependencies
    
    def get_environment_summary(self) -> Dict[str, Any]:
        """
        获取环境摘要信息
        
        Returns:
            环境摘要
        """
        return {
            'environment_type': self.get_environment_type().value,
            'is_frozen': self.is_frozen(),
            'system_info': self._system_info.__dict__ if self._system_info else None,
            'directories': {
                'app_dir': str(self._app_dir) if self._app_dir else None,
                'data_dir': str(self._data_dir) if self._data_dir else None,
                'config_dir': str(self._config_dir) if self._config_dir else None,
                'log_dir': str(self._log_dir) if self._log_dir else None,
            },
            'dependencies': self.validate_dependencies(),
            'important_env_vars': {
                'PATH': self.get_env('PATH', ''),
                'PYTHONPATH': self.get_env('PYTHONPATH', ''),
                'TEMP': self.get_env('TEMP', ''),
                'TMP': self.get_env('TMP', ''),
            }
        }
    
    def _save_to_env_file(self, key: str, value: str):
        """保存环境变量到.env文件"""
        try:
            env_file = Path(".env")
            
            # 读取现有内容
            lines = []
            if env_file.exists():
                with open(env_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            
            # 更新或添加环境变量
            key_line = f"{key}={value}\n"
            found = False
            
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{key}="):
                    lines[i] = key_line
                    found = True
                    break
            
            if not found:
                lines.append(key_line)
            
            # 写回文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            logger.info(f"环境变量已保存到.env文件: {key}")
            
        except Exception as e:
            logger.error(f"保存环境变量到.env文件失败: {e}")
    
    def check_permissions(self) -> Dict[str, bool]:
        """
        检查文件系统权限
        
        Returns:
            权限检查结果
        """
        permissions = {}
        
        try:
            # 检查各目录权限
            for name, directory in [
                ('app_dir', self._app_dir),
                ('data_dir', self._data_dir),
                ('config_dir', self._config_dir),
                ('log_dir', self._log_dir)
            ]:
                if directory and directory.exists():
                    permissions[f"{name}_read"] = os.access(directory, os.R_OK)
                    permissions[f"{name}_write"] = os.access(directory, os.W_OK)
                else:
                    permissions[f"{name}_read"] = False
                    permissions[f"{name}_write"] = False
            
            # 检查临时目录权限
            temp_dir = self.get_temp_dir()
            permissions['temp_dir_read'] = os.access(temp_dir, os.R_OK)
            permissions['temp_dir_write'] = os.access(temp_dir, os.W_OK)
            
        except Exception as e:
            logger.error(f"检查权限失败: {e}")
        
        return permissions
    
    def export_environment(self, export_file: str) -> bool:
        """
        导出环境信息
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            import json
            
            export_data = {
                'timestamp': str(datetime.now()),
                'environment_summary': self.get_environment_summary(),
                'permissions': self.check_permissions(),
                'all_env_vars': dict(self._env_vars)
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"环境信息导出成功: {export_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出环境信息失败: {e}")
            return False

    def ensure_paths_exist(self):
        """确保必要的目录存在"""
        try:
            # 确保数据目录存在
            os.makedirs(self.data_path, exist_ok=True)

            # 确保日志目录存在
            os.makedirs(self.log_path, exist_ok=True)

            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_path)
            os.makedirs(config_dir, exist_ok=True)

            logger.info("必要目录已确保存在")

        except Exception as e:
            logger.error(f"创建目录失败: {e}")
            raise