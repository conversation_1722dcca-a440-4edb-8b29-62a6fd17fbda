#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成模拟历史数据

为5150只股票生成365天的模拟历史K线数据，用于项目演示
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager
from src.utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="generate_mock_history",
        level="INFO",
        log_to_file=True,
        log_to_console=True
    )


def generate_stock_history(stock_code, stock_name, days=365, base_price=10.0):
    """为单只股票生成历史数据"""
    try:
        # 生成日期序列（只包含工作日）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days * 1.5)  # 多生成一些，然后筛选工作日
        
        # 生成所有日期
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # 只保留工作日（周一到周五）
        trading_days = [d for d in date_range if d.weekday() < 5][-days:]
        
        if len(trading_days) < days:
            trading_days = trading_days  # 如果不够就用现有的
        
        # 生成价格数据
        prices = []
        current_price = base_price
        
        for i, date in enumerate(trading_days):
            # 模拟价格波动（随机游走 + 趋势）
            daily_return = np.random.normal(0.001, 0.02)  # 平均0.1%日收益，2%波动率
            
            # 添加一些趋势和周期性
            trend = 0.0001 * np.sin(i / 50)  # 长期趋势
            seasonal = 0.0005 * np.sin(i / 10)  # 短期波动
            
            current_price *= (1 + daily_return + trend + seasonal)
            current_price = max(current_price, 1.0)  # 价格不能低于1元
            
            # 生成OHLC数据
            volatility = current_price * 0.03  # 日内波动3%
            
            open_price = current_price + np.random.normal(0, volatility * 0.5)
            close_price = current_price + np.random.normal(0, volatility * 0.5)
            
            high_price = max(open_price, close_price) + abs(np.random.normal(0, volatility * 0.3))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, volatility * 0.3))
            
            # 确保价格逻辑正确
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # 生成成交量（基于价格变化）
            price_change = abs(close_price - open_price) / open_price
            base_volume = random.randint(100000, 1000000)  # 基础成交量
            volume = int(base_volume * (1 + price_change * 5))  # 价格变化大时成交量大
            
            # 计算成交额
            amount = volume * (open_price + close_price) / 2
            
            prices.append({
                'trade_date': date.strftime('%Y-%m-%d'),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(amount, 2),
                'change': round(close_price - open_price, 2),
                'change_pct': round((close_price - open_price) / open_price * 100, 2)
            })
            
            current_price = close_price
        
        return prices
        
    except Exception as e:
        logger.error(f"生成 {stock_code} 历史数据失败: {e}")
        return []


def progress_callback(current, total, stock_code):
    """进度回调"""
    progress = (current / total) * 100
    print(f"\r📊 生成历史数据进度: {progress:.1f}% ({current}/{total}) 当前: {stock_code}", end='', flush=True)


def main():
    """主函数"""
    try:
        print("🚀 开始生成模拟历史数据...")
        
        # 设置日志
        setup_logging()
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 获取所有股票
        print("📋 获取股票列表...")
        stocks = db_manager.get_all_stocks()
        
        if not stocks:
            print("❌ 数据库中没有股票数据，请先运行基本信息下载")
            return False
        
        print(f"✅ 获取到 {len(stocks)} 只股票")
        
        # 生成历史数据
        print("📈 开始生成历史数据...")
        success_count = 0
        error_count = 0
        
        for i, stock in enumerate(stocks):
            stock_code = stock.get('stock_code', '')
            stock_name = stock.get('stock_name', '')
            
            # 显示进度
            progress_callback(i + 1, len(stocks), stock_code)
            
            try:
                # 检查是否已有历史数据
                existing_count = db_manager.get_stock_quote_count(stock_code)
                if existing_count > 0:
                    continue  # 跳过已有数据的股票
                
                # 生成基础价格（根据股票代码）
                code_num = int(''.join(filter(str.isdigit, stock_code[:6])))
                base_price = 5 + (code_num % 100) * 0.5  # 5-55元之间
                
                # 生成历史数据
                history_data = generate_stock_history(
                    stock_code, 
                    stock_name, 
                    days=365, 
                    base_price=base_price
                )
                
                if history_data:
                    # 批量存储到数据库
                    if db_manager.insert_stock_quotes_batch(stock_code, history_data):
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
                
                # 每100只股票提交一次
                if (i + 1) % 100 == 0:
                    print(f"\n💾 已处理 {i + 1} 只股票，成功: {success_count}, 错误: {error_count}")
                
            except Exception as e:
                logger.error(f"处理股票 {stock_code} 失败: {e}")
                error_count += 1
                continue
        
        print(f"\n{'='*60}")
        print(f"🎉 历史数据生成完成！")
        print(f"   - 总股票数: {len(stocks)}")
        print(f"   - 成功生成: {success_count}")
        print(f"   - 失败数量: {error_count}")
        print(f"   - 成功率: {success_count/len(stocks)*100:.1f}%")
        
        # 更新数据库统计
        stats = db_manager.get_database_stats()
        print(f"\n📊 数据库统计:")
        print(f"   - 股票数量: {stats.get('total_stocks', 0):,}")
        print(f"   - 行情记录: {stats.get('total_quotes', 0):,}")
        print(f"   - 数据库大小: {stats.get('db_size_mb', 0):.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成历史数据失败: {e}")
        logger.error(f"生成历史数据失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 模拟历史数据生成完成")
        sys.exit(0)
    else:
        print("\n❌ 模拟历史数据生成失败")
        sys.exit(1)
