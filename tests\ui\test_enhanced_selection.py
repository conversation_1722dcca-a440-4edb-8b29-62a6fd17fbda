#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强选股配置界面测试
验证双重筛选工作流功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer

def test_enhanced_selection_imports():
    """测试增强选股界面导入"""
    print("🧪 测试增强选股界面导入...")
    
    try:
        from src.ui.components.enhanced_selection_widget import EnhancedSelectionWidget
        print("✅ EnhancedSelectionWidget 导入成功")
        
        from src.ui.main_window import MainWindow
        print("✅ 更新后的 MainWindow 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_enhanced_selection_ui():
    """测试增强选股界面UI"""
    print("\n🧪 测试增强选股界面UI...")
    
    app = QApplication(sys.argv)
    
    try:
        from src.ui.components.enhanced_selection_widget import EnhancedSelectionWidget
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("增强选股配置界面测试")
        window.setGeometry(100, 100, 1400, 900)
        
        # 创建中央控件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建增强选股控件
        enhanced_widget = EnhancedSelectionWidget()
        layout.addWidget(enhanced_widget)
        
        # 连接信号进行测试
        enhanced_widget.workflow_started.connect(
            lambda config: print(f"🚀 工作流开始: {config}")
        )
        enhanced_widget.workflow_completed.connect(
            lambda results: print(f"✅ 工作流完成: {results}")
        )
        enhanced_widget.sector_phase_completed.connect(
            lambda sectors: print(f"📊 板块筛选完成: {len(sectors)} 个板块")
        )
        enhanced_widget.stock_phase_completed.connect(
            lambda stocks: print(f"🎯 个股筛选完成: {len(stocks)} 只股票")
        )
        
        window.show()
        
        print("✅ 增强选股界面创建成功")
        print("📋 界面功能:")
        print("   • 双重筛选工作流控制")
        print("   • 五步筛选进度展示")
        print("   • 板块筛选 + 个股筛选集成")
        print("   • 最终结果汇总展示")
        print("   • 筛选统计和成功率计算")
        
        # 自动关闭窗口
        def close_window():
            print("⏰ 自动关闭测试窗口")
            window.close()
            app.quit()
        
        QTimer.singleShot(8000, close_window)  # 8秒后自动关闭
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ UI测试失败: {e}")
        app.quit()
        return False


def test_main_window_integration():
    """测试主窗口集成"""
    print("\n🧪 测试主窗口集成...")
    
    app = QApplication(sys.argv)
    
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ 主窗口创建成功")
        print("📋 集成功能:")
        print("   • 双重筛选工作流标签页")
        print("   • 菜单栏双重筛选选项")
        print("   • 工具栏双重筛选按钮")
        print("   • 快捷键 Ctrl+W 支持")
        print("   • 工作流状态反馈")
        
        print("\n💡 测试操作:")
        print("   - 点击菜单栏 '选股' -> '双重筛选工作流'")
        print("   - 点击工具栏 '双重筛选' 按钮")
        print("   - 使用快捷键 Ctrl+W")
        print("   - 在选股系统标签页中查看双重筛选工作流")
        
        # 自动关闭窗口
        def close_window():
            print("⏰ 自动关闭测试窗口")
            main_window.close()
            app.quit()
        
        QTimer.singleShot(10000, close_window)  # 10秒后自动关闭
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        app.quit()
        return False


def test_workflow_simulation():
    """测试工作流模拟"""
    print("\n🧪 测试工作流模拟...")
    
    app = QApplication(sys.argv)
    
    try:
        from src.ui.components.enhanced_selection_widget import EnhancedSelectionWidget
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("双重筛选工作流模拟测试")
        window.setGeometry(100, 100, 1400, 900)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        enhanced_widget = EnhancedSelectionWidget()
        layout.addWidget(enhanced_widget)
        
        # 设置详细的信号监听
        def on_workflow_started(config):
            print(f"🚀 [工作流开始] 时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"   配置: {config}")
        
        def on_sector_phase_completed(sectors):
            print(f"📊 [板块筛选完成] 时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"   强势板块数量: {len(sectors)}")
            if sectors:
                print(f"   前3个板块: {[s.get('name', 'Unknown') for s in sectors[:3]]}")
        
        def on_stock_phase_completed(stocks):
            print(f"🎯 [个股筛选完成] 时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"   候选股票数量: {len(stocks)}")
        
        def on_workflow_completed(results):
            print(f"✅ [工作流完成] 时间: {datetime.now().strftime('%H:%M:%S')}")
            print(f"   板块数量: {results.get('sector_count', 0)}")
            print(f"   最终股票数量: {results.get('final_count', 0)}")
            print(f"   筛选成功！")
        
        enhanced_widget.workflow_started.connect(on_workflow_started)
        enhanced_widget.sector_phase_completed.connect(on_sector_phase_completed)
        enhanced_widget.stock_phase_completed.connect(on_stock_phase_completed)
        enhanced_widget.workflow_completed.connect(on_workflow_completed)
        
        window.show()
        
        # 自动启动工作流测试
        def auto_start_workflow():
            print("🤖 自动启动双重筛选工作流...")
            enhanced_widget._start_workflow()
        
        QTimer.singleShot(2000, auto_start_workflow)  # 2秒后自动启动
        
        # 自动关闭窗口
        def close_window():
            print("⏰ 自动关闭测试窗口")
            window.close()
            app.quit()
        
        QTimer.singleShot(15000, close_window)  # 15秒后自动关闭
        
        print("✅ 工作流模拟测试准备就绪")
        print("⏰ 2秒后自动启动工作流...")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 工作流模拟测试失败: {e}")
        app.quit()
        return False


def main():
    """主测试函数"""
    print("🚀 第19周增强选股配置界面测试")
    print("=" * 60)
    print("验证双重筛选工作流功能和UI集成")
    print("=" * 60)
    
    test_options = {
        '1': ('测试增强选股界面导入', test_enhanced_selection_imports),
        '2': ('测试增强选股界面UI', test_enhanced_selection_ui),
        '3': ('测试主窗口集成', test_main_window_integration),
        '4': ('测试工作流模拟', test_workflow_simulation)
    }
    
    print("\n📋 可用测试选项:")
    for key, (desc, _) in test_options.items():
        print(f"   {key}. {desc}")
    
    choice = input("\n请选择测试选项 (1-4, 默认4): ").strip() or '4'
    
    if choice in test_options:
        desc, test_func = test_options[choice]
        print(f"\n🎯 执行测试: {desc}")
        print("-" * 40)
        result = test_func()
        
        if result:
            print(f"\n✅ 测试 '{desc}' 完成")
        else:
            print(f"\n❌ 测试 '{desc}' 失败")
    else:
        print("❌ 无效选项，执行默认测试")
        test_workflow_simulation()
    
    print("\n📋 第19周UI优化成果:")
    print("• ✅ 创建增强选股配置界面")
    print("• ✅ 实现双重筛选工作流")
    print("• ✅ 集成五步筛选进度展示")
    print("• ✅ 添加工作流控制和状态反馈")
    print("• ✅ 完善主窗口菜单和工具栏")


if __name__ == "__main__":
    main()
