#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用模拟XtData模块测试适配器

测试XtData适配器的逻辑，无需真实的MiniQMT客户端
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 在导入适配器之前，先将模拟模块添加到sys.modules
import mock_xtdata
sys.modules['xtdata'] = mock_xtdata

from src.data_sources.base import DataSourceConfig
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_xtdata_adapter_with_mock():
    """使用模拟数据测试XtData适配器"""
    print("=" * 60)
    print("XtData适配器功能测试（使用模拟数据）")
    print("=" * 60)
    
    # 创建配置
    config = DataSourceConfig(
        name="xtdata_mock_test",
        enabled=True,
        timeout=30,
        retry_times=3,
        auto_reconnect=True,
        config={
            'type': 'xtdata',
            'ip': '127.0.0.1',
            'port': 58610
        }
    )
    
    try:
        # 创建适配器
        print("1. 创建XtData适配器...")
        adapter = XtDataAdapter(config)
        print("✅ 适配器创建成功")
        
        # 测试连接
        print("\n2. 测试连接...")
        if adapter.connect():
            print("✅ 连接成功")
            
            # 测试获取股票列表
            print("\n3. 测试获取股票列表...")
            try:
                stock_list = adapter.get_stock_list()
                if stock_list:
                    print(f"✅ 获取到 {len(stock_list)} 只股票")
                    # 显示前5只股票
                    for i, stock in enumerate(stock_list[:5]):
                        print(f"   {i+1}. {stock['code']} - {stock['name']} ({stock['market']})")
                    if len(stock_list) > 5:
                        print(f"   ... 还有 {len(stock_list) - 5} 只股票")
                else:
                    print("⚠️ 未获取到股票列表")
            except Exception as e:
                print(f"❌ 获取股票列表失败: {e}")
                logger.error(f"获取股票列表失败: {e}")
            
            # 测试获取市场数据
            print("\n4. 测试获取市场数据...")
            try:
                test_symbol = "000001.SZ"
                market_data = adapter.get_market_data(
                    test_symbol, 
                    period="1d",
                    start_date="2025-01-01",
                    end_date="2025-01-10"
                )
                if market_data and not market_data.data.empty:
                    print(f"✅ 获取到 {test_symbol} 市场数据")
                    print(f"   数据行数: {len(market_data.data)}")
                    print(f"   数据列: {list(market_data.data.columns)}")
                    if not market_data.data.empty:
                        latest = market_data.data.iloc[-1]
                        print(f"   最新数据: 日期={latest['trade_date']}, 收盘={latest['close']:.2f}")
                        print(f"   数据范围: {market_data.data['trade_date'].min()} 到 {market_data.data['trade_date'].max()}")
                else:
                    print("⚠️ 未获取到市场数据")
            except Exception as e:
                print(f"❌ 获取市场数据失败: {e}")
                logger.error(f"获取市场数据失败: {e}")
            
            # 测试获取板块列表
            print("\n5. 测试获取板块列表...")
            try:
                sectors = adapter.get_sector_list("industry")
                if sectors:
                    print(f"✅ 获取到 {len(sectors)} 个行业板块")
                    # 显示前3个板块
                    for i, sector in enumerate(sectors[:3]):
                        print(f"   {i+1}. {sector.sector_name} ({sector.sector_type})")
                    if len(sectors) > 3:
                        print(f"   ... 还有 {len(sectors) - 3} 个板块")
                else:
                    print("⚠️ 未获取到板块列表")
            except Exception as e:
                print(f"❌ 获取板块列表失败: {e}")
                logger.error(f"获取板块列表失败: {e}")
            
            # 测试获取板块成分股
            print("\n6. 测试获取板块成分股...")
            try:
                constituents = adapter.get_sector_constituents("银行")
                if constituents:
                    print(f"✅ 银行板块包含 {len(constituents)} 只股票")
                    print(f"   成分股: {constituents}")
                else:
                    print("⚠️ 未获取到板块成分股")
            except Exception as e:
                print(f"❌ 获取板块成分股失败: {e}")
                logger.error(f"获取板块成分股失败: {e}")
            
            # 测试获取交易日历
            print("\n7. 测试获取交易日历...")
            try:
                trading_dates = adapter.get_trading_calendar("2025-01-01", "2025-01-31")
                if trading_dates:
                    print(f"✅ 获取到 {len(trading_dates)} 个交易日")
                    print(f"   示例: {trading_dates[:3]}...")
                else:
                    print("⚠️ 未获取到交易日历")
            except Exception as e:
                print(f"❌ 获取交易日历失败: {e}")
                logger.error(f"获取交易日历失败: {e}")
            
            # 测试批量下载历史数据
            print("\n8. 测试批量下载历史数据...")
            try:
                symbols = ["000001.SZ", "000002.SZ", "600000.SH"]
                success = adapter.download_history_data(
                    symbols, 
                    period="1d",
                    start_date="2025-01-01"
                )
                if success:
                    print(f"✅ 批量下载 {len(symbols)} 只股票历史数据成功")
                else:
                    print("⚠️ 批量下载历史数据失败")
            except Exception as e:
                print(f"❌ 批量下载历史数据失败: {e}")
                logger.error(f"批量下载历史数据失败: {e}")
            
            # 断开连接
            print("\n9. 断开连接...")
            adapter.disconnect()
            print("✅ 连接已断开")
            
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        logger.error(f"测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_xtdata_adapter_with_mock()
