#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户操作向导系统
第14周：用户体验优化 - 新手引导和操作提示
"""

from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QWidget, QFrame, QScrollArea, QProgressBar, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QColor
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


class GuideStep:
    """向导步骤"""
    def __init__(self, title: str, description: str, target_widget: str = None, 
                 action: str = None, validation: Callable = None):
        self.title = title
        self.description = description
        self.target_widget = target_widget  # 目标控件名称
        self.action = action  # 需要执行的操作
        self.validation = validation  # 验证函数
        self.completed = False


class GuideType(Enum):
    """向导类型"""
    FIRST_TIME = "first_time"  # 首次使用
    FEATURE_INTRO = "feature_intro"  # 功能介绍
    QUICK_START = "quick_start"  # 快速开始
    TROUBLESHOOTING = "troubleshooting"  # 故障排除


@dataclass
class GuideConfig:
    """向导配置"""
    guide_id: str
    title: str
    description: str
    guide_type: GuideType
    steps: List[GuideStep]
    auto_start: bool = False
    show_progress: bool = True
    allow_skip: bool = True


class OperationGuideManager:
    """操作向导管理器"""
    
    def __init__(self):
        """初始化向导管理器"""
        self.guides: Dict[str, GuideConfig] = {}
        self.user_preferences = self._load_user_preferences()
        self.current_guide: Optional['OperationGuideDialog'] = None
        self._init_default_guides()
        logger.info("操作向导管理器初始化完成")
    
    def _init_default_guides(self):
        """初始化默认向导"""
        # 首次使用向导
        first_time_steps = [
            GuideStep(
                "欢迎使用威科夫选股系统",
                "欢迎！这是一个基于威科夫理论的智能选股系统。让我们快速了解主要功能。",
                action="welcome"
            ),
            GuideStep(
                "连接数据源",
                "首先需要连接数据源。点击菜单栏的'数据源'→'配置'来设置XtData连接。",
                target_widget="data_source_menu",
                action="highlight_menu"
            ),
            GuideStep(
                "加载股票数据",
                "连接成功后，系统会自动加载股票列表。您可以在左侧面板看到所有可用股票。",
                target_widget="stock_list_widget",
                action="highlight_widget"
            ),
            GuideStep(
                "选择分析股票",
                "点击任意股票来选择它。系统会自动开始威科夫分析和相对强弱计算。",
                target_widget="stock_list_widget",
                action="demo_selection"
            ),
            GuideStep(
                "查看分析结果",
                "分析结果会显示在右侧面板。包括威科夫信号、相对强弱排名和技术指标。",
                target_widget="analysis_widget",
                action="highlight_widget"
            ),
            GuideStep(
                "使用选股功能",
                "点击'智能选股'标签页，可以设置选股条件并执行自动选股。",
                target_widget="selection_widget",
                action="highlight_widget"
            )
        ]
        
        self.guides["first_time"] = GuideConfig(
            guide_id="first_time",
            title="首次使用向导",
            description="快速了解系统主要功能",
            guide_type=GuideType.FIRST_TIME,
            steps=first_time_steps,
            auto_start=True
        )
        
        # 威科夫分析功能介绍
        wyckoff_steps = [
            GuideStep(
                "威科夫理论简介",
                "威科夫理论通过分析价格和成交量的关系，识别市场的累积和派发阶段。",
                action="show_theory"
            ),
            GuideStep(
                "市场阶段识别",
                "系统会自动识别股票当前处于哪个威科夫阶段：累积、上涨、派发或下跌。",
                target_widget="wyckoff_stage_display",
                action="highlight_widget"
            ),
            GuideStep(
                "量价分析",
                "观察成交量与价格的配合关系，判断趋势的真实性和持续性。",
                target_widget="volume_price_chart",
                action="highlight_widget"
            ),
            GuideStep(
                "威科夫信号",
                "系统会生成买入、卖出和观望信号，帮助您做出投资决策。",
                target_widget="wyckoff_signals",
                action="highlight_widget"
            )
        ]
        
        self.guides["wyckoff_intro"] = GuideConfig(
            guide_id="wyckoff_intro",
            title="威科夫分析功能介绍",
            description="深入了解威科夫理论和分析方法",
            guide_type=GuideType.FEATURE_INTRO,
            steps=wyckoff_steps
        )
        
        # 快速选股向导
        quick_select_steps = [
            GuideStep(
                "设置选股条件",
                "在选股配置面板中，设置您的选股条件，如价格区间、市值范围等。",
                target_widget="selection_config_panel",
                action="highlight_widget"
            ),
            GuideStep(
                "配置威科夫参数",
                "调整威科夫分析参数，如敏感度、时间周期等，以匹配您的投资风格。",
                target_widget="wyckoff_params",
                action="highlight_widget"
            ),
            GuideStep(
                "设置相对强弱条件",
                "配置相对强弱筛选条件，选择强势股票。",
                target_widget="rs_params",
                action="highlight_widget"
            ),
            GuideStep(
                "执行智能选股",
                "点击'开始选股'按钮，系统会根据您的条件筛选出符合要求的股票。",
                target_widget="start_selection_button",
                action="highlight_widget"
            ),
            GuideStep(
                "查看选股结果",
                "选股完成后，结果会显示在下方列表中，包括评分和选股理由。",
                target_widget="selection_results",
                action="highlight_widget"
            )
        ]
        
        self.guides["quick_select"] = GuideConfig(
            guide_id="quick_select",
            title="快速选股向导",
            description="学习如何使用智能选股功能",
            guide_type=GuideType.QUICK_START,
            steps=quick_select_steps
        )
    
    def should_show_guide(self, guide_id: str) -> bool:
        """判断是否应该显示向导"""
        if guide_id not in self.guides:
            return False
        
        # 检查用户偏好
        if not self.user_preferences.get("show_guides", True):
            return False
        
        # 检查是否已经完成过
        completed_guides = self.user_preferences.get("completed_guides", [])
        if guide_id in completed_guides:
            return False
        
        # 检查是否被跳过
        skipped_guides = self.user_preferences.get("skipped_guides", [])
        if guide_id in skipped_guides:
            return False
        
        return True
    
    def start_guide(self, guide_id: str, parent_widget=None) -> bool:
        """启动向导"""
        if not self.should_show_guide(guide_id):
            return False
        
        if guide_id not in self.guides:
            logger.warning(f"向导不存在: {guide_id}")
            return False
        
        guide_config = self.guides[guide_id]
        
        # 如果已有向导在运行，先关闭
        if self.current_guide:
            self.current_guide.close()
        
        # 创建并显示向导对话框
        self.current_guide = OperationGuideDialog(guide_config, parent_widget)
        self.current_guide.guide_completed.connect(lambda: self._on_guide_completed(guide_id))
        self.current_guide.guide_skipped.connect(lambda: self._on_guide_skipped(guide_id))
        self.current_guide.show()
        
        logger.info(f"启动向导: {guide_id}")
        return True
    
    def _on_guide_completed(self, guide_id: str):
        """向导完成回调"""
        completed_guides = self.user_preferences.get("completed_guides", [])
        if guide_id not in completed_guides:
            completed_guides.append(guide_id)
            self.user_preferences["completed_guides"] = completed_guides
            self._save_user_preferences()
        
        logger.info(f"向导完成: {guide_id}")
    
    def _on_guide_skipped(self, guide_id: str):
        """向导跳过回调"""
        skipped_guides = self.user_preferences.get("skipped_guides", [])
        if guide_id not in skipped_guides:
            skipped_guides.append(guide_id)
            self.user_preferences["skipped_guides"] = skipped_guides
            self._save_user_preferences()
        
        logger.info(f"向导跳过: {guide_id}")
    
    def _load_user_preferences(self) -> Dict:
        """加载用户偏好"""
        try:
            prefs_file = "config/user_guide_preferences.json"
            if os.path.exists(prefs_file):
                with open(prefs_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载用户向导偏好失败: {e}")
        
        return {"show_guides": True, "completed_guides": [], "skipped_guides": []}
    
    def _save_user_preferences(self):
        """保存用户偏好"""
        try:
            os.makedirs("config", exist_ok=True)
            prefs_file = "config/user_guide_preferences.json"
            with open(prefs_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_preferences, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存用户向导偏好失败: {e}")


class OperationGuideDialog(QDialog):
    """操作向导对话框"""
    
    guide_completed = pyqtSignal()
    guide_skipped = pyqtSignal()
    
    def __init__(self, guide_config: GuideConfig, parent=None):
        super().__init__(parent)
        self.guide_config = guide_config
        self.current_step = 0
        self.setup_ui()
        self.show_current_step()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle(self.guide_config.title)
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel(self.guide_config.title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2196F3;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(self.guide_config.description)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin-bottom: 15px;")
        layout.addWidget(desc_label)
        
        # 进度条
        if self.guide_config.show_progress:
            self.progress_bar = QProgressBar()
            self.progress_bar.setMaximum(len(self.guide_config.steps))
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #2196F3;
                    border-radius: 3px;
                }
            """)
            layout.addWidget(self.progress_bar)
        
        # 步骤内容区域
        self.content_frame = QFrame()
        self.content_frame.setFrameStyle(QFrame.Shape.Box)
        self.content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
            }
        """)
        self.content_layout = QVBoxLayout(self.content_frame)
        layout.addWidget(self.content_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 跳过按钮
        if self.guide_config.allow_skip:
            self.skip_button = QPushButton("跳过向导")
            self.skip_button.clicked.connect(self.skip_guide)
            button_layout.addWidget(self.skip_button)
        
        button_layout.addStretch()
        
        # 上一步按钮
        self.prev_button = QPushButton("上一步")
        self.prev_button.clicked.connect(self.prev_step)
        button_layout.addWidget(self.prev_button)
        
        # 下一步/完成按钮
        self.next_button = QPushButton("下一步")
        self.next_button.clicked.connect(self.next_step)
        self.next_button.setDefault(True)
        button_layout.addWidget(self.next_button)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def show_current_step(self):
        """显示当前步骤"""
        if self.current_step >= len(self.guide_config.steps):
            self.complete_guide()
            return
        
        step = self.guide_config.steps[self.current_step]
        
        # 清除之前的内容
        for i in reversed(range(self.content_layout.count())):
            self.content_layout.itemAt(i).widget().setParent(None)
        
        # 步骤标题
        step_title = QLabel(f"步骤 {self.current_step + 1}: {step.title}")
        step_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
        """)
        self.content_layout.addWidget(step_title)
        
        # 步骤描述
        step_desc = QLabel(step.description)
        step_desc.setWordWrap(True)
        step_desc.setStyleSheet("color: #555; line-height: 1.4;")
        self.content_layout.addWidget(step_desc)
        
        # 更新进度条
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(self.current_step + 1)
        
        # 更新按钮状态
        self.prev_button.setEnabled(self.current_step > 0)
        
        if self.current_step == len(self.guide_config.steps) - 1:
            self.next_button.setText("完成")
        else:
            self.next_button.setText("下一步")
    
    def next_step(self):
        """下一步"""
        if self.current_step < len(self.guide_config.steps) - 1:
            self.current_step += 1
            self.show_current_step()
        else:
            self.complete_guide()
    
    def prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self.show_current_step()
    
    def skip_guide(self):
        """跳过向导"""
        self.guide_skipped.emit()
        self.close()
    
    def complete_guide(self):
        """完成向导"""
        self.guide_completed.emit()
        self.close()
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)


# 全局向导管理器实例
guide_manager = OperationGuideManager()
