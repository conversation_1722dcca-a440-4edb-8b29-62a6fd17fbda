"""
用户偏好设置管理

实现用户个性化配置的保存、加载和管理
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import json
import os
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal
from .theme_manager import ThemeType
from ...utils.logger import get_logger

logger = get_logger(__name__)


class LayoutMode(Enum):
    """布局模式"""
    COMPACT = "compact"
    STANDARD = "standard"
    EXPANDED = "expanded"


class DataRefreshMode(Enum):
    """数据刷新模式"""
    MANUAL = "manual"
    AUTO = "auto"
    SCHEDULED = "scheduled"


@dataclass
class UIPreferences:
    """界面偏好设置"""
    theme: str = "light"
    layout_mode: str = "standard"
    window_width: int = 1200
    window_height: int = 800
    window_maximized: bool = False
    show_toolbar: bool = True
    show_statusbar: bool = True
    font_size: int = 12
    font_family: str = "Microsoft YaHei"
    tab_position: str = "top"  # top, bottom, left, right
    show_tooltips: bool = True
    animation_enabled: bool = True


@dataclass
class DataPreferences:
    """数据偏好设置"""
    refresh_mode: str = "manual"
    auto_refresh_interval: int = 300  # 秒
    cache_enabled: bool = True
    cache_duration: int = 3600  # 秒
    default_market: str = "all"  # all, sh, sz
    data_source_timeout: int = 30
    retry_times: int = 3
    show_suspended_stocks: bool = False


@dataclass
class AnalysisPreferences:
    """分析偏好设置"""
    default_period: int = 250  # 默认分析周期
    wyckoff_sensitivity: float = 0.7
    rs_calculation_period: int = 20
    min_confidence_threshold: float = 0.6
    auto_save_results: bool = True
    show_analysis_details: bool = True
    enable_background_analysis: bool = False


@dataclass
class SelectionPreferences:
    """选股偏好设置"""
    max_selection_count: int = 50
    default_strategy: str = "balanced"  # conservative, balanced, aggressive
    min_market_cap: float = 10.0  # 亿元
    max_market_cap: float = 1000.0  # 亿元
    exclude_st_stocks: bool = True
    exclude_new_stocks: bool = True
    new_stock_days_threshold: int = 60


@dataclass
class NotificationPreferences:
    """通知偏好设置"""
    enable_notifications: bool = True
    show_success_notifications: bool = True
    show_warning_notifications: bool = True
    show_error_notifications: bool = True
    notification_duration: int = 5000  # 毫秒
    sound_enabled: bool = False
    desktop_notifications: bool = False


@dataclass
class UserPreferences:
    """用户偏好设置总配置"""
    ui: UIPreferences
    data: DataPreferences
    analysis: AnalysisPreferences
    selection: SelectionPreferences
    notifications: NotificationPreferences
    version: str = "1.0"
    last_updated: str = ""


class UserPreferencesManager(QObject):
    """用户偏好设置管理器"""
    
    preferences_changed = pyqtSignal(str, object)  # 设置项名称, 新值
    preferences_loaded = pyqtSignal()
    preferences_saved = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.config_dir = Path.home() / ".wyckoff_stock_analyzer"
        self.config_file = self.config_dir / "user_preferences.json"
        self.backup_dir = self.config_dir / "backups"
        
        self._ensure_config_dir()
        self.preferences = self._load_default_preferences()
        
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        
    def _load_default_preferences(self) -> UserPreferences:
        """加载默认偏好设置"""
        return UserPreferences(
            ui=UIPreferences(),
            data=DataPreferences(),
            analysis=AnalysisPreferences(),
            selection=SelectionPreferences(),
            notifications=NotificationPreferences()
        )
    
    def load_preferences(self) -> bool:
        """加载用户偏好设置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 验证版本兼容性
                if self._is_compatible_version(data.get('version', '1.0')):
                    self.preferences = self._dict_to_preferences(data)
                    self.preferences_loaded.emit()
                    logger.info("用户偏好设置加载成功")
                    return True
                else:
                    logger.warning("配置文件版本不兼容，使用默认设置")
                    
        except Exception as e:
            logger.error(f"加载用户偏好设置失败: {e}")
            
        # 加载失败时使用默认设置
        self.preferences = self._load_default_preferences()
        return False
    
    def save_preferences(self) -> bool:
        """保存用户偏好设置"""
        try:
            # 创建备份
            self._create_backup()
            
            # 更新时间戳
            from datetime import datetime
            self.preferences.last_updated = datetime.now().isoformat()
            
            # 保存配置
            data = self._preferences_to_dict(self.preferences)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.preferences_saved.emit()
            logger.info("用户偏好设置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存用户偏好设置失败: {e}")
            return False
    
    def get_preference(self, category: str, key: str, default=None):
        """获取特定偏好设置"""
        try:
            category_obj = getattr(self.preferences, category)
            return getattr(category_obj, key, default)
        except AttributeError:
            return default
    
    def set_preference(self, category: str, key: str, value: Any):
        """设置特定偏好设置"""
        try:
            category_obj = getattr(self.preferences, category)
            if hasattr(category_obj, key):
                setattr(category_obj, key, value)
                self.preferences_changed.emit(f"{category}.{key}", value)
                logger.debug(f"偏好设置已更新: {category}.{key} = {value}")
                return True
        except AttributeError:
            logger.error(f"无效的偏好设置: {category}.{key}")
        return False
    
    def export_preferences(self, file_path: str) -> bool:
        """导出偏好设置"""
        try:
            data = self._preferences_to_dict(self.preferences)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"偏好设置已导出到: {file_path}")
            return True
        except Exception as e:
            logger.error(f"导出偏好设置失败: {e}")
            return False
    
    def import_preferences(self, file_path: str) -> bool:
        """导入偏好设置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if self._is_compatible_version(data.get('version', '1.0')):
                # 创建当前设置的备份
                self._create_backup()
                
                # 导入新设置
                self.preferences = self._dict_to_preferences(data)
                self.save_preferences()
                
                logger.info(f"偏好设置已从 {file_path} 导入")
                return True
            else:
                logger.error("导入文件版本不兼容")
                return False
                
        except Exception as e:
            logger.error(f"导入偏好设置失败: {e}")
            return False
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        self._create_backup()
        self.preferences = self._load_default_preferences()
        self.save_preferences()
        logger.info("偏好设置已重置为默认值")
    
    def _create_backup(self):
        """创建配置备份"""
        try:
            if self.config_file.exists():
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = self.backup_dir / f"preferences_backup_{timestamp}.json"
                
                import shutil
                shutil.copy2(self.config_file, backup_file)
                
                # 保持最多10个备份文件
                backup_files = sorted(self.backup_dir.glob("preferences_backup_*.json"))
                if len(backup_files) > 10:
                    for old_backup in backup_files[:-10]:
                        old_backup.unlink()
                        
        except Exception as e:
            logger.warning(f"创建配置备份失败: {e}")
    
    def _is_compatible_version(self, version: str) -> bool:
        """检查版本兼容性"""
        # 简单的版本兼容性检查
        try:
            major, minor = version.split('.')[:2]
            return major == '1'  # 只支持1.x版本
        except:
            return False
    
    def _preferences_to_dict(self, preferences: UserPreferences) -> Dict[str, Any]:
        """将偏好设置对象转换为字典"""
        return asdict(preferences)
    
    def _dict_to_preferences(self, data: Dict[str, Any]) -> UserPreferences:
        """将字典转换为偏好设置对象"""
        return UserPreferences(
            ui=UIPreferences(**data.get('ui', {})),
            data=DataPreferences(**data.get('data', {})),
            analysis=AnalysisPreferences(**data.get('analysis', {})),
            selection=SelectionPreferences(**data.get('selection', {})),
            notifications=NotificationPreferences(**data.get('notifications', {})),
            version=data.get('version', '1.0'),
            last_updated=data.get('last_updated', '')
        )
    
    def get_ui_preferences(self) -> UIPreferences:
        """获取界面偏好设置"""
        return self.preferences.ui
    
    def get_data_preferences(self) -> DataPreferences:
        """获取数据偏好设置"""
        return self.preferences.data
    
    def get_analysis_preferences(self) -> AnalysisPreferences:
        """获取分析偏好设置"""
        return self.preferences.analysis
    
    def get_selection_preferences(self) -> SelectionPreferences:
        """获取选股偏好设置"""
        return self.preferences.selection
    
    def get_notification_preferences(self) -> NotificationPreferences:
        """获取通知偏好设置"""
        return self.preferences.notifications


# 全局用户偏好管理器实例
user_preferences = UserPreferencesManager()
