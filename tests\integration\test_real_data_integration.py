#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据源集成测试脚本
测试第一阶段：真实数据源集成功能
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.manager import DatabaseManager
from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.data_source_config import DataSourceConfig
from src.services.sector_manager import SectorManager
from src.services.market_index_manager import MarketIndexManager
from src.services.return_calculator import ReturnCalculator
from src.utils.cache import CacheManager
from src.utils.data_quality import DataQualityChecker, DataQualityConfig
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RealDataIntegrationTester:
    """真实数据源集成测试器"""
    
    def __init__(self):
        self.db_manager = None
        self.data_source_manager = None
        self.sector_manager = None
        self.market_index_manager = None
        self.return_calculator = None
        self.quality_checker = None
    
    async def initialize(self):
        """初始化测试环境"""
        try:
            print("🔧 初始化真实数据源集成测试环境...")
            
            # 初始化数据库
            self.db_manager = DatabaseManager("data/real_data_test.db")
            if not self.db_manager.initialize():
                raise Exception("数据库初始化失败")
            
            # 初始化数据源管理器
            self.data_source_manager = DataSourceManager()
            
            # 尝试配置XtData数据源
            try:
                xtdata_config = DataSourceConfig(
                    name="xtdata_real_test",
                    enabled=True,
                    timeout=30,
                    config={"ip": "127.0.0.1", "port": 58610}
                )
                
                xtdata_adapter = XtDataAdapter(xtdata_config)
                self.data_source_manager.register_source("xtdata", xtdata_adapter, priority=10)
                print("✅ XtData数据源配置完成")
                
            except Exception as e:
                print(f"⚠️  XtData数据源配置失败: {e}")
                print("    将使用模拟数据进行测试")
            
            # 初始化管理器
            self.sector_manager = SectorManager(self.db_manager, self.data_source_manager)
            self.market_index_manager = MarketIndexManager(self.db_manager, self.data_source_manager)
            
            cache_manager = CacheManager()
            self.return_calculator = ReturnCalculator(
                self.db_manager, self.sector_manager, self.market_index_manager, cache_manager
            )
            
            # 初始化数据质量检查器
            quality_config = DataQualityConfig()
            self.quality_checker = DataQualityChecker(quality_config)
            
            print("✅ 测试环境初始化完成")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    async def test_data_source_connection(self):
        """测试数据源连接"""
        try:
            print("\n📡 测试数据源连接...")
            
            # 检查数据源状态
            best_source = self.data_source_manager.get_best_source()
            if best_source:
                print(f"  最佳数据源: {best_source.__class__.__name__}")
                
                # 测试连接
                if best_source.test_connection():
                    print("  ✅ 数据源连接正常")
                    return True
                else:
                    print("  ❌ 数据源连接失败")
                    return False
            else:
                print("  ⚠️  没有可用的数据源")
                return False
                
        except Exception as e:
            print(f"  ❌ 数据源连接测试失败: {e}")
            return False
    
    async def test_market_index_integration(self):
        """测试市场指数集成"""
        try:
            print("\n📊 测试市场指数集成...")
            
            # 1. 初始化市场指数
            print("  - 初始化市场指数...")
            success = await self.market_index_manager.initialize_indices()
            if not success:
                print("  ❌ 市场指数初始化失败")
                return False
            
            # 2. 下载指数数据
            print("  - 下载指数历史数据...")
            test_indices = ["000001.SH", "399001.SZ"]  # 测试上证指数和深证成指
            
            def progress_callback(message, progress):
                print(f"    进度 {progress:.1f}%: {message}")
            
            success = await self.market_index_manager.download_index_data(
                test_indices, days=30, progress_callback=progress_callback
            )
            
            if not success:
                print("  ❌ 指数数据下载失败")
                return False
            
            # 3. 验证数据质量
            print("  - 验证指数数据质量...")
            for index_code in test_indices:
                df = self.market_index_manager.get_index_data(index_code, limit=30)
                if not df.empty:
                    metrics = self.quality_checker.check_market_data_quality(
                        df, index_code, "index"
                    )
                    print(f"    {index_code}: {metrics.quality_level.value} "
                          f"(得分: {metrics.overall_score:.3f})")
                    
                    if metrics.issues:
                        print(f"      问题: {', '.join(metrics.issues[:3])}")
                else:
                    print(f"    {index_code}: 无数据")
            
            # 4. 测试收益率计算
            print("  - 测试指数收益率计算...")
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
            
            for index_code in test_indices:
                return_rate = self.market_index_manager.calculate_index_return(
                    index_code, start_date, end_date
                )
                if return_rate is not None:
                    print(f"    {index_code}: {return_rate:.4f} ({return_rate*100:.2f}%)")
                else:
                    print(f"    {index_code}: 计算失败")
            
            print("  ✅ 市场指数集成测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 市场指数集成测试失败: {e}")
            return False
    
    async def test_sector_integration(self):
        """测试板块集成"""
        try:
            print("\n🏭 测试板块集成...")
            
            # 1. 初始化板块数据
            print("  - 初始化板块数据...")
            success = await self.sector_manager.initialize_sectors()
            if not success:
                print("  ❌ 板块数据初始化失败")
                return False
            
            # 2. 获取板块列表
            print("  - 获取板块列表...")
            industry_sectors = self.sector_manager.get_sector_list("industry")
            concept_sectors = self.sector_manager.get_sector_list("concept")
            
            print(f"    行业板块: {len(industry_sectors)} 个")
            print(f"    概念板块: {len(concept_sectors)} 个")
            
            if len(industry_sectors) == 0 and len(concept_sectors) == 0:
                print("  ❌ 未获取到板块数据")
                return False
            
            # 3. 下载板块历史数据
            print("  - 下载板块历史数据...")
            test_sectors = [s['sector_code'] for s in industry_sectors[:3]]  # 测试前3个行业板块
            
            if test_sectors:
                def progress_callback(message, progress):
                    print(f"    进度 {progress:.1f}%: {message}")
                
                success = await self.sector_manager.download_sector_data(
                    test_sectors, days=30, progress_callback=progress_callback
                )
                
                if success:
                    print("  ✅ 板块数据下载成功")
                else:
                    print("  ⚠️  板块数据下载部分失败")
            
            # 4. 测试成分股更新
            print("  - 测试成分股更新...")
            if test_sectors:
                test_sector = test_sectors[0]
                success = await self.sector_manager.update_sector_constituents(test_sector)
                if success:
                    constituents = self.sector_manager.get_sector_constituents(test_sector)
                    print(f"    板块 {test_sector} 成分股: {len(constituents)} 只")
                else:
                    print(f"    板块 {test_sector} 成分股更新失败")
            
            print("  ✅ 板块集成测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 板块集成测试失败: {e}")
            return False
    
    async def test_data_quality_monitoring(self):
        """测试数据质量监控"""
        try:
            print("\n🔍 测试数据质量监控...")
            
            # 获取质量检查摘要
            summary = self.quality_checker.get_quality_summary()
            if 'error' not in summary:
                print(f"  总检查次数: {summary.get('total_checks', 0)}")
                print(f"  最近平均得分: {summary.get('recent_avg_score', 0):.3f}")
                print(f"  最后检查时间: {summary.get('last_check', 'N/A')}")
            else:
                print(f"  质量摘要: {summary['error']}")
            
            print("  ✅ 数据质量监控测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 数据质量监控测试失败: {e}")
            return False
    
    async def test_return_calculation_integration(self):
        """测试收益率计算集成"""
        try:
            print("\n📈 测试收益率计算集成...")
            
            # 设置测试参数
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
            
            # 1. 测试市场指数收益率
            print("  - 测试市场指数收益率...")
            market_symbols = ["000001.SH", "399001.SZ"]
            market_returns = await self.return_calculator.calculate_batch_returns(
                market_symbols, 'market', start_date, end_date
            )
            
            print(f"    市场指数收益率: {len(market_returns)} 个")
            for symbol, return_rate in market_returns.items():
                print(f"      {symbol}: {return_rate:.4f}")
            
            # 2. 测试板块收益率
            print("  - 测试板块收益率...")
            sector_list = self.sector_manager.get_sector_list()
            if sector_list:
                test_sector_codes = [s['sector_code'] for s in sector_list[:3]]
                sector_returns = await self.return_calculator.calculate_batch_returns(
                    test_sector_codes, 'sector', start_date, end_date
                )
                
                print(f"    板块收益率: {len(sector_returns)} 个")
                for symbol, return_rate in sector_returns.items():
                    print(f"      {symbol}: {return_rate:.4f}")
            
            print("  ✅ 收益率计算集成测试通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 收益率计算集成测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有集成测试"""
        try:
            print("🧪 开始真实数据源集成测试")
            print("=" * 60)
            
            # 初始化
            await self.initialize()
            
            # 运行各项测试
            tests = [
                ("数据源连接", self.test_data_source_connection),
                ("市场指数集成", self.test_market_index_integration),
                ("板块集成", self.test_sector_integration),
                ("数据质量监控", self.test_data_quality_monitoring),
                ("收益率计算集成", self.test_return_calculation_integration)
            ]
            
            results = {}
            for test_name, test_func in tests:
                try:
                    result = await test_func()
                    results[test_name] = result
                except Exception as e:
                    print(f"  ❌ {test_name} 测试异常: {e}")
                    results[test_name] = False
            
            # 汇总结果
            print("\n" + "=" * 60)
            print("📊 真实数据源集成测试结果汇总")
            print("=" * 60)
            
            passed = 0
            total = len(results)
            
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"  {test_name}: {status}")
                if result:
                    passed += 1
            
            success_rate = passed / total if total > 0 else 0
            print(f"\n总体结果: {passed}/{total} 通过 (成功率: {success_rate:.1%})")
            
            if success_rate >= 0.8:
                print("\n🎉 真实数据源集成测试基本通过！")
                print("\n✅ 第一阶段完成情况:")
                print("  🔹 真实数据源连接和配置")
                print("  🔹 市场指数数据集成")
                print("  🔹 板块数据管理和维护")
                print("  🔹 数据质量检查和监控")
                print("  🔹 收益率计算引擎集成")
                return True
            else:
                print("\n⚠️  真实数据源集成测试存在问题，需要进一步优化")
                return False
            
        except Exception as e:
            print(f"\n❌ 集成测试失败: {e}")
            return False


async def main():
    """主函数"""
    tester = RealDataIntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 第一阶段总结:")
        print("  ✅ 真实数据源集成完成")
        print("  ✅ 数据质量保障机制建立")
        print("  ✅ 系统具备真实数据处理能力")
        print("\n📋 下一步: 开始第二阶段用户界面优化")
        return 0
    else:
        print("\n💥 第一阶段存在问题，需要修复后再进行下一阶段")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
