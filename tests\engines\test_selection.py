"""
选股策略引擎测试用例

测试选股策略引擎的核心功能：
- 策略配置和管理
- 多策略组合
- 选股执行
- 回测验证
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.engines.selection import (
    StockSelectionEngine, StrategyType, SelectionCriteria,
    StrategyConfig, SelectionResult, PortfolioMetrics,
    BaseStrategy, WyckoffStrategy, RelativeStrengthStrategy
)
from src.utils.exceptions import CalculationError


class TestStockSelectionEngine:
    """选股策略引擎基础测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = StockSelectionEngine()
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
        
        # 创建多只股票的测试数据
        self.stocks_data = {}
        
        # 强势股票
        strong_prices = [100 * (1.01 ** i) for i in range(100)]
        self.stocks_data['STRONG'] = pd.DataFrame({
            'open': strong_prices,
            'high': [p * 1.01 for p in strong_prices],
            'low': [p * 0.99 for p in strong_prices],
            'close': strong_prices,
            'volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
        
        # 弱势股票
        weak_prices = [100 * (0.999 ** i) for i in range(100)]
        self.stocks_data['WEAK'] = pd.DataFrame({
            'open': weak_prices,
            'high': [p * 1.01 for p in weak_prices],
            'low': [p * 0.99 for p in weak_prices],
            'close': weak_prices,
            'volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
        
        # 中性股票
        neutral_prices = [100 + np.random.normal(0, 1) for _ in range(100)]
        self.stocks_data['NEUTRAL'] = pd.DataFrame({
            'open': neutral_prices,
            'high': [p * 1.01 for p in neutral_prices],
            'low': [p * 0.99 for p in neutral_prices],
            'close': neutral_prices,
            'volume': np.random.randint(1000000, 5000000, 100)
        }, index=dates)
        
        # 基准数据
        benchmark_prices = [100 * (1.005 ** i) for i in range(100)]
        self.benchmark_data = pd.DataFrame({
            'open': benchmark_prices,
            'high': [p * 1.01 for p in benchmark_prices],
            'low': [p * 0.99 for p in benchmark_prices],
            'close': benchmark_prices,
            'volume': np.random.randint(5000000, 10000000, 100)
        }, index=dates)
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        engine = StockSelectionEngine()
        assert len(engine.strategies) == 0
        assert engine.benchmark_data is None
    
    def test_add_remove_strategy(self):
        """测试策略添加和移除"""
        config = StrategyConfig(
            name="test_strategy",
            strategy_type=StrategyType.RELATIVE_STRENGTH,
            weight=1.0
        )
        
        strategy = RelativeStrengthStrategy(config)
        
        # 添加策略
        self.engine.add_strategy(strategy)
        assert "test_strategy" in self.engine.strategies
        assert len(self.engine.strategies) == 1
        
        # 移除策略
        self.engine.remove_strategy("test_strategy")
        assert "test_strategy" not in self.engine.strategies
        assert len(self.engine.strategies) == 0
    
    def test_set_benchmark(self):
        """测试设置基准"""
        self.engine.set_benchmark(self.benchmark_data)
        assert self.engine.benchmark_data is not None
        assert len(self.engine.benchmark_data) == len(self.benchmark_data)
    
    def test_select_stocks_no_strategies(self):
        """测试没有策略时的选股"""
        with pytest.raises(CalculationError):
            self.engine.select_stocks(self.stocks_data)
    
    def test_select_stocks_with_single_strategy(self):
        """测试单策略选股"""
        # 添加相对强弱策略
        config = StrategyConfig(
            name="rs_strategy",
            strategy_type=StrategyType.RELATIVE_STRENGTH,
            weight=1.0,
            parameters={'min_rs_score': 0.5}
        )
        
        strategy = RelativeStrengthStrategy(config)
        self.engine.add_strategy(strategy)
        self.engine.set_benchmark(self.benchmark_data)
        
        results = self.engine.select_stocks(
            self.stocks_data,
            selection_criteria=SelectionCriteria.TOP_PERCENTILE,
            max_selections=2
        )
        
        assert isinstance(results, list)
        assert len(results) <= 2
        
        for result in results:
            assert isinstance(result, SelectionResult)
            assert result.symbol in self.stocks_data.keys()
            assert 0 <= result.score <= 1
            assert result.rank >= 1
    
    def test_select_stocks_with_multiple_strategies(self):
        """测试多策略选股"""
        # 添加威科夫策略
        wyckoff_config = StrategyConfig(
            name="wyckoff_strategy",
            strategy_type=StrategyType.WYCKOFF_ACCUMULATION,
            weight=0.6,
            parameters={'min_score': 0.4}
        )
        wyckoff_strategy = WyckoffStrategy(wyckoff_config)
        
        # 添加相对强弱策略
        rs_config = StrategyConfig(
            name="rs_strategy",
            strategy_type=StrategyType.RELATIVE_STRENGTH,
            weight=0.4,
            parameters={'min_rs_score': 0.5}
        )
        rs_strategy = RelativeStrengthStrategy(rs_config)
        
        self.engine.add_strategy(wyckoff_strategy)
        self.engine.add_strategy(rs_strategy)
        self.engine.set_benchmark(self.benchmark_data)
        
        results = self.engine.select_stocks(
            self.stocks_data,
            selection_criteria=SelectionCriteria.TOP_PERCENTILE,
            max_selections=3
        )
        
        assert len(results) <= 3
        
        # 检查结果包含多个策略的得分
        for result in results:
            assert len(result.strategy_scores) == 2
            assert 'wyckoff_strategy' in result.strategy_scores
            assert 'rs_strategy' in result.strategy_scores
    
    def test_selection_criteria_threshold_based(self):
        """测试基于阈值的选股标准"""
        config = StrategyConfig(
            name="test_strategy",
            strategy_type=StrategyType.RELATIVE_STRENGTH,
            weight=1.0
        )
        
        strategy = RelativeStrengthStrategy(config)
        self.engine.add_strategy(strategy)
        self.engine.set_benchmark(self.benchmark_data)
        
        results = self.engine.select_stocks(
            self.stocks_data,
            selection_criteria=SelectionCriteria.THRESHOLD_BASED,
            threshold=0.3,
            max_selections=10
        )
        
        # 所有结果的得分都应该大于等于阈值
        for result in results:
            assert result.score >= 0.3
    
    def test_backtest_strategy(self):
        """测试策略回测"""
        metrics = self.engine.backtest_strategy(
            self.stocks_data,
            start_date='2023-01-01',
            end_date='2023-12-31',
            rebalance_frequency='M'
        )
        
        assert isinstance(metrics, PortfolioMetrics)
        assert hasattr(metrics, 'total_return')
        assert hasattr(metrics, 'sharpe_ratio')
        assert hasattr(metrics, 'max_drawdown')
        assert hasattr(metrics, 'win_rate')
    
    def test_optimize_parameters(self):
        """测试参数优化"""
        optimized_params = self.engine.optimize_parameters(
            self.stocks_data,
            optimization_target='sharpe_ratio'
        )
        
        assert isinstance(optimized_params, dict)
        assert len(optimized_params) > 0


class TestStrategyConfig:
    """策略配置测试"""
    
    def test_strategy_config_creation(self):
        """测试策略配置创建"""
        config = StrategyConfig(
            name="test_config",
            strategy_type=StrategyType.MOMENTUM,
            weight=0.8,
            enabled=True,
            parameters={'param1': 'value1'},
            filters={'filter1': 'value1'}
        )
        
        assert config.name == "test_config"
        assert config.strategy_type == StrategyType.MOMENTUM
        assert config.weight == 0.8
        assert config.enabled is True
        assert config.parameters['param1'] == 'value1'
        assert config.filters['filter1'] == 'value1'
    
    def test_strategy_config_defaults(self):
        """测试策略配置默认值"""
        config = StrategyConfig(
            name="minimal_config",
            strategy_type=StrategyType.VALUE
        )
        
        assert config.weight == 1.0
        assert config.enabled is True
        assert config.parameters == {}
        assert config.filters == {}


class TestWyckoffStrategy:
    """威科夫策略测试"""
    
    def setup_method(self):
        """测试前准备"""
        config = StrategyConfig(
            name="wyckoff_test",
            strategy_type=StrategyType.WYCKOFF_ACCUMULATION,
            parameters={'min_score': 0.5}
        )
        self.strategy = WyckoffStrategy(config)
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        prices = [100 + np.random.normal(0, 2) for _ in range(50)]
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, 50)
        }, index=dates)
    
    def test_calculate_score(self):
        """测试威科夫策略评分"""
        score = self.strategy.calculate_score({'price_data': self.test_data})
        
        assert isinstance(score, float)
        assert 0 <= score <= 1
    
    def test_filter_stocks(self):
        """测试威科夫策略股票过滤"""
        stocks_data = {
            'STOCK1': self.test_data,
            'STOCK2': self.test_data.copy()
        }
        
        filtered = self.strategy.filter_stocks(stocks_data)
        
        assert isinstance(filtered, list)
        assert all(symbol in stocks_data.keys() for symbol in filtered)


class TestRelativeStrengthStrategy:
    """相对强弱策略测试"""
    
    def setup_method(self):
        """测试前准备"""
        config = StrategyConfig(
            name="rs_test",
            strategy_type=StrategyType.RELATIVE_STRENGTH,
            parameters={'min_rs_score': 0.6}
        )
        self.strategy = RelativeStrengthStrategy(config)
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        
        # 强势股票数据
        strong_prices = [100 * (1.01 ** i) for i in range(50)]
        self.strong_data = pd.DataFrame({
            'open': strong_prices,
            'high': [p * 1.01 for p in strong_prices],
            'low': [p * 0.99 for p in strong_prices],
            'close': strong_prices,
            'volume': np.random.randint(1000000, 5000000, 50)
        }, index=dates)
        
        # 基准数据
        benchmark_prices = [100 * (1.005 ** i) for i in range(50)]
        self.benchmark_data = pd.DataFrame({
            'open': benchmark_prices,
            'high': [p * 1.01 for p in benchmark_prices],
            'low': [p * 0.99 for p in benchmark_prices],
            'close': benchmark_prices,
            'volume': np.random.randint(5000000, 10000000, 50)
        }, index=dates)
    
    def test_calculate_score(self):
        """测试相对强弱策略评分"""
        score = self.strategy.calculate_score({
            'price_data': self.strong_data,
            'benchmark_data': self.benchmark_data
        })
        
        assert isinstance(score, float)
        assert 0 <= score <= 1
        # 强势股票应该得到较高分数
        assert score > 0.5
    
    def test_filter_stocks(self):
        """测试相对强弱策略股票过滤"""
        stocks_data = {
            'STRONG': self.strong_data,
            'BENCHMARK': self.benchmark_data
        }
        
        filtered = self.strategy.filter_stocks(stocks_data)
        
        assert isinstance(filtered, list)
        # 强势股票应该被筛选出来
        assert 'STRONG' in filtered or len(filtered) == 0  # 可能因为简化的基准比较而为空


class TestSelectionResult:
    """选股结果测试"""
    
    def test_selection_result_creation(self):
        """测试选股结果创建"""
        result = SelectionResult(
            symbol="TEST",
            score=0.85,
            rank=1,
            strategy_scores={'strategy1': 0.8, 'strategy2': 0.9},
            selection_reason="高分策略组合",
            risk_metrics={'volatility': 0.15},
            fundamental_data={'pe_ratio': 15.5}
        )
        
        assert result.symbol == "TEST"
        assert result.score == 0.85
        assert result.rank == 1
        assert len(result.strategy_scores) == 2
        assert result.selection_reason == "高分策略组合"
        assert result.risk_metrics['volatility'] == 0.15
        assert result.fundamental_data['pe_ratio'] == 15.5


if __name__ == '__main__':
    pytest.main([__file__])
