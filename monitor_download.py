#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据下载监控脚本

定期检查数据下载进度并显示统计信息
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


def format_number(num):
    """格式化数字显示"""
    return f"{num:,}"


def calculate_progress(current, total):
    """计算进度百分比"""
    if total == 0:
        return 0.0
    return (current / total) * 100


def display_progress_bar(progress, width=50):
    """显示进度条"""
    filled = int(width * progress / 100)
    bar = '█' * filled + '░' * (width - filled)
    return f"[{bar}] {progress:.1f}%"


def main():
    """主函数"""
    try:
        print("🔍 数据下载监控启动...")
        print("按 Ctrl+C 停止监控\n")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 目标数量
        target_stocks = 5150
        
        last_count = 0
        start_time = time.time()
        
        while True:
            try:
                # 获取当前统计信息
                stats = db_manager.get_database_stats()
                current_stocks = stats.get('total_stocks', 0)
                current_quotes = stats.get('total_quotes', 0)
                db_size_mb = stats.get('db_size_mb', 0)
                
                # 计算进度
                stock_progress = calculate_progress(current_stocks, target_stocks)
                
                # 计算下载速度
                elapsed_time = time.time() - start_time
                if elapsed_time > 0:
                    speed = (current_stocks - last_count) / elapsed_time if elapsed_time > 60 else 0
                else:
                    speed = 0
                
                # 估算剩余时间
                remaining_stocks = target_stocks - current_stocks
                if speed > 0:
                    eta_seconds = remaining_stocks / speed
                    eta_minutes = eta_seconds / 60
                    eta_hours = eta_minutes / 60
                    
                    if eta_hours >= 1:
                        eta_str = f"{eta_hours:.1f}小时"
                    elif eta_minutes >= 1:
                        eta_str = f"{eta_minutes:.1f}分钟"
                    else:
                        eta_str = f"{eta_seconds:.0f}秒"
                else:
                    eta_str = "计算中..."
                
                # 清屏并显示信息
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print("🚀 威科夫选股系统 - 数据下载监控")
                print("=" * 60)
                print(f"⏰ 监控时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
                
                print("📊 股票基本信息下载:")
                print(f"   进度: {format_number(current_stocks)}/{format_number(target_stocks)}")
                print(f"   {display_progress_bar(stock_progress)}")
                print(f"   下载速度: {speed:.1f} 股票/秒")
                print(f"   预计完成: {eta_str}")
                print()
                
                print("📈 行情数据:")
                print(f"   记录数量: {format_number(current_quotes)}")
                print()
                
                print("💾 数据库状态:")
                print(f"   文件大小: {db_size_mb:.2f} MB")
                print()
                
                # 检查是否完成
                if current_stocks >= target_stocks:
                    print("🎉 股票基本信息下载完成！")
                    if current_quotes > 0:
                        print("🎉 历史数据下载也已完成！")
                    else:
                        print("⏳ 等待历史数据下载...")
                    break
                
                print("按 Ctrl+C 停止监控")
                
                # 更新计数器
                if elapsed_time > 60:  # 每分钟重置速度计算
                    last_count = current_stocks
                    start_time = time.time()
                
                # 等待5秒
                time.sleep(5)
                
            except KeyboardInterrupt:
                print("\n\n👋 监控已停止")
                break
            except Exception as e:
                print(f"\n❌ 监控过程中发生错误: {e}")
                time.sleep(5)
                continue
                
    except Exception as e:
        print(f"❌ 监控启动失败: {e}")
        logger.error(f"数据下载监控失败: {e}")


if __name__ == "__main__":
    main()
