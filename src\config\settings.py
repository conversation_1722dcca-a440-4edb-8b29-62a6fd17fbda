"""
系统设置管理

管理用户偏好设置、系统配置等
"""

from typing import Dict, Any, Optional, Union, Type, List
from dataclasses import dataclass, field, asdict
from pathlib import Path
import json
from enum import Enum

from ..utils.logger import get_logger

logger = get_logger(__name__)


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class Theme(Enum):
    """主题枚举"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


@dataclass
class DatabaseSettings:
    """数据库设置"""
    connection_pool_size: int = 10
    connection_timeout: int = 30
    query_timeout: int = 60
    enable_wal_mode: bool = True
    enable_foreign_keys: bool = True
    vacuum_interval_days: int = 7
    backup_retention_days: int = 30


@dataclass
class DataSourceSettings:
    """数据源设置"""
    xtdata_timeout: int = 30
    retry_count: int = 3
    retry_delay: int = 5
    cache_duration_minutes: int = 30
    max_concurrent_requests: int = 5
    enable_data_validation: bool = True


@dataclass
class CalculationSettings:
    """计算设置"""
    default_periods: list = field(default_factory=lambda: [5, 10, 20, 60, 120])
    enable_parallel_processing: bool = True
    max_worker_threads: int = 4
    batch_size: int = 1000
    precision_digits: int = 6
    enable_caching: bool = True


@dataclass
class UISettings:
    """界面设置"""
    theme: Theme = Theme.LIGHT
    language: str = "zh_CN"
    window_width: int = 1200
    window_height: int = 800
    window_x: int = -1  # -1表示居中
    window_y: int = -1  # -1表示居中
    window_maximized: bool = False
    font_family: str = "Microsoft YaHei"
    font_size: int = 10
    show_tooltips: bool = True
    auto_save_results: bool = True


@dataclass
class LoggingSettings:
    """日志设置"""
    level: LogLevel = LogLevel.INFO
    file_logging: bool = True
    console_logging: bool = True
    max_file_size_mb: int = 10
    backup_count: int = 5
    log_format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"


@dataclass
class NotificationSettings:
    """通知设置"""
    enable_sound: bool = True
    enable_popup: bool = True
    enable_system_tray: bool = True
    auto_hide_timeout: int = 5
    email_notifications: bool = False
    email_smtp_server: str = ""
    email_username: str = ""
    email_password: str = ""


@dataclass
class PerformanceSettings:
    """性能设置"""
    enable_multithreading: bool = True
    max_memory_usage_mb: int = 2048
    enable_disk_cache: bool = True
    cache_size_mb: int = 512
    gc_threshold: int = 1000
    enable_profiling: bool = False


@dataclass
class SystemSettings:
    """系统设置主类"""
    database: DatabaseSettings = field(default_factory=DatabaseSettings)
    data_source: DataSourceSettings = field(default_factory=DataSourceSettings)
    calculation: CalculationSettings = field(default_factory=CalculationSettings)
    ui: UISettings = field(default_factory=UISettings)
    logging: LoggingSettings = field(default_factory=LoggingSettings)
    notification: NotificationSettings = field(default_factory=NotificationSettings)
    performance: PerformanceSettings = field(default_factory=PerformanceSettings)
    
    # 元数据
    version: str = "1.0.0"
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class Settings:
    """设置管理器"""
    
    def __init__(self, settings_file: Optional[str] = None):
        """
        初始化设置管理器
        
        Args:
            settings_file: 设置文件路径
        """
        if settings_file is None:
            settings_file = Path("config") / "settings.json"
        
        self.settings_file = Path(settings_file)
        self.settings_file.parent.mkdir(parents=True, exist_ok=True)
        
        self._settings: SystemSettings = SystemSettings()
        self._load_settings()
        
        logger.info(f"设置管理器初始化完成: {self.settings_file}")
    
    def _load_settings(self):
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 转换枚举值
                if 'ui' in data and 'theme' in data['ui']:
                    data['ui']['theme'] = Theme(data['ui']['theme'])
                
                if 'logging' in data and 'level' in data['logging']:
                    data['logging']['level'] = LogLevel(data['logging']['level'])
                
                # 重构设置对象
                self._settings = self._dict_to_dataclass(data, SystemSettings)
                
                logger.info("设置加载成功")
            else:
                # 创建默认设置文件
                self.save_settings()
                logger.info("创建默认设置文件")
                
        except Exception as e:
            logger.error(f"加载设置失败: {e}")
            self._settings = SystemSettings()
    
    def save_settings(self) -> bool:
        """
        保存设置
        
        Returns:
            是否保存成功
        """
        try:
            # 备份现有设置
            if self.settings_file.exists():
                backup_file = self.settings_file.with_suffix(f".backup.{int(time.time())}")
                self.settings_file.rename(backup_file)
            
            # 转换为字典并处理枚举
            data = asdict(self._settings)
            
            # 转换枚举为字符串
            if 'ui' in data and 'theme' in data['ui']:
                data['ui']['theme'] = data['ui']['theme'].value
            
            if 'logging' in data and 'level' in data['logging']:
                data['logging']['level'] = data['logging']['level'].value
            
            # 更新时间戳
            from datetime import datetime
            data['updated_at'] = datetime.now().isoformat()
            if data.get('created_at') is None:
                data['created_at'] = data['updated_at']
            
            # 保存到文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info("设置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取设置值
        
        Args:
            key_path: 设置键路径，如 'ui.theme'
            default: 默认值
            
        Returns:
            设置值
        """
        try:
            keys = key_path.split('.')
            current = self._settings
            
            for key in keys:
                if hasattr(current, key):
                    current = getattr(current, key)
                else:
                    return default
            
            return current
            
        except Exception as e:
            logger.error(f"获取设置值失败: {key_path}, 错误: {e}")
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        设置值
        
        Args:
            key_path: 设置键路径
            value: 设置值
            
        Returns:
            是否设置成功
        """
        try:
            keys = key_path.split('.')
            current = self._settings
            
            # 导航到父对象
            for key in keys[:-1]:
                if hasattr(current, key):
                    current = getattr(current, key)
                else:
                    logger.error(f"无效的设置路径: {key_path}")
                    return False
            
            # 设置值
            final_key = keys[-1]
            if hasattr(current, final_key):
                setattr(current, final_key, value)
                logger.info(f"设置值已更新: {key_path} = {value}")
                return True
            else:
                logger.error(f"无效的设置键: {final_key}")
                return False
                
        except Exception as e:
            logger.error(f"设置值失败: {key_path}, 错误: {e}")
            return False
    
    def reset_to_defaults(self, section: Optional[str] = None) -> bool:
        """
        重置为默认值
        
        Args:
            section: 要重置的部分，如果为None则重置全部
            
        Returns:
            是否重置成功
        """
        try:
            if section is None:
                self._settings = SystemSettings()
                logger.info("所有设置已重置为默认值")
            else:
                if hasattr(self._settings, section):
                    section_class = getattr(SystemSettings, section).default_factory()
                    setattr(self._settings, section, section_class)
                    logger.info(f"设置部分已重置为默认值: {section}")
                else:
                    logger.error(f"无效的设置部分: {section}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"重置设置失败: {e}")
            return False
    
    def export_settings(self, export_file: str) -> bool:
        """
        导出设置
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            export_path = Path(export_file)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            data = asdict(self._settings)
            
            # 转换枚举
            if 'ui' in data and 'theme' in data['ui']:
                data['ui']['theme'] = data['ui']['theme'].value
            
            if 'logging' in data and 'level' in data['logging']:
                data['logging']['level'] = data['logging']['level'].value
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"设置导出成功: {export_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出设置失败: {e}")
            return False
    
    def import_settings(self, import_file: str) -> bool:
        """
        导入设置
        
        Args:
            import_file: 导入文件路径
            
        Returns:
            是否导入成功
        """
        try:
            import_path = Path(import_file)
            if not import_path.exists():
                logger.error(f"导入文件不存在: {import_file}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换枚举值
            if 'ui' in data and 'theme' in data['ui']:
                data['ui']['theme'] = Theme(data['ui']['theme'])
            
            if 'logging' in data and 'level' in data['logging']:
                data['logging']['level'] = LogLevel(data['logging']['level'])
            
            # 重构设置对象
            self._settings = self._dict_to_dataclass(data, SystemSettings)
            
            logger.info(f"设置导入成功: {import_file}")
            return True
            
        except Exception as e:
            logger.error(f"导入设置失败: {e}")
            return False
    
    def validate_settings(self) -> Dict[str, List[str]]:
        """
        验证设置
        
        Returns:
            验证结果，键为设置部分，值为错误列表
        """
        errors = {}
        
        try:
            # 验证数据库设置
            db_errors = []
            if self._settings.database.connection_pool_size <= 0:
                db_errors.append("连接池大小必须大于0")
            if self._settings.database.connection_timeout <= 0:
                db_errors.append("连接超时时间必须大于0")
            if errors:
                errors['database'] = db_errors
            
            # 验证界面设置
            ui_errors = []
            if self._settings.ui.window_width < 800:
                ui_errors.append("窗口宽度不能小于800")
            if self._settings.ui.window_height < 600:
                ui_errors.append("窗口高度不能小于600")
            if self._settings.ui.font_size < 8:
                ui_errors.append("字体大小不能小于8")
            if ui_errors:
                errors['ui'] = ui_errors
            
            # 验证性能设置
            perf_errors = []
            if self._settings.performance.max_memory_usage_mb < 512:
                perf_errors.append("最大内存使用量不能小于512MB")
            if self._settings.performance.cache_size_mb < 64:
                perf_errors.append("缓存大小不能小于64MB")
            if perf_errors:
                errors['performance'] = perf_errors
            
        except Exception as e:
            logger.error(f"验证设置时出错: {e}")
            errors['general'] = [f"验证过程出错: {e}"]
        
        return errors
    
    def _dict_to_dataclass(self, data: Dict[str, Any], dataclass_type: Type) -> Any:
        """将字典转换为数据类"""
        try:
            # 获取数据类的字段
            field_types = {f.name: f.type for f in dataclass_type.__dataclass_fields__.values()}
            
            kwargs = {}
            for key, value in data.items():
                if key in field_types:
                    field_type = field_types[key]
                    
                    # 处理嵌套数据类
                    if hasattr(field_type, '__dataclass_fields__'):
                        kwargs[key] = self._dict_to_dataclass(value, field_type)
                    else:
                        kwargs[key] = value
            
            return dataclass_type(**kwargs)
            
        except Exception as e:
            logger.error(f"字典转数据类失败: {e}")
            return dataclass_type()
    
    @property
    def settings(self) -> SystemSettings:
        """获取设置对象"""
        return self._settings
    
    def __getattr__(self, name: str):
        """委托属性访问到设置对象"""
        return getattr(self._settings, name)