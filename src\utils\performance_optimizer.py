#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化管理器
第15周：性能优化和部署准备 - 算法性能和内存使用优化
"""

import time
import psutil
import threading
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import functools
import gc
import numpy as np
from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    execution_time: float
    memory_usage: float
    cpu_usage: float
    peak_memory: float
    function_name: str
    timestamp: float


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.metrics: List[PerformanceMetrics] = []
        self.enabled = True
        self.memory_threshold = 500  # MB
        self.time_threshold = 1.0    # 秒
        logger.info("性能分析器初始化完成")
    
    def profile(self, func_name: str = None):
        """性能分析装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if not self.enabled:
                    return func(*args, **kwargs)
                
                name = func_name or f"{func.__module__}.{func.__name__}"
                
                # 记录开始状态
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                start_cpu = psutil.cpu_percent()
                
                try:
                    # 执行函数
                    result = func(*args, **kwargs)
                    
                    # 记录结束状态
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                    end_cpu = psutil.cpu_percent()
                    
                    # 计算指标
                    execution_time = end_time - start_time
                    memory_usage = end_memory - start_memory
                    cpu_usage = (start_cpu + end_cpu) / 2
                    peak_memory = max(start_memory, end_memory)
                    
                    # 记录指标
                    metrics = PerformanceMetrics(
                        execution_time=execution_time,
                        memory_usage=memory_usage,
                        cpu_usage=cpu_usage,
                        peak_memory=peak_memory,
                        function_name=name,
                        timestamp=start_time
                    )
                    
                    self.metrics.append(metrics)
                    
                    # 检查性能警告
                    self._check_performance_warnings(metrics)
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"函数执行异常 {name}: {e}")
                    raise
            
            return wrapper
        return decorator
    
    def _check_performance_warnings(self, metrics: PerformanceMetrics):
        """检查性能警告"""
        if metrics.execution_time > self.time_threshold:
            logger.warning(f"函数执行时间过长: {metrics.function_name} - {metrics.execution_time:.2f}s")
        
        if metrics.peak_memory > self.memory_threshold:
            logger.warning(f"内存使用过高: {metrics.function_name} - {metrics.peak_memory:.1f}MB")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.metrics:
            return {"message": "暂无性能数据"}
        
        # 按函数名分组统计
        function_stats = {}
        for metric in self.metrics:
            name = metric.function_name
            if name not in function_stats:
                function_stats[name] = {
                    "count": 0,
                    "total_time": 0,
                    "total_memory": 0,
                    "max_time": 0,
                    "max_memory": 0
                }
            
            stats = function_stats[name]
            stats["count"] += 1
            stats["total_time"] += metric.execution_time
            stats["total_memory"] += metric.memory_usage
            stats["max_time"] = max(stats["max_time"], metric.execution_time)
            stats["max_memory"] = max(stats["max_memory"], metric.peak_memory)
        
        # 计算平均值
        for name, stats in function_stats.items():
            stats["avg_time"] = stats["total_time"] / stats["count"]
            stats["avg_memory"] = stats["total_memory"] / stats["count"]
        
        return {
            "total_functions": len(function_stats),
            "total_calls": len(self.metrics),
            "function_stats": function_stats,
            "top_time_consumers": sorted(
                function_stats.items(),
                key=lambda x: x[1]["total_time"],
                reverse=True
            )[:5],
            "top_memory_consumers": sorted(
                function_stats.items(),
                key=lambda x: x[1]["max_memory"],
                reverse=True
            )[:5]
        }
    
    def clear_metrics(self):
        """清除性能指标"""
        self.metrics.clear()
        logger.info("性能指标已清除")


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        """初始化内存优化器"""
        self.cache_size_limit = 100 * 1024 * 1024  # 100MB
        self.gc_threshold = 0.8  # 80%内存使用率触发GC
        logger.info("内存优化器初始化完成")
    
    def optimize_memory(self):
        """优化内存使用"""
        try:
            # 获取当前内存使用情况
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            
            logger.info(f"当前内存使用率: {memory_percent:.1f}%")
            
            if memory_percent > self.gc_threshold * 100:
                # 强制垃圾回收
                collected = gc.collect()
                logger.info(f"执行垃圾回收，回收对象数: {collected}")
                
                # 清理numpy缓存
                if hasattr(np, 'clear_cache'):
                    np.clear_cache()
                
                # 再次检查内存
                new_memory_info = psutil.virtual_memory()
                new_memory_percent = new_memory_info.percent
                
                saved_memory = memory_percent - new_memory_percent
                logger.info(f"内存优化完成，节省: {saved_memory:.1f}%")
                
                return saved_memory
            
            return 0
            
        except Exception as e:
            logger.error(f"内存优化失败: {e}")
            return 0
    
    def monitor_memory_usage(self, interval: int = 30):
        """监控内存使用"""
        def monitor():
            while True:
                try:
                    memory_info = psutil.virtual_memory()
                    if memory_info.percent > self.gc_threshold * 100:
                        self.optimize_memory()
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logger.error(f"内存监控异常: {e}")
                    time.sleep(interval)
        
        # 在后台线程中运行监控
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        logger.info(f"内存监控已启动，检查间隔: {interval}秒")


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        """初始化异步任务管理器"""
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=max_workers)
        self.running_tasks: List[asyncio.Task] = []
        logger.info(f"异步任务管理器初始化完成，最大工作线程: {max_workers}")
    
    async def run_async_task(self, func: Callable, *args, **kwargs):
        """运行异步任务"""
        try:
            loop = asyncio.get_event_loop()
            
            # 根据函数类型选择执行方式
            if asyncio.iscoroutinefunction(func):
                # 协程函数直接执行
                result = await func(*args, **kwargs)
            else:
                # 普通函数在线程池中执行
                result = await loop.run_in_executor(
                    self.thread_pool, 
                    functools.partial(func, *args, **kwargs)
                )
            
            return result
            
        except Exception as e:
            logger.error(f"异步任务执行失败: {e}")
            raise
    
    def run_cpu_intensive_task(self, func: Callable, *args, **kwargs):
        """运行CPU密集型任务"""
        try:
            # 在进程池中执行CPU密集型任务
            future = self.process_pool.submit(func, *args, **kwargs)
            return future
            
        except Exception as e:
            logger.error(f"CPU密集型任务执行失败: {e}")
            raise
    
    def batch_process(self, func: Callable, data_list: List, batch_size: int = 100):
        """批量处理数据"""
        try:
            results = []
            
            # 分批处理
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                
                # 并行处理批次
                futures = []
                for item in batch:
                    future = self.thread_pool.submit(func, item)
                    futures.append(future)
                
                # 收集结果
                batch_results = [future.result() for future in futures]
                results.extend(batch_results)
                
                logger.debug(f"批次处理完成: {i//batch_size + 1}/{(len(data_list)-1)//batch_size + 1}")
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            raise
    
    def shutdown(self):
        """关闭任务管理器"""
        try:
            self.thread_pool.shutdown(wait=True)
            self.process_pool.shutdown(wait=True)
            logger.info("异步任务管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭任务管理器失败: {e}")


class AlgorithmOptimizer:
    """算法优化器"""
    
    def __init__(self):
        """初始化算法优化器"""
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("算法优化器初始化完成")
    
    def memoize(self, func):
        """记忆化装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(sorted(kwargs.items()))
            
            if key in self.cache:
                self.cache_hits += 1
                return self.cache[key]
            
            # 计算结果并缓存
            result = func(*args, **kwargs)
            self.cache[key] = result
            self.cache_misses += 1
            
            # 限制缓存大小
            if len(self.cache) > 1000:
                # 删除最旧的缓存项
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
            
            return result
        
        return wrapper
    
    def vectorize_calculation(self, data: np.ndarray, operation: str):
        """向量化计算优化"""
        try:
            if operation == "moving_average":
                # 使用numpy的卷积实现移动平均
                window = 20
                return np.convolve(data, np.ones(window)/window, mode='valid')
            
            elif operation == "rsi":
                # 向量化RSI计算
                delta = np.diff(data)
                gain = np.where(delta > 0, delta, 0)
                loss = np.where(delta < 0, -delta, 0)
                
                avg_gain = np.mean(gain[-14:]) if len(gain) >= 14 else np.mean(gain)
                avg_loss = np.mean(loss[-14:]) if len(loss) >= 14 else np.mean(loss)
                
                if avg_loss == 0:
                    return 100
                
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
            
            elif operation == "bollinger_bands":
                # 向量化布林带计算
                window = 20
                rolling_mean = np.convolve(data, np.ones(window)/window, mode='valid')
                rolling_std = np.array([
                    np.std(data[i:i+window]) 
                    for i in range(len(data) - window + 1)
                ])
                
                upper_band = rolling_mean + (rolling_std * 2)
                lower_band = rolling_mean - (rolling_std * 2)
                
                return {
                    "middle": rolling_mean,
                    "upper": upper_band,
                    "lower": lower_band
                }
            
            else:
                logger.warning(f"未知的向量化操作: {operation}")
                return data
                
        except Exception as e:
            logger.error(f"向量化计算失败: {e}")
            return data
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "cache_size": len(self.cache),
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": hit_rate,
            "total_requests": total_requests
        }
    
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("算法缓存已清除")


class PerformanceOptimizer:
    """性能优化管理器"""
    
    def __init__(self):
        """初始化性能优化管理器"""
        self.profiler = PerformanceProfiler()
        self.memory_optimizer = MemoryOptimizer()
        self.task_manager = AsyncTaskManager()
        self.algorithm_optimizer = AlgorithmOptimizer()
        
        # 启动内存监控
        self.memory_optimizer.monitor_memory_usage()
        
        logger.info("性能优化管理器初始化完成")
    
    def get_system_performance(self) -> Dict[str, Any]:
        """获取系统性能状态"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "core_count": cpu_count
                },
                "memory": {
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": memory.percent,
                    "process_memory_mb": process_memory.rss / (1024**2)
                },
                "disk": {
                    "total_gb": disk.total / (1024**3),
                    "free_gb": disk.free / (1024**3),
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "performance_report": self.profiler.get_performance_report(),
                "cache_stats": self.algorithm_optimizer.get_cache_stats()
            }
            
        except Exception as e:
            logger.error(f"获取系统性能失败: {e}")
            return {"error": str(e)}
    
    def optimize_system(self) -> Dict[str, Any]:
        """优化系统性能"""
        try:
            results = {}
            
            # 内存优化
            memory_saved = self.memory_optimizer.optimize_memory()
            results["memory_optimization"] = f"节省内存: {memory_saved:.1f}%"
            
            # 清理算法缓存（如果缓存命中率低）
            cache_stats = self.algorithm_optimizer.get_cache_stats()
            if cache_stats["hit_rate"] < 50 and cache_stats["cache_size"] > 100:
                self.algorithm_optimizer.clear_cache()
                results["cache_optimization"] = "缓存已清理"
            
            # 清理性能指标（保留最近1000条）
            if len(self.profiler.metrics) > 1000:
                self.profiler.metrics = self.profiler.metrics[-1000:]
                results["metrics_cleanup"] = "性能指标已清理"
            
            logger.info(f"系统优化完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"系统优化失败: {e}")
            return {"error": str(e)}
    
    def shutdown(self):
        """关闭性能优化器"""
        try:
            self.task_manager.shutdown()
            logger.info("性能优化管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭性能优化器失败: {e}")


# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()

# 导出装饰器
profile = performance_optimizer.profiler.profile
memoize = performance_optimizer.algorithm_optimizer.memoize
