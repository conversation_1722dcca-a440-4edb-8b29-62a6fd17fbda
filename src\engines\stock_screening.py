#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块内个股筛选引擎
实现需求文档第4步：板块内个股筛选
"""

import asyncio
from typing import Dict, List, Optional, Tuple, Any, Set
from datetime import datetime
from dataclasses import dataclass, field
import pandas as pd

from ..services.sector_manager import SectorManager
from ..services.return_calculator import ReturnCalculator
from ..engines.sector_screening import SectorStrength, SectorScreeningResult
from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


@dataclass
class StockStrength:
    """个股强弱数据类"""
    stock_code: str
    stock_name: str
    sector_code: str
    sector_name: str
    stock_return: float
    sector_return: float
    relative_strength: float  # 个股相对强弱度 = 个股涨幅 - 板块涨幅
    rank_in_sector: int = 0
    sector_rank: int = 0  # 所属板块的排名
    final_score: float = 0.0  # 综合得分
    calculation_time: datetime = field(default_factory=datetime.now)


@dataclass
class StockScreeningParams:
    """个股筛选参数"""
    start_date: str
    end_date: str
    stocks_per_sector: int = 3  # 每个板块选择的股票数量
    min_relative_strength: float = 0.0  # 最小相对强弱度
    exclude_st: bool = True  # 排除ST股票
    exclude_suspended: bool = True  # 排除停牌股票
    exclude_new_stocks: bool = True  # 排除新股（上市不足30天）
    min_market_cap: Optional[float] = None  # 最小市值
    max_stocks_total: int = 50  # 总的最大股票数量


@dataclass
class StockScreeningResult:
    """个股筛选结果"""
    params: StockScreeningParams
    sector_result: SectorScreeningResult
    selected_stocks: List[StockStrength]
    total_candidates: int
    selected_count: int
    sectors_processed: int
    calculation_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'params': {
                'start_date': self.params.start_date,
                'end_date': self.params.end_date,
                'stocks_per_sector': self.params.stocks_per_sector,
                'min_relative_strength': self.params.min_relative_strength,
                'exclude_st': self.params.exclude_st,
                'exclude_suspended': self.params.exclude_suspended,
                'exclude_new_stocks': self.params.exclude_new_stocks
            },
            'sector_summary': {
                'market_return': self.sector_result.market_return,
                'strong_sectors_count': len(self.sector_result.strong_sectors)
            },
            'selected_stocks': [
                {
                    'stock_code': s.stock_code,
                    'stock_name': s.stock_name,
                    'sector_code': s.sector_code,
                    'sector_name': s.sector_name,
                    'stock_return': s.stock_return,
                    'sector_return': s.sector_return,
                    'relative_strength': s.relative_strength,
                    'rank_in_sector': s.rank_in_sector,
                    'sector_rank': s.sector_rank,
                    'final_score': s.final_score
                }
                for s in self.selected_stocks
            ],
            'total_candidates': self.total_candidates,
            'selected_count': self.selected_count,
            'sectors_processed': self.sectors_processed,
            'calculation_time': self.calculation_time.isoformat()
        }


class StockScreeningEngine:
    """个股筛选引擎"""
    
    def __init__(self, 
                 sector_manager: SectorManager,
                 return_calculator: ReturnCalculator):
        self.sector_manager = sector_manager
        self.return_calculator = return_calculator
    
    async def screen_stocks_in_sectors(self, 
                                     sector_result: SectorScreeningResult,
                                     params: StockScreeningParams,
                                     progress_callback: Optional[callable] = None) -> StockScreeningResult:
        """
        在强势板块中筛选个股
        
        Args:
            sector_result: 板块筛选结果
            params: 个股筛选参数
            progress_callback: 进度回调函数
            
        Returns:
            StockScreeningResult: 个股筛选结果
        """
        try:
            logger.info(f"开始个股筛选，处理{len(sector_result.strong_sectors)}个强势板块")
            
            all_selected_stocks = []
            total_candidates = 0
            sectors_processed = 0
            
            for i, sector in enumerate(sector_result.strong_sectors):
                try:
                    if progress_callback:
                        progress = (i / len(sector_result.strong_sectors)) * 100
                        progress_callback(f"筛选板块 {sector.sector_name} 中的个股...", progress)
                    
                    # 1. 获取板块成分股
                    constituents = self.sector_manager.get_sector_constituents(sector.sector_code)
                    if not constituents:
                        logger.warning(f"板块{sector.sector_code}没有成分股数据")
                        continue
                    
                    # 2. 过滤异常股票
                    valid_stocks = await self._filter_valid_stocks(constituents, params)
                    total_candidates += len(valid_stocks)
                    
                    if not valid_stocks:
                        logger.warning(f"板块{sector.sector_code}没有有效的成分股")
                        continue
                    
                    # 3. 计算个股收益率
                    stock_codes = [s['stock_code'] for s in valid_stocks]
                    stock_returns = await self.return_calculator.calculate_batch_returns(
                        stock_codes, 'stock', params.start_date, params.end_date
                    )
                    
                    # 4. 筛选和排名板块内个股
                    sector_stocks = await self._screen_stocks_in_single_sector(
                        sector, valid_stocks, stock_returns, params
                    )
                    
                    all_selected_stocks.extend(sector_stocks)
                    sectors_processed += 1
                    
                except Exception as e:
                    logger.warning(f"处理板块{sector.sector_code}失败: {e}")
                    continue
            
            # 5. 去重和最终排序
            if progress_callback:
                progress_callback("去重和最终排序...", 95)
            
            final_stocks = self._deduplicate_and_rank(all_selected_stocks, params)
            
            # 6. 生成结果
            result = StockScreeningResult(
                params=params,
                sector_result=sector_result,
                selected_stocks=final_stocks,
                total_candidates=total_candidates,
                selected_count=len(final_stocks),
                sectors_processed=sectors_processed
            )
            
            logger.info(f"个股筛选完成，从{total_candidates}只候选股票中选出{len(final_stocks)}只")
            return result
            
        except Exception as e:
            logger.error(f"个股筛选失败: {e}")
            raise CalculationError(f"个股筛选失败: {e}")
    
    async def _filter_valid_stocks(self, 
                                 constituents: List[Dict[str, Any]],
                                 params: StockScreeningParams) -> List[Dict[str, Any]]:
        """过滤有效股票"""
        try:
            valid_stocks = []
            
            for stock in constituents:
                stock_code = stock['stock_code']
                
                # 检查股票基本信息
                stock_info = await self._get_stock_info(stock_code)
                if not stock_info:
                    continue
                
                # 排除ST股票
                if params.exclude_st and stock_info.get('is_st', False):
                    continue
                
                # 排除停牌股票
                if params.exclude_suspended and stock_info.get('status') == 'suspended':
                    continue
                
                # 排除新股
                if params.exclude_new_stocks:
                    list_date = stock_info.get('list_date')
                    if list_date and self._is_new_stock(list_date, params.start_date):
                        continue
                
                # 市值筛选
                if params.min_market_cap:
                    market_cap = stock_info.get('market_cap', 0)
                    if market_cap < params.min_market_cap:
                        continue
                
                # 更新股票名称
                stock['stock_name'] = stock_info.get('stock_name', stock['stock_name'])
                valid_stocks.append(stock)
            
            logger.debug(f"过滤后有效股票：{len(valid_stocks)}/{len(constituents)}")
            return valid_stocks
            
        except Exception as e:
            logger.error(f"过滤有效股票失败: {e}")
            return []
    
    async def _get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        try:
            sql = """
            SELECT stock_code, stock_name, list_date, status, market_cap, is_st
            FROM stock_info WHERE stock_code = ?
            """
            
            results = self.sector_manager.db_manager.execute_query(sql, (stock_code,))
            if not results:
                return None
            
            row = results[0]
            return {
                'stock_code': row[0],
                'stock_name': row[1],
                'list_date': row[2],
                'status': row[3],
                'market_cap': row[4],
                'is_st': bool(row[5])
            }
            
        except Exception as e:
            logger.warning(f"获取股票{stock_code}信息失败: {e}")
            return None
    
    def _is_new_stock(self, list_date: str, start_date: str) -> bool:
        """判断是否为新股"""
        try:
            from datetime import datetime, timedelta
            
            list_dt = datetime.strptime(list_date, '%Y-%m-%d')
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            
            # 上市不足30天视为新股
            return (start_dt - list_dt).days < 30
            
        except Exception:
            return False
    
    async def _screen_stocks_in_single_sector(self, 
                                            sector: SectorStrength,
                                            valid_stocks: List[Dict[str, Any]],
                                            stock_returns: Dict[str, float],
                                            params: StockScreeningParams) -> List[StockStrength]:
        """在单个板块中筛选个股"""
        try:
            stock_strengths = []
            
            # 计算每只股票的相对强弱度
            for stock in valid_stocks:
                stock_code = stock['stock_code']
                stock_return = stock_returns.get(stock_code)
                
                if stock_return is not None:
                    relative_strength = stock_return - sector.sector_return
                    
                    # 筛选条件：个股涨幅 > 板块涨幅
                    if relative_strength >= params.min_relative_strength:
                        strength = StockStrength(
                            stock_code=stock_code,
                            stock_name=stock['stock_name'],
                            sector_code=sector.sector_code,
                            sector_name=sector.sector_name,
                            stock_return=stock_return,
                            sector_return=sector.sector_return,
                            relative_strength=relative_strength,
                            sector_rank=sector.rank
                        )
                        stock_strengths.append(strength)
            
            # 按相对强弱度排序
            stock_strengths.sort(key=lambda x: x.relative_strength, reverse=True)
            
            # 填充板块内排名
            for i, stock in enumerate(stock_strengths):
                stock.rank_in_sector = i + 1
            
            # 选择前N只股票
            selected_stocks = stock_strengths[:params.stocks_per_sector]
            
            logger.debug(f"板块{sector.sector_name}筛选出{len(selected_stocks)}只股票")
            return selected_stocks
            
        except Exception as e:
            logger.error(f"板块内个股筛选失败: {e}")
            return []
    
    def _deduplicate_and_rank(self, 
                            all_stocks: List[StockStrength],
                            params: StockScreeningParams) -> List[StockStrength]:
        """去重和最终排序"""
        try:
            # 去重：如果股票在多个板块中都被选中，保留得分最高的
            stock_dict = {}
            
            for stock in all_stocks:
                stock_code = stock.stock_code
                
                # 计算综合得分：相对强弱度 + 板块排名权重
                sector_weight = 1.0 / stock.sector_rank  # 板块排名越高权重越大
                stock.final_score = stock.relative_strength + sector_weight * 0.1
                
                if stock_code not in stock_dict or stock.final_score > stock_dict[stock_code].final_score:
                    stock_dict[stock_code] = stock
            
            # 转换为列表并排序
            unique_stocks = list(stock_dict.values())
            unique_stocks.sort(key=lambda x: x.final_score, reverse=True)
            
            # 限制总数量
            final_stocks = unique_stocks[:params.max_stocks_total]
            
            logger.info(f"去重后股票数量：{len(final_stocks)}/{len(all_stocks)}")
            return final_stocks
            
        except Exception as e:
            logger.error(f"去重和排序失败: {e}")
            return []
    
    def analyze_stock_distribution(self, 
                                 result: StockScreeningResult) -> Dict[str, Any]:
        """分析股票分布情况"""
        try:
            # 按板块统计
            sector_stats = {}
            for stock in result.selected_stocks:
                sector_code = stock.sector_code
                if sector_code not in sector_stats:
                    sector_stats[sector_code] = {
                        'sector_name': stock.sector_name,
                        'stock_count': 0,
                        'avg_relative_strength': 0.0,
                        'stocks': []
                    }
                
                sector_stats[sector_code]['stock_count'] += 1
                sector_stats[sector_code]['stocks'].append({
                    'stock_code': stock.stock_code,
                    'stock_name': stock.stock_name,
                    'relative_strength': stock.relative_strength,
                    'rank_in_sector': stock.rank_in_sector
                })
            
            # 计算平均相对强弱度
            for sector_code, stats in sector_stats.items():
                stocks = stats['stocks']
                avg_strength = sum(s['relative_strength'] for s in stocks) / len(stocks)
                stats['avg_relative_strength'] = avg_strength
            
            # 整体统计
            total_relative_strength = sum(s.relative_strength for s in result.selected_stocks)
            avg_relative_strength = total_relative_strength / len(result.selected_stocks) if result.selected_stocks else 0
            
            return {
                'total_stocks': len(result.selected_stocks),
                'sectors_represented': len(sector_stats),
                'avg_relative_strength': avg_relative_strength,
                'sector_distribution': sector_stats,
                'top_performers': [
                    {
                        'stock_code': s.stock_code,
                        'stock_name': s.stock_name,
                        'sector_name': s.sector_name,
                        'relative_strength': s.relative_strength,
                        'final_score': s.final_score
                    }
                    for s in result.selected_stocks[:10]  # 前10名
                ]
            }
            
        except Exception as e:
            logger.error(f"分析股票分布失败: {e}")
            return {}
