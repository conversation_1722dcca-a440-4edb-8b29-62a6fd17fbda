#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实数据功能

验证数据下载、存储和显示功能
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig
from src.database.database_manager import DatabaseManager
from src.services.data_download_service import DataDownloadService, DownloadProgress
from src.services.data_service import DataService
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_database_manager():
    """测试数据库管理器"""
    print("=" * 60)
    print("测试数据库管理器")
    print("=" * 60)
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager("test_data.db")
        
        # 获取统计信息
        stats = db_manager.get_database_stats()
        print(f"数据库统计信息: {stats}")
        
        # 获取股票列表
        stocks = db_manager.get_stock_list()
        print(f"数据库中的股票数量: {len(stocks)}")
        
        if stocks:
            print("前5只股票:")
            for stock in stocks[:5]:
                print(f"  {stock['stock_code']} - {stock['stock_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库管理器测试失败: {e}")
        return False


def test_data_source_connection():
    """测试数据源连接"""
    print("\n" + "=" * 60)
    print("测试数据源连接")
    print("=" * 60)
    
    try:
        # 创建数据源管理器
        data_manager = DataSourceManager()
        
        # 创建XtData配置
        config = DataSourceConfig(
            name="xtdata_test",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                "host": "127.0.0.1",
                "port": 58610,
                "username": "",
                "password": ""
            }
        )
        
        # 创建XtData适配器
        xtdata_adapter = XtDataAdapter(config)
        
        # 注册数据源
        data_manager.register_source("xtdata", xtdata_adapter, priority=10)
        
        # 测试连接
        if data_manager.test_connection("xtdata"):
            print("✅ XtData连接成功")
            
            # 获取股票列表
            best_source = data_manager.get_best_source()
            if best_source:
                stock_list = best_source.get_stock_list()
                print(f"✅ 获取到 {len(stock_list)} 只股票")
                
                if stock_list:
                    print("前10只股票:")
                    for stock in stock_list[:10]:
                        print(f"  {stock}")
                
                return True, data_manager
            else:
                print("❌ 无法获取数据源")
                return False, None
        else:
            print("❌ XtData连接失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 数据源连接测试失败: {e}")
        return False, None


async def test_data_download():
    """测试数据下载"""
    print("\n" + "=" * 60)
    print("测试数据下载")
    print("=" * 60)
    
    try:
        # 测试数据源连接
        success, data_manager = test_data_source_connection()
        if not success:
            print("❌ 数据源连接失败，跳过下载测试")
            return False
        
        # 创建数据库管理器
        db_manager = DatabaseManager("test_download.db")
        
        # 创建下载服务
        download_service = DataDownloadService(data_manager, db_manager)
        
        # 进度回调函数
        def progress_callback(progress: DownloadProgress):
            print(f"进度: {progress.progress_percent:.1f}% - "
                  f"阶段: {progress.stage} - "
                  f"当前: {progress.current_stock} - "
                  f"完成: {progress.completed_stocks}/{progress.total_stocks}")
        
        print("开始下载数据（仅下载前50只股票用于测试）...")
        
        # 修改下载服务以限制股票数量
        original_method = download_service._download_stock_list
        
        async def limited_stock_list():
            full_list = await original_method()
            return full_list[:50] if full_list else []
        
        download_service._download_stock_list = limited_stock_list
        
        # 开始下载
        success = await download_service.download_all_data(
            progress_callback=progress_callback,
            include_history=False  # 不包含历史数据以加快测试
        )
        
        if success:
            print("✅ 数据下载完成")
            
            # 检查数据库
            stats = db_manager.get_database_stats()
            print(f"下载后数据库统计: {stats}")
            
            return True
        else:
            print("❌ 数据下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据下载测试失败: {e}")
        return False


def test_data_service():
    """测试数据服务"""
    print("\n" + "=" * 60)
    print("测试数据服务")
    print("=" * 60)
    
    try:
        # 测试数据源连接
        success, data_manager = test_data_source_connection()
        if not success:
            print("❌ 数据源连接失败，使用模拟数据测试")
            data_manager = None
        
        # 创建数据服务（真实数据模式）
        data_service = DataService(
            data_source_manager=data_manager,
            use_real_data=True
        )
        
        # 加载股票列表
        stock_list = data_service.load_stock_list()
        print(f"✅ 加载股票列表: {len(stock_list)} 只股票")
        
        if stock_list:
            print("前5只股票:")
            for stock in stock_list[:5]:
                print(f"  {stock.symbol} - {stock.name} - {stock.price}")
        
        # 获取数据更新信息
        update_info = data_service.get_data_update_info()
        print(f"数据更新信息: {update_info}")
        
        # 测试获取股票数据
        if stock_list:
            test_symbol = stock_list[0].symbol
            stock_data = data_service.get_stock_data(test_symbol)
            if stock_data is not None and not stock_data.empty:
                print(f"✅ 获取股票数据 {test_symbol}: {len(stock_data)} 条记录")
                print(f"最新数据: {stock_data.tail(1).to_dict('records')}")
            else:
                print(f"⚠️ 股票数据为空: {test_symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("威科夫相对强弱选股系统 - 真实数据功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 数据库管理器
    result1 = test_database_manager()
    test_results.append(("数据库管理器", result1))
    
    # 测试2: 数据源连接
    result2, _ = test_data_source_connection()
    test_results.append(("数据源连接", result2))
    
    # 测试3: 数据下载（仅在连接成功时）
    if result2:
        result3 = await test_data_download()
        test_results.append(("数据下载", result3))
    else:
        print("\n⚠️ 跳过数据下载测试（数据源连接失败）")
        test_results.append(("数据下载", False))
    
    # 测试4: 数据服务
    result4 = test_data_service()
    test_results.append(("数据服务", result4))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
    
    success_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！真实数据功能正常工作")
    elif success_count > 0:
        print("⚠️ 部分测试通过，请检查失败的项目")
    else:
        print("❌ 所有测试失败，请检查系统配置")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
