"""
操作成功反馈系统

为用户操作提供详细的成功反馈、进度显示和结果统计
"""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QTextEdit, QFrame, QScrollArea, QWidget
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPixmap, QIcon
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class OperationResult:
    """操作结果"""
    operation_name: str          # 操作名称
    success: bool               # 是否成功
    start_time: datetime        # 开始时间
    end_time: datetime          # 结束时间
    data_processed: int         # 处理的数据量
    results_count: int          # 结果数量
    details: Dict[str, Any]     # 详细信息
    warnings: List[str]         # 警告信息
    
    @property
    def duration(self) -> float:
        """操作耗时（秒）"""
        return (self.end_time - self.start_time).total_seconds()


class OperationProgressTracker(QObject):
    """操作进度跟踪器"""
    
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态消息
    operation_completed = pyqtSignal(object)  # 操作结果
    
    def __init__(self):
        super().__init__()
        self.current_operation = None
        self.start_time = None
        self.total_steps = 0
        self.current_step = 0
        
    def start_operation(self, operation_name: str, total_steps: int = 100):
        """
        开始操作跟踪
        
        Args:
            operation_name: 操作名称
            total_steps: 总步骤数
        """
        self.current_operation = operation_name
        self.start_time = datetime.now()
        self.total_steps = total_steps
        self.current_step = 0
        
        self.progress_updated.emit(0, f"开始{operation_name}...")
        logger.info(f"开始操作跟踪: {operation_name}")
        
    def update_progress(self, step: int, message: str = ""):
        """
        更新进度
        
        Args:
            step: 当前步骤
            message: 状态消息
        """
        self.current_step = step
        progress = int((step / self.total_steps) * 100) if self.total_steps > 0 else 0
        
        status_message = message or f"正在执行{self.current_operation}... ({step}/{self.total_steps})"
        self.progress_updated.emit(progress, status_message)
        
    def complete_operation(self, success: bool, data_processed: int = 0, 
                          results_count: int = 0, details: Optional[Dict[str, Any]] = None,
                          warnings: Optional[List[str]] = None):
        """
        完成操作
        
        Args:
            success: 是否成功
            data_processed: 处理的数据量
            results_count: 结果数量
            details: 详细信息
            warnings: 警告信息
        """
        if not self.current_operation or not self.start_time:
            return
            
        result = OperationResult(
            operation_name=self.current_operation,
            success=success,
            start_time=self.start_time,
            end_time=datetime.now(),
            data_processed=data_processed,
            results_count=results_count,
            details=details or {},
            warnings=warnings or []
        )
        
        self.operation_completed.emit(result)
        
        # 重置状态
        self.current_operation = None
        self.start_time = None
        self.current_step = 0
        self.total_steps = 0


class OperationSuccessDialog(QDialog):
    """操作成功对话框"""
    
    def __init__(self, result: OperationResult, parent: Optional[QWidget] = None):
        """
        初始化操作成功对话框
        
        Args:
            result: 操作结果
            parent: 父控件
        """
        super().__init__(parent)
        self.result = result
        
        self._init_ui()
        self._apply_styles()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("操作完成")
        self.setFixedSize(500, 400)
        self.setModal(False)  # 非模态，允许用户继续操作
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 成功图标和标题
        header_layout = QHBoxLayout()
        
        icon_label = QLabel("✅")
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(f"{self.result.operation_name}完成")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #28a745;
                margin-left: 10px;
            }
        """)
        header_layout.addWidget(title_label, 1)
        
        layout.addLayout(header_layout)
        
        # 操作统计
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Shape.Box)
        stats_layout = QVBoxLayout(stats_frame)
        
        # 耗时
        duration_label = QLabel(f"⏱️ 耗时: {self.result.duration:.2f} 秒")
        stats_layout.addWidget(duration_label)
        
        # 处理数据量
        if self.result.data_processed > 0:
            data_label = QLabel(f"📊 处理数据: {self.result.data_processed:,} 条")
            stats_layout.addWidget(data_label)
        
        # 结果数量
        if self.result.results_count > 0:
            results_label = QLabel(f"📋 生成结果: {self.result.results_count:,} 项")
            stats_layout.addWidget(results_label)
        
        # 完成时间
        time_label = QLabel(f"🕐 完成时间: {self.result.end_time.strftime('%H:%M:%S')}")
        stats_layout.addWidget(time_label)
        
        layout.addWidget(stats_frame)
        
        # 详细信息
        if self.result.details:
            details_label = QLabel("详细信息:")
            details_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
            layout.addWidget(details_label)
            
            details_text = QTextEdit()
            details_text.setReadOnly(True)
            details_text.setMaximumHeight(120)
            
            details_content = ""
            for key, value in self.result.details.items():
                details_content += f"{key}: {value}\n"
            
            details_text.setPlainText(details_content)
            layout.addWidget(details_text)
        
        # 警告信息
        if self.result.warnings:
            warnings_label = QLabel("注意事项:")
            warnings_label.setStyleSheet("font-weight: bold; color: #ffc107; margin-top: 10px;")
            layout.addWidget(warnings_label)
            
            for warning in self.result.warnings:
                warning_label = QLabel(f"⚠️ {warning}")
                warning_label.setWordWrap(True)
                warning_label.setStyleSheet("color: #856404; margin-left: 10px;")
                layout.addWidget(warning_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("确定")
        close_button.clicked.connect(self.accept)
        close_button.setDefault(True)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
            }
            QLabel {
                font-size: 14px;
                margin: 2px 0;
            }
            QTextEdit {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)


class OperationFeedbackManager:
    """操作反馈管理器"""
    
    def __init__(self, main_window):
        """
        初始化操作反馈管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.progress_tracker = OperationProgressTracker()
        self.operation_history: List[OperationResult] = []
        
        # 连接信号
        self.progress_tracker.operation_completed.connect(self._on_operation_completed)
        
        logger.info("操作反馈管理器初始化完成")
        
    def start_operation_tracking(self, operation_name: str, total_steps: int = 100) -> OperationProgressTracker:
        """
        开始操作跟踪
        
        Args:
            operation_name: 操作名称
            total_steps: 总步骤数
            
        Returns:
            进度跟踪器
        """
        self.progress_tracker.start_operation(operation_name, total_steps)
        return self.progress_tracker
        
    def show_success_feedback(self, result: OperationResult):
        """
        显示成功反馈
        
        Args:
            result: 操作结果
        """
        try:
            dialog = OperationSuccessDialog(result, self.main_window)
            dialog.show()
            
            # 同时显示简短的通知
            if hasattr(self.main_window, 'ux_enhancer'):
                summary = f"{result.operation_name}完成"
                if result.results_count > 0:
                    summary += f"，生成{result.results_count}项结果"
                self.main_window.ux_enhancer.show_success(summary)
                
            logger.info(f"显示操作成功反馈: {result.operation_name}")
            
        except Exception as e:
            logger.error(f"显示操作成功反馈失败: {e}")
            
    def _on_operation_completed(self, result: OperationResult):
        """操作完成处理"""
        # 添加到历史记录
        self.operation_history.append(result)
        
        # 保持历史记录数量限制
        if len(self.operation_history) > 100:
            self.operation_history = self.operation_history[-100:]
        
        # 显示成功反馈
        if result.success:
            self.show_success_feedback(result)
        
        logger.info(f"操作完成: {result.operation_name}, 成功: {result.success}")
        
    def get_operation_history(self) -> List[OperationResult]:
        """获取操作历史"""
        return self.operation_history.copy()
        
    def clear_operation_history(self):
        """清空操作历史"""
        self.operation_history.clear()
        logger.info("操作历史已清空")


# 操作反馈的便捷函数
def create_data_refresh_result(processed_count: int, success_count: int, 
                              warnings: Optional[List[str]] = None) -> Dict[str, Any]:
    """创建数据刷新结果"""
    return {
        'data_processed': processed_count,
        'results_count': success_count,
        'details': {
            '成功加载': success_count,
            '处理总数': processed_count,
            '成功率': f"{(success_count/processed_count*100):.1f}%" if processed_count > 0 else "0%"
        },
        'warnings': warnings or []
    }


def create_analysis_result(analyzed_stocks: int, analysis_results: int,
                          analysis_type: str, warnings: Optional[List[str]] = None) -> Dict[str, Any]:
    """创建分析结果"""
    return {
        'data_processed': analyzed_stocks,
        'results_count': analysis_results,
        'details': {
            '分析类型': analysis_type,
            '分析股票': analyzed_stocks,
            '有效结果': analysis_results,
            '分析覆盖率': f"{(analysis_results/analyzed_stocks*100):.1f}%" if analyzed_stocks > 0 else "0%"
        },
        'warnings': warnings or []
    }


def create_selection_result(total_stocks: int, selected_stocks: int,
                           strategy_name: str, warnings: Optional[List[str]] = None) -> Dict[str, Any]:
    """创建选股结果"""
    return {
        'data_processed': total_stocks,
        'results_count': selected_stocks,
        'details': {
            '选股策略': strategy_name,
            '候选股票': total_stocks,
            '筛选结果': selected_stocks,
            '选中比例': f"{(selected_stocks/total_stocks*100):.1f}%" if total_stocks > 0 else "0%"
        },
        'warnings': warnings or []
    }
