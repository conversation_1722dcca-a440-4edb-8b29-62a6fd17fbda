"""
威科夫相对强弱选股系统 - 数据库迁移机制

提供数据库版本管理和迁移功能，支持安全的数据库结构升级
"""

import os
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass

from ..utils.logger import get_logger
from .manager import DatabaseManager

logger = get_logger(__name__)


@dataclass
class Migration:
    """迁移定义"""
    version: str
    description: str
    up_sql: str
    down_sql: Optional[str] = None
    pre_check: Optional[Callable] = None
    post_check: Optional[Callable] = None


class MigrationManager:
    """
    数据库迁移管理器
    
    负责数据库版本管理和结构升级，确保数据库模式的安全演进
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化迁移管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.migrations: List[Migration] = []
        self._register_migrations()
        
        logger.info("数据库迁移管理器初始化完成")
    
    def _register_migrations(self) -> None:
        """注册所有迁移"""
        
        # 迁移 1.0.1 - 添加数据质量监控表
        migration_1_0_1 = Migration(
            version="1.0.1",
            description="添加数据质量监控表",
            up_sql="""
            -- 数据质量监控表
            CREATE TABLE IF NOT EXISTS data_quality_monitor (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name VARCHAR(50) NOT NULL,
                check_type VARCHAR(30) NOT NULL,
                check_date DATE NOT NULL,
                total_records INTEGER DEFAULT 0,
                valid_records INTEGER DEFAULT 0,
                invalid_records INTEGER DEFAULT 0,
                quality_score DECIMAL(5,2),
                issues_found TEXT,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_data_quality_table ON data_quality_monitor(table_name);
            CREATE INDEX IF NOT EXISTS idx_data_quality_date ON data_quality_monitor(check_date);
            """,
            down_sql="DROP TABLE IF EXISTS data_quality_monitor;"
        )
        
        # 迁移 1.0.2 - 添加性能监控表
        migration_1_0_2 = Migration(
            version="1.0.2",
            description="添加性能监控表",
            up_sql="""
            -- 性能监控表
            CREATE TABLE IF NOT EXISTS performance_monitor (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operation_type VARCHAR(50) NOT NULL,
                operation_name VARCHAR(100) NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                duration_ms INTEGER,
                records_processed INTEGER DEFAULT 0,
                memory_usage_mb DECIMAL(10,2),
                cpu_usage_pct DECIMAL(5,2),
                status VARCHAR(20) DEFAULT 'running',
                error_message TEXT,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_performance_type ON performance_monitor(operation_type);
            CREATE INDEX IF NOT EXISTS idx_performance_time ON performance_monitor(start_time);
            CREATE INDEX IF NOT EXISTS idx_performance_status ON performance_monitor(status);
            """,
            down_sql="DROP TABLE IF EXISTS performance_monitor;"
        )
        
        # 迁移 1.0.3 - 添加用户设置表
        migration_1_0_3 = Migration(
            version="1.0.3",
            description="添加用户设置表",
            up_sql="""
            -- 用户设置表
            CREATE TABLE IF NOT EXISTS user_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_group VARCHAR(50) NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(setting_group, setting_key)
            );
            
            CREATE INDEX IF NOT EXISTS idx_user_settings_group ON user_settings(setting_group);
            CREATE INDEX IF NOT EXISTS idx_user_settings_key ON user_settings(setting_key);
            
            -- 插入默认用户设置
            INSERT OR IGNORE INTO user_settings (setting_group, setting_key, setting_value, setting_type, description) VALUES
            ('ui', 'theme', 'light', 'string', '界面主题'),
            ('ui', 'language', 'zh_CN', 'string', '界面语言'),
            ('ui', 'window_width', '1200', 'integer', '窗口宽度'),
            ('ui', 'window_height', '800', 'integer', '窗口高度'),
            ('selection', 'default_top_sectors', '5', 'integer', '默认强势板块数量'),
            ('selection', 'default_stocks_per_sector', '3', 'integer', '默认每板块选股数'),
            ('selection', 'exclude_st_default', 'true', 'boolean', '默认排除ST股票'),
            ('data', 'auto_update_enabled', 'true', 'boolean', '启用自动数据更新'),
            ('data', 'update_interval_minutes', '60', 'integer', '数据更新间隔（分钟）');
            """,
            down_sql="DROP TABLE IF EXISTS user_settings;"
        )
        
        # 迁移 1.0.4 - 优化索引结构
        migration_1_0_4 = Migration(
            version="1.0.4",
            description="优化索引结构",
            up_sql="""
            -- 添加复合索引以优化查询性能
            CREATE INDEX IF NOT EXISTS idx_stock_quotes_code_date_close ON stock_quotes(stock_code, trade_date, close);
            CREATE INDEX IF NOT EXISTS idx_sector_quotes_code_date_close ON sector_quotes(sector_code, trade_date, close);
            
            -- 为相对强弱结果表添加复合索引
            CREATE INDEX IF NOT EXISTS idx_rs_results_type_date_strength ON relative_strength_results(symbol_type, start_date, end_date, relative_strength DESC);
            
            -- 为筛选结果添加复合索引
            CREATE INDEX IF NOT EXISTS idx_selection_details_task_rank ON selection_details(task_id, rank_order);
            """,
            down_sql="""
            DROP INDEX IF EXISTS idx_stock_quotes_code_date_close;
            DROP INDEX IF EXISTS idx_sector_quotes_code_date_close;
            DROP INDEX IF EXISTS idx_rs_results_type_date_strength;
            DROP INDEX IF EXISTS idx_selection_details_task_rank;
            """
        )
        
        # 注册所有迁移
        self.migrations.extend([
            migration_1_0_1,
            migration_1_0_2,
            migration_1_0_3,
            migration_1_0_4
        ])
    
    def _ensure_migration_table(self) -> None:
        """确保迁移记录表存在"""
        try:
            sql = """
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version VARCHAR(20) UNIQUE NOT NULL,
                description TEXT,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                execution_time_ms INTEGER,
                success BOOLEAN DEFAULT 1
            );
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_version ON schema_migrations(version);
            """
            
            with self.db_manager.get_connection() as conn:
                conn.executescript(sql)
                conn.commit()
                
        except Exception as e:
            logger.error(f"创建迁移记录表失败: {e}")
            raise
    
    def get_current_version(self) -> str:
        """
        获取当前数据库版本
        
        Returns:
            str: 当前版本号
        """
        try:
            # 首先尝试从system_config表获取
            version = self.db_manager.get_config_value('db_version', '1.0.0')
            
            # 验证版本是否与迁移记录一致
            self._ensure_migration_table()
            
            sql = "SELECT MAX(version) FROM schema_migrations WHERE success = 1"
            result = self.db_manager.execute_query(sql)
            
            migration_version = result[0][0] if result and result[0][0] else '1.0.0'
            
            # 如果版本不一致，以迁移记录为准
            if version != migration_version:
                logger.warning(f"版本不一致，配置版本: {version}, 迁移版本: {migration_version}")
                version = migration_version
                self.db_manager.set_config_value('db_version', version)
            
            return version
            
        except Exception as e:
            logger.error(f"获取当前版本失败: {e}")
            return '1.0.0'
    
    def get_pending_migrations(self) -> List[Migration]:
        """
        获取待执行的迁移
        
        Returns:
            List[Migration]: 待执行的迁移列表
        """
        try:
            current_version = self.get_current_version()
            
            # 获取已应用的迁移
            self._ensure_migration_table()
            sql = "SELECT version FROM schema_migrations WHERE success = 1"
            applied_results = self.db_manager.execute_query(sql)
            applied_versions = {row[0] for row in applied_results}
            
            # 筛选待执行的迁移
            pending_migrations = []
            for migration in self.migrations:
                if migration.version not in applied_versions and migration.version > current_version:
                    pending_migrations.append(migration)
            
            # 按版本号排序
            pending_migrations.sort(key=lambda m: m.version)
            
            return pending_migrations
            
        except Exception as e:
            logger.error(f"获取待执行迁移失败: {e}")
            return []
    
    def apply_migration(self, migration: Migration) -> bool:
        """
        应用单个迁移
        
        Args:
            migration: 要应用的迁移
            
        Returns:
            bool: 迁移是否成功
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始应用迁移 {migration.version}: {migration.description}")
            
            # 执行前置检查
            if migration.pre_check and not migration.pre_check():
                logger.error(f"迁移 {migration.version} 前置检查失败")
                return False
            
            # 创建备份点
            backup_success = self._create_migration_backup(migration.version)
            if not backup_success:
                logger.warning(f"迁移 {migration.version} 备份创建失败，但继续执行")
            
            # 执行迁移SQL
            with self.db_manager.get_connection() as conn:
                # 开启事务
                conn.execute("BEGIN TRANSACTION")
                
                try:
                    # 分割并执行SQL语句
                    statements = [stmt.strip() for stmt in migration.up_sql.split(';') if stmt.strip()]
                    for statement in statements:
                        if statement:
                            conn.execute(statement)
                    
                    # 记录迁移应用
                    execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                    
                    self._ensure_migration_table()
                    conn.execute("""
                        INSERT OR REPLACE INTO schema_migrations 
                        (version, description, execution_time_ms, success)
                        VALUES (?, ?, ?, 1)
                    """, (migration.version, migration.description, execution_time))
                    
                    # 更新系统配置中的版本号
                    conn.execute("""
                        INSERT OR REPLACE INTO system_config 
                        (config_key, config_value, config_type, update_time)
                        VALUES ('db_version', ?, 'string', CURRENT_TIMESTAMP)
                    """, (migration.version,))
                    
                    # 提交事务
                    conn.commit()
                    
                    # 执行后置检查
                    if migration.post_check and not migration.post_check():
                        raise Exception("后置检查失败")
                    
                    logger.info(f"迁移 {migration.version} 应用成功，耗时: {execution_time}ms")
                    return True
                    
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    raise e
                    
        except Exception as e:
            # 记录失败的迁移
            try:
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                self._record_failed_migration(migration, str(e), execution_time)
            except:
                pass
            
            logger.error(f"迁移 {migration.version} 应用失败: {e}")
            return False
    
    def rollback_migration(self, version: str) -> bool:
        """
        回滚指定版本的迁移
        
        Args:
            version: 要回滚的版本号
            
        Returns:
            bool: 回滚是否成功
        """
        try:
            # 查找对应的迁移
            migration = None
            for m in self.migrations:
                if m.version == version:
                    migration = m
                    break
            
            if not migration:
                logger.error(f"未找到版本 {version} 的迁移定义")
                return False
            
            if not migration.down_sql:
                logger.error(f"迁移 {version} 没有定义回滚SQL")
                return False
            
            logger.info(f"开始回滚迁移 {version}: {migration.description}")
            
            # 执行回滚SQL
            with self.db_manager.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                try:
                    # 分割并执行回滚SQL语句
                    statements = [stmt.strip() for stmt in migration.down_sql.split(';') if stmt.strip()]
                    for statement in statements:
                        if statement:
                            conn.execute(statement)
                    
                    # 删除迁移记录
                    conn.execute("DELETE FROM schema_migrations WHERE version = ?", (version,))
                    
                    # 更新版本号到前一个版本
                    previous_version = self._get_previous_version(version)
                    conn.execute("""
                        UPDATE system_config 
                        SET config_value = ?, update_time = CURRENT_TIMESTAMP
                        WHERE config_key = 'db_version'
                    """, (previous_version,))
                    
                    conn.commit()
                    
                    logger.info(f"迁移 {version} 回滚成功")
                    return True
                    
                except Exception as e:
                    conn.rollback()
                    raise e
                    
        except Exception as e:
            logger.error(f"迁移 {version} 回滚失败: {e}")
            return False
    
    def migrate_to_latest(self) -> bool:
        """
        迁移到最新版本
        
        Returns:
            bool: 迁移是否成功
        """
        try:
            pending_migrations = self.get_pending_migrations()
            
            if not pending_migrations:
                logger.info("数据库已是最新版本，无需迁移")
                return True
            
            logger.info(f"发现 {len(pending_migrations)} 个待执行的迁移")
            
            # 依次应用所有待执行的迁移
            for migration in pending_migrations:
                if not self.apply_migration(migration):
                    logger.error(f"迁移过程在版本 {migration.version} 处失败")
                    return False
            
            logger.info("所有迁移应用成功，数据库已更新到最新版本")
            return True
            
        except Exception as e:
            logger.error(f"迁移到最新版本失败: {e}")
            return False
    
    def _create_migration_backup(self, version: str) -> bool:
        """
        为迁移创建备份
        
        Args:
            version: 迁移版本号
            
        Returns:
            bool: 备份是否成功
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f'migration_backup_{version}_{timestamp}.db'
            
            return self.db_manager.backup_database(backup_name)
            
        except Exception as e:
            logger.error(f"创建迁移备份失败: {e}")
            return False
    
    def _record_failed_migration(self, migration: Migration, error_message: str, execution_time: int) -> None:
        """
        记录失败的迁移
        
        Args:
            migration: 失败的迁移
            error_message: 错误信息
            execution_time: 执行时间（毫秒）
        """
        try:
            self._ensure_migration_table()
            
            sql = """
            INSERT INTO schema_migrations 
            (version, description, execution_time_ms, success)
            VALUES (?, ?, ?, 0)
            """
            
            self.db_manager.execute_non_query(
                sql, (migration.version, f"{migration.description} (FAILED: {error_message})", execution_time)
            )
            
        except Exception as e:
            logger.error(f"记录失败迁移失败: {e}")
    
    def _get_previous_version(self, current_version: str) -> str:
        """
        获取前一个版本号
        
        Args:
            current_version: 当前版本号
            
        Returns:
            str: 前一个版本号
        """
        try:
            # 获取所有已应用的版本，按版本号排序
            sql = "SELECT version FROM schema_migrations WHERE success = 1 AND version < ? ORDER BY version DESC LIMIT 1"
            result = self.db_manager.execute_query(sql, (current_version,))
            
            return result[0][0] if result else '1.0.0'
            
        except Exception as e:
            logger.error(f"获取前一个版本失败: {e}")
            return '1.0.0'
    
    def get_migration_history(self) -> List[Dict[str, Any]]:
        """
        获取迁移历史
        
        Returns:
            List[Dict[str, Any]]: 迁移历史记录
        """
        try:
            self._ensure_migration_table()
            
            sql = """
            SELECT version, description, applied_at, execution_time_ms, success
            FROM schema_migrations
            ORDER BY applied_at DESC
            """
            
            results = self.db_manager.execute_query(sql)
            
            return [
                {
                    'version': row[0],
                    'description': row[1],
                    'applied_at': row[2],
                    'execution_time_ms': row[3],
                    'success': bool(row[4])
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"获取迁移历史失败: {e}")
            return []
    
    def validate_database_schema(self) -> Dict[str, Any]:
        """
        验证数据库架构完整性
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            validation_result = {
                'valid': True,
                'current_version': self.get_current_version(),
                'missing_tables': [],
                'missing_indexes': [],
                'issues': []
            }
            
            # 检查必需的表
            required_tables = [
                'sector_info', 'sector_quotes', 'stock_info', 'stock_quotes',
                'sector_constituents', 'relative_strength_results',
                'selection_results', 'selection_details', 'system_config',
                'sync_logs', 'schema_migrations'
            ]
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = {row[0] for row in cursor.fetchall()}
                
                missing_tables = set(required_tables) - existing_tables
                if missing_tables:
                    validation_result['missing_tables'] = list(missing_tables)
                    validation_result['valid'] = False
                
                # 检查关键索引
                required_indexes = [
                    'idx_stock_quotes_code_date',
                    'idx_sector_quotes_code_date',
                    'idx_rs_results_symbol',
                    'idx_selection_results_task'
                ]
                
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='index'")
                existing_indexes = {row[0] for row in cursor.fetchall() if row[0]}
                
                missing_indexes = set(required_indexes) - existing_indexes
                if missing_indexes:
                    validation_result['missing_indexes'] = list(missing_indexes)
                    validation_result['issues'].append("缺少关键索引")
                
                # 执行完整性检查
                cursor = conn.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()[0]
                
                if integrity_result != 'ok':
                    validation_result['valid'] = False
                    validation_result['issues'].append(f"数据库完整性检查失败: {integrity_result}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"数据库架构验证失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }