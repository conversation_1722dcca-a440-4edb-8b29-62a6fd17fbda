# 威科夫相对强弱选股系统 - 系统设计方案

## 系统架构概览

### 整体架构
采用分层架构模式，从下到上分为：
- **数据访问层**：数据源接口、本地数据库
- **业务逻辑层**：计算引擎、策略管理、数据处理
- **应用服务层**：任务调度、缓存管理、配置管理
- **表示层**：GUI界面、用户交互

### 核心设计原则
- **单一职责**：每个模块专注单一功能
- **开闭原则**：对扩展开放，对修改封闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：细粒度接口设计

## 详细模块设计

### 1. 数据源接口层

#### 1.1 抽象数据源接口
```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
import pandas as pd

class IDataSource(ABC):
    """数据源抽象接口"""
    
    @abstractmethod
    def connect(self) -> bool:
        """建立连接"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开连接"""
        pass
    
    @abstractmethod
    def get_market_data(self, symbol: str, period: str, 
                       start_date: str, end_date: str, 
                       dividend_type: str = 'none') -> pd.DataFrame:
        """获取市场数据"""
        pass
    
    @abstractmethod
    def get_sector_list(self, sector_type: str = 'industry') -> List[Dict]:
        """获取板块列表"""
        pass
    
    @abstractmethod
    def get_sector_constituents(self, sector_code: str) -> List[str]:
        """获取板块成分股"""
        pass
    
    @abstractmethod
    def download_history_data(self, symbols: List[str], 
                            period: str, start_date: str) -> bool:
        """下载历史数据"""
        pass
```

#### 1.2 XtData适配器实现
```python
import xtdata
from .base import IDataSource

class XtDataAdapter(IDataSource):
    """XtData数据源适配器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.connected = False
    
    def connect(self) -> bool:
        """连接到MiniQMT"""
        try:
            if self.config.get('ip') and self.config.get('port'):
                xtdata.reconnect(self.config['ip'], self.config['port'])
            self.connected = True
            return True
        except Exception as e:
            logger.error(f"XtData连接失败: {e}")
            return False
    
    def get_market_data(self, symbol: str, period: str, 
                       start_date: str, end_date: str,
                       dividend_type: str = 'none') -> pd.DataFrame:
        """获取K线数据"""
        try:
            data = xtdata.get_market_data_ex(
                stock_list=[symbol],
                period=period,
                start_time=start_date,
                end_time=end_date,
                dividend_type=dividend_type
            )
            return self._format_market_data(data[symbol])
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return pd.DataFrame()
    
    def _format_market_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """格式化市场数据"""
        if raw_data.empty:
            return pd.DataFrame()
        
        # 标准化列名
        column_mapping = {
            'time': 'trade_date',
            'open': 'open',
            'high': 'high', 
            'low': 'low',
            'close': 'close',
            'volume': 'volume',
            'amount': 'amount'
        }
        
        formatted_data = raw_data.rename(columns=column_mapping)
        formatted_data['trade_date'] = pd.to_datetime(formatted_data['trade_date'])
        return formatted_data[list(column_mapping.values())]
```

#### 1.3 数据源管理器
```python
class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.data_sources: Dict[str, IDataSource] = {}
        self.primary_source: Optional[str] = None
        self.fallback_sources: List[str] = []
    
    def register_source(self, name: str, source: IDataSource, 
                       is_primary: bool = False) -> None:
        """注册数据源"""
        self.data_sources[name] = source
        if is_primary:
            self.primary_source = name
        else:
            self.fallback_sources.append(name)
    
    def get_data_with_fallback(self, method: str, *args, **kwargs):
        """带故障转移的数据获取"""
        sources_to_try = [self.primary_source] + self.fallback_sources
        
        for source_name in sources_to_try:
            if source_name in self.data_sources:
                try:
                    source = self.data_sources[source_name]
                    method_func = getattr(source, method)
                    result = method_func(*args, **kwargs)
                    if self._is_valid_result(result):
                        return result
                except Exception as e:
                    logger.warning(f"数据源 {source_name} 失败: {e}")
                    continue
        
        raise Exception("所有数据源都不可用")
```

### 2. 数据库设计

#### 2.1 数据表结构
```sql
-- 板块基础信息表
CREATE TABLE sector_info (
    sector_code VARCHAR(20) PRIMARY KEY,
    sector_name VARCHAR(100) NOT NULL,
    sector_type VARCHAR(20) NOT NULL, -- industry/concept
    parent_code VARCHAR(20),
    create_date DATE,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 板块历史行情表  
CREATE TABLE sector_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sector_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open DECIMAL(10,3),
    high DECIMAL(10,3), 
    low DECIMAL(10,3),
    close DECIMAL(10,3) NOT NULL,
    volume BIGINT,
    amount DECIMAL(15,2),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(sector_code, trade_date)
);

-- 个股基础信息表
CREATE TABLE stock_info (
    stock_code VARCHAR(20) PRIMARY KEY,
    stock_name VARCHAR(100) NOT NULL,
    list_date DATE,
    delist_date DATE,
    market VARCHAR(10), -- SH/SZ/BJ
    status VARCHAR(10) DEFAULT 'active', -- active/delisted/suspended
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 个股历史行情表
CREATE TABLE stock_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL, 
    open DECIMAL(10,3),
    high DECIMAL(10,3),
    low DECIMAL(10,3), 
    close DECIMAL(10,3) NOT NULL,
    volume BIGINT,
    amount DECIMAL(15,2),
    adj_factor DECIMAL(10,6) DEFAULT 1.0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stock_code, trade_date)
);

-- 板块成分股关系表
CREATE TABLE sector_constituents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sector_code VARCHAR(20) NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    weight DECIMAL(8,4),
    in_date DATE,
    out_date DATE,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(sector_code, stock_code, in_date)
);
```

#### 2.2 数据访问层
```python
class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection_pool = self._create_connection_pool()
    
    def get_sector_quotes(self, sector_code: str, 
                         start_date: str, end_date: str) -> pd.DataFrame:
        """获取板块行情数据"""
        query = """
        SELECT trade_date, open, high, low, close, volume, amount
        FROM sector_quotes 
        WHERE sector_code = ? AND trade_date BETWEEN ? AND ?
        ORDER BY trade_date
        """
        return pd.read_sql_query(query, self.get_connection(), 
                               params=[sector_code, start_date, end_date])
    
    def get_stock_quotes(self, stock_code: str,
                        start_date: str, end_date: str) -> pd.DataFrame:
        """获取个股行情数据"""
        query = """
        SELECT trade_date, open, high, low, close, volume, amount, adj_factor
        FROM stock_quotes
        WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
        ORDER BY trade_date  
        """
        return pd.read_sql_query(query, self.get_connection(),
                               params=[stock_code, start_date, end_date])
    
    def batch_insert_quotes(self, table_name: str, data: pd.DataFrame) -> None:
        """批量插入行情数据"""
        with self.get_connection() as conn:
            data.to_sql(table_name, conn, if_exists='append', 
                       index=False, method='multi')
```

### 3. 计算引擎设计

#### 3.1 相对强弱计算器
```python
import numpy as np
from concurrent.futures import ProcessPoolExecutor
from typing import Tuple, Dict

class RelativeStrengthCalculator:
    """相对强弱计算引擎"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.cache = {}
    
    def calculate_returns_batch(self, symbols: List[str], 
                              start_date: str, end_date: str,
                              symbol_type: str = 'stock') -> Dict[str, float]:
        """批量计算收益率"""
        cache_key = f"{symbol_type}_{start_date}_{end_date}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 并行计算收益率
        with ProcessPoolExecutor(max_workers=4) as executor:
            futures = []
            for symbol in symbols:
                future = executor.submit(
                    self._calculate_single_return,
                    symbol, start_date, end_date, symbol_type
                )
                futures.append((symbol, future))
            
            results = {}
            for symbol, future in futures:
                try:
                    results[symbol] = future.result(timeout=30)
                except Exception as e:
                    logger.error(f"计算{symbol}收益率失败: {e}")
                    results[symbol] = 0.0
        
        self.cache[cache_key] = results
        return results
    
    def _calculate_single_return(self, symbol: str, start_date: str, 
                               end_date: str, symbol_type: str) -> float:
        """计算单个标的收益率"""
        if symbol_type == 'stock':
            data = self.db_manager.get_stock_quotes(symbol, start_date, end_date)
        else:
            data = self.db_manager.get_sector_quotes(symbol, start_date, end_date)
        
        if len(data) < 2:
            return 0.0
        
        start_price = data.iloc[0]['close']
        end_price = data.iloc[-1]['close'] 
        
        if start_price <= 0:
            return 0.0
        
        return (end_price - start_price) / start_price * 100
```

#### 3.2 筛选策略引擎
```python
@dataclass
class SelectionParams:
    """筛选参数"""
    start_date: str
    end_date: str
    market_index: str = '000300.SH'  # 沪深300
    sector_types: List[str] = field(default_factory=lambda: ['industry'])
    top_sectors: int = 5
    stocks_per_sector: int = 3
    min_market_cap: float = 0  # 最小市值(亿)
    exclude_st: bool = True
    exclude_new_stock_days: int = 60

class SelectionEngine:
    """筛选引擎"""
    
    def __init__(self, calculator: RelativeStrengthCalculator,
                 db_manager: DatabaseManager):
        self.calculator = calculator
        self.db_manager = db_manager
    
    def execute_selection(self, params: SelectionParams) -> Dict:
        """执行筛选流程"""
        logger.info(f"开始执行筛选: {params.start_date} -> {params.end_date}")
        
        # 第一步：计算大盘收益率
        market_return = self.calculator._calculate_single_return(
            params.market_index, params.start_date, params.end_date, 'stock'
        )
        
        # 第二步：筛选强势板块
        strong_sectors = self._select_strong_sectors(params, market_return)
        
        # 第三步：筛选板块内强势个股
        selected_stocks = self._select_stocks_in_sectors(params, strong_sectors)
        
        return {
            'timestamp': datetime.now(),
            'params': params,
            'market_return': market_return,
            'strong_sectors': strong_sectors,
            'selected_stocks': selected_stocks,
            'summary': self._generate_summary(selected_stocks)
        }
    
    def _select_strong_sectors(self, params: SelectionParams, 
                             market_return: float) -> List[Dict]:
        """筛选强势板块"""
        # 获取所有板块
        sectors = self.db_manager.get_sectors_by_types(params.sector_types)
        sector_codes = [s['sector_code'] for s in sectors]
        
        # 批量计算板块收益率
        sector_returns = self.calculator.calculate_returns_batch(
            sector_codes, params.start_date, params.end_date, 'sector'
        )
        
        # 筛选强于大盘的板块
        strong_sectors = []
        for sector in sectors:
            sector_code = sector['sector_code']
            sector_return = sector_returns.get(sector_code, 0)
            
            if sector_return > market_return:
                relative_strength = sector_return - market_return
                strong_sectors.append({
                    'sector_code': sector_code,
                    'sector_name': sector['sector_name'],
                    'sector_return': sector_return,
                    'relative_strength': relative_strength
                })
        
        # 按相对强弱度排序，取前N个
        strong_sectors.sort(key=lambda x: x['relative_strength'], reverse=True)
        return strong_sectors[:params.top_sectors]
```

### 4. 用户界面设计

#### 4.1 主界面架构
```python
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("威科夫相对强弱选股系统")
        self.setMinimumSize(1200, 800)
        
        # 创建中央widget和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(self.tab_widget)
        
        # 添加各功能页面
        self.selection_tab = SelectionTab()
        self.data_tab = DataManagementTab()
        self.analysis_tab = AnalysisTab()
        self.settings_tab = SettingsTab()
        
        self.tab_widget.addTab(self.selection_tab, "选股筛选")
        self.tab_widget.addTab(self.data_tab, "数据管理")
        self.tab_widget.addTab(self.analysis_tab, "结果分析")
        self.tab_widget.addTab(self.settings_tab, "系统设置")
        
        # 创建状态栏
        self.status_bar = self.statusBar()
        self.progress_bar = QProgressBar()
        self.status_bar.addPermanentWidget(self.progress_bar)
        self.progress_bar.hide()

class SelectionTab(QWidget):
    """选股筛选页面"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.selection_engine = None
    
    def setup_ui(self):
        layout = QHBoxLayout(self)
        
        # 左侧参数设置面板
        self.param_panel = self.create_param_panel()
        layout.addWidget(self.param_panel, 1)
        
        # 右侧结果显示面板
        self.result_panel = self.create_result_panel()
        layout.addWidget(self.result_panel, 2)
    
    def create_param_panel(self) -> QWidget:
        """创建参数设置面板"""
        panel = QGroupBox("筛选参数")
        layout = QVBoxLayout(panel)
        
        # 时间设置
        time_group = QGroupBox("时间区间")
        time_layout = QGridLayout(time_group)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        
        time_layout.addWidget(QLabel("开始日期:"), 0, 0)
        time_layout.addWidget(self.start_date, 0, 1)
        time_layout.addWidget(QLabel("结束日期:"), 1, 0)
        time_layout.addWidget(self.end_date, 1, 1)
        
        layout.addWidget(time_group)
        
        # 板块设置
        sector_group = QGroupBox("板块设置")
        sector_layout = QGridLayout(sector_group)
        
        self.sector_type_combo = QComboBox()
        self.sector_type_combo.addItems(["行业板块", "概念板块", "全部板块"])
        
        self.top_sectors_spin = QSpinBox()
        self.top_sectors_spin.setRange(1, 20)
        self.top_sectors_spin.setValue(5)
        
        sector_layout.addWidget(QLabel("板块类型:"), 0, 0)
        sector_layout.addWidget(self.sector_type_combo, 0, 1)
        sector_layout.addWidget(QLabel("强势板块数:"), 1, 0)
        sector_layout.addWidget(self.top_sectors_spin, 1, 1)
        
        layout.addWidget(sector_group)
        
        # 个股设置
        stock_group = QGroupBox("个股设置")
        stock_layout = QGridLayout(stock_group)
        
        self.stocks_per_sector_spin = QSpinBox()
        self.stocks_per_sector_spin.setRange(1, 10)
        self.stocks_per_sector_spin.setValue(3)
        
        self.exclude_st_check = QCheckBox("排除ST股票")
        self.exclude_st_check.setChecked(True)
        
        stock_layout.addWidget(QLabel("每板块选股数:"), 0, 0)
        stock_layout.addWidget(self.stocks_per_sector_spin, 0, 1)
        stock_layout.addWidget(self.exclude_st_check, 1, 0, 1, 2)
        
        layout.addWidget(stock_group)
        
        # 执行按钮
        self.execute_btn = QPushButton("开始筛选")
        self.execute_btn.setMinimumHeight(40)
        self.execute_btn.clicked.connect(self.execute_selection)
        layout.addWidget(self.execute_btn)
        
        layout.addStretch()
        return panel
```

### 5. 配置管理设计

#### 5.1 配置文件结构
```yaml
# config.yaml
application:
  name: "威科夫相对强弱选股系统"
  version: "1.0.0"
  debug: false

database:
  path: "./data/database.db"
  backup_path: "./data/backup/"
  auto_backup: true
  backup_interval: 24  # hours

data_sources:
  primary: "xtdata"
  fallback: ["tushare", "akshare"]
  
  xtdata:
    ip: "127.0.0.1"
    port: 58610
    timeout: 30
    retry_times: 3
    
  tushare:
    token: ""
    pro_api: true
    
ui:
  theme: "light"  # light/dark
  language: "zh_CN"
  window_size: [1200, 800]
  auto_save_layout: true

selection:
  default_params:
    market_index: "000300.SH"
    top_sectors: 5
    stocks_per_sector: 3
    exclude_st: true
    min_market_cap: 0

logging:
  level: "INFO"
  file_path: "./logs/app.log"
  max_file_size: "10MB"
  backup_count: 5
```

## 性能优化策略

### 1. 数据访问优化
- **连接池管理**：复用数据库连接
- **批量操作**：减少数据库往返次数
- **索引优化**：关键查询字段建立索引
- **分页查询**：大数据集分页处理

### 2. 计算性能优化
- **向量化计算**：使用NumPy加速数值计算
- **并行处理**：多进程处理独立计算任务
- **内存映射**：大文件使用内存映射
- **结果缓存**：缓存计算结果避免重复计算

### 3. 界面响应优化
- **异步操作**：长时间操作使用后台线程
- **进度反馈**：提供操作进度显示
- **懒加载**：按需加载界面组件
- **虚拟化**：大列表使用虚拟化显示

## 部署架构

### 1. 打包策略
```python
# build.py - 打包脚本
import PyInstaller.__main__

PyInstaller.__main__.run([
    'main.py',
    '--onefile',
    '--windowed',
    '--name=WyckoffRS',
    '--icon=resources/icon.ico',
    '--add-data=resources;resources',
    '--add-data=config.yaml;.',
    '--hidden-import=xtdata',
    '--clean'
])
```

### 2. 安装程序
使用NSIS创建Windows安装程序，包含：
- 程序文件安装
- 开始菜单快捷方式
- 桌面快捷方式
- 卸载程序
- 注册表项管理

### 3. 自动更新机制
- 版本检查服务
- 增量更新下载
- 自动重启升级
- 回滚机制

## 总结

本设计方案采用模块化、可扩展的架构，通过抽象接口实现数据源的灵活切换，使用高效的计算引擎保证性能，提供直观友好的用户界面。整体设计既满足当前需求，又为未来扩展留下了充分的空间。