"""
威科夫相对强弱选股系统 - 数据库模块

本模块提供数据库访问和管理功能，包括：
- 数据库表结构定义和创建
- 数据库连接池管理
- 基础CRUD操作
- 数据库迁移机制
- 批量数据操作优化
"""

from .manager import DatabaseManager
from .connection_pool import DatabaseConnectionPool
from .crud import CRUDOperations
from .migration import MigrationManager
from .optimization import DatabaseOptimizer

__all__ = [
    'DatabaseManager',
    'DatabaseConnectionPool', 
    'CRUDOperations',
    'MigrationManager',
    'DatabaseOptimizer'
]

__version__ = '1.0.0'