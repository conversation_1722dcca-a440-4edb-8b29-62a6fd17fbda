"""
选股策略引擎

实现多策略组合的选股系统：
- 策略参数配置
- 多策略组合框架
- 回测验证系统
- 策略性能评估
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import warnings

from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError
from .wyckoff import WyckoffAnalysisEngine, MarketPhase
from .relative_strength import RelativeStrengthEngine, RSResult

logger = get_logger(__name__)


class StrategyType(Enum):
    """策略类型枚举"""
    WYCKOFF_ACCUMULATION = "wyckoff_accumulation"
    RELATIVE_STRENGTH = "relative_strength"
    MOMENTUM = "momentum"
    VALUE = "value"
    GROWTH = "growth"
    COMBINED = "combined"


class SelectionCriteria(Enum):
    """选股标准枚举"""
    TOP_PERCENTILE = "top_percentile"
    THRESHOLD_BASED = "threshold_based"
    SECTOR_ROTATION = "sector_rotation"
    RISK_ADJUSTED = "risk_adjusted"


@dataclass
class StrategyConfig:
    """策略配置数据类"""
    name: str
    strategy_type: StrategyType
    weight: float = 1.0
    enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SelectionResult:
    """选股结果数据类"""
    symbol: str
    score: float
    rank: int
    strategy_scores: Dict[str, float]
    selection_reason: str
    risk_metrics: Dict[str, float] = field(default_factory=dict)
    fundamental_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PortfolioMetrics:
    """组合指标数据类"""
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_holding_period: float
    turnover_rate: float


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        self.weight = config.weight
    
    @abstractmethod
    def calculate_score(self, data: Dict[str, Any]) -> float:
        """计算股票得分"""
        pass
    
    @abstractmethod
    def filter_stocks(self, stocks_data: Dict[str, pd.DataFrame]) -> List[str]:
        """过滤股票"""
        pass


class WyckoffStrategy(BaseStrategy):
    """威科夫策略"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.wyckoff_engine = WyckoffAnalysisEngine()
    
    def calculate_score(self, data: Dict[str, Any]) -> float:
        """基于威科夫分析计算得分"""
        try:
            stock_data = data.get('price_data')
            if stock_data is None or stock_data.empty:
                return 0.0
            
            # 分析市场结构
            market_structure = self.wyckoff_engine.analyze_market_structure(stock_data)
            
            # 根据市场阶段给分
            phase_scores = {
                MarketPhase.ACCUMULATION: 0.9,
                MarketPhase.MARKUP: 0.7,
                MarketPhase.DISTRIBUTION: 0.2,
                MarketPhase.MARKDOWN: 0.1,
                MarketPhase.UNKNOWN: 0.3
            }
            
            base_score = phase_scores.get(market_structure.phase, 0.3)
            
            # 考虑置信度
            confidence_adjusted_score = base_score * market_structure.confidence
            
            return confidence_adjusted_score
            
        except Exception as e:
            logger.warning(f"威科夫策略评分失败: {e}")
            return 0.0
    
    def filter_stocks(self, stocks_data: Dict[str, pd.DataFrame]) -> List[str]:
        """过滤符合威科夫条件的股票"""
        filtered_stocks = []
        
        for symbol, data in stocks_data.items():
            try:
                score = self.calculate_score({'price_data': data})
                if score > self.config.parameters.get('min_score', 0.5):
                    filtered_stocks.append(symbol)
            except Exception as e:
                logger.warning(f"威科夫过滤{symbol}失败: {e}")
                continue
        
        return filtered_stocks


class RelativeStrengthStrategy(BaseStrategy):
    """相对强弱策略"""
    
    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.rs_engine = RelativeStrengthEngine()
    
    def calculate_score(self, data: Dict[str, Any]) -> float:
        """基于相对强弱计算得分"""
        try:
            stock_data = data.get('price_data')
            benchmark_data = data.get('benchmark_data')
            
            if stock_data is None or benchmark_data is None:
                return 0.0
            
            # 计算RS值
            rs_value = self.rs_engine.calculate_rs_value(stock_data, benchmark_data)
            
            # 将RS值转换为0-1分数
            # RS > 1.2 得满分，RS < 0.8 得0分
            if rs_value >= 1.2:
                score = 1.0
            elif rs_value <= 0.8:
                score = 0.0
            else:
                score = (rs_value - 0.8) / 0.4
            
            return score
            
        except Exception as e:
            logger.warning(f"相对强弱策略评分失败: {e}")
            return 0.0
    
    def filter_stocks(self, stocks_data: Dict[str, pd.DataFrame]) -> List[str]:
        """过滤相对强弱符合条件的股票"""
        filtered_stocks = []
        
        # 需要基准数据，这里简化处理
        for symbol, data in stocks_data.items():
            try:
                # 简化：使用自身数据作为基准进行演示
                score = self.calculate_score({
                    'price_data': data,
                    'benchmark_data': data  # 实际应该是市场指数
                })
                if score > self.config.parameters.get('min_rs_score', 0.6):
                    filtered_stocks.append(symbol)
            except Exception as e:
                logger.warning(f"相对强弱过滤{symbol}失败: {e}")
                continue
        
        return filtered_stocks


class StockSelectionEngine:
    """选股策略引擎"""
    
    def __init__(self):
        """初始化选股引擎"""
        self.strategies: Dict[str, BaseStrategy] = {}
        self.benchmark_data: Optional[pd.DataFrame] = None
        
        logger.info("选股策略引擎初始化完成")
    
    def add_strategy(self, strategy: BaseStrategy):
        """添加策略"""
        self.strategies[strategy.name] = strategy
        logger.info(f"添加策略: {strategy.name}")
    
    def remove_strategy(self, strategy_name: str):
        """移除策略"""
        if strategy_name in self.strategies:
            del self.strategies[strategy_name]
            logger.info(f"移除策略: {strategy_name}")
    
    def set_benchmark(self, benchmark_data: pd.DataFrame):
        """设置基准数据"""
        self.benchmark_data = benchmark_data
        logger.info("基准数据已设置")
    
    def select_stocks(self,
                     stocks_data: Dict[str, pd.DataFrame],
                     selection_criteria: SelectionCriteria = SelectionCriteria.TOP_PERCENTILE,
                     max_selections: int = 20,
                     **kwargs) -> List[SelectionResult]:
        """
        执行选股
        
        Args:
            stocks_data: 股票数据字典
            selection_criteria: 选股标准
            max_selections: 最大选股数量
            **kwargs: 其他参数
            
        Returns:
            List[SelectionResult]: 选股结果列表
        """
        try:
            if not self.strategies:
                raise CalculationError("没有配置任何策略")
            
            results = []
            
            # 为每只股票计算综合得分
            for symbol, stock_data in stocks_data.items():
                try:
                    strategy_scores = {}
                    total_score = 0.0
                    total_weight = 0.0
                    
                    # 计算各策略得分
                    for strategy_name, strategy in self.strategies.items():
                        if not strategy.config.enabled:
                            continue
                        
                        data_dict = {
                            'price_data': stock_data,
                            'benchmark_data': self.benchmark_data
                        }
                        
                        score = strategy.calculate_score(data_dict)
                        strategy_scores[strategy_name] = score
                        
                        # 加权平均
                        total_score += score * strategy.weight
                        total_weight += strategy.weight
                    
                    # 计算最终得分
                    if total_weight > 0:
                        final_score = total_score / total_weight
                    else:
                        final_score = 0.0
                    
                    # 创建选股结果
                    result = SelectionResult(
                        symbol=symbol,
                        score=final_score,
                        rank=0,  # 稍后填充
                        strategy_scores=strategy_scores,
                        selection_reason=self._generate_selection_reason(strategy_scores)
                    )
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"计算{symbol}得分失败: {e}")
                    continue
            
            # 按得分排序
            results.sort(key=lambda x: x.score, reverse=True)
            
            # 填充排名
            for i, result in enumerate(results):
                result.rank = i + 1
            
            # 根据选股标准筛选
            selected_results = self._apply_selection_criteria(
                results, selection_criteria, max_selections, **kwargs
            )
            
            logger.info(f"完成选股，从{len(stocks_data)}只股票中选出{len(selected_results)}只")
            return selected_results
            
        except Exception as e:
            logger.error(f"选股执行失败: {e}")
            raise CalculationError(f"选股执行失败: {e}")
    
    def backtest_strategy(self,
                         historical_data: Dict[str, pd.DataFrame],
                         start_date: str,
                         end_date: str,
                         rebalance_frequency: str = "M") -> PortfolioMetrics:
        """
        策略回测
        
        Args:
            historical_data: 历史数据
            start_date: 开始日期
            end_date: 结束日期
            rebalance_frequency: 再平衡频率
            
        Returns:
            PortfolioMetrics: 组合指标
        """
        try:
            # 简化的回测逻辑
            # 实际实现需要更复杂的回测框架
            
            logger.info(f"开始回测: {start_date} 到 {end_date}")
            
            # 模拟回测结果
            metrics = PortfolioMetrics(
                total_return=0.15,  # 15%年化收益
                sharpe_ratio=1.2,
                max_drawdown=0.08,
                win_rate=0.65,
                avg_holding_period=30.0,
                turnover_rate=0.5
            )
            
            logger.info("回测完成")
            return metrics
            
        except Exception as e:
            logger.error(f"策略回测失败: {e}")
            raise CalculationError(f"策略回测失败: {e}")
    
    def optimize_parameters(self,
                          historical_data: Dict[str, pd.DataFrame],
                          optimization_target: str = "sharpe_ratio") -> Dict[str, Any]:
        """
        参数优化
        
        Args:
            historical_data: 历史数据
            optimization_target: 优化目标
            
        Returns:
            Dict: 优化后的参数
        """
        try:
            logger.info(f"开始参数优化，目标: {optimization_target}")
            
            # 简化的参数优化逻辑
            optimized_params = {
                'wyckoff_min_score': 0.6,
                'rs_min_score': 0.7,
                'max_selections': 15
            }
            
            logger.info("参数优化完成")
            return optimized_params
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            raise CalculationError(f"参数优化失败: {e}")
    
    def _generate_selection_reason(self, strategy_scores: Dict[str, float]) -> str:
        """生成选股理由"""
        reasons = []
        
        for strategy_name, score in strategy_scores.items():
            if score > 0.7:
                reasons.append(f"{strategy_name}得分高({score:.2f})")
            elif score > 0.5:
                reasons.append(f"{strategy_name}得分中等({score:.2f})")
        
        if not reasons:
            return "综合得分较低"
        
        return "; ".join(reasons)
    
    def _apply_selection_criteria(self,
                                results: List[SelectionResult],
                                criteria: SelectionCriteria,
                                max_selections: int,
                                **kwargs) -> List[SelectionResult]:
        """应用选股标准"""
        if criteria == SelectionCriteria.TOP_PERCENTILE:
            percentile = kwargs.get('percentile', 20)  # 默认前20%
            cutoff_index = int(len(results) * percentile / 100)
            return results[:min(cutoff_index, max_selections)]
        
        elif criteria == SelectionCriteria.THRESHOLD_BASED:
            threshold = kwargs.get('threshold', 0.6)
            filtered = [r for r in results if r.score >= threshold]
            return filtered[:max_selections]
        
        else:
            # 默认返回前N只
            return results[:max_selections]
