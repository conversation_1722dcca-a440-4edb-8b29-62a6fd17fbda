#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据状态检查脚本

检查数据库中的股票数据状态和统计信息
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """主函数"""
    try:
        print("📊 检查数据库状态...")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 获取统计信息
        stats = db_manager.get_database_stats()
        
        print("=" * 60)
        print("📈 数据库统计信息:")
        print(f"   - 股票数量: {stats.get('total_stocks', 0):,}")
        print(f"   - 行情记录: {stats.get('total_quotes', 0):,}")
        print(f"   - 最新交易日: {stats.get('latest_trade_date', 'N/A')}")
        print(f"   - 数据库大小: {stats.get('db_size_mb', 0):.2f} MB")
        print("=" * 60)
        
        # 检查股票信息表
        stock_count = db_manager.get_stock_count()
        print(f"📋 股票信息表: {stock_count:,} 条记录")
        
        # 检查行情数据表
        quote_count = db_manager.get_quote_count()
        print(f"📊 行情数据表: {quote_count:,} 条记录")
        
        # 检查最近的股票记录
        recent_stocks = db_manager.get_recent_stocks(limit=10)
        if recent_stocks:
            print("\n🔍 最近添加的股票:")
            for stock in recent_stocks:
                print(f"   - {stock.get('stock_code', 'N/A')}: {stock.get('stock_name', 'N/A')}")
        
        # 检查数据完整性
        print("\n🔍 数据完整性检查:")
        
        # 检查是否有重复的股票代码
        duplicate_count = db_manager.check_duplicate_stocks()
        if duplicate_count > 0:
            print(f"   ⚠️  发现 {duplicate_count} 个重复的股票代码")
        else:
            print("   ✅ 没有重复的股票代码")
        
        # 检查是否有空的股票名称
        empty_name_count = db_manager.check_empty_stock_names()
        if empty_name_count > 0:
            print(f"   ⚠️  发现 {empty_name_count} 个空的股票名称")
        else:
            print("   ✅ 所有股票都有名称")
        
        # 检查市场分布
        market_distribution = db_manager.get_market_distribution()
        if market_distribution:
            print("\n📈 市场分布:")
            for market, count in market_distribution.items():
                print(f"   - {market}: {count:,} 只股票")
        
        print("\n✅ 数据状态检查完成")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        logger.error(f"数据状态检查失败: {e}")


if __name__ == "__main__":
    main()
