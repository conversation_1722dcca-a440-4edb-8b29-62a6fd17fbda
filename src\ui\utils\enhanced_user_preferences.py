#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强用户偏好设置系统
第14周：用户体验优化 - 个性化配置和界面定制
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import json
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QComboBox, QCheckBox, QSpinBox, QSlider, QPushButton,
    QGroupBox, QFormLayout, QColorDialog, QFontDialog, QMessageBox,
    QLineEdit, QTextEdit, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QSettings
from PyQt6.QtGui import QFont, QColor, QPalette
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ThemeType(Enum):
    """主题类型"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class LanguageType(Enum):
    """语言类型"""
    ZH_CN = "zh_CN"
    EN_US = "en_US"


@dataclass
class UIPreferences:
    """界面偏好设置"""
    theme: str = ThemeType.LIGHT.value
    language: str = LanguageType.ZH_CN.value
    font_family: str = "Microsoft YaHei"
    font_size: int = 10
    window_opacity: float = 1.0
    show_tooltips: bool = True
    show_status_bar: bool = True
    show_toolbar: bool = True
    auto_save_layout: bool = True
    animation_enabled: bool = True


@dataclass
class DataPreferences:
    """数据偏好设置"""
    auto_refresh: bool = True
    refresh_interval: int = 5000  # 毫秒
    cache_size: int = 100  # MB
    data_retention_days: int = 30
    enable_data_validation: bool = True
    show_data_quality_warnings: bool = True
    auto_backup: bool = True
    backup_interval_hours: int = 24


@dataclass
class AnalysisPreferences:
    """分析偏好设置"""
    default_time_frame: str = "1d"
    wyckoff_sensitivity: float = 0.7
    rs_calculation_period: int = 20
    enable_auto_analysis: bool = True
    show_analysis_details: bool = True
    save_analysis_history: bool = True
    max_analysis_history: int = 1000


@dataclass
class NotificationPreferences:
    """通知偏好设置"""
    enable_notifications: bool = True
    show_popup_notifications: bool = True
    play_sound: bool = False
    notification_duration: int = 5000  # 毫秒
    notify_on_analysis_complete: bool = True
    notify_on_data_update: bool = False
    notify_on_errors: bool = True


@dataclass
class UserPreferences:
    """用户偏好设置"""
    ui: UIPreferences
    data: DataPreferences
    analysis: AnalysisPreferences
    notifications: NotificationPreferences
    
    def __init__(self):
        self.ui = UIPreferences()
        self.data = DataPreferences()
        self.analysis = AnalysisPreferences()
        self.notifications = NotificationPreferences()


class UserPreferencesManager:
    """用户偏好设置管理器"""
    
    def __init__(self):
        """初始化偏好设置管理器"""
        self.preferences = UserPreferences()
        self.settings_file = "config/user_preferences.json"
        self.load_preferences()
        logger.info("用户偏好设置管理器初始化完成")
    
    def load_preferences(self):
        """加载偏好设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 更新偏好设置
                if 'ui' in data:
                    for key, value in data['ui'].items():
                        if hasattr(self.preferences.ui, key):
                            setattr(self.preferences.ui, key, value)
                
                if 'data' in data:
                    for key, value in data['data'].items():
                        if hasattr(self.preferences.data, key):
                            setattr(self.preferences.data, key, value)
                
                if 'analysis' in data:
                    for key, value in data['analysis'].items():
                        if hasattr(self.preferences.analysis, key):
                            setattr(self.preferences.analysis, key, value)
                
                if 'notifications' in data:
                    for key, value in data['notifications'].items():
                        if hasattr(self.preferences.notifications, key):
                            setattr(self.preferences.notifications, key, value)
                
                logger.info("用户偏好设置加载成功")
            else:
                logger.info("使用默认偏好设置")
                
        except Exception as e:
            logger.error(f"加载用户偏好设置失败: {e}")
            self.preferences = UserPreferences()  # 使用默认设置
    
    def save_preferences(self):
        """保存偏好设置"""
        try:
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            
            data = {
                'ui': asdict(self.preferences.ui),
                'data': asdict(self.preferences.data),
                'analysis': asdict(self.preferences.analysis),
                'notifications': asdict(self.preferences.notifications)
            }
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info("用户偏好设置保存成功")
            
        except Exception as e:
            logger.error(f"保存用户偏好设置失败: {e}")
    
    def get_preference(self, category: str, key: str, default=None):
        """获取特定偏好设置"""
        try:
            category_obj = getattr(self.preferences, category)
            return getattr(category_obj, key, default)
        except AttributeError:
            return default
    
    def set_preference(self, category: str, key: str, value):
        """设置特定偏好设置"""
        try:
            category_obj = getattr(self.preferences, category)
            setattr(category_obj, key, value)
            self.save_preferences()
        except AttributeError:
            logger.warning(f"无效的偏好设置: {category}.{key}")
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        self.preferences = UserPreferences()
        self.save_preferences()
        logger.info("偏好设置已重置为默认值")


class UserPreferencesDialog(QDialog):
    """用户偏好设置对话框"""
    
    preferences_changed = pyqtSignal()
    
    def __init__(self, preferences_manager: UserPreferencesManager, parent=None):
        super().__init__(parent)
        self.preferences_manager = preferences_manager
        self.temp_preferences = UserPreferences()
        self._copy_preferences()
        self.setup_ui()
    
    def _copy_preferences(self):
        """复制当前偏好设置到临时对象"""
        prefs = self.preferences_manager.preferences
        self.temp_preferences.ui = UIPreferences(**asdict(prefs.ui))
        self.temp_preferences.data = DataPreferences(**asdict(prefs.data))
        self.temp_preferences.analysis = AnalysisPreferences(**asdict(prefs.analysis))
        self.temp_preferences.notifications = NotificationPreferences(**asdict(prefs.notifications))
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("用户偏好设置")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 标签页控件
        tab_widget = QTabWidget()
        
        # 界面设置标签页
        ui_tab = self._create_ui_tab()
        tab_widget.addTab(ui_tab, "🎨 界面设置")
        
        # 数据设置标签页
        data_tab = self._create_data_tab()
        tab_widget.addTab(data_tab, "📊 数据设置")
        
        # 分析设置标签页
        analysis_tab = self._create_analysis_tab()
        tab_widget.addTab(analysis_tab, "🔍 分析设置")
        
        # 通知设置标签页
        notification_tab = self._create_notification_tab()
        tab_widget.addTab(notification_tab, "🔔 通知设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_button = QPushButton("重置默认")
        reset_button.clicked.connect(self.reset_preferences)
        button_layout.addWidget(reset_button)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept_preferences)
        ok_button.setDefault(True)
        button_layout.addWidget(ok_button)
        
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def _create_ui_tab(self) -> QWidget:
        """创建界面设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题", "自动切换"])
        theme_layout.addRow("界面主题:", self.theme_combo)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        theme_layout.addRow("界面语言:", self.language_combo)
        
        layout.addWidget(theme_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(self.temp_preferences.ui.font_size)
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(font_group)
        
        # 界面选项
        ui_group = QGroupBox("界面选项")
        ui_layout = QVBoxLayout(ui_group)
        
        self.show_tooltips_cb = QCheckBox("显示工具提示")
        self.show_tooltips_cb.setChecked(self.temp_preferences.ui.show_tooltips)
        ui_layout.addWidget(self.show_tooltips_cb)
        
        self.show_status_bar_cb = QCheckBox("显示状态栏")
        self.show_status_bar_cb.setChecked(self.temp_preferences.ui.show_status_bar)
        ui_layout.addWidget(self.show_status_bar_cb)
        
        self.animation_enabled_cb = QCheckBox("启用界面动画")
        self.animation_enabled_cb.setChecked(self.temp_preferences.ui.animation_enabled)
        ui_layout.addWidget(self.animation_enabled_cb)
        
        layout.addWidget(ui_group)
        layout.addStretch()
        
        return widget
    
    def _create_data_tab(self) -> QWidget:
        """创建数据设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据更新设置
        update_group = QGroupBox("数据更新")
        update_layout = QFormLayout(update_group)
        
        self.auto_refresh_cb = QCheckBox("自动刷新数据")
        self.auto_refresh_cb.setChecked(self.temp_preferences.data.auto_refresh)
        update_layout.addRow(self.auto_refresh_cb)
        
        self.refresh_interval_spin = QSpinBox()
        self.refresh_interval_spin.setRange(1000, 60000)
        self.refresh_interval_spin.setSuffix(" 毫秒")
        self.refresh_interval_spin.setValue(self.temp_preferences.data.refresh_interval)
        update_layout.addRow("刷新间隔:", self.refresh_interval_spin)
        
        layout.addWidget(update_group)
        
        # 缓存设置
        cache_group = QGroupBox("缓存设置")
        cache_layout = QFormLayout(cache_group)
        
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(50, 1000)
        self.cache_size_spin.setSuffix(" MB")
        self.cache_size_spin.setValue(self.temp_preferences.data.cache_size)
        cache_layout.addRow("缓存大小:", self.cache_size_spin)
        
        layout.addWidget(cache_group)
        
        # 数据质量
        quality_group = QGroupBox("数据质量")
        quality_layout = QVBoxLayout(quality_group)
        
        self.data_validation_cb = QCheckBox("启用数据验证")
        self.data_validation_cb.setChecked(self.temp_preferences.data.enable_data_validation)
        quality_layout.addWidget(self.data_validation_cb)
        
        self.quality_warnings_cb = QCheckBox("显示数据质量警告")
        self.quality_warnings_cb.setChecked(self.temp_preferences.data.show_data_quality_warnings)
        quality_layout.addWidget(self.quality_warnings_cb)
        
        layout.addWidget(quality_group)
        layout.addStretch()
        
        return widget
    
    def _create_analysis_tab(self) -> QWidget:
        """创建分析设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 分析参数
        params_group = QGroupBox("分析参数")
        params_layout = QFormLayout(params_group)
        
        self.time_frame_combo = QComboBox()
        self.time_frame_combo.addItems(["1分钟", "5分钟", "15分钟", "30分钟", "1小时", "日线", "周线"])
        params_layout.addRow("默认时间周期:", self.time_frame_combo)
        
        self.wyckoff_sensitivity_slider = QSlider(Qt.Orientation.Horizontal)
        self.wyckoff_sensitivity_slider.setRange(1, 100)
        self.wyckoff_sensitivity_slider.setValue(int(self.temp_preferences.analysis.wyckoff_sensitivity * 100))
        params_layout.addRow("威科夫敏感度:", self.wyckoff_sensitivity_slider)
        
        layout.addWidget(params_group)
        
        # 分析选项
        options_group = QGroupBox("分析选项")
        options_layout = QVBoxLayout(options_group)
        
        self.auto_analysis_cb = QCheckBox("启用自动分析")
        self.auto_analysis_cb.setChecked(self.temp_preferences.analysis.enable_auto_analysis)
        options_layout.addWidget(self.auto_analysis_cb)
        
        self.analysis_details_cb = QCheckBox("显示分析详情")
        self.analysis_details_cb.setChecked(self.temp_preferences.analysis.show_analysis_details)
        options_layout.addWidget(self.analysis_details_cb)
        
        layout.addWidget(options_group)
        layout.addStretch()
        
        return widget
    
    def _create_notification_tab(self) -> QWidget:
        """创建通知设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 通知选项
        notification_group = QGroupBox("通知选项")
        notification_layout = QVBoxLayout(notification_group)
        
        self.enable_notifications_cb = QCheckBox("启用通知")
        self.enable_notifications_cb.setChecked(self.temp_preferences.notifications.enable_notifications)
        notification_layout.addWidget(self.enable_notifications_cb)
        
        self.popup_notifications_cb = QCheckBox("显示弹出通知")
        self.popup_notifications_cb.setChecked(self.temp_preferences.notifications.show_popup_notifications)
        notification_layout.addWidget(self.popup_notifications_cb)
        
        self.play_sound_cb = QCheckBox("播放提示音")
        self.play_sound_cb.setChecked(self.temp_preferences.notifications.play_sound)
        notification_layout.addWidget(self.play_sound_cb)
        
        layout.addWidget(notification_group)
        
        # 通知事件
        events_group = QGroupBox("通知事件")
        events_layout = QVBoxLayout(events_group)
        
        self.notify_analysis_cb = QCheckBox("分析完成时通知")
        self.notify_analysis_cb.setChecked(self.temp_preferences.notifications.notify_on_analysis_complete)
        events_layout.addWidget(self.notify_analysis_cb)
        
        self.notify_errors_cb = QCheckBox("发生错误时通知")
        self.notify_errors_cb.setChecked(self.temp_preferences.notifications.notify_on_errors)
        events_layout.addWidget(self.notify_errors_cb)
        
        layout.addWidget(events_group)
        layout.addStretch()
        
        return widget
    
    def accept_preferences(self):
        """接受偏好设置"""
        # 更新临时偏好设置
        self.temp_preferences.ui.font_size = self.font_size_spin.value()
        self.temp_preferences.ui.show_tooltips = self.show_tooltips_cb.isChecked()
        self.temp_preferences.ui.show_status_bar = self.show_status_bar_cb.isChecked()
        self.temp_preferences.ui.animation_enabled = self.animation_enabled_cb.isChecked()
        
        self.temp_preferences.data.auto_refresh = self.auto_refresh_cb.isChecked()
        self.temp_preferences.data.refresh_interval = self.refresh_interval_spin.value()
        self.temp_preferences.data.cache_size = self.cache_size_spin.value()
        self.temp_preferences.data.enable_data_validation = self.data_validation_cb.isChecked()
        self.temp_preferences.data.show_data_quality_warnings = self.quality_warnings_cb.isChecked()
        
        self.temp_preferences.analysis.wyckoff_sensitivity = self.wyckoff_sensitivity_slider.value() / 100.0
        self.temp_preferences.analysis.enable_auto_analysis = self.auto_analysis_cb.isChecked()
        self.temp_preferences.analysis.show_analysis_details = self.analysis_details_cb.isChecked()
        
        self.temp_preferences.notifications.enable_notifications = self.enable_notifications_cb.isChecked()
        self.temp_preferences.notifications.show_popup_notifications = self.popup_notifications_cb.isChecked()
        self.temp_preferences.notifications.play_sound = self.play_sound_cb.isChecked()
        self.temp_preferences.notifications.notify_on_analysis_complete = self.notify_analysis_cb.isChecked()
        self.temp_preferences.notifications.notify_on_errors = self.notify_errors_cb.isChecked()
        
        # 应用到管理器
        self.preferences_manager.preferences = self.temp_preferences
        self.preferences_manager.save_preferences()
        
        # 发出信号
        self.preferences_changed.emit()
        
        self.accept()
    
    def reset_preferences(self):
        """重置偏好设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有偏好设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.temp_preferences = UserPreferences()
            self._update_ui_from_preferences()
    
    def _update_ui_from_preferences(self):
        """从偏好设置更新界面"""
        self.font_size_spin.setValue(self.temp_preferences.ui.font_size)
        self.show_tooltips_cb.setChecked(self.temp_preferences.ui.show_tooltips)
        self.show_status_bar_cb.setChecked(self.temp_preferences.ui.show_status_bar)
        self.animation_enabled_cb.setChecked(self.temp_preferences.ui.animation_enabled)
        
        self.auto_refresh_cb.setChecked(self.temp_preferences.data.auto_refresh)
        self.refresh_interval_spin.setValue(self.temp_preferences.data.refresh_interval)
        self.cache_size_spin.setValue(self.temp_preferences.data.cache_size)
        self.data_validation_cb.setChecked(self.temp_preferences.data.enable_data_validation)
        self.quality_warnings_cb.setChecked(self.temp_preferences.data.show_data_quality_warnings)
        
        self.wyckoff_sensitivity_slider.setValue(int(self.temp_preferences.analysis.wyckoff_sensitivity * 100))
        self.auto_analysis_cb.setChecked(self.temp_preferences.analysis.enable_auto_analysis)
        self.analysis_details_cb.setChecked(self.temp_preferences.analysis.show_analysis_details)
        
        self.enable_notifications_cb.setChecked(self.temp_preferences.notifications.enable_notifications)
        self.popup_notifications_cb.setChecked(self.temp_preferences.notifications.show_popup_notifications)
        self.play_sound_cb.setChecked(self.temp_preferences.notifications.play_sound)
        self.notify_analysis_cb.setChecked(self.temp_preferences.notifications.notify_on_analysis_complete)
        self.notify_errors_cb.setChecked(self.temp_preferences.notifications.notify_on_errors)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)


# 全局偏好设置管理器实例
preferences_manager = UserPreferencesManager()
