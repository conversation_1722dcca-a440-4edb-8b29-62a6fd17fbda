#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from src.data_sources.base import (
    IDataSource, DataSourceConfig, MarketData, SectorInfo,
    DataSourceException, ConnectionException, DataException
)
from src.data_sources.manager import (
    DataSourceManager, DataSourceStatus, DataSourceStats
)


class MockDataSource(IDataSource):
    """模拟数据源"""
    
    def __init__(self, config: DataSourceConfig, fail_connect: bool = False):
        super().__init__(config)
        self.connected = False
        self.fail_connect = fail_connect
        self.call_count = 0
    
    async def connect(self) -> bool:
        """模拟连接"""
        self.call_count += 1
        if self.fail_connect:
            raise ConnectionException("连接失败")
        self.connected = True
        return True
    
    async def disconnect(self) -> bool:
        """模拟断开连接"""
        self.connected = False
        return True
    
    async def is_connected(self) -> bool:
        """模拟检查连接状态"""
        return self.connected
    
    async def test_connection(self) -> bool:
        """测试连接"""
        return self.connected
    
    async def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        if not self.connected:
            raise ConnectionException("未连接")
        return ["000001.SZ", "000002.SZ", "600000.SH"]
    
    async def get_sector_list(self) -> List[SectorInfo]:
        """获取板块列表"""
        if not self.connected:
            raise ConnectionException("未连接")
        return [
            SectorInfo(code="BK001", name="银行", stocks=["000001.SZ"]),
            SectorInfo(code="BK002", name="地产", stocks=["000002.SZ"])
        ]
    
    async def get_sector_constituents(self, sector_code: str) -> List[str]:
        """获取板块成分股"""
        if not self.connected:
            raise ConnectionException("未连接")
        if sector_code == "BK001":
            return ["000001.SZ", "600036.SH"]
        return []
    
    async def get_trading_calendar(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        if not self.connected:
            raise ConnectionException("未连接")
        return ["2024-01-02", "2024-01-03", "2024-01-04"]
    
    async def download_history_data(self, symbols: List[str], 
                                  start_date: str, end_date: str,
                                  period: str = "1d") -> bool:
        """下载历史数据"""
        if not self.connected:
            raise ConnectionException("未连接")
        return True
    
    async def get_market_data(self, symbols: List[str], 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None) -> List[MarketData]:
        """模拟获取市场数据"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        self.call_count += 1
        
        if self.config.name == "failing_source":
            raise DataException("数据获取失败")
        
        result = []
        for symbol in symbols:
            data = MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                open=100.0,
                high=110.0,
                low=90.0,
                close=105.0,
                volume=1000000,
                amount=105000000.0
            )
            result.append(data)
        
        return result
    
    async def get_sectors(self) -> List[SectorInfo]:
        """模拟获取板块信息"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        return [
            SectorInfo(
                code="BK001",
                name="银行",
                stocks=["000001.SZ", "600036.SH"]
            )
        ]
    
    async def get_sector_stocks(self, sector_code: str) -> List[str]:
        """模拟获取板块股票"""
        if not self.connected:
            raise ConnectionException("未连接")
        
        if sector_code == "BK001":
            return ["000001.SZ", "600036.SH"]
        return []
    
    async def health_check(self) -> Dict[str, Any]:
        """模拟健康检查"""
        return {
            "status": "healthy" if self.connected else "disconnected",
            "timestamp": datetime.now(),
            "config": self.config.name,
            "call_count": self.call_count
        }


class TestDataSourceManager:
    """测试数据源管理器"""
    
    @pytest.fixture
    def manager(self):
        """创建数据源管理器"""
        return DataSourceManager()
    
    @pytest.fixture
    def mock_data_sources(self):
        """创建模拟数据源"""
        configs = [
            DataSourceConfig(name="primary", timeout=30),
            DataSourceConfig(name="secondary", timeout=60),
            DataSourceConfig(name="backup", timeout=45)
        ]
        
        sources = []
        for config in configs:
            source = MockDataSource(config)
            sources.append(source)
        
        return sources
    
    def test_init(self, manager):
        """测试初始化"""
        assert manager._sources == {}
        assert manager._health_check_interval == 60
        assert manager._load_balance_strategy.value == "priority"
    
    def test_init_with_custom_params(self):
        """测试使用自定义参数初始化"""
        from src.data_sources.manager import LoadBalanceStrategy
        
        manager = DataSourceManager(
            health_check_interval=30,
            load_balance_strategy=LoadBalanceStrategy.ROUND_ROBIN
        )
        
        assert manager._health_check_interval == 30
        assert manager._load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN
    
    def test_register_source(self, manager, mock_data_sources):
        """测试注册数据源"""
        source = mock_data_sources[0]
        
        result = manager.register_source(source.config.name, source, auto_connect=False)
        
        assert result is True
        assert source.config.name in manager._sources
        assert manager._sources[source.config.name].source == source
        assert manager._sources[source.config.name].status.value == "inactive"
    
    def test_register_multiple_sources(self, manager, mock_data_sources):
        """测试注册多个数据源"""
        # 注册多个数据源
        for i, source in enumerate(mock_data_sources):
            result = manager.register_source(
                source.config.name, 
                source, 
                priority=3-i,  # 第一个优先级最高
                auto_connect=False
            )
            assert result is True
        
        assert len(manager._sources) == 3
        
        # 检查所有数据源都已注册
        for source in mock_data_sources:
            assert source.config.name in manager._sources
    
    def test_register_duplicate_source(self, manager, mock_data_sources):
        """测试注册重复数据源"""
        source = mock_data_sources[0]
        
        # 第一次注册
        result1 = manager.register_source(source.config.name, source, auto_connect=False)
        assert result1 is True
        
        # 第二次注册同名数据源（会覆盖）
        duplicate_source = MockDataSource(DataSourceConfig(name="primary"))
        result2 = manager.register_source(source.config.name, duplicate_source, auto_connect=False)
        assert result2 is True  # 会覆盖原有配置
        
        # 确保新数据源已替换
        assert manager._sources["primary"].source == duplicate_source
    
    def test_unregister_source(self, manager, mock_data_sources):
        """测试注销数据源"""
        source = mock_data_sources[0]
        manager.register_source(source.config.name, source, auto_connect=False)
        
        result = manager.unregister_source(source.config.name)
        
        assert result is True
        assert source.config.name not in manager._sources
    
    def test_unregister_nonexistent_source(self, manager):
        """测试注销不存在的数据源"""
        result = manager.unregister_source("nonexistent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_connect_all_success(self, manager, mock_data_sources):
        """测试成功连接所有数据源"""
        # 注册数据源
        for i, source in enumerate(mock_data_sources):
            manager.register_source(source, is_primary=(i == 0))
        
        results = await manager.connect_all()
        
        assert len(results) == 3
        assert all(result is True for result in results.values())
        
        # 检查所有数据源状态
        for source in mock_data_sources:
            source_info = manager.sources[source.config.name]
            assert source_info['status'] == DataSourceStatus.CONNECTED
            assert await source.is_connected() is True
    
    @pytest.mark.asyncio
    async def test_connect_all_with_failure(self, manager):
        """测试连接时有失败的情况"""
        # 创建一个会失败的数据源
        failing_source = MockDataSource(
            DataSourceConfig(name="failing"), 
            fail_connect=True
        )
        normal_source = MockDataSource(DataSourceConfig(name="normal"))
        
        manager.register_source(failing_source, is_primary=True)
        manager.register_source(normal_source, is_primary=False)
        
        results = await manager.connect_all()
        
        assert results["failing"] is False
        assert results["normal"] is True
        
        # 检查状态
        assert manager.sources["failing"]['status'] == DataSourceStatus.ERROR
        assert manager.sources["normal"]['status'] == DataSourceStatus.CONNECTED
    
    @pytest.mark.asyncio
    async def test_disconnect_all(self, manager, mock_data_sources):
        """测试断开所有连接"""
        # 注册并连接数据源
        for i, source in enumerate(mock_data_sources):
            manager.register_source(source, is_primary=(i == 0))
        
        await manager.connect_all()
        
        # 断开所有连接
        results = await manager.disconnect_all()
        
        assert len(results) == 3
        assert all(result is True for result in results.values())
        
        # 检查所有数据源状态
        for source in mock_data_sources:
            source_info = manager.sources[source.config.name]
            assert source_info['status'] == DataSourceStatus.DISCONNECTED
            assert await source.is_connected() is False
    
    @pytest.mark.asyncio
    async def test_get_market_data_primary_success(self, manager, mock_data_sources):
        """测试使用主数据源成功获取市场数据"""
        # 注册并连接数据源
        manager.register_source(mock_data_sources[0], is_primary=True)
        await manager.connect_all()
        
        symbols = ["000001.SZ", "600036.SH"]
        result = await manager.get_market_data(symbols)
        
        assert len(result) == 2
        assert all(isinstance(data, MarketData) for data in result)
        assert result[0].symbol == "000001.SZ"
        assert result[1].symbol == "600036.SH"
    
    @pytest.mark.asyncio
    async def test_get_market_data_with_failover(self, manager):
        """测试故障转移获取市场数据"""
        # 创建一个会失败的主数据源和正常的备用数据源
        failing_source = MockDataSource(DataSourceConfig(name="failing_source"))
        backup_source = MockDataSource(DataSourceConfig(name="backup"))
        
        manager.register_source(failing_source, is_primary=True)
        manager.register_source(backup_source, is_primary=False)
        
        await manager.connect_all()
        
        symbols = ["000001.SZ"]
        result = await manager.get_market_data(symbols)
        
        # 应该从备用数据源获取到数据
        assert len(result) == 1
        assert result[0].symbol == "000001.SZ"
        
        # 检查主数据源状态变为错误
        assert manager.sources["failing_source"]['status'] == DataSourceStatus.ERROR
    
    @pytest.mark.asyncio
    async def test_get_market_data_no_available_source(self, manager):
        """测试没有可用数据源时获取市场数据"""
        # 创建失败的数据源
        failing_source = MockDataSource(
            DataSourceConfig(name="failing_source"), 
            fail_connect=True
        )
        manager.register_source(failing_source, is_primary=True)
        
        # 连接会失败
        await manager.connect_all()
        
        symbols = ["000001.SZ"]
        
        with pytest.raises(DataSourceException):
            await manager.get_market_data(symbols)
    
    @pytest.mark.asyncio
    async def test_get_sectors(self, manager, mock_data_sources):
        """测试获取板块信息"""
        manager.register_source(mock_data_sources[0], is_primary=True)
        await manager.connect_all()
        
        result = await manager.get_sectors()
        
        assert len(result) == 1
        assert isinstance(result[0], SectorInfo)
        assert result[0].sector_code == "BK001"
    
    @pytest.mark.asyncio
    async def test_get_sector_stocks(self, manager, mock_data_sources):
        """测试获取板块股票"""
        manager.register_source(mock_data_sources[0], is_primary=True)
        await manager.connect_all()
        
        result = await manager.get_sector_stocks("BK001")
        
        assert result == ["000001.SZ", "600036.SH"]
    
    @pytest.mark.asyncio
    async def test_health_check_all(self, manager, mock_data_sources):
        """测试所有数据源健康检查"""
        # 注册并连接数据源
        for i, source in enumerate(mock_data_sources):
            manager.register_source(source, is_primary=(i == 0))
        
        await manager.connect_all()
        
        results = await manager.health_check_all()
        
        assert len(results) == 3
        for source_name, health_info in results.items():
            assert health_info["status"] == "healthy"
            assert "timestamp" in health_info
            assert health_info["config"] == source_name
    
    def test_get_source_stats(self, manager, mock_data_sources):
        """测试获取数据源统计信息"""
        source = mock_data_sources[0]
        manager.register_source(source, is_primary=True)
        
        # 模拟一些统计数据
        source_info = manager.sources[source.config.name]
        source_info['stats'].total_requests = 10
        source_info['stats'].successful_requests = 8
        source_info['stats'].failed_requests = 2
        source_info['stats'].avg_response_time = 1.5
        
        stats = manager.get_source_stats(source.config.name)
        
        assert isinstance(stats, DataSourceStats)
        assert stats.total_requests == 10
        assert stats.successful_requests == 8
        assert stats.failed_requests == 2
        assert stats.avg_response_time == 1.5
    
    def test_get_source_stats_nonexistent(self, manager):
        """测试获取不存在数据源的统计信息"""
        stats = manager.get_source_stats("nonexistent")
        assert stats is None
    
    def test_get_all_stats(self, manager, mock_data_sources):
        """测试获取所有数据源统计信息"""
        # 注册数据源
        for i, source in enumerate(mock_data_sources):
            manager.register_source(source, is_primary=(i == 0))
        
        stats = manager.get_all_stats()
        
        assert len(stats) == 3
        for source_name, source_stats in stats.items():
            assert isinstance(source_stats, DataSourceStats)
            assert source_name in [s.config.name for s in mock_data_sources]
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_data_sources):
        """测试上下文管理器"""
        async with DataSourceManager() as manager:
            # 注册数据源
            for i, source in enumerate(mock_data_sources):
                manager.register_source(source, is_primary=(i == 0))
            
            await manager.connect_all()
            
            # 验证连接状态
            for source in mock_data_sources:
                assert await source.is_connected() is True
        
        # 退出上下文管理器后，所有连接应该被断开
        for source in mock_data_sources:
            assert await source.is_connected() is False
    
    def test_source_selection_round_robin(self, manager, mock_data_sources):
        """测试轮询负载均衡"""
        manager.load_balance_enabled = True
        
        # 注册多个数据源
        for source in mock_data_sources:
            manager.register_source(source, is_primary=False)
        
        # 获取多个数据源，应该轮询
        source1 = manager._select_source()
        source2 = manager._select_source()
        source3 = manager._select_source()
        source4 = manager._select_source()  # 应该回到第一个
        
        assert source1 != source2
        assert source2 != source3
        assert source1 == source4  # 轮询回到第一个
    
    def test_source_selection_primary_only(self, manager, mock_data_sources):
        """测试只使用主数据源"""
        manager.load_balance_enabled = False
        
        # 注册主数据源和备用数据源
        manager.register_source(mock_data_sources[0], is_primary=True)
        manager.register_source(mock_data_sources[1], is_primary=False)
        
        # 选择数据源应该总是返回主数据源
        source1 = manager._select_source()
        source2 = manager._select_source()
        
        assert source1 == mock_data_sources[0]
        assert source2 == mock_data_sources[0]
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, manager):
        """测试重试机制"""
        # 创建一个前几次会失败的数据源
        class RetryTestSource(MockDataSource):
            def __init__(self, config):
                super().__init__(config)
                self.attempt_count = 0
            
            async def get_market_data(self, symbols):
                self.attempt_count += 1
                if self.attempt_count < 3:  # 前两次失败
                    raise DataException("临时失败")
                return await super().get_market_data(symbols)
        
        source = RetryTestSource(DataSourceConfig(name="retry_test"))
        manager.register_source(source, is_primary=True)
        await manager.connect_all()
        
        # 设置重试参数
        manager.max_retries = 3
        manager.retry_delay = 0.1
        
        symbols = ["000001.SZ"]
        result = await manager.get_market_data(symbols)
        
        # 应该成功获取数据（第三次尝试成功）
        assert len(result) == 1
        assert source.attempt_count == 3