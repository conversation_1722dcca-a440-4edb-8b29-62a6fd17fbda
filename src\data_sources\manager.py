#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器
统一管理多个数据源，提供负载均衡、故障转移、健康检查等功能
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import random

from .base import IDataSource, DataSourceConfig, MarketData, SectorInfo, DataSourceException
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataSourceStatus(Enum):
    """数据源状态枚举"""
    ACTIVE = "active"          # 活跃状态
    INACTIVE = "inactive"      # 非活跃状态
    ERROR = "error"           # 错误状态
    MAINTENANCE = "maintenance" # 维护状态


@dataclass
class DataSourceStats:
    """数据源统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_request_time: Optional[datetime] = None
    last_error_time: Optional[datetime] = None
    last_error_message: str = ""
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        return 1.0 - self.success_rate


@dataclass
class DataSourceInfo:
    """数据源信息"""
    source: IDataSource
    status: DataSourceStatus = DataSourceStatus.INACTIVE
    priority: int = 1  # 优先级，数值越大优先级越高
    weight: int = 1    # 权重，用于负载均衡
    max_retries: int = 3
    retry_delay: float = 1.0
    health_check_interval: int = 60  # 健康检查间隔（秒）
    last_health_check: Optional[datetime] = None
    stats: DataSourceStats = field(default_factory=DataSourceStats)
    
    def __post_init__(self):
        if self.stats is None:
            self.stats = DataSourceStats()


class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"      # 轮询
    WEIGHTED = "weighted"            # 权重
    RESPONSE_TIME = "response_time"  # 响应时间
    PRIORITY = "priority"            # 优先级


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, 
                 health_check_interval: int = 60,
                 load_balance_strategy: LoadBalanceStrategy = LoadBalanceStrategy.PRIORITY):
        """
        初始化数据源管理器
        
        Args:
            health_check_interval: 健康检查间隔（秒）
            load_balance_strategy: 负载均衡策略
        """
        self._sources: Dict[str, DataSourceInfo] = {}
        self._lock = threading.RLock()
        self._health_check_interval = health_check_interval
        self._load_balance_strategy = load_balance_strategy
        self._round_robin_index = 0
        self._health_check_thread = None
        self._stop_health_check = False
        
        logger.info(f"数据源管理器初始化完成，负载均衡策略: {load_balance_strategy.value}")
    
    def register_source(self, 
                       name: str, 
                       source: IDataSource,
                       priority: int = 1,
                       weight: int = 1,
                       max_retries: int = 3,
                       auto_connect: bool = True) -> bool:
        """
        注册数据源
        
        Args:
            name: 数据源名称
            source: 数据源实例
            priority: 优先级
            weight: 权重
            max_retries: 最大重试次数
            auto_connect: 是否自动连接
            
        Returns:
            bool: 注册是否成功
        """
        try:
            with self._lock:
                if name in self._sources:
                    logger.warning(f"数据源 {name} 已存在，将覆盖原有配置")
                
                source_info = DataSourceInfo(
                    source=source,
                    priority=priority,
                    weight=weight,
                    max_retries=max_retries
                )
                
                # 尝试连接数据源
                if auto_connect:
                    if source.connect():
                        source_info.status = DataSourceStatus.ACTIVE
                        logger.info(f"数据源 {name} 注册成功并已连接")
                    else:
                        source_info.status = DataSourceStatus.ERROR
                        logger.warning(f"数据源 {name} 注册成功但连接失败")
                else:
                    source_info.status = DataSourceStatus.INACTIVE
                    logger.info(f"数据源 {name} 注册成功（未自动连接）")
                
                self._sources[name] = source_info
                
                # 启动健康检查线程（如果未被停止）
                if not self._stop_health_check and (not self._health_check_thread or not self._health_check_thread.is_alive()):
                    self._start_health_check()
                
                return True
                
        except Exception as e:
            logger.error(f"注册数据源 {name} 失败: {e}")
            return False
    
    def unregister_source(self, name: str) -> bool:
        """
        注销数据源
        
        Args:
            name: 数据源名称
            
        Returns:
            bool: 注销是否成功
        """
        try:
            with self._lock:
                if name not in self._sources:
                    logger.warning(f"数据源 {name} 不存在")
                    return False
                
                source_info = self._sources[name]
                
                # 断开连接
                try:
                    source_info.source.disconnect()
                except Exception as e:
                    logger.warning(f"断开数据源 {name} 连接时发生错误: {e}")
                
                # 移除数据源
                del self._sources[name]
                logger.info(f"数据源 {name} 已注销")
                
                return True
                
        except Exception as e:
            logger.error(f"注销数据源 {name} 失败: {e}")
            return False
    
    def get_available_sources(self) -> List[str]:
        """
        获取可用的数据源列表

        Returns:
            List[str]: 可用数据源名称列表
        """
        with self._lock:
            return [name for name, info in self._sources.items()
                   if info.status == DataSourceStatus.ACTIVE]

    def get_active_source_name(self) -> Optional[str]:
        """
        获取当前活跃的主要数据源名称

        Returns:
            Optional[str]: 主要数据源名称
        """
        with self._lock:
            available_sources = [(name, info) for name, info in self._sources.items()
                               if info.status == DataSourceStatus.ACTIVE]

            if not available_sources:
                return None

            # 返回优先级最高的数据源名称
            best_source = max(available_sources, key=lambda x: x[1].priority)
            return best_source[0]
    
    def get_source_by_name(self, name: str) -> Optional[IDataSource]:
        """
        根据名称获取数据源
        
        Args:
            name: 数据源名称
            
        Returns:
            Optional[IDataSource]: 数据源实例
        """
        with self._lock:
            if name in self._sources:
                source_info = self._sources[name]
                if source_info.status == DataSourceStatus.ACTIVE:
                    return source_info.source
        return None
    
    def get_best_source(self) -> Optional[IDataSource]:
        """
        根据负载均衡策略获取最佳数据源
        
        Returns:
            Optional[IDataSource]: 最佳数据源实例
        """
        with self._lock:
            available_sources = [(name, info) for name, info in self._sources.items()
                               if info.status == DataSourceStatus.ACTIVE]
            
            if not available_sources:
                logger.warning("没有可用的数据源")
                return None
            
            if self._load_balance_strategy == LoadBalanceStrategy.PRIORITY:
                # 按优先级排序，选择优先级最高的
                best_source = max(available_sources, key=lambda x: x[1].priority)
                return best_source[1].source
            
            elif self._load_balance_strategy == LoadBalanceStrategy.ROUND_ROBIN:
                # 轮询策略
                if self._round_robin_index >= len(available_sources):
                    self._round_robin_index = 0
                selected = available_sources[self._round_robin_index]
                self._round_robin_index += 1
                return selected[1].source
            
            elif self._load_balance_strategy == LoadBalanceStrategy.WEIGHTED:
                # 权重策略
                total_weight = sum(info.weight for _, info in available_sources)
                random_weight = random.uniform(0, total_weight)
                current_weight = 0
                
                for name, info in available_sources:
                    current_weight += info.weight
                    if current_weight >= random_weight:
                        return info.source
            
            elif self._load_balance_strategy == LoadBalanceStrategy.RESPONSE_TIME:
                # 响应时间策略，选择平均响应时间最短的
                best_source = min(available_sources, 
                                key=lambda x: x[1].stats.avg_response_time or float('inf'))
                return best_source[1].source
            
            # 默认返回第一个可用的数据源
            return available_sources[0][1].source
    
    def execute_with_failover(self, 
                            operation: Callable[[IDataSource], Any],
                            max_attempts: int = None) -> Any:
        """
        使用故障转移机制执行操作
        
        Args:
            operation: 要执行的操作函数
            max_attempts: 最大尝试次数
            
        Returns:
            Any: 操作结果
            
        Raises:
            DataSourceException: 所有数据源都失败时抛出
        """
        if max_attempts is None:
            max_attempts = len(self._sources) * 2
        
        attempts = 0
        last_error = None
        
        while attempts < max_attempts:
            attempts += 1
            
            # 获取最佳数据源
            source = self.get_best_source()
            if not source:
                break
            
            source_name = self._get_source_name(source)
            
            try:
                start_time = time.time()
                result = operation(source)
                end_time = time.time()
                
                # 更新统计信息
                self._update_stats(source_name, True, end_time - start_time)
                
                logger.debug(f"操作成功，使用数据源: {source_name}")
                return result
                
            except Exception as e:
                last_error = e
                logger.warning(f"数据源 {source_name} 操作失败: {e}")
                
                # 更新统计信息
                self._update_stats(source_name, False, 0, str(e))
                
                # 标记数据源为错误状态
                self._mark_source_error(source_name)
                
                # 指数退避延迟
                delay = min(2 ** (attempts - 1), 30)  # 最大延迟30秒
                if attempts < max_attempts:
                    logger.debug(f"等待 {delay} 秒后重试")
                    time.sleep(delay)
        
        # 所有数据源都失败
        error_msg = f"所有数据源操作失败，尝试次数: {attempts}"
        if last_error:
            error_msg += f"，最后错误: {last_error}"
        
        logger.error(error_msg)
        raise DataSourceException(error_msg)
    
    def get_market_data_with_failover(self, 
                                    symbol: str,
                                    period: str = "1d",
                                    start_date: Optional[str] = None,
                                    end_date: Optional[str] = None,
                                    dividend_type: str = "none") -> MarketData:
        """
        使用故障转移获取市场数据
        
        Args:
            symbol: 股票代码
            period: 数据周期
            start_date: 开始日期
            end_date: 结束日期
            dividend_type: 复权类型
            
        Returns:
            MarketData: 市场数据
        """
        def operation(source: IDataSource) -> MarketData:
            return source.get_market_data(symbol, period, start_date, end_date, dividend_type)
        
        return self.execute_with_failover(operation)
    
    def get_source_stats(self, name: str) -> Optional[DataSourceStats]:
        """
        获取数据源统计信息
        
        Args:
            name: 数据源名称
            
        Returns:
            Optional[DataSourceStats]: 统计信息
        """
        with self._lock:
            if name in self._sources:
                return self._sources[name].stats
        return None
    
    def get_all_stats(self) -> Dict[str, DataSourceStats]:
        """
        获取所有数据源统计信息
        
        Returns:
            Dict[str, DataSourceStats]: 所有数据源的统计信息
        """
        with self._lock:
            return {name: info.stats for name, info in self._sources.items()}
    
    def health_check(self, name: str = None) -> Dict[str, bool]:
        """
        执行健康检查
        
        Args:
            name: 特定数据源名称，None表示检查所有数据源
            
        Returns:
            Dict[str, bool]: 健康检查结果
        """
        results = {}
        
        with self._lock:
            sources_to_check = {name: self._sources[name]} if name and name in self._sources else self._sources
        
        for source_name, source_info in sources_to_check.items():
            try:
                is_healthy = source_info.source.test_connection()
                results[source_name] = is_healthy
                
                if is_healthy:
                    if source_info.status == DataSourceStatus.ERROR:
                        source_info.status = DataSourceStatus.ACTIVE
                        logger.info(f"数据源 {source_name} 恢复正常")
                else:
                    source_info.status = DataSourceStatus.ERROR
                    logger.warning(f"数据源 {source_name} 健康检查失败")
                
                source_info.last_health_check = datetime.now()
                
            except Exception as e:
                results[source_name] = False
                source_info.status = DataSourceStatus.ERROR
                logger.error(f"数据源 {source_name} 健康检查异常: {e}")
        
        return results

    def test_connection(self, name: str = None) -> bool:
        """
        测试数据源连接

        Args:
            name: 特定数据源名称，None表示测试主要数据源

        Returns:
            bool: 连接是否成功
        """
        try:
            if name:
                # 测试指定数据源
                source = self.get_source_by_name(name)
                if source:
                    return source.test_connection()
                else:
                    logger.warning(f"数据源 {name} 不存在")
                    return False
            else:
                # 测试主要数据源
                source = self.get_best_source()
                if source:
                    return source.test_connection()
                else:
                    logger.warning("没有可用的数据源")
                    return False

        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False

    def add_source(self, name: str, source: IDataSource, priority: int = 1) -> bool:
        """
        添加数据源（兼容旧接口）

        Args:
            name: 数据源名称
            source: 数据源实例
            priority: 优先级

        Returns:
            bool: 添加是否成功
        """
        return self.register_source(name, source, priority=priority)

    def _get_source_name(self, source: IDataSource) -> str:
        """获取数据源名称"""
        with self._lock:
            for name, info in self._sources.items():
                if info.source is source:
                    return name
        return "unknown"
    
    def _update_stats(self, 
                     source_name: str, 
                     success: bool, 
                     response_time: float,
                     error_message: str = "") -> None:
        """更新统计信息"""
        with self._lock:
            if source_name not in self._sources:
                return
            
            stats = self._sources[source_name].stats
            stats.total_requests += 1
            stats.last_request_time = datetime.now()
            
            if success:
                stats.successful_requests += 1
                # 更新平均响应时间
                if stats.avg_response_time == 0:
                    stats.avg_response_time = response_time
                else:
                    stats.avg_response_time = (stats.avg_response_time + response_time) / 2
            else:
                stats.failed_requests += 1
                stats.last_error_time = datetime.now()
                stats.last_error_message = error_message
    
    def _mark_source_error(self, source_name: str) -> None:
        """标记数据源为错误状态"""
        with self._lock:
            if source_name in self._sources:
                self._sources[source_name].status = DataSourceStatus.ERROR
    
    def _start_health_check(self) -> None:
        """启动健康检查线程"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
        
        self._stop_health_check = False
        self._health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True,
            name="DataSourceHealthCheck"
        )
        self._health_check_thread.start()
        logger.info("健康检查线程已启动")
    
    def _health_check_worker(self) -> None:
        """健康检查工作线程"""
        while not self._stop_health_check:
            try:
                self.health_check()
                time.sleep(self._health_check_interval)
            except Exception as e:
                logger.error(f"健康检查线程异常: {e}")
                time.sleep(10)  # 异常时短暂休眠
    
    def stop_health_check(self) -> None:
        """停止健康检查"""
        self._stop_health_check = True
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
        logger.info("健康检查线程已停止")
    
    def shutdown(self) -> None:
        """关闭管理器"""
        logger.info("开始关闭数据源管理器")
        
        # 停止健康检查
        self.stop_health_check()
        
        # 断开所有数据源连接
        with self._lock:
            for name, source_info in self._sources.items():
                try:
                    source_info.source.disconnect()
                    logger.debug(f"数据源 {name} 已断开连接")
                except Exception as e:
                    logger.warning(f"断开数据源 {name} 连接时发生错误: {e}")
            
            self._sources.clear()
        
        logger.info("数据源管理器已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()