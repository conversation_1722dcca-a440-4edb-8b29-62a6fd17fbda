#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心功能测试脚本
验证修复后的核心数据获取功能
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.base import DataSourceConfig


def test_core_data_functionality():
    """测试核心数据功能"""
    print("=" * 60)
    print("核心数据功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建适配器
        config = DataSourceConfig(
            name="core_test",
            enabled=True,
            config={"host": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        print("✅ 适配器创建成功")
        
        # 测试连接
        if adapter.connect():
            print("✅ 数据源连接成功")
            
            # 测试获取股票列表
            stock_list = adapter.get_stock_list()
            if stock_list and len(stock_list) > 0:
                print(f"✅ 获取股票列表成功，共 {len(stock_list)} 只股票")
                
                # 测试获取单只股票数据
                test_symbol = "000001.SZ"
                print(f"\n测试获取 {test_symbol} 的数据...")
                
                market_data = adapter.get_market_data(symbol=test_symbol, period='1d')
                
                if market_data and not market_data.data.empty:
                    print(f"✅ 成功获取 {test_symbol} 数据")
                    print(f"  - 数据行数: {len(market_data.data)}")
                    print(f"  - 数据列数: {len(market_data.data.columns)}")
                    print(f"  - 数据源: {market_data.source}")
                    
                    # 显示数据样例
                    print("\n数据样例:")
                    print(market_data.data.to_string())
                    
                    # 测试多只股票
                    print(f"\n测试获取多只股票数据...")
                    test_symbols = ["000001.SZ", "600000.SH"]
                    
                    success_count = 0
                    for symbol in test_symbols:
                        try:
                            data = adapter.get_market_data(symbol=symbol, period='1d')
                            if data and not data.data.empty:
                                success_count += 1
                                print(f"  ✅ {symbol}: 成功")
                            else:
                                print(f"  ❌ {symbol}: 数据为空")
                        except Exception as e:
                            print(f"  ❌ {symbol}: 失败 - {e}")
                    
                    print(f"\n多股票测试结果: {success_count}/{len(test_symbols)} 成功")
                    
                    if success_count > 0:
                        print("\n🎉 核心数据功能测试通过！")
                        print("\n系统现在可以:")
                        print("- ✅ 连接XtData数据源")
                        print("- ✅ 获取股票列表")
                        print("- ✅ 获取实时股票数据")
                        print("- ✅ 自动处理历史数据不可用的情况")
                        print("- ✅ 将实时数据转换为标准格式")
                        
                        print("\n注意事项:")
                        print("- 当前使用实时数据替代历史数据")
                        print("- 如需完整历史数据，请配置MiniQMT历史数据权限")
                        print("- 系统已具备基本的股票数据分析能力")
                        
                        return True
                    else:
                        print("❌ 多股票测试失败")
                        return False
                else:
                    print(f"❌ 获取 {test_symbol} 数据失败")
                    return False
            else:
                print("❌ 获取股票列表失败")
                return False
        else:
            print("❌ 数据源连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_core_data_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 数据源问题修复成功！")
        print("\n下一步建议:")
        print("1. 运行完整的系统测试")
        print("2. 开始使用股票分析功能")
        print("3. 如需历史数据，请联系券商开通权限")
        return 0
    else:
        print("❌ 仍存在问题，请检查:")
        print("1. MiniQMT客户端是否正常运行")
        print("2. 网络连接是否正常")
        print("3. 考虑使用模拟数据模式")
        return 1


if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
