# 技术上下文文档 (techContext.md)

## 项目：威科夫相对强弱选股系统
**文档版本**: 1.0  
**创建日期**: 2024-12-28  
**最后更新**: 2024-12-28  

---

## 技术栈概览

### 核心技术选择

#### 编程语言
- **Python 3.8+**: 主要开发语言
  - 丰富的数据处理生态系统
  - 优秀的金融数据分析库支持
  - 成熟的GUI框架
  - 活跃的开源社区

#### 用户界面框架
- **PyQt6**: 主UI框架
  - 跨平台兼容性
  - 丰富的控件和布局管理器
  - 良好的性能表现
  - 成熟的开发生态

#### 数据管理
- **SQLite**: 本地数据库
  - 轻量级、无需安装
  - 事务支持
  - 良好的Python集成
  - 适合单用户应用

#### 数据源接口
- **XtData**: 主要数据源（MiniQMT）
  - 实时行情数据
  - 历史K线数据
  - 板块成分股数据
  - 财务数据支持

### 开发工具链

#### 开发环境
```
IDE: Visual Studio Code / PyCharm
Python: 3.8+ (推荐3.10)
包管理: pip + requirements.txt
虚拟环境: venv
```

#### 代码质量工具
```
格式化: black
代码检查: flake8, pylint
类型检查: mypy
测试框架: pytest
覆盖率: pytest-cov
```

#### 构建和打包
```
打包工具: PyInstaller
安装程序: NSIS (Windows)
版本控制: Git
```

---

## 核心技术组件

### 1. 数据源适配器架构

#### 抽象接口设计
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
import pandas as pd

class IDataSource(ABC):
    """数据源抽象接口"""
    
    @abstractmethod
    def connect(self) -> bool:
        """建立连接"""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """断开连接"""
        pass
    
    @abstractmethod
    def get_kline_data(self, code: str, period: str, 
                      start_date: str, end_date: str) -> pd.DataFrame:
        """获取K线数据"""
        pass
    
    @abstractmethod
    def get_sector_stocks(self, sector_code: str) -> List[str]:
        """获取板块成分股"""
        pass
    
    @abstractmethod
    def get_all_sectors(self) -> Dict[str, str]:
        """获取所有板块信息"""
        pass
```

#### XtData适配器实现
```python
class XtDataAdapter(IDataSource):
    """XtData数据源适配器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.xt_data = None
        self._connected = False
    
    def connect(self) -> bool:
        """建立XtData连接"""
        try:
            from xtdata import xtdata
            self.xt_data = xtdata
            
            # 连接MiniQMT
            result = self.xt_data.connect()
            self._connected = (result == 0)
            return self._connected
        except Exception as e:
            logger.error(f"XtData连接失败: {e}")
            return False
    
    def get_kline_data(self, code: str, period: str, 
                      start_date: str, end_date: str) -> pd.DataFrame:
        """获取K线数据"""
        if not self._connected:
            raise ConnectionError("XtData未连接")
        
        # 转换周期格式
        period_map = {
            '1d': '1d', '1w': '1w', '1m': '1mon',
            '5m': '5m', '15m': '15m', '30m': '30m', '1h': '1h'
        }
        xt_period = period_map.get(period, '1d')
        
        # 获取数据
        data = self.xt_data.get_market_data(
            stock_list=[code],
            period=xt_period,
            start_time=start_date,
            end_time=end_date
        )
        
        return self._format_kline_data(data)
```

### 2. 数据库设计与ORM

#### 数据库架构
```sql
-- 板块信息表
CREATE TABLE sectors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    market VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 股票信息表
CREATE TABLE stocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    sector_id INTEGER,
    market VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sector_id) REFERENCES sectors(id)
);

-- K线数据表
CREATE TABLE kline_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    period VARCHAR(10) NOT NULL,
    open_price DECIMAL(10,3) NOT NULL,
    high_price DECIMAL(10,3) NOT NULL,
    low_price DECIMAL(10,3) NOT NULL,
    close_price DECIMAL(10,3) NOT NULL,
    volume BIGINT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stock_code, trade_date, period)
);

-- 相对强弱计算结果表
CREATE TABLE relative_strength (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    benchmark_code VARCHAR(20) NOT NULL,
    period VARCHAR(10) NOT NULL,
    time_range INTEGER NOT NULL,
    rs_value DECIMAL(8,4) NOT NULL,
    rank_in_sector INTEGER,
    rank_in_market INTEGER,
    calculation_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stock_code, benchmark_code, period, time_range, calculation_date)
);

-- 筛选结果表
CREATE TABLE screening_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(50) NOT NULL,
    stock_code VARCHAR(20) NOT NULL,
    stock_name VARCHAR(100) NOT NULL,
    sector_name VARCHAR(100) NOT NULL,
    rs_vs_market DECIMAL(8,4) NOT NULL,
    rs_vs_sector DECIMAL(8,4) NOT NULL,
    final_score DECIMAL(8,4) NOT NULL,
    rank_position INTEGER NOT NULL,
    screening_date DATE NOT NULL,
    parameters TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### SQLAlchemy模型定义
```python
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Numeric, ForeignKey, Text, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Sector(Base):
    __tablename__ = 'sectors'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    market = Column(String(10), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关系
    stocks = relationship("Stock", back_populates="sector")

class Stock(Base):
    __tablename__ = 'stocks'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(20), unique=True, nullable=False)
    name = Column(String(100), nullable=False)
    sector_id = Column(Integer, ForeignKey('sectors.id'))
    market = Column(String(10), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关系
    sector = relationship("Sector", back_populates="stocks")
```

### 3. 相对强弱计算引擎

#### 核心算法实现
```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

class RelativeStrengthEngine:
    """相对强弱计算引擎"""
    
    def __init__(self, data_manager, config: Dict):
        self.data_manager = data_manager
        self.config = config
        self.cache = {}
    
    def calculate_rs_batch(self, stocks: List[str], benchmark: str, 
                          time_ranges: List[int], period: str = '1d') -> Dict:
        """批量计算相对强弱"""
        results = {}
        
        # 并行计算
        with ThreadPoolExecutor(max_workers=self.config.get('max_workers', 4)) as executor:
            futures = {}
            
            for stock_code in stocks:
                for time_range in time_ranges:
                    future = executor.submit(
                        self._calculate_single_rs,
                        stock_code, benchmark, time_range, period
                    )
                    futures[future] = (stock_code, time_range)
            
            # 收集结果
            for future in as_completed(futures):
                stock_code, time_range = futures[future]
                try:
                    rs_value = future.result()
                    if stock_code not in results:
                        results[stock_code] = {}
                    results[stock_code][time_range] = rs_value
                except Exception as e:
                    logger.error(f"计算RS失败 {stock_code} vs {benchmark}: {e}")
        
        return results
    
    def _calculate_single_rs(self, stock_code: str, benchmark_code: str, 
                           time_range: int, period: str) -> float:
        """计算单个股票的相对强弱"""
        # 获取数据
        stock_data = self.data_manager.get_price_data(
            stock_code, period, time_range
        )
        benchmark_data = self.data_manager.get_price_data(
            benchmark_code, period, time_range
        )
        
        if stock_data.empty or benchmark_data.empty:
            return 0.0
        
        # 计算涨幅
        stock_return = self._calculate_return(stock_data)
        benchmark_return = self._calculate_return(benchmark_data)
        
        # 相对强弱 = 股票涨幅 - 基准涨幅
        rs_value = stock_return - benchmark_return
        
        return rs_value
    
    def _calculate_return(self, price_data: pd.DataFrame) -> float:
        """计算价格涨幅"""
        if len(price_data) < 2:
            return 0.0
        
        start_price = price_data.iloc[0]['close_price']
        end_price = price_data.iloc[-1]['close_price']
        
        if start_price <= 0:
            return 0.0
        
        return (end_price - start_price) / start_price * 100
```

### 4. 筛选策略框架

#### 策略接口设计
```python
from abc import ABC, abstractmethod
from typing import Dict, List
import pandas as pd

class IScreeningStrategy(ABC):
    """筛选策略接口"""
    
    @abstractmethod
    def screen(self, data: pd.DataFrame, parameters: Dict) -> pd.DataFrame:
        """执行筛选"""
        pass
    
    @abstractmethod
    def get_parameters_schema(self) -> Dict:
        """获取参数结构"""
        pass

class WyckoffScreeningStrategy(IScreeningStrategy):
    """威科夫筛选策略"""
    
    def screen(self, data: pd.DataFrame, parameters: Dict) -> pd.DataFrame:
        """威科夫双重筛选"""
        # 第一层：板块相对强弱筛选
        sector_threshold = parameters.get('sector_rs_threshold', 0.0)
        sector_filtered = data[data['rs_vs_market'] >= sector_threshold]
        
        # 第二层：个股相对板块强弱筛选
        stock_threshold = parameters.get('stock_rs_threshold', 0.0)
        stock_filtered = sector_filtered[
            sector_filtered['rs_vs_sector'] >= stock_threshold
        ]
        
        # 计算综合得分
        stock_filtered = self._calculate_composite_score(stock_filtered, parameters)
        
        # 按得分排序
        result = stock_filtered.sort_values('final_score', ascending=False)
        
        # 限制返回数量
        max_results = parameters.get('max_results', 50)
        return result.head(max_results)
    
    def _calculate_composite_score(self, data: pd.DataFrame, parameters: Dict) -> pd.DataFrame:
        """计算综合得分"""
        # 权重配置
        market_weight = parameters.get('market_rs_weight', 0.6)
        sector_weight = parameters.get('sector_rs_weight', 0.4)
        
        # 计算综合得分
        data = data.copy()
        data['final_score'] = (
            data['rs_vs_market'] * market_weight +
            data['rs_vs_sector'] * sector_weight
        )
        
        return data
```

### 5. 缓存和性能优化

#### 多级缓存架构
```python
import functools
import hashlib
import pickle
from typing import Any, Dict, Optional
from datetime import datetime, timedelta

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.memory_cache = {}
        self.disk_cache_path = config.get('cache_path', './cache')
        self.max_memory_size = config.get('max_memory_cache_mb', 100) * 1024 * 1024
        self.current_memory_size = 0
    
    def get(self, key: str, ttl: int = 3600) -> Optional[Any]:
        """获取缓存数据"""
        # 先检查内存缓存
        if key in self.memory_cache:
            data, timestamp = self.memory_cache[key]
            if datetime.now() - timestamp < timedelta(seconds=ttl):
                return data
            else:
                del self.memory_cache[key]
        
        # 检查磁盘缓存
        return self._get_disk_cache(key, ttl)
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> None:
        """设置缓存数据"""
        # 内存缓存
        data_size = len(pickle.dumps(value))
        if data_size < self.max_memory_size * 0.1:  # 单个对象不超过总内存的10%
            self._set_memory_cache(key, value)
        
        # 磁盘缓存
        self._set_disk_cache(key, value, ttl)
    
    def cache_result(self, ttl: int = 3600):
        """装饰器：缓存函数结果"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self._generate_cache_key(func.__name__, args, kwargs)
                
                # 尝试获取缓存
                cached_result = self.get(cache_key, ttl)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
```

### 6. 异步数据更新机制

#### 后台数据更新服务
```python
import asyncio
import schedule
from datetime import datetime, time
from typing import List, Dict

class DataUpdateService:
    """数据更新服务"""
    
    def __init__(self, data_manager, config: Dict):
        self.data_manager = data_manager
        self.config = config
        self.is_running = False
        self.update_tasks = []
    
    def start(self):
        """启动更新服务"""
        self.is_running = True
        
        # 配置定时任务
        self._setup_schedules()
        
        # 启动后台线程
        asyncio.create_task(self._run_scheduler())
    
    def stop(self):
        """停止更新服务"""
        self.is_running = False
    
    def _setup_schedules(self):
        """设置更新计划"""
        # 每日收盘后更新
        schedule.every().day.at("15:30").do(self._update_daily_data)
        
        # 每周末更新周线数据
        schedule.every().saturday.at("09:00").do(self._update_weekly_data)
        
        # 实时更新（交易时间内）
        if self.config.get('enable_realtime_update', False):
            schedule.every(5).minutes.do(self._update_realtime_data)
    
    async def _run_scheduler(self):
        """运行调度器"""
        while self.is_running:
            schedule.run_pending()
            await asyncio.sleep(60)  # 每分钟检查一次
    
    async def _update_daily_data(self):
        """更新日线数据"""
        try:
            # 获取所有需要更新的股票
            stocks = await self.data_manager.get_active_stocks()
            
            # 批量更新
            await self._batch_update_stocks(stocks, '1d')
            
            logger.info(f"日线数据更新完成，共更新 {len(stocks)} 只股票")
        except Exception as e:
            logger.error(f"日线数据更新失败: {e}")
```

---

## 性能优化策略

### 1. 数据库优化

#### 索引策略
```sql
-- 核心查询索引
CREATE INDEX idx_kline_stock_date ON kline_data(stock_code, trade_date);
CREATE INDEX idx_kline_period_date ON kline_data(period, trade_date);
CREATE INDEX idx_rs_stock_date ON relative_strength(stock_code, calculation_date);
CREATE INDEX idx_screening_session ON screening_results(session_id, screening_date);

-- 复合索引
CREATE INDEX idx_kline_composite ON kline_data(stock_code, period, trade_date);
CREATE INDEX idx_rs_composite ON relative_strength(stock_code, benchmark_code, time_range);
```

#### 批量操作优化
```python
def batch_insert_kline_data(self, data_list: List[Dict], batch_size: int = 1000):
    """批量插入K线数据"""
    with self.engine.begin() as conn:
        for i in range(0, len(data_list), batch_size):
            batch = data_list[i:i + batch_size]
            conn.execute(
                insert(KlineData).prefix_with('OR IGNORE'),
                batch
            )
```

### 2. 计算优化

#### 向量化计算
```python
import numpy as np
import pandas as pd

def vectorized_rs_calculation(stock_prices: np.ndarray, 
                            benchmark_prices: np.ndarray) -> np.ndarray:
    """向量化相对强弱计算"""
    # 计算收益率
    stock_returns = (stock_prices[-1] - stock_prices[0]) / stock_prices[0]
    benchmark_returns = (benchmark_prices[-1] - benchmark_prices[0]) / benchmark_prices[0]
    
    # 相对强弱
    rs_values = stock_returns - benchmark_returns
    
    return rs_values
```

#### 并行计算
```python
from multiprocessing import Pool, cpu_count
from functools import partial

def parallel_screening(self, sectors: List[str], parameters: Dict) -> Dict:
    """并行筛选处理"""
    # 确定进程数
    num_processes = min(cpu_count(), len(sectors))
    
    # 创建进程池
    with Pool(num_processes) as pool:
        # 部分应用参数
        screen_func = partial(self._screen_sector, parameters=parameters)
        
        # 并行执行
        results = pool.map(screen_func, sectors)
    
    # 合并结果
    return self._merge_screening_results(results)
```

### 3. 内存管理

#### 数据分块处理
```python
def process_large_dataset(self, stock_codes: List[str], chunk_size: int = 100):
    """分块处理大数据集"""
    results = []
    
    for i in range(0, len(stock_codes), chunk_size):
        chunk = stock_codes[i:i + chunk_size]
        
        # 处理当前块
        chunk_result = self._process_stock_chunk(chunk)
        results.extend(chunk_result)
        
        # 释放内存
        del chunk_result
        gc.collect()
    
    return results
```

---

## 安全性考虑

### 1. 数据安全

#### 数据加密
```python
from cryptography.fernet import Fernet
import base64

class DataEncryption:
    """数据加密工具"""
    
    def __init__(self, key: bytes = None):
        if key is None:
            key = Fernet.generate_key()
        self.cipher = Fernet(key)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        encrypted = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted = self.cipher.decrypt(encrypted_bytes)
        return decrypted.decode()
```

### 2. 系统安全

#### 输入验证
```python
import re
from typing import Union

class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_stock_code(code: str) -> bool:
        """验证股票代码格式"""
        pattern = r'^[0-9]{6}\.(SZ|SH)$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> bool:
        """验证日期范围"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            return start <= end
        except ValueError:
            return False
    
    @staticmethod
    def sanitize_sql_input(input_str: str) -> str:
        """SQL注入防护"""
        # 移除危险字符
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        sanitized = input_str
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()
```

---

## 测试策略

### 1. 单元测试

#### 核心组件测试
```python
import unittest
from unittest.mock import Mock, patch
import pandas as pd

class TestRelativeStrengthEngine(unittest.TestCase):
    """相对强弱引擎测试"""
    
    def setUp(self):
        self.mock_data_manager = Mock()
        self.config = {'max_workers': 2}
        self.engine = RelativeStrengthEngine(self.mock_data_manager, self.config)
    
    def test_calculate_single_rs(self):
        """测试单个RS计算"""
        # 模拟数据
        stock_data = pd.DataFrame({
            'close_price': [10.0, 11.0, 12.0]
        })
        benchmark_data = pd.DataFrame({
            'close_price': [100.0, 105.0, 108.0]
        })
        
        self.mock_data_manager.get_price_data.side_effect = [stock_data, benchmark_data]
        
        # 执行测试
        rs_value = self.engine._calculate_single_rs('000001.SZ', '000300.SH', 30, '1d')
        
        # 验证结果
        expected_stock_return = (12.0 - 10.0) / 10.0 * 100  # 20%
        expected_benchmark_return = (108.0 - 100.0) / 100.0 * 100  # 8%
        expected_rs = expected_stock_return - expected_benchmark_return  # 12%
        
        self.assertAlmostEqual(rs_value, expected_rs, places=2)
```

### 2. 集成测试

#### 数据流测试
```python
class TestDataFlow(unittest.TestCase):
    """数据流集成测试"""
    
    def setUp(self):
        self.app = create_test_app()
        self.data_manager = self.app.data_manager
    
    def test_full_screening_workflow(self):
        """测试完整筛选流程"""
        # 1. 数据准备
        self._prepare_test_data()
        
        # 2. 执行筛选
        parameters = {
            'time_ranges': [20, 60],
            'sector_rs_threshold': 0.0,
            'stock_rs_threshold': 0.0,
            'max_results': 10
        }
        
        results = self.app.screening_service.execute_screening(parameters)
        
        # 3. 验证结果
        self.assertIsInstance(results, pd.DataFrame)
        self.assertLessEqual(len(results), 10)
        self.assertIn('final_score', results.columns)
```

### 3. 性能测试

#### 基准测试
```python
import time
import psutil
from memory_profiler import profile

class PerformanceTest:
    """性能测试"""
    
    @profile
    def test_large_dataset_screening(self):
        """大数据集筛选性能测试"""
        # 准备大量测试数据
        stock_codes = [f"{i:06d}.SZ" for i in range(1, 1001)]  # 1000只股票
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 执行筛选
        results = self.screening_service.screen_stocks(stock_codes)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # 性能指标
        execution_time = end_time - start_time
        memory_usage = end_memory - start_memory
        
        print(f"执行时间: {execution_time:.2f}秒")
        print(f"内存使用: {memory_usage:.2f}MB")
        
        # 性能断言
        self.assertLess(execution_time, 30)  # 30秒内完成
        self.assertLess(memory_usage, 500)   # 内存增长不超过500MB
```

---

## 部署和运维

### 1. 打包配置

#### PyInstaller配置
```python
# build.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=['E:/cursor/xdqr'],
    binaries=[],
    datas=[
        ('src/resources', 'resources'),
        ('src/config', 'config'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'sqlalchemy.dialects.sqlite',
        'xtdata',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WyckoffScreener',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/resources/icons/app.ico'
)
```

### 2. 安装程序

#### NSIS脚本
```nsis
; WyckoffScreener.nsi
!define APPNAME "威科夫相对强弱选股系统"
!define COMPANYNAME "QuantTech"
!define DESCRIPTION "基于威科夫理论的智能选股工具"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!include "MUI2.nsh"

Name "${APPNAME}"
OutFile "WyckoffScreener_Setup.exe"
InstallDir "$PROGRAMFILES\${COMPANYNAME}\${APPNAME}"

; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "resources\icons\installer.ico"
!define MUI_UNICON "resources\icons\uninstaller.ico"

; 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

!insertmacro MUI_LANGUAGE "SimpChinese"

Section "主程序" SecMain
    SetOutPath "$INSTDIR"
    File /r "dist\WyckoffScreener\*"
    
    ; 创建快捷方式
    CreateDirectory "$SMPROGRAMS\${COMPANYNAME}"
    CreateShortcut "$SMPROGRAMS\${COMPANYNAME}\${APPNAME}.lnk" "$INSTDIR\WyckoffScreener.exe"
    CreateShortcut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\WyckoffScreener.exe"
    
    ; 注册表项
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$INSTDIR"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
    
    WriteUninstaller "$INSTDIR\Uninstall.exe"
SectionEnd
```

### 3. 日志和监控

#### 日志配置
```python
import logging
import logging.handlers
from pathlib import Path

def setup_logging(config: Dict):
    """设置日志系统"""
    log_dir = Path(config.get('log_dir', './logs'))
    log_dir.mkdir(exist_ok=True)
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 文件处理器（按日期轮转）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_dir / 'wyckoff_screener.log',
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 错误文件处理器
    error_handler = logging.FileHandler(
        log_dir / 'error.log',
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    root_logger.addHandler(error_handler)
    
    # 控制台处理器（开发模式）
    if config.get('debug', False):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
```

---

## 技术债务和改进计划

### 当前技术债务
1. **数据源依赖**: 目前仅支持XtData，需要增加更多数据源
2. **缓存策略**: 缓存机制相对简单，需要更智能的缓存策略
3. **错误处理**: 部分模块的错误处理不够完善
4. **测试覆盖**: 测试覆盖率需要提升到80%以上

### 技术改进计划

#### 第一阶段（1-2个月）
- 完善错误处理和日志系统
- 增加TuShare数据源支持
- 优化数据库查询性能
- 提升测试覆盖率

#### 第二阶段（3-4个月）
- 实现智能缓存策略
- 增加AkShare数据源支持
- 实现数据源故障自动切换
- 优化内存使用和垃圾回收

#### 第三阶段（5-6个月）
- 微服务架构重构准备
- 实现插件化架构
- 增加云端数据同步功能
- 性能监控和诊断系统

### 技术选型评估

#### 替代技术方案
1. **数据库**: 考虑升级到PostgreSQL（支持更复杂查询）
2. **UI框架**: 评估PySide6作为备选方案
3. **缓存**: 考虑Redis作为分布式缓存
4. **消息队列**: 评估RabbitMQ用于异步任务处理

#### 技术风险评估
1. **XtData稳定性**: 依赖第三方接口的稳定性风险
2. **Python GIL限制**: 多线程性能限制
3. **内存使用**: 大数据量处理的内存压力
4. **跨平台兼容**: Windows专用限制了扩展性

---

## 结论

本技术上下文文档详细描述了威科夫相对强弱选股系统的技术实现方案。系统采用分层架构设计，具备良好的可扩展性和可维护性。通过合理的技术选型和优化策略，能够满足高性能选股分析的需求。

随着项目的发展，我们将持续优化技术架构，引入更多先进技术，提升系统的整体性能和用户体验。

---

**文档维护**: 本文档将随着技术实现的进展持续更新，确保与实际代码保持同步。