"""
数据源配置控件

提供数据源连接配置和管理功能
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QSpinBox, QLineEdit, QComboBox,
    QCheckBox, QTabWidget, QScrollArea, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QAbstractItemView, QMessageBox,
    QProgressBar, QFrame, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QColor

from ...utils.logger import get_logger
from ...data_sources import (
    DataSourceConfig, DataSourceManager, XtDataAdapter
)
from ...utils.exceptions import ConnectionError

logger = get_logger(__name__)


class ConnectionTestThread(QThread):
    """连接测试线程"""

    # 信号定义
    test_completed = pyqtSignal(str, bool, str)  # 数据源名称, 成功状态, 消息
    progress_updated = pyqtSignal(str)  # 进度更新消息

    def __init__(self, config: DataSourceConfig):
        super().__init__()
        self.config = config

    def run(self):
        """执行连接测试"""
        try:
            self.progress_updated.emit("正在初始化数据源适配器...")

            # 创建适配器并测试连接
            if self.config.config.get('type') == 'xtdata':
                self.progress_updated.emit("正在创建XtData适配器...")
                adapter = XtDataAdapter(self.config)

                self.progress_updated.emit("正在测试连接...")
                success = adapter.connect()

                if success:
                    self.progress_updated.emit("连接成功，正在测试数据获取...")

                    # 进一步测试数据获取功能
                    try:
                        # 测试获取股票列表
                        stock_list = adapter.get_stock_list()
                        if stock_list and len(stock_list) > 0:
                            self.progress_updated.emit(f"获取到 {len(stock_list)} 只股票")
                        else:
                            self.progress_updated.emit("警告：未获取到股票列表")

                        # 测试获取市场数据
                        test_symbol = "000001.SZ"
                        market_data = adapter.get_market_data(test_symbol, period="1d")
                        if market_data and not market_data.data.empty:
                            self.progress_updated.emit(f"成功获取 {test_symbol} 市场数据")
                        else:
                            self.progress_updated.emit("警告：未获取到市场数据")

                        adapter.disconnect()
                        self.test_completed.emit(self.config.name, True, "连接测试成功，所有功能正常")

                    except Exception as e:
                        adapter.disconnect()
                        self.test_completed.emit(self.config.name, True, f"连接成功，但数据获取有问题: {e}")
                else:
                    self.test_completed.emit(self.config.name, False, "连接失败")
            else:
                self.test_completed.emit(self.config.name, False, "不支持的数据源类型")

        except Exception as e:
            # 提供更详细的错误信息
            error_message = str(e)
            if "XtData模块不可用" in error_message:
                # 如果是XtData模块问题，传递完整的安装指南
                self.test_completed.emit(self.config.name, False, error_message)
            else:
                self.test_completed.emit(self.config.name, False, f"连接测试失败: {error_message}")


class DataSourceConfigWidget(QWidget):
    """数据源配置控件"""
    
    # 信号定义
    config_changed = pyqtSignal(dict)  # 配置变更
    connection_tested = pyqtSignal(str, bool)  # 连接测试结果
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化数据源配置控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 数据源管理器
        self.data_source_manager = DataSourceManager()
        
        # 当前配置
        self.current_configs: Dict[str, Dict[str, Any]] = {}
        
        # 控件引用
        self.config_tabs: Optional[QTabWidget] = None
        self.source_table: Optional[QTableWidget] = None
        self.status_label: Optional[QLabel] = None
        self.progress_bar: Optional[QProgressBar] = None
        
        # XtData配置控件
        self.xtdata_settings = {}
        
        # 连接测试线程
        self.test_thread: Optional[ConnectionTestThread] = None
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_connection_status)
        self.status_timer.start(5000)  # 每5秒更新一次状态
        
        self._init_ui()
        self._load_current_configs()
        
        logger.info("数据源配置控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("🔌 数据源配置")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        self.config_tabs = QTabWidget()
        
        # 数据源列表标签页
        source_list_tab = self._create_source_list_tab()
        self.config_tabs.addTab(source_list_tab, "📋 数据源列表")
        
        # XtData配置标签页
        xtdata_tab = self._create_xtdata_config_tab()
        self.config_tabs.addTab(xtdata_tab, "📊 XtData配置")
        
        # 连接管理标签页
        connection_tab = self._create_connection_management_tab()
        self.config_tabs.addTab(connection_tab, "🔗 连接管理")
        
        layout.addWidget(self.config_tabs)
        
        # 状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 9pt;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        layout.addLayout(status_layout)
    
    def _create_source_list_tab(self) -> QWidget:
        """创建数据源列表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据源表格
        self.source_table = QTableWidget()
        self._setup_source_table()
        layout.addWidget(self.source_table)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("➕ 添加数据源")
        add_button.clicked.connect(self._add_data_source)
        button_layout.addWidget(add_button)
        
        edit_button = QPushButton("✏️ 编辑")
        edit_button.clicked.connect(self._edit_data_source)
        button_layout.addWidget(edit_button)
        
        delete_button = QPushButton("🗑️ 删除")
        delete_button.clicked.connect(self._delete_data_source)
        button_layout.addWidget(delete_button)
        
        test_button = QPushButton("🔍 测试连接")
        test_button.clicked.connect(self._test_selected_connection)
        button_layout.addWidget(test_button)
        
        button_layout.addStretch()
        
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self._refresh_source_list)
        button_layout.addWidget(refresh_button)
        
        layout.addLayout(button_layout)
        
        return widget
    
    def _setup_source_table(self):
        """设置数据源表格"""
        if not self.source_table:
            return
        
        # 设置列
        columns = ["名称", "类型", "状态", "地址", "端口", "启用"]
        self.source_table.setColumnCount(len(columns))
        self.source_table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.source_table.setAlternatingRowColors(True)
        self.source_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.source_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        
        # 列宽设置
        header = self.source_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)   # 名称
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)   # 类型
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)   # 状态
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch) # 地址
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)   # 端口
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)   # 启用
        
        self.source_table.setColumnWidth(0, 120)  # 名称
        self.source_table.setColumnWidth(1, 80)   # 类型
        self.source_table.setColumnWidth(2, 80)   # 状态
        self.source_table.setColumnWidth(4, 60)   # 端口
        self.source_table.setColumnWidth(5, 60)   # 启用
    
    def _create_xtdata_config_tab(self) -> QWidget:
        """创建XtData配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 配置说明
        info_label = QLabel("📊 XtData 是专业的金融数据接口，提供实时行情和历史数据服务")
        info_label.setStyleSheet("color: #666; font-size: 9pt; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 配置表单
        config_group = QGroupBox("连接配置")
        config_layout = QGridLayout(config_group)
        
        # 服务器地址
        config_layout.addWidget(QLabel("服务器地址:"), 0, 0)
        self.xtdata_settings['host'] = QLineEdit("127.0.0.1")
        self.xtdata_settings['host'].setPlaceholderText("通常为 127.0.0.1")
        config_layout.addWidget(self.xtdata_settings['host'], 0, 1)
        
        # 端口号
        config_layout.addWidget(QLabel("端口号:"), 1, 0)
        self.xtdata_settings['port'] = QSpinBox()
        self.xtdata_settings['port'].setRange(1, 65535)
        self.xtdata_settings['port'].setValue(58610)
        config_layout.addWidget(self.xtdata_settings['port'], 1, 1)
        
        # 用户名（可选）
        config_layout.addWidget(QLabel("用户名:"), 2, 0)
        self.xtdata_settings['username'] = QLineEdit()
        self.xtdata_settings['username'].setPlaceholderText("如果需要身份验证")
        config_layout.addWidget(self.xtdata_settings['username'], 2, 1)
        
        # 密码（可选）
        config_layout.addWidget(QLabel("密码:"), 3, 0)
        self.xtdata_settings['password'] = QLineEdit()
        self.xtdata_settings['password'].setEchoMode(QLineEdit.EchoMode.Password)
        self.xtdata_settings['password'].setPlaceholderText("如果需要身份验证")
        config_layout.addWidget(self.xtdata_settings['password'], 3, 1)
        
        # 自动重连
        self.xtdata_settings['auto_reconnect'] = QCheckBox("自动重连")
        self.xtdata_settings['auto_reconnect'].setChecked(True)
        config_layout.addWidget(self.xtdata_settings['auto_reconnect'], 4, 0, 1, 2)
        
        layout.addWidget(config_group)
        
        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QGridLayout(advanced_group)
        
        # 连接超时
        advanced_layout.addWidget(QLabel("连接超时(秒):"), 0, 0)
        self.xtdata_settings['timeout'] = QSpinBox()
        self.xtdata_settings['timeout'].setRange(5, 60)
        self.xtdata_settings['timeout'].setValue(30)
        advanced_layout.addWidget(self.xtdata_settings['timeout'], 0, 1)
        
        # 重试次数
        advanced_layout.addWidget(QLabel("重试次数:"), 1, 0)
        self.xtdata_settings['retry_count'] = QSpinBox()
        self.xtdata_settings['retry_count'].setRange(1, 10)
        self.xtdata_settings['retry_count'].setValue(3)
        advanced_layout.addWidget(self.xtdata_settings['retry_count'], 1, 1)
        
        # 请求频率限制
        advanced_layout.addWidget(QLabel("请求频率(次/秒):"), 2, 0)
        self.xtdata_settings['rate_limit'] = QSpinBox()
        self.xtdata_settings['rate_limit'].setRange(1, 50)
        self.xtdata_settings['rate_limit'].setValue(10)
        advanced_layout.addWidget(self.xtdata_settings['rate_limit'], 2, 1)
        
        layout.addWidget(advanced_group)
        
        # 数据设置
        data_group = QGroupBox("数据设置")
        data_layout = QGridLayout(data_group)
        
        # 缓存时间
        data_layout.addWidget(QLabel("缓存时间(分钟):"), 0, 0)
        self.xtdata_settings['cache_duration'] = QSpinBox()
        self.xtdata_settings['cache_duration'].setRange(1, 1440)
        self.xtdata_settings['cache_duration'].setValue(60)
        data_layout.addWidget(self.xtdata_settings['cache_duration'], 0, 1)
        
        # 数据质量检查
        self.xtdata_settings['data_validation'] = QCheckBox("启用数据质量检查")
        self.xtdata_settings['data_validation'].setChecked(True)
        data_layout.addWidget(self.xtdata_settings['data_validation'], 1, 0, 1, 2)
        
        # 自动数据更新
        self.xtdata_settings['auto_update'] = QCheckBox("自动更新数据")
        self.xtdata_settings['auto_update'].setChecked(True)
        data_layout.addWidget(self.xtdata_settings['auto_update'], 2, 0, 1, 2)
        
        layout.addWidget(data_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 测试连接按钮
        test_button = QPushButton("🔍 测试连接")
        test_button.clicked.connect(self._test_xtdata_connection)
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(test_button)
        
        # 保存配置按钮
        save_button = QPushButton("💾 保存配置")
        save_button.clicked.connect(self._save_xtdata_config)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(save_button)
        
        # 重置配置按钮
        reset_button = QPushButton("🔄 重置配置")
        reset_button.clicked.connect(self._reset_xtdata_config)
        button_layout.addWidget(reset_button)
        
        # 安装指南按钮
        guide_button = QPushButton("📋 安装指南")
        guide_button.clicked.connect(self._show_installation_guide)
        button_layout.addWidget(guide_button)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 状态显示
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_layout = QVBoxLayout(status_frame)
        
        status_title = QLabel("📊 连接状态")
        status_title.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        status_layout.addWidget(status_title)
        
        self.xtdata_status_label = QLabel("未连接")
        self.xtdata_status_label.setStyleSheet("color: #666; font-size: 9pt;")
        status_layout.addWidget(self.xtdata_status_label)
        
        layout.addWidget(status_frame)
        
        return widget
    
    def _reset_xtdata_config(self):
        """重置XtData配置"""
        reply = QMessageBox.question(
            self,
            "重置配置",
            "确定要重置 XtData 配置到默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 重置为默认值
            self.xtdata_settings['host'].setText("127.0.0.1")
            self.xtdata_settings['port'].setValue(58610)
            self.xtdata_settings['username'].setText("")
            self.xtdata_settings['password'].setText("")
            self.xtdata_settings['auto_reconnect'].setChecked(True)
            self.xtdata_settings['timeout'].setValue(30)
            self.xtdata_settings['retry_count'].setValue(3)
            self.xtdata_settings['rate_limit'].setValue(10)
            self.xtdata_settings['cache_duration'].setValue(60)
            self.xtdata_settings['data_validation'].setChecked(True)
            self.xtdata_settings['auto_update'].setChecked(True)
            
            self.status_label.setText("XtData 配置已重置为默认值")
            QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
    
    def _create_connection_management_tab(self) -> QWidget:
        """创建连接管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 连接状态组
        status_group = QGroupBox("连接状态")
        status_layout = QVBoxLayout(status_group)
        
        self.connection_status_text = QTextEdit()
        self.connection_status_text.setReadOnly(True)
        self.connection_status_text.setMaximumHeight(150)
        self.connection_status_text.setFont(QFont("Consolas", 9))
        status_layout.addWidget(self.connection_status_text)
        
        layout.addWidget(status_group)
        
        # 连接管理按钮
        management_layout = QHBoxLayout()
        
        connect_all_button = QPushButton("🔗 连接所有数据源")
        connect_all_button.clicked.connect(self._connect_all_sources)
        management_layout.addWidget(connect_all_button)
        
        disconnect_all_button = QPushButton("🔌 断开所有连接")
        disconnect_all_button.clicked.connect(self._disconnect_all_sources)
        management_layout.addWidget(disconnect_all_button)
        
        health_check_button = QPushButton("🏥 健康检查")
        health_check_button.clicked.connect(self._perform_health_check)
        management_layout.addWidget(health_check_button)
        
        management_layout.addStretch()
        
        layout.addLayout(management_layout)
        layout.addStretch()
        
        return widget

    def _load_current_configs(self):
        """加载当前配置"""
        try:
            # 加载默认XtData配置
            self.current_configs = {
                'xtdata_default': {
                    'name': 'xtdata_default',
                    'type': 'xtdata',
                    'enabled': True,
                    'ip': '127.0.0.1',
                    'port': 58610,
                    'timeout': 30,
                    'retry_times': 3,
                    'auto_reconnect': True,
                    'status': 'disconnected'
                }
            }

            self._update_source_table()
            self._apply_configs_to_ui()
            self.status_label.setText("配置已加载")

        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self.status_label.setText(f"加载配置失败: {e}")

    def _apply_configs_to_ui(self):
        """将配置应用到UI"""
        try:
            # 应用XtData配置
            xtdata_config = self.current_configs.get('xtdata_default', {})
            if xtdata_config:
                self.xtdata_settings['host'].setText(xtdata_config.get('ip', '127.0.0.1'))
                self.xtdata_settings['port'].setValue(xtdata_config.get('port', 58610))
                self.xtdata_settings['timeout'].setValue(xtdata_config.get('timeout', 30))
                self.xtdata_settings['retry_count'].setValue(xtdata_config.get('retry_times', 3))
                self.xtdata_settings['auto_reconnect'].setChecked(xtdata_config.get('auto_reconnect', True))
                # 处理可能不存在的 enabled 字段
                if 'enabled' in xtdata_config:
                    # 如果界面中有 enabled 控件才设置
                    if hasattr(self, 'xtdata_enabled') and self.xtdata_enabled:
                        self.xtdata_enabled.setChecked(xtdata_config.get('enabled', True))
            
            # 更新数据源表格
            self._update_source_table()
            
        except Exception as e:
            logger.error(f"应用配置到UI失败: {e}")
    
    @pyqtSlot(str)
    def _on_test_progress_updated(self, message: str):
        """连接测试进度更新回调"""
        self.status_label.setText(message)
        
    def _test_xtdata_connection(self):
        """测试XtData连接"""
        try:
            # 创建配置
            config = DataSourceConfig(
                name="xtdata_test",
                config={
                    'type': 'xtdata',
                    'host': self.xtdata_settings['host'].text(),
                    'port': self.xtdata_settings['port'].value(),
                    'username': self.xtdata_settings.get('username', QLineEdit()).text(),
                    'password': self.xtdata_settings.get('password', QLineEdit()).text(),
                    'timeout': self.xtdata_settings['timeout'].value(),
                    'retry_count': self.xtdata_settings['retry_count'].value(),
                    'auto_reconnect': self.xtdata_settings['auto_reconnect'].isChecked()
                }
            )
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条
            self.status_label.setText("正在测试连接...")
            
            # 创建并启动测试线程
            self.test_thread = ConnectionTestThread(config)
            self.test_thread.test_completed.connect(self._on_connection_test_completed)
            self.test_thread.progress_updated.connect(self._on_test_progress_updated)
            self.test_thread.finished.connect(lambda: self.progress_bar.setVisible(False))
            self.test_thread.start()
            
        except Exception as e:
            logger.error(f"启动连接测试失败: {e}")
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"测试失败: {e}")
    
    def _get_xtdata_config(self) -> Dict[str, Any]:
        """获取XtData配置"""
        return {
            'name': 'xtdata_default',
            'type': 'xtdata',
            'enabled': True,  # 默认启用
            'host': self.xtdata_settings['host'].text(),
            'port': self.xtdata_settings['port'].value(),
            'timeout': self.xtdata_settings['timeout'].value(),
            'retry_count': self.xtdata_settings['retry_count'].value(),
            'auto_reconnect': self.xtdata_settings['auto_reconnect'].isChecked(),
            'status': 'disconnected'
        }

    @pyqtSlot(str, bool, str)
    def _on_connection_test_completed(self, source_name: str, success: bool, message: str):
        """连接测试完成回调"""
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText(f"✅ {message}")
            QMessageBox.information(self, "连接测试成功", f"✅ {message}")
        else:
            self.status_label.setText(f"❌ 连接失败")
            # 为错误消息创建更详细的对话框
            self._show_connection_error_dialog(source_name, message)

        self.connection_tested.emit(source_name, success)

    def _show_connection_error_dialog(self, source_name: str, error_message: str):
        """显示连接错误对话框"""
        dialog = QMessageBox(self)
        dialog.setWindowTitle("连接测试失败")
        dialog.setIcon(QMessageBox.Icon.Critical)
        
        # 检查错误类型并提供相应的解决方案
        if "XtData模块不可用" in error_message:
            dialog.setText("XtData模块安装问题")
            dialog.setDetailedText(error_message)
            
            # 添加安装指导按钮
            install_button = dialog.addButton("查看安装指南", QMessageBox.ButtonRole.ActionRole)
            dialog.addButton("确定", QMessageBox.ButtonRole.AcceptRole)
            
            result = dialog.exec()
            
            if dialog.clickedButton() == install_button:
                self._show_installation_guide()
        else:
            dialog.setText(f"数据源 '{source_name}' 连接失败")
            dialog.setDetailedText(f"错误详情：\n{error_message}")
            
            # 添加故障排除按钮
            troubleshoot_button = dialog.addButton("故障排除", QMessageBox.ButtonRole.ActionRole)
            dialog.addButton("确定", QMessageBox.ButtonRole.AcceptRole)
            
            result = dialog.exec()
            
            if dialog.clickedButton() == troubleshoot_button:
                self._show_troubleshooting_guide()
    
    def _show_installation_guide(self):
        """显示安装指南对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle("XtData 安装指南")
        dialog.setFixedSize(600, 500)
        
        layout = QVBoxLayout(dialog)
        
        # 安装指南内容
        guide_text = QTextEdit()
        guide_text.setReadOnly(True)
        guide_text.setPlainText("""
📋 XtData 完整安装配置指南

🔧 第一步：安装 Python 库
1. 打开命令提示符（管理员权限）
2. 执行安装命令：
   pip install xtquant
   
   如果安装失败，请尝试：
   pip install xtquant -i https://pypi.tuna.tsinghua.edu.cn/simple/

🏢 第二步：获取 XtData 客户端
1. 访问迅投官网：https://www.xtdata.com/
2. 下载最新版本的 XtData 客户端
3. 或联系您的券商获取客户端安装包

💻 第三步：安装并配置客户端
1. 运行 XtData 安装程序
2. 按照向导完成安装
3. 启动 XtData 客户端
4. 使用您的账户信息登录

🔌 第四步：配置连接参数
1. 确保 XtData 客户端正在运行
2. 在本系统中配置连接参数：
   - 服务器地址：127.0.0.1（本地）
   - 端口号：58610（默认）
   - 用户名：（如果需要）
   - 密码：（如果需要）

🔍 第五步：测试连接
1. 点击"测试连接"按钮
2. 等待测试结果
3. 如果失败，请检查以下项目：
   - XtData 客户端是否正在运行
   - 防火墙是否阻止了端口 58610
   - 网络连接是否正常

⚠️ 常见问题解决：
• 如果提示"模块不存在"，请重新安装 xtquant 库
• 如果连接超时，请检查防火墙设置
• 如果登录失败，请确认账户信息正确
• 如果数据获取失败，请检查数据权限

💡 获取帮助：
• 查看 XtData 官方文档
• 联系券商技术支持
• 访问系统帮助中心

🎯 验证安装成功：
当连接测试显示"连接成功，所有功能正常"时，表示安装配置完成。
""")
        
        layout.addWidget(guide_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        copy_button = QPushButton("复制安装命令")
        copy_button.clicked.connect(lambda: self._copy_to_clipboard("pip install xtquant"))
        button_layout.addWidget(copy_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        dialog.exec()
    
    def _show_troubleshooting_guide(self):
        """显示故障排除指南"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle("XtData 故障排除指南")
        dialog.setFixedSize(600, 500)
        
        layout = QVBoxLayout(dialog)
        
        # 故障排除内容
        troubleshoot_text = QTextEdit()
        troubleshoot_text.setReadOnly(True)
        troubleshoot_text.setPlainText("""
🔧 XtData 连接故障排除指南

🔍 问题诊断步骤：

1️⃣ 检查 XtData 客户端状态
✓ 确认 XtData 客户端正在运行
✓ 检查客户端是否已成功登录
✓ 查看客户端状态栏是否显示"已连接"
✓ 尝试重新启动客户端

2️⃣ 检查网络连接
✓ 确认网络连接正常
✓ 检查服务器地址是否正确：127.0.0.1
✓ 确认端口号是否正确：58610
✓ 尝试 ping 127.0.0.1 测试本地连接

3️⃣ 检查防火墙设置
✓ 确保防火墙没有阻止端口 58610
✓ 将 XtData 客户端添加到防火墙白名单
✓ 检查杀毒软件是否阻止连接
✓ 临时关闭防火墙测试连接

4️⃣ 检查 Python 环境
✓ 确认 xtquant 库已正确安装
✓ 检查 Python 版本兼容性
✓ 尝试重新安装 xtquant 库
✓ 检查是否有多个 Python 环境冲突

5️⃣ 检查系统权限
✓ 以管理员权限运行本程序
✓ 确认 XtData 客户端有足够权限
✓ 检查用户账户控制（UAC）设置

6️⃣ 检查数据权限
✓ 确认账户有数据访问权限
✓ 检查数据订阅状态
✓ 联系券商确认数据权限

🚨 常见错误及解决方案：

错误："连接被拒绝"
解决：检查 XtData 客户端是否运行，重启客户端

错误："模块不存在"
解决：重新安装 xtquant 库

错误："连接超时"
解决：检查防火墙设置，确保端口 58610 开放

错误："认证失败"
解决：检查用户名密码，确认账户状态

错误："数据获取失败"
解决：检查数据权限，确认数据订阅

🔄 重置步骤：
如果以上步骤都无法解决问题，可以尝试：
1. 完全卸载并重新安装 XtData 客户端
2. 重新安装 xtquant 库
3. 重启计算机
4. 联系技术支持

📞 获取支持：
• 查看 XtData 官方文档
• 联系券商技术支持热线
• 访问在线帮助中心
• 提交技术支持工单
""")
        
        layout.addWidget(troubleshoot_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        test_button = QPushButton("重新测试连接")
        test_button.clicked.connect(lambda: (dialog.accept(), self._test_xtdata_connection()))
        button_layout.addWidget(test_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        dialog.exec()
    
    def _copy_to_clipboard(self, text: str):
        """复制文本到剪贴板"""
        try:
            from PyQt6.QtGui import QClipboard
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            
            # 显示复制成功提示
            self.status_label.setText("安装命令已复制到剪贴板")
            QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
            
        except Exception as e:
            logger.error(f"复制到剪贴板失败: {e}")

    def _save_xtdata_config(self):
        """保存XtData配置"""
        try:
            # 收集配置
            config = {
                'name': 'xtdata_default',
                'type': 'xtdata',
                'enabled': self.xtdata_settings['enabled'].isChecked(),
                'ip': self.xtdata_settings['host'].text(),
                'port': self.xtdata_settings['port'].value(),
                'timeout': self.xtdata_settings['timeout'].value(),
                'retry_count': self.xtdata_settings['retry_count'].value(),
                'auto_reconnect': self.xtdata_settings['auto_reconnect'].isChecked(),
                'status': 'disconnected'
            }

            # 更新配置
            self.current_configs['xtdata_default'] = config
            self._update_source_table()

            # 发送配置变更信号
            self.config_changed.emit(self.current_configs)

            self.status_label.setText("XtData配置已保存")
            QMessageBox.information(self, "保存配置", "XtData配置已保存成功！")

        except Exception as e:
            logger.error(f"保存XtData配置失败: {e}")
            self.status_label.setText(f"保存配置失败: {e}")
            QMessageBox.warning(self, "保存配置", f"保存配置失败: {e}")

    def _add_data_source(self):
        """添加数据源"""
        self.status_label.setText("添加数据源功能开发中...")

    def _edit_data_source(self):
        """编辑数据源"""
        current_row = self.source_table.currentRow()
        if current_row >= 0:
            self.status_label.setText("编辑数据源功能开发中...")
        else:
            QMessageBox.information(self, "编辑数据源", "请先选择要编辑的数据源")

    def _delete_data_source(self):
        """删除数据源"""
        current_row = self.source_table.currentRow()
        if current_row >= 0:
            self.status_label.setText("删除数据源功能开发中...")
        else:
            QMessageBox.information(self, "删除数据源", "请先选择要删除的数据源")

    def _test_selected_connection(self):
        """测试选中的连接"""
        current_row = self.source_table.currentRow()
        if current_row >= 0:
            try:
                # 获取选中的数据源名称
                name_item = self.source_table.item(current_row, 0)
                if name_item:
                    source_name = name_item.text()
                    if source_name in self.current_configs:
                        config = self.current_configs[source_name]

                        # 创建数据源配置对象
                        ds_config = DataSourceConfig(
                            name=source_name,
                            enabled=config.get('enabled', True),
                            timeout=config.get('timeout', 30),
                            retry_times=config.get('retry_times', 3),
                            auto_reconnect=config.get('auto_reconnect', True),
                            config={
                                'type': config.get('type', 'xtdata'),
                                'ip': config.get('ip', '127.0.0.1'),
                                'port': config.get('port', 58610)
                            }
                        )

                        # 启动连接测试
                        self.test_thread = ConnectionTestThread(ds_config)
                        self.test_thread.test_completed.connect(self._on_connection_test_completed)
                        self.test_thread.progress_updated.connect(self._on_test_progress_updated)
                        self.test_thread.start()

                        self.progress_bar.setVisible(True)
                        self.progress_bar.setRange(0, 0)
                        self.status_label.setText(f"正在测试 {source_name} 连接...")
                    else:
                        QMessageBox.warning(self, "测试连接", "未找到选中的数据源配置")
                else:
                    QMessageBox.warning(self, "测试连接", "无法获取数据源名称")
            except Exception as e:
                logger.error(f"测试选中连接失败: {e}")
                self.status_label.setText(f"测试失败: {e}")
        else:
            QMessageBox.information(self, "测试连接", "请先选择要测试的数据源")

    def _refresh_source_list(self):
        """刷新数据源列表"""
        self._update_connection_status()
        self._update_source_table()
        self.status_label.setText("数据源列表已刷新")

    def _connect_all_sources(self):
        """连接所有数据源"""
        try:
            self.status_label.setText("正在连接所有数据源...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            success_count = 0
            total_count = len(self.current_configs)

            for name, config in self.current_configs.items():
                if not config.get('enabled', False):
                    continue

                try:
                    # 创建数据源配置对象
                    ds_config = DataSourceConfig(
                        name=name,
                        enabled=config.get('enabled', True),
                        timeout=config.get('timeout', 30),
                        retry_times=config.get('retry_times', 3),
                        auto_reconnect=config.get('auto_reconnect', True),
                        config={
                            'type': config.get('type', 'xtdata'),
                            'ip': config.get('ip', '127.0.0.1'),
                            'port': config.get('port', 58610)
                        }
                    )

                    # 创建适配器并连接
                    if config.get('type') == 'xtdata':
                        adapter = XtDataAdapter(ds_config)
                        if adapter.connect():
                            config['status'] = 'connected'
                            success_count += 1
                            logger.info(f"数据源 {name} 连接成功")
                        else:
                            config['status'] = 'error'
                            logger.warning(f"数据源 {name} 连接失败")

                except Exception as e:
                    config['status'] = 'error'
                    logger.error(f"连接数据源 {name} 失败: {e}")

            self.progress_bar.setVisible(False)
            self._update_source_table()

            if success_count == total_count:
                self.status_label.setText(f"✅ 所有数据源连接成功 ({success_count}/{total_count})")
            else:
                self.status_label.setText(f"⚠️ 部分数据源连接成功 ({success_count}/{total_count})")

        except Exception as e:
            self.progress_bar.setVisible(False)
            logger.error(f"连接所有数据源失败: {e}")
            self.status_label.setText(f"连接失败: {e}")

    def _disconnect_all_sources(self):
        """断开所有连接"""
        try:
            self.status_label.setText("正在断开所有连接...")

            for name, config in self.current_configs.items():
                config['status'] = 'disconnected'

            self._update_source_table()
            self.status_label.setText("✅ 已断开所有数据源连接")
            logger.info("已断开所有数据源连接")

        except Exception as e:
            logger.error(f"断开连接失败: {e}")
            self.status_label.setText(f"断开连接失败: {e}")

    def _perform_health_check(self):
        """执行健康检查"""
        try:
            status_text = "=== 数据源健康检查 ===\n"
            status_text += f"检查时间: {QTimer().remainingTime()}\n\n"

            for name, config in self.current_configs.items():
                status_text += f"数据源: {name}\n"
                status_text += f"  类型: {config.get('type', 'unknown')}\n"
                status_text += f"  状态: {config.get('status', 'unknown')}\n"
                status_text += f"  地址: {config.get('ip', 'N/A')}:{config.get('port', 'N/A')}\n"
                status_text += f"  启用: {'是' if config.get('enabled', False) else '否'}\n\n"

            self.connection_status_text.setText(status_text)
            self.status_label.setText("健康检查已完成")

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            self.status_label.setText(f"健康检查失败: {e}")

    def _update_connection_status(self):
        """更新连接状态"""
        try:
            # 这里可以实际检查数据源连接状态
            # 现在只是模拟更新
            for name, config in self.current_configs.items():
                # 模拟状态检查
                if config.get('enabled', False):
                    config['status'] = 'disconnected'  # 实际应该检查真实状态
                else:
                    config['status'] = 'disabled'

            self._update_source_table()

        except Exception as e:
            logger.error(f"更新连接状态失败: {e}")

    # 公共方法
    def get_current_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取当前配置"""
        return self.current_configs.copy()

    def apply_config(self, config_name: str, config_data: Dict[str, Any]):
        """应用配置"""
        self.current_configs[config_name] = config_data
        self._update_source_table()
        self._apply_configs_to_ui()

    def get_xtdata_config(self) -> Dict[str, Any]:
        """获取XtData配置"""
        return self.current_configs.get('xtdata_default', {})

    def _update_source_table(self):
        """更新数据源表格"""
        if not self.source_table:
            return

        self.source_table.setRowCount(len(self.current_configs))

        for row, (name, config) in enumerate(self.current_configs.items()):
            # 名称
            name_item = QTableWidgetItem(config.get('name', name))
            self.source_table.setItem(row, 0, name_item)

            # 类型
            type_item = QTableWidgetItem(config.get('type', 'unknown'))
            self.source_table.setItem(row, 1, type_item)

            # 状态
            status = config.get('status', 'unknown')
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # 设置状态颜色
            status_colors = {
                'connected': QColor("#4CAF50"),
                'disconnected': QColor("#F44336"),
                'connecting': QColor("#FF9800"),
                'error': QColor("#9C27B0"),
                'unknown': QColor("#9E9E9E")
            }

            if status in status_colors:
                status_item.setForeground(status_colors[status])

            self.source_table.setItem(row, 2, status_item)

            # 地址
            host = config.get('host', config.get('ip', ''))  # 兼容新旧字段名
            host_item = QTableWidgetItem(host)
            self.source_table.setItem(row, 3, host_item)

            # 端口
            port_item = QTableWidgetItem(str(config.get('port', '')))
            port_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.source_table.setItem(row, 4, port_item)

            # 启用状态
            enabled_item = QTableWidgetItem("是" if config.get('enabled', False) else "否")
            enabled_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.source_table.setItem(row, 5, enabled_item)
