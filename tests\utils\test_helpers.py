"""
通用工具函数测试
"""

import pytest
import sys
import os
import tempfile
import json
import csv
from datetime import datetime, date, timedelta
from pathlib import Path
from unittest.mock import patch, mock_open

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.utils.helpers import (
    # 时间处理
    get_current_timestamp, get_current_datetime, format_datetime,
    parse_datetime, get_trading_day, get_previous_trading_day,
    get_date_range, time_elapsed,
    
    # 数据验证
    is_valid_stock_code, is_valid_email, is_valid_phone,
    is_valid_url, validate_numeric_range,
    
    # 数据格式化
    format_number, format_percentage, format_currency,
    format_file_size, truncate_string,
    
    # 数据转换
    safe_int, safe_float, safe_str, dict_to_obj, obj_to_dict,
    
    # 文件处理
    ensure_dir_exists, get_file_extension, is_file_older_than,
    read_json_file, write_json_file, read_csv_file, write_csv_file,
    
    # 加密和哈希
    generate_uuid, generate_short_id, calculate_md5,
    calculate_sha256, calculate_file_hash,
    
    # 缓存和性能
    memoize, timing, batch_process,
    
    # 其他工具
    flatten_dict, get_nested_value, chunk_list,
    remove_duplicates, merge_dicts, retry
)


class TestTimeHandling:
    """时间处理测试"""
    
    def test_get_current_timestamp(self):
        """测试获取当前时间戳"""
        timestamp = get_current_timestamp()
        assert isinstance(timestamp, int)
        assert timestamp > 0
    
    def test_get_current_datetime(self):
        """测试获取当前日期时间"""
        dt = get_current_datetime()
        assert isinstance(dt, datetime)
    
    def test_format_datetime(self):
        """测试格式化日期时间"""
        dt = datetime(2024, 1, 15, 10, 30, 45)
        
        # 默认格式
        result = format_datetime(dt)
        assert result == "2024-01-15 10:30:45"
        
        # 自定义格式
        result = format_datetime(dt, "%Y/%m/%d")
        assert result == "2024/01/15"
    
    def test_parse_datetime(self):
        """测试解析日期时间字符串"""
        # 成功解析
        dt = parse_datetime("2024-01-15 10:30:45")
        assert dt == datetime(2024, 1, 15, 10, 30, 45)
        
        # 解析失败
        dt = parse_datetime("invalid date")
        assert dt is None
    
    def test_get_trading_day(self):
        """测试获取交易日"""
        # 工作日
        monday = date(2024, 1, 15)  # 假设是周一
        result = get_trading_day(monday)
        assert result == monday
        
        # 周六
        saturday = date(2024, 1, 13)  # 假设是周六
        result = get_trading_day(saturday)
        assert result == date(2024, 1, 12)  # 上一个周五
        
        # 周日
        sunday = date(2024, 1, 14)  # 假设是周日
        result = get_trading_day(sunday)
        assert result == date(2024, 1, 12)  # 上一个周五
    
    def test_get_previous_trading_day(self):
        """测试获取前N个交易日"""
        # 从周三获取前一个交易日
        wednesday = date(2024, 1, 17)  # 假设是周三
        result = get_previous_trading_day(wednesday, 1)
        assert result == date(2024, 1, 16)  # 周二
        
        # 从周一获取前一个交易日
        monday = date(2024, 1, 15)  # 假设是周一
        result = get_previous_trading_day(monday, 1)
        assert result == date(2024, 1, 12)  # 上周五
    
    def test_get_date_range(self):
        """测试获取日期范围"""
        start_date = date(2024, 1, 15)  # 周一
        end_date = date(2024, 1, 19)    # 周五
        
        # 包含所有日期
        dates = get_date_range(start_date, end_date, trading_days_only=False)
        assert len(dates) == 5
        
        # 仅交易日
        dates = get_date_range(start_date, end_date, trading_days_only=True)
        assert len(dates) == 5  # 周一到周五
    
    def test_time_elapsed(self):
        """测试计算经过时间"""
        import time
        
        start_time = time.time()
        result = time_elapsed(start_time)
        assert "秒" in result


class TestDataValidation:
    """数据验证测试"""
    
    def test_is_valid_stock_code(self):
        """测试股票代码验证"""
        # 有效代码
        assert is_valid_stock_code("000001")
        assert is_valid_stock_code("600519")
        
        # 无效代码
        assert not is_valid_stock_code("00001")   # 长度不对
        assert not is_valid_stock_code("12345a")  # 包含字母
        assert not is_valid_stock_code(None)      # None
    
    def test_is_valid_email(self):
        """测试邮箱验证"""
        # 有效邮箱
        assert is_valid_email("<EMAIL>")
        assert is_valid_email("<EMAIL>")
        
        # 无效邮箱
        assert not is_valid_email("invalid.email")
        assert not is_valid_email("@domain.com")
        assert not is_valid_email("user@")
    
    def test_is_valid_phone(self):
        """测试手机号验证"""
        # 有效手机号
        assert is_valid_phone("13812345678")
        assert is_valid_phone("15987654321")
        
        # 无效手机号
        assert not is_valid_phone("12812345678")  # 不是1开头
        assert not is_valid_phone("1381234567")   # 长度不对
        assert not is_valid_phone("138123456789") # 长度不对
    
    def test_is_valid_url(self):
        """测试URL验证"""
        # 有效URL
        assert is_valid_url("http://example.com")
        assert is_valid_url("https://www.example.com/path")
        
        # 无效URL
        assert not is_valid_url("ftp://example.com")  # 不是http(s)
        assert not is_valid_url("not a url")
        assert not is_valid_url("http://")
    
    def test_validate_numeric_range(self):
        """测试数值范围验证"""
        # 在范围内
        assert validate_numeric_range(5, 0, 10)
        assert validate_numeric_range(0, 0, 10)  # 边界值
        assert validate_numeric_range(10, 0, 10) # 边界值
        
        # 超出范围
        assert not validate_numeric_range(-1, 0, 10)
        assert not validate_numeric_range(11, 0, 10)
        
        # 仅最小值
        assert validate_numeric_range(5, min_val=0)
        assert not validate_numeric_range(-1, min_val=0)
        
        # 仅最大值
        assert validate_numeric_range(5, max_val=10)
        assert not validate_numeric_range(11, max_val=10)


class TestDataFormatting:
    """数据格式化测试"""
    
    def test_format_number(self):
        """测试数字格式化"""
        # 默认精度
        assert format_number(1234.567) == "1,234.57"
        
        # 指定精度
        assert format_number(1234.567, precision=1) == "1,234.6"
        assert format_number(1234.567, precision=0) == "1,235"
        
        # 无千位分隔符
        assert format_number(1234.567, thousands_separator="") == "1234.57"
        
        # 自定义分隔符
        assert format_number(1234.567, thousands_separator=" ") == "1 234.57"
    
    def test_format_percentage(self):
        """测试百分比格式化"""
        assert format_percentage(0.1234) == "12.34%"
        assert format_percentage(0.1234, precision=1) == "12.3%"
        assert format_percentage(1.0) == "100.00%"
    
    def test_format_currency(self):
        """测试货币格式化"""
        assert format_currency(1234.56) == "¥1,234.56"
        assert format_currency(1234.56, currency="$") == "$1,234.56"
        assert format_currency(1234.56, precision=0) == "¥1,235"
    
    def test_format_file_size(self):
        """测试文件大小格式化"""
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(1024 * 1024) == "1.0 MB"
        assert format_file_size(1024 * 1024 * 1024) == "1.0 GB"
        assert format_file_size(500) == "500.0 B"
    
    def test_truncate_string(self):
        """测试字符串截断"""
        text = "这是一个很长的文本内容"
        
        # 不需要截断
        result = truncate_string(text, 20)
        assert result == text
        
        # 需要截断
        result = truncate_string(text, 10)
        assert len(result) == 10
        assert result.endswith("...")


class TestDataConversion:
    """数据转换测试"""
    
    def test_safe_int(self):
        """测试安全整数转换"""
        assert safe_int("123") == 123
        assert safe_int(123.45) == 123
        assert safe_int("invalid") == 0
        assert safe_int("invalid", default=999) == 999
        assert safe_int(None) == 0
    
    def test_safe_float(self):
        """测试安全浮点数转换"""
        assert safe_float("123.45") == 123.45
        assert safe_float(123) == 123.0
        assert safe_float("invalid") == 0.0
        assert safe_float("invalid", default=999.0) == 999.0
        assert safe_float(None) == 0.0
    
    def test_safe_str(self):
        """测试安全字符串转换"""
        assert safe_str(123) == "123"
        assert safe_str(123.45) == "123.45"
        assert safe_str(None) == ""
        assert safe_str(None, default="default") == "default"
    
    def test_dict_to_obj(self):
        """测试字典转对象"""
        data = {
            "name": "test",
            "value": 123,
            "nested": {
                "key": "value"
            }
        }
        
        obj = dict_to_obj(data)
        assert obj.name == "test"
        assert obj.value == 123
        assert obj.nested.key == "value"
    
    def test_obj_to_dict(self):
        """测试对象转字典"""
        class TestObj:
            def __init__(self):
                self.name = "test"
                self.value = 123
                self._private = "hidden"
        
        obj = TestObj()
        result = obj_to_dict(obj)
        
        assert result["name"] == "test"
        assert result["value"] == 123
        assert "_private" not in result


class TestFileHandling:
    """文件处理测试"""
    
    def test_ensure_dir_exists(self):
        """测试确保目录存在"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = Path(temp_dir) / "test" / "nested"
            
            # 目录不存在
            assert not test_dir.exists()
            
            # 创建目录
            result = ensure_dir_exists(test_dir)
            assert result is True
            assert test_dir.exists()
    
    def test_get_file_extension(self):
        """测试获取文件扩展名"""
        assert get_file_extension("test.txt") == "txt"
        assert get_file_extension("test.JSON") == "json"
        assert get_file_extension("test") == ""
        assert get_file_extension("test.tar.gz") == "gz"
    
    def test_is_file_older_than(self):
        """测试检查文件是否超时"""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_file = f.name
        
        try:
            # 新创建的文件不应该超时
            assert not is_file_older_than(temp_file, 1)
            
            # 不存在的文件应该返回True
            assert is_file_older_than("nonexistent.txt", 1)
            
        finally:
            os.unlink(temp_file)
    
    def test_json_file_operations(self):
        """测试JSON文件操作"""
        test_data = {"key": "value", "number": 123}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        try:
            # 写入JSON文件
            result = write_json_file(temp_file, test_data)
            assert result is True
            
            # 读取JSON文件
            loaded_data = read_json_file(temp_file)
            assert loaded_data == test_data
            
            # 读取不存在的文件
            result = read_json_file("nonexistent.json", default={})
            assert result == {}
            
        finally:
            os.unlink(temp_file)
    
    def test_csv_file_operations(self):
        """测试CSV文件操作"""
        test_data = [
            {"name": "Alice", "age": "30"},
            {"name": "Bob", "age": "25"}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            temp_file = f.name
        
        try:
            # 写入CSV文件
            result = write_csv_file(temp_file, test_data)
            assert result is True
            
            # 读取CSV文件
            loaded_data = read_csv_file(temp_file)
            assert len(loaded_data) == 2
            assert loaded_data[0]["name"] == "Alice"
            assert loaded_data[1]["name"] == "Bob"
            
        finally:
            os.unlink(temp_file)


class TestHashAndEncryption:
    """加密和哈希测试"""
    
    def test_generate_uuid(self):
        """测试UUID生成"""
        uuid1 = generate_uuid()
        uuid2 = generate_uuid()
        
        assert len(uuid1) == 36  # UUID格式长度
        assert uuid1 != uuid2    # 应该不同
        assert "-" in uuid1      # 应该包含连字符
    
    def test_generate_short_id(self):
        """测试短ID生成"""
        id1 = generate_short_id()
        id2 = generate_short_id()
        
        assert len(id1) == 8     # 默认长度
        assert id1 != id2        # 应该不同
        assert "-" not in id1    # 不应该包含连字符
        
        # 自定义长度
        custom_id = generate_short_id(length=12)
        assert len(custom_id) == 12
    
    def test_calculate_md5(self):
        """测试MD5计算"""
        text = "Hello, World!"
        md5_hash = calculate_md5(text)
        
        assert len(md5_hash) == 32
        assert md5_hash == calculate_md5(text)  # 相同输入应该产生相同输出
    
    def test_calculate_sha256(self):
        """测试SHA256计算"""
        text = "Hello, World!"
        sha256_hash = calculate_sha256(text)
        
        assert len(sha256_hash) == 64
        assert sha256_hash == calculate_sha256(text)  # 相同输入应该产生相同输出
    
    def test_calculate_file_hash(self):
        """测试文件哈希计算"""
        content = "Hello, World!"
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(content)
            temp_file = f.name
        
        try:
            # MD5
            md5_hash = calculate_file_hash(temp_file, 'md5')
            assert len(md5_hash) == 32
            
            # SHA256
            sha256_hash = calculate_file_hash(temp_file, 'sha256')
            assert len(sha256_hash) == 64
            
            # 不存在的文件
            result = calculate_file_hash("nonexistent.txt")
            assert result is None
            
        finally:
            os.unlink(temp_file)


class TestCacheAndPerformance:
    """缓存和性能测试"""
    
    def test_memoize(self):
        """测试函数结果缓存"""
        call_count = 0
        
        @memoize
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # 第一次调用
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1
        
        # 第二次调用相同参数，应该使用缓存
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 1  # 没有增加
        
        # 不同参数
        result3 = expensive_function(3)
        assert result3 == 6
        assert call_count == 2
        
        # 清除缓存
        expensive_function.cache_clear()
        result4 = expensive_function(5)
        assert result4 == 10
        assert call_count == 3  # 重新计算
    
    def test_timing(self):
        """测试函数计时装饰器"""
        @timing
        def test_function():
            import time
            time.sleep(0.01)  # 睡眠10毫秒
            return "done"
        
        result = test_function()
        assert result == "done"
    
    def test_batch_process(self):
        """测试批量处理"""
        def sum_batch(batch):
            return [sum(batch)]
        
        items = list(range(10))  # [0, 1, 2, ..., 9]
        results = batch_process(items, batch_size=3, process_func=sum_batch)
        
        # 应该有4个批次: [0,1,2], [3,4,5], [6,7,8], [9]
        assert len(results) == 4
        assert results[0] == 3   # 0+1+2
        assert results[1] == 12  # 3+4+5
        assert results[2] == 21  # 6+7+8
        assert results[3] == 9   # 9


class TestOtherUtilities:
    """其他工具测试"""
    
    def test_flatten_dict(self):
        """测试展平嵌套字典"""
        nested_dict = {
            "a": 1,
            "b": {
                "c": 2,
                "d": {
                    "e": 3
                }
            }
        }
        
        result = flatten_dict(nested_dict)
        
        assert result["a"] == 1
        assert result["b.c"] == 2
        assert result["b.d.e"] == 3
    
    def test_get_nested_value(self):
        """测试获取嵌套字典值"""
        data = {
            "a": {
                "b": {
                    "c": "value"
                }
            }
        }
        
        # 存在的路径
        assert get_nested_value(data, "a.b.c") == "value"
        
        # 不存在的路径
        assert get_nested_value(data, "a.b.x") is None
        assert get_nested_value(data, "a.b.x", default="default") == "default"
    
    def test_chunk_list(self):
        """测试列表分块"""
        items = list(range(10))  # [0, 1, 2, ..., 9]
        
        chunks = chunk_list(items, 3)
        
        assert len(chunks) == 4
        assert chunks[0] == [0, 1, 2]
        assert chunks[1] == [3, 4, 5]
        assert chunks[2] == [6, 7, 8]
        assert chunks[3] == [9]
    
    def test_remove_duplicates(self):
        """测试去重"""
        # 简单列表
        items = [1, 2, 2, 3, 3, 3, 4]
        result = remove_duplicates(items)
        assert result == [1, 2, 3, 4]
        
        # 使用键函数
        items = [{"id": 1, "name": "A"}, {"id": 2, "name": "B"}, {"id": 1, "name": "C"}]
        result = remove_duplicates(items, key_func=lambda x: x["id"])
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[1]["id"] == 2
    
    def test_merge_dicts(self):
        """测试字典合并"""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"c": 3, "d": 4}
        dict3 = {"b": 5, "e": 6}  # b会被覆盖
        
        result = merge_dicts(dict1, dict2, dict3)
        
        assert result["a"] == 1
        assert result["b"] == 5  # 被覆盖
        assert result["c"] == 3
        assert result["d"] == 4
        assert result["e"] == 6
    
    def test_retry(self):
        """测试重试装饰器"""
        attempt_count = 0
        
        @retry(max_attempts=3, delay=0.01)
        def failing_function():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise ValueError("Temporary failure")
            return "success"
        
        result = failing_function()
        assert result == "success"
        assert attempt_count == 3
        
        # 测试所有尝试都失败的情况
        attempt_count = 0
        
        @retry(max_attempts=2, delay=0.01)
        def always_failing_function():
            nonlocal attempt_count
            attempt_count += 1
            raise ValueError("Always fails")
        
        with pytest.raises(ValueError):
            always_failing_function()
        
        assert attempt_count == 2


if __name__ == "__main__":
    pytest.main([__file__])