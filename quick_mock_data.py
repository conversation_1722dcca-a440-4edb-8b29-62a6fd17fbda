#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速生成少量模拟历史数据

为前100只股票生成30天的模拟历史K线数据，用于快速测试
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.database_manager import DatabaseManager
from src.utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


def generate_quick_history(stock_code, days=30, base_price=10.0):
    """快速生成历史数据"""
    try:
        # 生成最近30个交易日
        end_date = datetime.now()
        dates = []
        current_date = end_date
        
        while len(dates) < days:
            if current_date.weekday() < 5:  # 工作日
                dates.append(current_date)
            current_date -= timedelta(days=1)
        
        dates.reverse()  # 按时间顺序
        
        # 生成价格数据
        prices = []
        current_price = base_price
        
        for i, date in enumerate(dates):
            # 简单的随机波动
            daily_change = np.random.normal(0, 0.02)  # 2%波动
            current_price *= (1 + daily_change)
            current_price = max(current_price, 1.0)
            
            # OHLC
            open_price = current_price * (1 + np.random.normal(0, 0.01))
            close_price = current_price * (1 + np.random.normal(0, 0.01))
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.01)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.01)))
            
            volume = random.randint(100000, 500000)
            amount = volume * (open_price + close_price) / 2
            
            prices.append({
                'trade_date': date.strftime('%Y-%m-%d'),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(amount, 2),
                'change': round(close_price - open_price, 2),
                'change_pct': round((close_price - open_price) / open_price * 100, 2)
            })
            
            current_price = close_price
        
        return prices
        
    except Exception as e:
        logger.error(f"生成 {stock_code} 快速历史数据失败: {e}")
        return []


def main():
    """主函数"""
    try:
        print("🚀 快速生成模拟历史数据（前100只股票，30天数据）...")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 获取前100只股票
        print("📋 获取股票列表...")
        all_stocks = db_manager.get_all_stocks()
        
        if not all_stocks:
            print("❌ 数据库中没有股票数据")
            return False
        
        # 只处理前100只股票
        stocks = all_stocks[:100]
        print(f"✅ 将为前 {len(stocks)} 只股票生成历史数据")
        
        success_count = 0
        error_count = 0
        
        for i, stock in enumerate(stocks):
            stock_code = stock.get('stock_code', '')
            
            print(f"\r📊 进度: {i+1}/{len(stocks)} ({(i+1)/len(stocks)*100:.1f}%) - {stock_code}", end='', flush=True)
            
            try:
                # 检查是否已有数据
                existing_count = db_manager.get_stock_quote_count(stock_code)
                if existing_count > 0:
                    continue
                
                # 生成基础价格
                code_num = int(''.join(filter(str.isdigit, stock_code[:6])))
                base_price = 5 + (code_num % 50) * 0.5  # 5-30元
                
                # 生成历史数据
                history_data = generate_quick_history(stock_code, days=30, base_price=base_price)
                
                if history_data:
                    # 批量插入
                    if db_manager.insert_stock_quotes_batch(stock_code, history_data):
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    error_count += 1
                
            except Exception as e:
                logger.error(f"处理股票 {stock_code} 失败: {e}")
                error_count += 1
        
        print(f"\n{'='*60}")
        print(f"🎉 快速历史数据生成完成！")
        print(f"   - 处理股票: {len(stocks)}")
        print(f"   - 成功生成: {success_count}")
        print(f"   - 失败数量: {error_count}")
        print(f"   - 成功率: {success_count/len(stocks)*100:.1f}%")
        
        # 检查数据库状态
        stats = db_manager.get_database_stats()
        print(f"\n📊 数据库统计:")
        print(f"   - 股票数量: {stats.get('total_stocks', 0):,}")
        print(f"   - 行情记录: {stats.get('total_quotes', 0):,}")
        print(f"   - 数据库大小: {stats.get('db_size_mb', 0):.2f} MB")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 快速生成历史数据失败: {e}")
        logger.error(f"快速生成历史数据失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 快速模拟历史数据生成完成")
        sys.exit(0)
    else:
        print("\n❌ 快速模拟历史数据生成失败")
        sys.exit(1)
