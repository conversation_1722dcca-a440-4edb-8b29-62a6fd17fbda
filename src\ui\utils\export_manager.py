#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出管理器
处理筛选结果的导出和报告生成
"""

import os
import json
import csv
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import pandas as pd
from PyQt6.QtWidgets import (
    QFileDialog, QWidget, QProgressDialog, QMessageBox
)
from PyQt6.QtCore import QObject, pyqtSignal, QThread

from ...utils.logger import get_logger

logger = get_logger(__name__)


class ExportWorker(QThread):
    """导出工作线程"""
    
    progress_updated = pyqtSignal(int)  # 进度更新
    export_completed = pyqtSignal(str)  # 导出完成
    export_failed = pyqtSignal(str)     # 导出失败
    
    def __init__(self, data: Dict[str, Any], file_path: str, export_type: str):
        super().__init__()
        self.data = data
        self.file_path = file_path
        self.export_type = export_type
    
    def run(self):
        """执行导出"""
        try:
            if self.export_type == "excel":
                self._export_to_excel()
            elif self.export_type == "csv":
                self._export_to_csv()
            elif self.export_type == "json":
                self._export_to_json()
            elif self.export_type == "report":
                self._export_to_report()
            else:
                raise ValueError(f"不支持的导出类型: {self.export_type}")
            
            self.export_completed.emit(self.file_path)
            
        except Exception as e:
            logger.error(f"导出失败: {e}")
            self.export_failed.emit(str(e))
    
    def _export_to_excel(self):
        """导出到Excel"""
        with pd.ExcelWriter(self.file_path, engine='openpyxl') as writer:
            # 导出板块筛选结果
            if 'sector_results' in self.data:
                sector_df = pd.DataFrame(self.data['sector_results'])
                sector_df.to_excel(writer, sheet_name='板块筛选结果', index=False)
                self.progress_updated.emit(30)
            
            # 导出个股筛选结果
            if 'stock_results' in self.data:
                stock_df = pd.DataFrame(self.data['stock_results'])
                stock_df.to_excel(writer, sheet_name='个股筛选结果', index=False)
                self.progress_updated.emit(60)
            
            # 导出筛选参数
            if 'parameters' in self.data:
                params_df = pd.DataFrame([self.data['parameters']])
                params_df.to_excel(writer, sheet_name='筛选参数', index=False)
                self.progress_updated.emit(80)
            
            # 导出统计信息
            if 'statistics' in self.data:
                stats_df = pd.DataFrame([self.data['statistics']])
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                self.progress_updated.emit(100)
    
    def _export_to_csv(self):
        """导出到CSV"""
        # 主要导出最终结果
        if 'stock_results' in self.data:
            df = pd.DataFrame(self.data['stock_results'])
            df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
        elif 'sector_results' in self.data:
            df = pd.DataFrame(self.data['sector_results'])
            df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
        
        self.progress_updated.emit(100)
    
    def _export_to_json(self):
        """导出到JSON"""
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, ensure_ascii=False, indent=2, default=str)
        
        self.progress_updated.emit(100)
    
    def _export_to_report(self):
        """导出到HTML报告"""
        html_content = self._generate_html_report()
        
        with open(self.file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.progress_updated.emit(100)
    
    def _generate_html_report(self) -> str:
        """生成HTML报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>威科夫相对强弱选股报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }}
        .section {{ background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .stats {{ display: flex; justify-content: space-around; flex-wrap: wrap; }}
        .stat-item {{ text-align: center; margin: 10px; }}
        .stat-value {{ font-size: 2em; font-weight: bold; color: #667eea; }}
        .stat-label {{ color: #666; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; font-weight: bold; }}
        .positive {{ color: #27ae60; }}
        .negative {{ color: #e74c3c; }}
        .footer {{ text-align: center; color: #666; margin-top: 30px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 威科夫相对强弱选股报告</h1>
        <p>生成时间: {timestamp}</p>
    </div>
"""
        
        # 添加统计信息
        if 'statistics' in self.data:
            stats = self.data['statistics']
            html += f"""
    <div class="section">
        <h2>📊 筛选统计</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">{stats.get('sector_count', 0)}</div>
                <div class="stat-label">强势板块</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats.get('candidate_count', 0)}</div>
                <div class="stat-label">候选股票</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats.get('final_count', 0)}</div>
                <div class="stat-label">最终选股</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{stats.get('success_rate', '0%')}</div>
                <div class="stat-label">成功率</div>
            </div>
        </div>
    </div>
"""
        
        # 添加板块筛选结果
        if 'sector_results' in self.data and self.data['sector_results']:
            html += """
    <div class="section">
        <h2>📈 强势板块筛选结果</h2>
        <table>
            <tr>
                <th>排名</th>
                <th>板块代码</th>
                <th>板块名称</th>
                <th>板块类型</th>
                <th>板块涨幅(%)</th>
                <th>相对强弱度(%)</th>
                <th>百分位排名</th>
            </tr>
"""
            for sector in self.data['sector_results'][:10]:  # 显示前10个
                rs_class = "positive" if sector.get('relative_strength', 0) > 0 else "negative"
                html += f"""
            <tr>
                <td>{sector.get('rank', '')}</td>
                <td>{sector.get('code', '')}</td>
                <td>{sector.get('name', '')}</td>
                <td>{sector.get('type', '')}</td>
                <td class="{rs_class}">{sector.get('return', 0):.2f}</td>
                <td class="{rs_class}">{sector.get('relative_strength', 0):.2f}</td>
                <td>{sector.get('percentile', 0):.1f}</td>
            </tr>
"""
            html += """
        </table>
    </div>
"""
        
        # 添加个股筛选结果
        if 'stock_results' in self.data and self.data['stock_results']:
            html += """
    <div class="section">
        <h2>🎯 最终选股结果</h2>
        <table>
            <tr>
                <th>排名</th>
                <th>股票代码</th>
                <th>股票名称</th>
                <th>所属板块</th>
                <th>威科夫评分</th>
                <th>相对强弱度</th>
                <th>综合评分</th>
                <th>推荐度</th>
            </tr>
"""
            for stock in self.data['stock_results']:
                score_class = "positive" if stock.get('composite_score', 0) >= 70 else "negative"
                html += f"""
            <tr>
                <td>{stock.get('rank', '')}</td>
                <td>{stock.get('code', '')}</td>
                <td>{stock.get('name', '')}</td>
                <td>{stock.get('sector', '')}</td>
                <td>{stock.get('wyckoff_score', 0)}</td>
                <td class="{score_class}">{stock.get('relative_strength', 0):.2f}</td>
                <td class="{score_class}">{stock.get('composite_score', 0):.1f}</td>
                <td>{stock.get('recommendation', '')}</td>
            </tr>
"""
            html += """
        </table>
    </div>
"""
        
        # 添加筛选参数
        if 'parameters' in self.data:
            params = self.data['parameters']
            html += f"""
    <div class="section">
        <h2>⚙️ 筛选参数</h2>
        <table>
            <tr><td>时间范围</td><td>{params.get('start_date', '')} 至 {params.get('end_date', '')}</td></tr>
            <tr><td>市场指数</td><td>{params.get('market_index', '')}</td></tr>
            <tr><td>最大板块数</td><td>{params.get('max_sectors', '')}</td></tr>
            <tr><td>最小相对强弱度</td><td>{params.get('min_relative_strength', '')}%</td></tr>
            <tr><td>排序方式</td><td>{params.get('sort_by', '')} ({params.get('sort_order', '')})</td></tr>
        </table>
    </div>
"""
        
        html += """
    <div class="footer">
        <p>本报告由威科夫相对强弱选股系统自动生成</p>
        <p>⚠️ 投资有风险，决策需谨慎。本报告仅供参考，不构成投资建议。</p>
    </div>
</body>
</html>
"""
        
        return html


class ExportManager(QObject):
    """导出管理器"""
    
    export_started = pyqtSignal()
    export_progress = pyqtSignal(int)
    export_completed = pyqtSignal(str)
    export_failed = pyqtSignal(str)
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.parent_widget = parent
        self.export_worker: Optional[ExportWorker] = None
        self.progress_dialog: Optional[QProgressDialog] = None
        
        logger.info("导出管理器初始化完成")
    
    def export_results(self, data: Dict[str, Any], export_type: str = "excel") -> bool:
        """
        导出结果
        
        Args:
            data: 要导出的数据
            export_type: 导出类型 (excel, csv, json, report)
            
        Returns:
            bool: 是否成功启动导出
        """
        try:
            # 选择保存文件
            file_path = self._get_save_file_path(export_type)
            if not file_path:
                return False
            
            # 创建导出工作线程
            self.export_worker = ExportWorker(data, file_path, export_type)
            self.export_worker.progress_updated.connect(self._on_progress_updated)
            self.export_worker.export_completed.connect(self._on_export_completed)
            self.export_worker.export_failed.connect(self._on_export_failed)
            
            # 显示进度对话框
            self._show_progress_dialog()
            
            # 启动导出
            self.export_started.emit()
            self.export_worker.start()
            
            logger.info(f"开始导出: {export_type} -> {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出启动失败: {e}")
            self.export_failed.emit(str(e))
            return False
    
    def _get_save_file_path(self, export_type: str) -> Optional[str]:
        """获取保存文件路径"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if export_type == "excel":
            file_filter = "Excel文件 (*.xlsx)"
            default_name = f"选股结果_{timestamp}.xlsx"
        elif export_type == "csv":
            file_filter = "CSV文件 (*.csv)"
            default_name = f"选股结果_{timestamp}.csv"
        elif export_type == "json":
            file_filter = "JSON文件 (*.json)"
            default_name = f"选股结果_{timestamp}.json"
        elif export_type == "report":
            file_filter = "HTML报告 (*.html)"
            default_name = f"选股报告_{timestamp}.html"
        else:
            return None
        
        file_path, _ = QFileDialog.getSaveFileName(
            self.parent_widget,
            f"导出{export_type.upper()}文件",
            default_name,
            file_filter
        )
        
        return file_path if file_path else None
    
    def _show_progress_dialog(self):
        """显示进度对话框"""
        self.progress_dialog = QProgressDialog(
            "正在导出数据...", "取消", 0, 100, self.parent_widget
        )
        self.progress_dialog.setWindowTitle("导出进度")
        self.progress_dialog.setModal(True)
        self.progress_dialog.canceled.connect(self._cancel_export)
        self.progress_dialog.show()
    
    def _on_progress_updated(self, progress: int):
        """进度更新"""
        if self.progress_dialog:
            self.progress_dialog.setValue(progress)
        self.export_progress.emit(progress)
    
    def _on_export_completed(self, file_path: str):
        """导出完成"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        # 显示成功消息
        QMessageBox.information(
            self.parent_widget,
            "导出成功",
            f"数据已成功导出到:\n{file_path}\n\n是否打开文件所在位置？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        self.export_completed.emit(file_path)
        logger.info(f"导出完成: {file_path}")
    
    def _on_export_failed(self, error_message: str):
        """导出失败"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        QMessageBox.critical(
            self.parent_widget,
            "导出失败",
            f"导出过程中发生错误:\n{error_message}"
        )
        
        self.export_failed.emit(error_message)
        logger.error(f"导出失败: {error_message}")
    
    def _cancel_export(self):
        """取消导出"""
        if self.export_worker and self.export_worker.isRunning():
            self.export_worker.terminate()
            self.export_worker.wait()
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        logger.info("用户取消导出")
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        return ["excel", "csv", "json", "report"]
