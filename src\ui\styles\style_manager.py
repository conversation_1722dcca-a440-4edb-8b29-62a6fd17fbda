"""
样式管理器

管理应用程序的样式和主题，提供现代化的UI外观
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QObject
from PyQt6.QtGui import QPalette, QColor, QFont

from ...utils.logger import get_logger

logger = get_logger(__name__)


class StyleManager(QObject):
    """样式管理器"""
    
    def __init__(self):
        """初始化样式管理器"""
        super().__init__()

        # 主题配置 - 默认使用明亮主题
        self.current_theme = "modern_light"
        self.themes = self._load_themes()

        logger.info("样式管理器初始化完成")
    
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载主题配置"""
        return {
            "modern_dark": {
                "name": "现代深色",
                "colors": {
                    "primary": "#2196F3",
                    "secondary": "#FFC107",
                    "success": "#4CAF50",
                    "warning": "#FF9800",
                    "error": "#F44336",
                    "background": "#1E1E1E",
                    "surface": "#2D2D2D",
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#B0B0B0",
                    "border": "#404040"
                },
                "fonts": {
                    "default": "Microsoft YaHei UI",
                    "monospace": "Consolas"
                }
            },
            "modern_light": {
                "name": "现代明亮",
                "colors": {
                    "primary": "#2196F3",        # 明亮蓝色
                    "secondary": "#FF9800",      # 橙色
                    "success": "#4CAF50",        # 绿色
                    "warning": "#FFC107",        # 黄色
                    "error": "#F44336",          # 红色
                    "background": "#F8F9FA",     # 浅灰背景
                    "surface": "#FFFFFF",        # 白色表面
                    "surface_variant": "#F5F5F5", # 变体表面
                    "text_primary": "#1A1A1A",   # 深色主文本
                    "text_secondary": "#666666",  # 灰色副文本
                    "text_hint": "#999999",      # 提示文本
                    "border": "#E1E5E9",         # 边框颜色
                    "border_light": "#F0F0F0",   # 浅边框
                    "hover": "#E3F2FD",          # 悬停背景
                    "selected": "#BBDEFB",       # 选中背景
                    "disabled": "#BDBDBD"        # 禁用颜色
                },
                "fonts": {
                    "default": "Microsoft YaHei UI",
                    "monospace": "Consolas",
                    "title": "Microsoft YaHei UI"
                }
            }
        }
    
    def get_main_window_style(self) -> str:
        """获取主窗口样式"""
        theme = self.themes[self.current_theme]
        colors = theme["colors"]

        return f"""
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            font-family: '{theme['fonts']['default']}';
            font-size: 9pt;
        }}

        QMenuBar {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-bottom: 1px solid {colors['border']};
            padding: 4px 8px;
            font-weight: 500;
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 6px 12px;
            margin: 2px;
            border-radius: 6px;
            font-weight: 500;
        }}

        QMenuBar::item:selected {{
            background-color: {colors['hover']};
            color: {colors['primary']};
        }}

        QMenuBar::item:pressed {{
            background-color: {colors['selected']};
            color: {colors['primary']};
        }}
        
        QMenu {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 4px;
        }}
        
        QMenu::item {{
            padding: 6px 20px;
            border-radius: 4px;
        }}
        
        QMenu::item:selected {{
            background-color: {colors['hover']};
            color: {colors['primary']};
        }}
        
        QToolBar {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 2px;
            spacing: 2px;
        }}
        
        QToolBar QToolButton {{
            background-color: transparent;
            border: none;
            padding: 6px;
            margin: 2px;
            border-radius: 4px;
            min-width: 60px;
        }}
        
        QToolBar QToolButton:hover {{
            background-color: {colors['hover']};
            color: {colors['primary']};
            border: 1px solid {colors['primary']};
        }}

        QToolBar QToolButton:pressed {{
            background-color: {colors['selected']};
            color: {colors['primary']};
            border: 1px solid {colors['primary']};
        }}
        
        QStatusBar {{
            background-color: {colors['surface']};
            color: {colors['text_secondary']};
            border-top: 1px solid {colors['border']};
            padding: 2px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            background-color: {colors['surface']};
            border-radius: 6px;
        }}
        
        QTabBar::tab {{
            background-color: {colors['background']};
            color: {colors['text_secondary']};
            padding: 8px 16px;
            margin: 2px;
            border-radius: 6px 6px 0px 0px;
            min-width: 100px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-bottom: 2px solid {colors['primary']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
        }}
        
        QSplitter::handle {{
            background-color: {colors['border']};
            width: 2px;
            height: 2px;
        }}
        
        QSplitter::handle:hover {{
            background-color: {colors['primary']};
        }}
        
        QProgressBar {{
            border: 1px solid {colors['border']};
            border-radius: 4px;
            text-align: center;
            background-color: {colors['background']};
        }}
        
        QProgressBar::chunk {{
            background-color: {colors['primary']};
            border-radius: 3px;
        }}
        """
    
    def get_table_style(self) -> str:
        """获取表格样式"""
        theme = self.themes[self.current_theme]
        colors = theme["colors"]
        
        return f"""
        QTableWidget {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border_light']};
            border-radius: 8px;
            gridline-color: {colors['border_light']};
            selection-background-color: {colors['selected']};
            alternate-background-color: {colors['surface_variant']};
            font-size: 9pt;
        }}

        QTableWidget::item {{
            padding: 10px 8px;
            border: none;
            border-bottom: 1px solid {colors['border_light']};
        }}

        QTableWidget::item:selected {{
            background-color: {colors['selected']};
            color: {colors['primary']};
        }}

        QTableWidget::item:hover {{
            background-color: {colors['hover']};
        }}
        
        QHeaderView::section {{
            background-color: {colors['surface_variant']};
            color: {colors['text_primary']};
            padding: 12px 8px;
            border: none;
            border-bottom: 2px solid {colors['border']};
            font-weight: 600;
            font-size: 9pt;
        }}

        QHeaderView::section:hover {{
            background-color: {colors['hover']};
            color: {colors['primary']};
        }}
        """
    
    def get_button_style(self) -> str:
        """获取按钮样式"""
        theme = self.themes[self.current_theme]
        colors = theme["colors"]
        
        return f"""
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 9pt;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {self._lighten_color(colors['primary'])};
        }}
        
        QPushButton:pressed {{
            background-color: {self._darken_color(colors['primary'])};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['border']};
            color: {colors['text_secondary']};
        }}
        
        QPushButton.secondary {{
            background-color: {colors['secondary']};
        }}
        
        QPushButton.success {{
            background-color: {colors['success']};
        }}
        
        QPushButton.warning {{
            background-color: {colors['warning']};
        }}
        
        QPushButton.error {{
            background-color: {colors['error']};
        }}
        """
    
    def get_input_style(self) -> str:
        """获取输入控件样式"""
        theme = self.themes[self.current_theme]
        colors = theme["colors"]
        
        return f"""
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 6px;
            font-size: 9pt;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, 
        QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border: 2px solid {colors['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors['text_secondary']};
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            selection-background-color: {colors['primary']};
        }}
        """
    
    def apply_main_window_style(self, window: QWidget):
        """应用主窗口样式"""
        style = self.get_main_window_style()
        window.setStyleSheet(style)
        logger.debug("主窗口样式已应用")
    
    def apply_widget_style(self, widget: QWidget, style_type: str = "default"):
        """应用控件样式"""
        styles = {
            "table": self.get_table_style(),
            "button": self.get_button_style(),
            "input": self.get_input_style()
        }
        
        if style_type in styles:
            widget.setStyleSheet(styles[style_type])
            logger.debug(f"控件样式已应用：{style_type}")
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            logger.info(f"主题已切换：{theme_name}")
        else:
            logger.warning(f"未知主题：{theme_name}")
    
    def get_color(self, color_name: str) -> str:
        """获取主题颜色"""
        theme = self.themes[self.current_theme]
        return theme["colors"].get(color_name, "#000000")
    
    def _lighten_color(self, color: str, factor: float = 0.2) -> str:
        """使颜色变亮"""
        # 简化的颜色处理，实际应该使用更复杂的算法
        if color.startswith('#'):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = min(255, int(r + (255 - r) * factor))
        g = min(255, int(g + (255 - g) * factor))
        b = min(255, int(b + (255 - b) * factor))
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def _darken_color(self, color: str, factor: float = 0.2) -> str:
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]
        
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        
        return f"#{r:02x}{g:02x}{b:02x}"
