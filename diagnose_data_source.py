#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源诊断脚本
用于诊断和修复股票数据下载问题
"""

import sys
import os
import time
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger, setup_logger
from src.config.config_manager import ConfigManager

logger = get_logger(__name__)


def setup_logging():
    """设置日志"""
    setup_logger(
        name="diagnose",
        level="DEBUG",
        log_to_file=True,
        log_to_console=True
    )


def check_xtdata_availability():
    """检查XtData可用性"""
    print("=" * 60)
    print("1. 检查XtData可用性")
    print("=" * 60)
    
    try:
        import xtquant.xtdata as xtdata
        print("✅ xtquant库导入成功")
        
        # 测试基本功能
        try:
            # 尝试获取股票列表
            stock_list = xtdata.get_stock_list_in_sector("沪深A股")
            if stock_list and len(stock_list) > 0:
                print(f"✅ 成功获取股票列表，共 {len(stock_list)} 只股票")
                return True, stock_list[:5]  # 返回前5只股票用于测试
            else:
                print("❌ 获取股票列表失败或为空")
                return False, []
        except Exception as e:
            print(f"❌ XtData基本功能测试失败: {e}")
            return False, []
            
    except ImportError as e:
        print(f"❌ xtquant库导入失败: {e}")
        print("\n解决方案:")
        print("1. 安装xtquant库: pip install xtquant")
        print("2. 下载并安装MiniQMT客户端")
        print("3. 启动MiniQMT客户端")
        return False, []


def test_data_retrieval(test_symbols):
    """测试数据获取"""
    print("\n" + "=" * 60)
    print("2. 测试数据获取")
    print("=" * 60)
    
    if not test_symbols:
        print("❌ 没有可用的测试股票代码")
        return False
    
    try:
        import xtquant.xtdata as xtdata
        
        # 测试单只股票数据获取 - 使用确定有数据的股票
        test_symbol = '000001.SZ'  # 平安银行
        print(f"测试股票: {test_symbol}")
        
        # 设置日期范围 - 使用更长的时间范围确保有交易数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=60)).strftime('%Y%m%d')
        
        print(f"日期范围: {start_date} - {end_date}")
        
        # 获取历史数据
        data = xtdata.get_market_data_ex(
            stock_list=[test_symbol],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if data:
            print(f"✅ 成功获取数据，返回类型: {type(data)}")
            print(f"数据键: {list(data.keys())}")
            
            if test_symbol in data:
                symbol_data = data[test_symbol]
                print(f"股票 {test_symbol} 数据类型: {type(symbol_data)}")
                
                if hasattr(symbol_data, 'empty'):
                    if symbol_data.empty:
                        print(f"❌ 股票 {test_symbol} 数据为空")
                        return False
                    else:
                        print(f"✅ 股票 {test_symbol} 数据正常，行数: {len(symbol_data)}")
                        print("数据列:", list(symbol_data.columns))
                        print("数据样例:")
                        print(symbol_data.head(3))
                        return True
                else:
                    print(f"❌ 股票 {test_symbol} 数据格式异常")
                    return False
            else:
                print(f"❌ 返回数据中不包含股票 {test_symbol}")
                return False
        else:
            print("❌ 获取数据失败，返回None")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False


def check_configuration():
    """检查配置"""
    print("\n" + "=" * 60)
    print("3. 检查配置")
    print("=" * 60)
    
    try:
        config_manager = ConfigManager()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        config_data = config_manager.load_config("config", config_path)
        
        # 检查数据下载配置
        download_config = config_data.get('data_download', {})
        print(f"数据下载启用: {download_config.get('enabled', False)}")
        print(f"使用真实数据: {download_config.get('use_real_data', False)}")
        print(f"包含历史数据: {download_config.get('include_history', False)}")
        
        # 检查数据源配置
        data_sources = config_data.get('data_sources', {})
        xtdata_config = data_sources.get('xtdata', {})
        print(f"XtData启用: {xtdata_config.get('enabled', False)}")
        print(f"XtData配置: {xtdata_config.get('config', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False


def test_adapter_integration():
    """测试适配器集成"""
    print("\n" + "=" * 60)
    print("4. 测试适配器集成")
    print("=" * 60)
    
    try:
        from src.data_sources.xtdata_adapter import XtDataAdapter
        from src.data_sources.base import DataSourceConfig
        
        # 创建配置
        config = DataSourceConfig(
            name="test_xtdata",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                "host": "127.0.0.1",
                "port": 58610
            }
        )
        
        # 创建适配器
        adapter = XtDataAdapter(config)
        print("✅ 适配器创建成功")
        
        # 测试连接
        if adapter.connect():
            print("✅ 适配器连接成功")
            
            # 测试获取股票列表
            try:
                stock_list = adapter.get_stock_list()
                if stock_list and len(stock_list) > 0:
                    print(f"✅ 获取股票列表成功，共 {len(stock_list)} 只股票")
                    
                    # 测试获取市场数据 - 使用确定有数据的大盘股
                    test_symbols = ['000001.SZ', '600000.SH', '000002.SZ', '600036.SH', '000858.SZ', '600519.SH']
                    test_symbol = '000001.SZ'  # 平安银行，确定存在且有数据

                    print(f"测试获取 {test_symbol} 的市场数据...")

                    end_date = datetime.now().strftime('%Y-%m-%d')
                    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
                    
                    market_data = adapter.get_market_data(
                        symbol=test_symbol,
                        period='1d',
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if market_data and not market_data.data.empty:
                        print(f"✅ 获取市场数据成功，数据行数: {len(market_data.data)}")
                        return True
                    else:
                        print("❌ 获取市场数据失败或数据为空")
                        return False
                else:
                    print("❌ 获取股票列表失败")
                    return False
                    
            except Exception as e:
                print(f"❌ 适配器功能测试失败: {e}")
                return False
        else:
            print("❌ 适配器连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 适配器集成测试失败: {e}")
        return False


def suggest_solutions(test_results):
    """建议解决方案"""
    print("\n" + "=" * 60)
    print("5. 解决方案建议")
    print("=" * 60)
    
    xtdata_available, data_retrieval, config_ok, adapter_ok = test_results
    
    if not xtdata_available:
        print("🔧 XtData不可用的解决方案:")
        print("1. 安装xtquant库: pip install xtquant")
        print("2. 下载MiniQMT客户端并安装")
        print("3. 启动MiniQMT客户端")
        print("4. 确保防火墙允许端口58610通信")
        
    elif not data_retrieval:
        print("🔧 数据获取失败的解决方案:")
        print("1. 检查MiniQMT客户端是否正常运行")
        print("2. 检查网络连接")
        print("3. 重启MiniQMT客户端")
        print("4. 检查股票代码是否正确")
        
    elif not config_ok:
        print("🔧 配置问题的解决方案:")
        print("1. 检查config.yaml文件是否存在")
        print("2. 确认数据源配置正确")
        print("3. 启用真实数据模式")
        
    elif not adapter_ok:
        print("🔧 适配器问题的解决方案:")
        print("1. 检查适配器连接逻辑")
        print("2. 增加错误处理和重试机制")
        print("3. 检查日期格式和参数")
        
    else:
        print("✅ 所有测试通过！系统应该可以正常工作。")
        print("如果仍有问题，请检查:")
        print("1. 数据库连接")
        print("2. 并发访问限制")
        print("3. 系统资源使用情况")


def main():
    """主函数"""
    print("股票数据源诊断工具")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置日志
    setup_logging()
    
    # 执行诊断测试
    test_results = []
    
    # 1. 检查XtData可用性
    xtdata_available, test_symbols = check_xtdata_availability()
    test_results.append(xtdata_available)
    
    # 2. 测试数据获取
    data_retrieval = test_data_retrieval(test_symbols) if xtdata_available else False
    test_results.append(data_retrieval)
    
    # 3. 检查配置
    config_ok = check_configuration()
    test_results.append(config_ok)
    
    # 4. 测试适配器集成
    adapter_ok = test_adapter_integration() if xtdata_available else False
    test_results.append(adapter_ok)
    
    # 5. 建议解决方案
    suggest_solutions(test_results)
    
    # 总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    test_names = ["XtData可用性", "数据获取", "配置检查", "适配器集成"]
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    print(f"\n总体状态: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有诊断测试通过！")
        return 0
    else:
        print("⚠️  存在问题，请根据上述建议进行修复。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
