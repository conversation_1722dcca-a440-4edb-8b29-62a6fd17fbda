#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试必要库的安装情况
"""

import sys
import importlib
from typing import Dict, List, Tuple

def test_library_import(library_name: str, import_name: str = None) -> Tuple[bool, str]:
    """
    测试库的导入情况
    
    Args:
        library_name: 库的显示名称
        import_name: 实际导入的模块名（如果与显示名称不同）
    
    Returns:
        (是否成功, 版本信息或错误信息)
    """
    if import_name is None:
        import_name = library_name.lower()
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', '未知版本')
        return True, version
    except ImportError as e:
        return False, str(e)
    except Exception as e:
        return False, f"其他错误: {str(e)}"

def main():
    """主测试函数"""
    print("=" * 60)
    print("威科夫相对强弱选股系统 - 库安装测试")
    print("=" * 60)
    
    # 定义需要测试的库
    libraries_to_test = [
        # 核心GUI框架
        ("PyQt6", "PyQt6"),
        
        # 数据处理
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),
        
        # 数据可视化
        ("pyqtgraph", "pyqtgraph"),
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn"),
        
        # 科学计算
        ("scikit-learn", "sklearn"),
        
        # 数据源
        ("requests", "requests"),
        ("aiohttp", "aiohttp"),
        
        # 配置管理
        ("PyYAML", "yaml"),
        ("configparser", "configparser"),
        
        # 日志和监控
        ("loguru", "loguru"),
        ("psutil", "psutil"),
        
        # 工具库
        ("python-dateutil", "dateutil"),
        ("tqdm", "tqdm"),
        ("openpyxl", "openpyxl"),
        
        # XtData (可能未安装)
        ("xtquant", "xtquant"),
    ]
    
    success_count = 0
    total_count = len(libraries_to_test)
    
    print(f"\n正在测试 {total_count} 个库的安装情况...\n")
    
    for display_name, import_name in libraries_to_test:
        success, info = test_library_import(display_name, import_name)
        
        if success:
            print(f"✅ {display_name:<20} - 版本: {info}")
            success_count += 1
        else:
            print(f"❌ {display_name:<20} - 错误: {info}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_count} 个库安装成功")
    
    if success_count == total_count:
        print("🎉 所有库都已成功安装！")
    else:
        print(f"⚠️  还有 {total_count - success_count} 个库需要安装")
        
        # 特别提示 xtdata
        if not test_library_import("xtquant", "xtquant")[0]:
            print("\n📋 xtquant 安装说明:")
            print("   1. 访问: https://dict.thinktrader.net/nativeApi/xtdata.html")
            print("   2. 下载 xtquant 安装包")
            print("   3. 在虚拟环境中安装: pip install xtquant-x.x.x.whl")
    
    print("=" * 60)
    
    # 测试基础功能
    print("\n正在测试基础功能...")
    test_basic_functionality()

def test_basic_functionality():
    """测试基础功能"""
    try:
        # 测试 pandas
        import pandas as pd
        df = pd.DataFrame({'test': [1, 2, 3]})
        print("✅ pandas 基础功能正常")
        
        # 测试 numpy
        import numpy as np
        arr = np.array([1, 2, 3])
        print("✅ numpy 基础功能正常")
        
        # 测试 PyQt6
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6 导入正常")
        
        # 测试 matplotlib
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        print("✅ matplotlib 基础功能正常")
        
        print("\n🎉 所有基础功能测试通过！")
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")

if __name__ == "__main__":
    main()
