"""
主题管理器

实现明亮/深色主题切换功能
"""

from typing import Dict, Any, Optional
from enum import Enum
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QPalette, QColor
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ThemeType(Enum):
    """主题类型"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(str)  # 主题变化信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = ThemeType.LIGHT
        self.themes = self._init_themes()
        
    def _init_themes(self) -> Dict[str, Dict[str, Any]]:
        """初始化主题配置"""
        return {
            "light": {
                "name": "明亮主题",
                "colors": {
                    "primary": "#2196F3",
                    "primary_dark": "#1976D2",
                    "primary_light": "#BBDEFB",
                    "secondary": "#FF9800",
                    "background": "#FFFFFF",
                    "surface": "#F8F9FA",
                    "error": "#F44336",
                    "warning": "#FF9800",
                    "success": "#4CAF50",
                    "info": "#2196F3",
                    "text_primary": "#212121",
                    "text_secondary": "#757575",
                    "text_disabled": "#BDBDBD",
                    "divider": "#E0E0E0",
                    "border": "#E0E0E0"
                },
                "styles": {
                    "window_background": "#FFFFFF",
                    "widget_background": "#F8F9FA",
                    "button_background": "#2196F3",
                    "button_hover": "#1976D2",
                    "button_pressed": "#1565C0",
                    "input_background": "#FFFFFF",
                    "input_border": "#E0E0E0",
                    "input_focus": "#2196F3",
                    "menu_background": "#FFFFFF",
                    "menu_hover": "#F5F5F5",
                    "toolbar_background": "#F8F9FA",
                    "statusbar_background": "#F0F0F0"
                }
            },
            "dark": {
                "name": "深色主题",
                "colors": {
                    "primary": "#90CAF9",
                    "primary_dark": "#42A5F5",
                    "primary_light": "#E3F2FD",
                    "secondary": "#FFB74D",
                    "background": "#121212",
                    "surface": "#1E1E1E",
                    "error": "#CF6679",
                    "warning": "#FFB74D",
                    "success": "#81C784",
                    "info": "#90CAF9",
                    "text_primary": "#FFFFFF",
                    "text_secondary": "#B0B0B0",
                    "text_disabled": "#6C6C6C",
                    "divider": "#373737",
                    "border": "#373737"
                },
                "styles": {
                    "window_background": "#121212",
                    "widget_background": "#1E1E1E",
                    "button_background": "#90CAF9",
                    "button_hover": "#42A5F5",
                    "button_pressed": "#1E88E5",
                    "input_background": "#2C2C2C",
                    "input_border": "#373737",
                    "input_focus": "#90CAF9",
                    "menu_background": "#1E1E1E",
                    "menu_hover": "#373737",
                    "toolbar_background": "#1E1E1E",
                    "statusbar_background": "#2C2C2C"
                }
            }
        }
    
    def get_current_theme(self) -> ThemeType:
        """获取当前主题"""
        return self.current_theme
    
    def set_theme(self, theme_type: ThemeType):
        """设置主题"""
        if theme_type != self.current_theme:
            self.current_theme = theme_type
            self._apply_theme()
            self.theme_changed.emit(theme_type.value)
            logger.info(f"主题已切换到: {theme_type.value}")
    
    def _apply_theme(self):
        """应用主题"""
        theme_name = self.current_theme.value
        if theme_name in self.themes:
            theme_config = self.themes[theme_name]
            self._apply_qt_palette(theme_config)
            self._apply_stylesheet(theme_config)
    
    def _apply_qt_palette(self, theme_config: Dict[str, Any]):
        """应用Qt调色板"""
        app = QApplication.instance()
        if not app:
            return
            
        palette = QPalette()
        colors = theme_config["colors"]
        
        # 设置基本颜色
        palette.setColor(QPalette.ColorRole.Window, QColor(colors["background"]))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Base, QColor(colors["surface"]))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(colors["surface"]))
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(colors["surface"]))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Text, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Button, QColor(colors["surface"]))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(colors["error"]))
        palette.setColor(QPalette.ColorRole.Link, QColor(colors["primary"]))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(colors["primary"]))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(colors["background"]))
        
        app.setPalette(palette)
    
    def _apply_stylesheet(self, theme_config: Dict[str, Any]):
        """应用样式表"""
        app = QApplication.instance()
        if not app:
            return
            
        styles = theme_config["styles"]
        colors = theme_config["colors"]
        
        stylesheet = f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {styles["window_background"]};
            color: {colors["text_primary"]};
        }}
        
        /* 通用控件样式 */
        QWidget {{
            background-color: {styles["widget_background"]};
            color: {colors["text_primary"]};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {colors["primary"]};
            color: {colors["background"]};
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 80px;
        }}
        
        QPushButton:hover {{
            background-color: {colors["primary_dark"]};
        }}
        
        QPushButton:pressed {{
            background-color: {colors["primary_dark"]};
        }}
        
        QPushButton:disabled {{
            background-color: {colors["text_disabled"]};
            color: {colors["text_secondary"]};
        }}
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {styles["input_background"]};
            border: 1px solid {styles["input_border"]};
            border-radius: 4px;
            padding: 6px;
            color: {colors["text_primary"]};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {styles["input_focus"]};
        }}
        
        /* 列表和表格样式 */
        QListWidget, QTableWidget, QTreeWidget {{
            background-color: {styles["input_background"]};
            border: 1px solid {styles["input_border"]};
            border-radius: 4px;
            color: {colors["text_primary"]};
        }}
        
        QListWidget::item:selected, QTableWidget::item:selected, QTreeWidget::item:selected {{
            background-color: {colors["primary_light"]};
            color: {colors["text_primary"]};
        }}
        
        /* 菜单样式 */
        QMenuBar {{
            background-color: {styles["menu_background"]};
            color: {colors["text_primary"]};
            border-bottom: 1px solid {colors["divider"]};
        }}
        
        QMenuBar::item:selected {{
            background-color: {styles["menu_hover"]};
        }}
        
        QMenu {{
            background-color: {styles["menu_background"]};
            color: {colors["text_primary"]};
            border: 1px solid {colors["border"]};
        }}
        
        QMenu::item:selected {{
            background-color: {styles["menu_hover"]};
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {styles["toolbar_background"]};
            border: none;
            spacing: 3px;
        }}
        
        QToolBar::separator {{
            background-color: {colors["divider"]};
            width: 1px;
            margin: 4px;
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {styles["statusbar_background"]};
            color: {colors["text_primary"]};
            border-top: 1px solid {colors["divider"]};
        }}
        
        /* 标签页样式 */
        QTabWidget::pane {{
            border: 1px solid {colors["border"]};
            background-color: {styles["widget_background"]};
        }}
        
        QTabBar::tab {{
            background-color: {styles["widget_background"]};
            color: {colors["text_primary"]};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {styles["input_background"]};
            border-bottom: 2px solid {colors["primary"]};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {styles["widget_background"]};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors["text_disabled"]};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors["text_secondary"]};
        }}
        
        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {colors["divider"]};
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            border: 1px solid {colors["border"]};
            border-radius: 4px;
            text-align: center;
            background-color: {styles["widget_background"]};
        }}
        
        QProgressBar::chunk {{
            background-color: {colors["primary"]};
            border-radius: 3px;
        }}
        
        /* 组合框样式 */
        QComboBox {{
            background-color: {styles["input_background"]};
            border: 1px solid {styles["input_border"]};
            border-radius: 4px;
            padding: 6px;
            color: {colors["text_primary"]};
        }}
        
        QComboBox:focus {{
            border-color: {styles["input_focus"]};
        }}
        
        QComboBox::drop-down {{
            border: none;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors["text_secondary"]};
        }}
        """
        
        app.setStyleSheet(stylesheet)
    
    def get_theme_config(self, theme_type: ThemeType) -> Dict[str, Any]:
        """获取主题配置"""
        return self.themes.get(theme_type.value, {})
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {key: config["name"] for key, config in self.themes.items()}


# 全局主题管理器实例
theme_manager = ThemeManager()
