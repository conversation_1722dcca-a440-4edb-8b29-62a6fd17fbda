#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化数据源并获取股票和板块代码
"""

import sys
import os
import pandas as pd
from typing import List, Dict, Optional
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def try_import_xtdata():
    """尝试导入 xtdata，如果失败则使用 mock 版本"""
    try:
        import xtquant.xtdata as xt
        print("✅ 成功导入真实的 xtquant.xtdata")
        return xt, True
    except ImportError:
        try:
            from mock_xtdata import MockXtData
            xt = MockXtData()
            print("⚠️  xtquant 未安装，使用 mock 数据源")
            return xt, False
        except ImportError as e:
            print(f"❌ 无法导入数据源: {e}")
            return None, False

def initialize_xtdata_client(xt_module, is_real: bool) -> bool:
    """
    初始化 xtdata 客户端

    Args:
        xt_module: xtdata 模块或 mock 模块
        is_real: 是否为真实的 xtdata

    Returns:
        是否初始化成功
    """
    try:
        if is_real:
            # 真实的 xtdata 初始化
            result = xt_module.connect()
            # xtdata.connect() 成功时返回客户端对象，失败时返回错误码
            if hasattr(result, '__class__') and 'Client' in str(result.__class__):
                print("✅ xtdata 客户端连接成功")
                return True
            elif result == 0:
                print("✅ xtdata 客户端连接成功")
                return True
            else:
                print(f"❌ xtdata 客户端连接失败，错误代码: {result}")
                return False
        else:
            # Mock 版本总是返回成功
            print("✅ Mock xtdata 客户端初始化成功")
            return True
    except Exception as e:
        print(f"❌ 初始化 xtdata 客户端时出错: {e}")
        return False

def get_all_stock_codes(xt_module, is_real: bool) -> List[str]:
    """
    获取所有股票代码
    
    Args:
        xt_module: xtdata 模块或 mock 模块
        is_real: 是否为真实的 xtdata
    
    Returns:
        股票代码列表
    """
    try:
        if is_real:
            # 获取沪深A股
            sh_stocks = xt_module.get_stock_list_in_sector('沪深A股')
            return sh_stocks if sh_stocks else []
        else:
            # 使用 mock 数据
            return xt_module.get_stock_list_in_sector('沪深A股')
    except Exception as e:
        print(f"❌ 获取股票代码时出错: {e}")
        return []

def get_sector_codes(xt_module, is_real: bool) -> Dict[str, List[str]]:
    """
    获取行业板块和概念板块代码
    
    Args:
        xt_module: xtdata 模块或 mock 模块
        is_real: 是否为真实的 xtdata
    
    Returns:
        板块代码字典
    """
    sectors = {}
    
    try:
        if is_real:
            # 获取行业板块
            industry_sectors = xt_module.get_sector_list()
            sectors['行业板块'] = industry_sectors if industry_sectors else []
            
            # 获取概念板块
            concept_sectors = xt_module.get_sector_list()  # 需要根据实际API调整
            sectors['概念板块'] = concept_sectors if concept_sectors else []
        else:
            # 使用 mock 数据
            sectors['行业板块'] = xt_module.get_sector_list()
            sectors['概念板块'] = xt_module.get_sector_list()
            
    except Exception as e:
        print(f"❌ 获取板块代码时出错: {e}")
        sectors = {'行业板块': [], '概念板块': []}
    
    return sectors

def save_codes_to_files(stock_codes: List[str], sector_codes: Dict[str, List[str]]):
    """
    将代码保存到文件
    
    Args:
        stock_codes: 股票代码列表
        sector_codes: 板块代码字典
    """
    # 创建数据目录
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    try:
        # 保存股票代码
        stock_df = pd.DataFrame({'stock_code': stock_codes})
        stock_file = data_dir / "stock_codes.csv"
        stock_df.to_csv(stock_file, index=False, encoding='utf-8-sig')
        print(f"✅ 股票代码已保存到: {stock_file}")
        print(f"   共 {len(stock_codes)} 只股票")
        
        # 保存板块代码
        sector_file = data_dir / "sector_codes.json"
        with open(sector_file, 'w', encoding='utf-8') as f:
            json.dump(sector_codes, f, ensure_ascii=False, indent=2)
        print(f"✅ 板块代码已保存到: {sector_file}")
        
        for sector_type, codes in sector_codes.items():
            print(f"   {sector_type}: {len(codes)} 个")
            
        # 创建汇总信息
        summary = {
            "更新时间": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
            "股票数量": len(stock_codes),
            "板块统计": {sector_type: len(codes) for sector_type, codes in sector_codes.items()},
            "数据源": "xtquant" if len(stock_codes) > 100 else "mock"
        }
        
        summary_file = data_dir / "data_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print(f"✅ 数据汇总已保存到: {summary_file}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("威科夫相对强弱选股系统 - 数据源初始化")
    print("=" * 60)
    
    # 1. 导入 xtdata
    print("\n1. 导入数据源模块...")
    xt_module, is_real = try_import_xtdata()
    if xt_module is None:
        print("❌ 无法导入任何数据源模块，程序退出")
        return
    
    # 2. 初始化客户端
    print("\n2. 初始化数据源客户端...")
    if not initialize_xtdata_client(xt_module, is_real):
        print("❌ 客户端初始化失败，程序退出")
        return
    
    # 3. 获取股票代码
    print("\n3. 获取所有股票代码...")
    stock_codes = get_all_stock_codes(xt_module, is_real)
    print(f"✅ 获取到 {len(stock_codes)} 只股票")
    
    if stock_codes:
        print("   示例股票代码:")
        for i, code in enumerate(stock_codes[:5]):
            print(f"     {code}")
        if len(stock_codes) > 5:
            print(f"     ... 还有 {len(stock_codes) - 5} 只")
    
    # 4. 获取板块代码
    print("\n4. 获取行业板块和概念板块代码...")
    sector_codes = get_sector_codes(xt_module, is_real)
    
    for sector_type, codes in sector_codes.items():
        print(f"✅ {sector_type}: {len(codes)} 个")
        if codes:
            print("   示例板块:")
            for i, code in enumerate(codes[:3]):
                print(f"     {code}")
            if len(codes) > 3:
                print(f"     ... 还有 {len(codes) - 3} 个")
    
    # 5. 保存到文件
    print("\n5. 保存数据到文件...")
    save_codes_to_files(stock_codes, sector_codes)
    
    print("\n" + "=" * 60)
    print("🎉 数据源初始化完成！")
    print("\n下一步:")
    print("1. 如需使用真实数据，请安装 xtquant:")
    print("   - 访问: https://dict.thinktrader.net/nativeApi/xtdata.html")
    print("   - 下载并安装 xtquant")
    print("2. 运行主程序开始数据分析")
    print("=" * 60)

if __name__ == "__main__":
    main()
