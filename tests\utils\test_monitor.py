"""
系统监控模块测试
"""

import pytest
import sys
import os
import time
import threading
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.utils.monitor import (
    SystemInfo, ResourceUsage, ProcessInfo, AlertRule, AlertStatus,
    SystemMonitor, get_system_summary, check_system_health
)


class TestDataStructures:
    """数据结构测试"""
    
    def test_system_info(self):
        """测试系统信息数据结构"""
        info = SystemInfo(
            platform="Windows-10",
            hostname="test-host",
            cpu_count=8,
            memory_total=16,
            disk_total=500,
            python_version="3.8.0",
            boot_time=datetime.now()
        )
        
        assert info.platform == "Windows-10"
        assert info.cpu_count == 8
        assert info.memory_total == 16
        assert isinstance(info.boot_time, datetime)
    
    def test_resource_usage(self):
        """测试资源使用情况数据结构"""
        usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=70.0,
            memory_used=8000,
            memory_available=4000,
            disk_usage={"C:": 60.0},
            network_io={"bytes_sent": 1000, "bytes_recv": 2000},
            process_count=150
        )
        
        assert usage.cpu_percent == 50.0
        assert usage.memory_percent == 70.0
        assert "C:" in usage.disk_usage
        assert usage.process_count == 150
    
    def test_process_info(self):
        """测试进程信息数据结构"""
        process = ProcessInfo(
            pid=1234,
            name="test.exe",
            cpu_percent=5.0,
            memory_percent=2.0,
            memory_mb=100.0,
            status="running",
            create_time=datetime.now()
        )
        
        assert process.pid == 1234
        assert process.name == "test.exe"
        assert process.cpu_percent == 5.0
        assert process.status == "running"
    
    def test_alert_rule(self):
        """测试警报规则数据结构"""
        rule = AlertRule(
            name="High CPU",
            metric="cpu",
            threshold=80.0,
            comparison="gt",
            duration=60,
            enabled=True
        )
        
        assert rule.name == "High CPU"
        assert rule.metric == "cpu"
        assert rule.threshold == 80.0
        assert rule.comparison == "gt"
        assert rule.enabled is True


class TestSystemMonitor:
    """系统监控器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.monitor = SystemMonitor(history_size=10)
    
    def teardown_method(self):
        """测试后清理"""
        if self.monitor.monitoring:
            self.monitor.stop_monitoring()
    
    def test_initialization(self):
        """测试初始化"""
        assert self.monitor.history_size == 10
        assert len(self.monitor.history) == 0
        assert len(self.monitor.alert_rules) == 0
        assert not self.monitor.monitoring
        assert self.monitor.interval == 5.0
    
    @patch('src.utils.monitor.psutil')
    def test_get_system_info(self, mock_psutil):
        """测试获取系统信息"""
        # 模拟psutil数据
        mock_psutil.cpu_count.return_value = 8
        
        # 模拟内存信息
        mock_memory = MagicMock()
        mock_memory.total = 16 * 1024 ** 3  # 16GB
        mock_psutil.virtual_memory.return_value = mock_memory
        
        # 模拟磁盘分区
        mock_partition = MagicMock()
        mock_partition.mountpoint = "C:\\"
        mock_psutil.disk_partitions.return_value = [mock_partition]
        
        # 模拟磁盘使用
        mock_usage = MagicMock()
        mock_usage.total = 500 * 1024 ** 3  # 500GB
        mock_psutil.disk_usage.return_value = mock_usage
        
        # 模拟启动时间
        mock_psutil.boot_time.return_value = time.time() - 3600  # 1小时前
        
        # 执行测试
        system_info = self.monitor.get_system_info()
        
        assert isinstance(system_info, SystemInfo)
        assert system_info.cpu_count == 8
        assert system_info.memory_total == 16.0
        assert system_info.disk_total == 500.0
        assert isinstance(system_info.boot_time, datetime)
    
    @patch('src.utils.monitor.psutil')
    def test_collect_current_usage(self, mock_psutil):
        """测试收集当前资源使用情况"""
        # 模拟CPU使用率
        mock_psutil.cpu_percent.return_value = 45.5
        
        # 模拟内存信息
        mock_memory = MagicMock()
        mock_memory.total = 16 * 1024 ** 3
        mock_memory.available = 8 * 1024 ** 3
        mock_memory.percent = 50.0
        mock_psutil.virtual_memory.return_value = mock_memory
        
        # 模拟磁盘分区
        mock_partition = MagicMock()
        mock_partition.device = "C:"
        mock_partition.mountpoint = "C:\\"
        mock_psutil.disk_partitions.return_value = [mock_partition]
        
        # 模拟磁盘使用
        mock_usage = MagicMock()
        mock_usage.percent = 60.0
        mock_psutil.disk_usage.return_value = mock_usage
        
        # 模拟网络IO
        mock_network = MagicMock()
        mock_network.bytes_sent = 1000000
        mock_network.bytes_recv = 2000000
        mock_network.packets_sent = 1000
        mock_network.packets_recv = 2000
        mock_psutil.net_io_counters.return_value = mock_network
        
        # 模拟进程数量
        mock_psutil.pids.return_value = list(range(100))
        
        # 执行测试
        usage = self.monitor.collect_current_usage()
        
        assert isinstance(usage, ResourceUsage)
        assert usage.cpu_percent == 45.5
        assert usage.memory_percent == 50.0
        assert usage.memory_used == 8192.0  # 8GB in MB
        assert usage.memory_available == 8192.0  # 8GB in MB
        assert "C:" in usage.disk_usage
        assert usage.disk_usage["C:"] == 60.0
        assert usage.process_count == 100
    
    @patch('src.utils.monitor.psutil')
    def test_get_top_processes(self, mock_psutil):
        """测试获取资源使用最多的进程"""
        # 模拟进程信息
        mock_proc1 = MagicMock()
        mock_proc1.info = {
            'pid': 1234,
            'name': 'test1.exe',
            'cpu_percent': 10.0,
            'memory_percent': 5.0,
            'memory_info': MagicMock(rss=100 * 1024 * 1024),  # 100MB
            'status': 'running',
            'create_time': time.time() - 3600,
            'cmdline': ['test1.exe', '--arg']
        }
        
        mock_proc2 = MagicMock()
        mock_proc2.info = {
            'pid': 5678,
            'name': 'test2.exe',
            'cpu_percent': 20.0,
            'memory_percent': 10.0,
            'memory_info': MagicMock(rss=200 * 1024 * 1024),  # 200MB
            'status': 'running',
            'create_time': time.time() - 1800,
            'cmdline': ['test2.exe']
        }
        
        mock_psutil.process_iter.return_value = [mock_proc1, mock_proc2]
        
        # 执行测试
        processes = self.monitor.get_top_processes(count=2, sort_by='cpu')
        
        assert len(processes) == 2
        assert processes[0].pid == 5678  # CPU使用率更高的进程排在前面
        assert processes[0].cpu_percent == 20.0
        assert processes[1].pid == 1234
        assert processes[1].cpu_percent == 10.0
    
    def test_alert_rule_management(self):
        """测试警报规则管理"""
        # 添加警报规则
        rule = AlertRule(
            name="High CPU",
            metric="cpu",
            threshold=80.0,
            comparison="gt",
            duration=60
        )
        
        self.monitor.add_alert_rule(rule)
        
        assert "High CPU" in self.monitor.alert_rules
        assert "High CPU" in self.monitor.alert_status
        assert not self.monitor.alert_status["High CPU"].triggered
        
        # 移除警报规则
        self.monitor.remove_alert_rule("High CPU")
        
        assert "High CPU" not in self.monitor.alert_rules
        assert "High CPU" not in self.monitor.alert_status
    
    def test_check_alerts(self):
        """测试警报检查"""
        # 添加CPU警报规则
        cpu_rule = AlertRule(
            name="High CPU",
            metric="cpu",
            threshold=80.0,
            comparison="gt",
            duration=60
        )
        self.monitor.add_alert_rule(cpu_rule)
        
        # 模拟高CPU使用率
        high_cpu_usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=85.0,  # 超过阈值
            memory_percent=50.0,
            memory_used=8000,
            memory_available=8000,
            disk_usage={},
            network_io={},
            process_count=100
        )
        
        # 检查警报
        self.monitor.check_alerts(high_cpu_usage)
        
        # 验证警报被触发
        alert_status = self.monitor.alert_status["High CPU"]
        assert alert_status.triggered
        assert alert_status.current_value == 85.0
        assert alert_status.threshold == 80.0
        
        # 模拟CPU使用率降低
        normal_cpu_usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=50.0,  # 低于阈值
            memory_percent=50.0,
            memory_used=8000,
            memory_available=8000,
            disk_usage={},
            network_io={},
            process_count=100
        )
        
        # 再次检查警报
        self.monitor.check_alerts(normal_cpu_usage)
        
        # 验证警报被清除
        alert_status = self.monitor.alert_status["High CPU"]
        assert not alert_status.triggered
        assert alert_status.current_value == 50.0
    
    def test_get_metric_value(self):
        """测试获取指标值"""
        usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=70.0,
            memory_used=8000,
            memory_available=8000,
            disk_usage={"C:": 60.0, "D:": 40.0},
            network_io={"bytes_sent": 1000, "bytes_recv": 2000},
            process_count=100
        )
        
        # 测试不同指标
        assert self.monitor._get_metric_value(usage, "cpu") == 50.0
        assert self.monitor._get_metric_value(usage, "memory") == 70.0
        assert self.monitor._get_metric_value(usage, "disk:C:") == 60.0
        assert self.monitor._get_metric_value(usage, "disk:D:") == 40.0
        assert self.monitor._get_metric_value(usage, "network:bytes_sent") == 1000
        assert self.monitor._get_metric_value(usage, "invalid") is None
    
    def test_check_threshold(self):
        """测试阈值检查"""
        # 大于
        assert self.monitor._check_threshold(85.0, 80.0, "gt")
        assert not self.monitor._check_threshold(75.0, 80.0, "gt")
        
        # 大于等于
        assert self.monitor._check_threshold(80.0, 80.0, "gte")
        assert self.monitor._check_threshold(85.0, 80.0, "gte")
        assert not self.monitor._check_threshold(75.0, 80.0, "gte")
        
        # 小于
        assert self.monitor._check_threshold(75.0, 80.0, "lt")
        assert not self.monitor._check_threshold(85.0, 80.0, "lt")
        
        # 小于等于
        assert self.monitor._check_threshold(80.0, 80.0, "lte")
        assert self.monitor._check_threshold(75.0, 80.0, "lte")
        assert not self.monitor._check_threshold(85.0, 80.0, "lte")
    
    def test_callback_management(self):
        """测试回调函数管理"""
        callback_called = False
        
        def test_callback(usage):
            nonlocal callback_called
            callback_called = True
        
        # 添加回调
        self.monitor.add_callback('data_collected', test_callback)
        assert test_callback in self.monitor.callbacks['data_collected']
        
        # 移除回调
        self.monitor.remove_callback('data_collected', test_callback)
        assert test_callback not in self.monitor.callbacks['data_collected']
    
    def test_history_management(self):
        """测试历史数据管理"""
        # 添加一些历史数据
        now = datetime.now()
        for i in range(5):
            usage = ResourceUsage(
                timestamp=now - timedelta(minutes=i),
                cpu_percent=50.0 + i,
                memory_percent=60.0,
                memory_used=8000,
                memory_available=8000,
                disk_usage={},
                network_io={},
                process_count=100
            )
            self.monitor.history.append(usage)
        
        # 获取最近3分钟的历史数据
        recent_history = self.monitor.get_history(minutes=3)
        assert len(recent_history) == 4  # 包含当前时间
        
        # 获取统计信息
        stats = self.monitor.get_statistics(minutes=3)
        assert 'cpu' in stats
        assert 'memory' in stats
        assert stats['data_points'] == 4
    
    def test_export_data(self):
        """测试数据导出"""
        # 添加一些历史数据
        now = datetime.now()
        usage = ResourceUsage(
            timestamp=now,
            cpu_percent=50.0,
            memory_percent=60.0,
            memory_used=8000,
            memory_available=8000,
            disk_usage={"C:": 70.0},
            network_io={"bytes_sent": 1000, "bytes_recv": 2000},
            process_count=100
        )
        self.monitor.history.append(usage)
        
        # 测试JSON导出
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            json_file = f.name
        
        try:
            result = self.monitor.export_data(json_file, format='json', minutes=60)
            assert result is True
            assert os.path.exists(json_file)
        finally:
            os.unlink(json_file)
        
        # 测试CSV导出
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as f:
            csv_file = f.name
        
        try:
            result = self.monitor.export_data(csv_file, format='csv', minutes=60)
            assert result is True
            assert os.path.exists(csv_file)
        finally:
            os.unlink(csv_file)
    
    @patch('src.utils.monitor.psutil')
    def test_monitoring_lifecycle(self, mock_psutil):
        """测试监控生命周期"""
        # 模拟psutil数据
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = MagicMock(
            total=16*1024**3, available=8*1024**3, percent=50.0
        )
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = MagicMock(
            bytes_sent=1000, bytes_recv=2000, packets_sent=10, packets_recv=20
        )
        mock_psutil.pids.return_value = list(range(100))
        
        # 开始监控
        self.monitor.start_monitoring(interval=0.1)
        assert self.monitor.monitoring
        assert self.monitor.monitor_thread is not None
        
        # 等待一段时间让监控运行
        time.sleep(0.3)
        
        # 停止监控
        self.monitor.stop_monitoring()
        assert not self.monitor.monitoring
        
        # 验证收集了一些数据
        assert len(self.monitor.history) > 0


class TestUtilityFunctions:
    """工具函数测试"""
    
    @patch('src.utils.monitor.SystemMonitor')
    def test_get_system_summary(self, mock_monitor_class):
        """测试获取系统摘要"""
        # 模拟监控器实例
        mock_monitor = MagicMock()
        mock_monitor_class.return_value = mock_monitor
        
        # 模拟系统信息
        mock_system_info = SystemInfo(
            platform="Windows-10",
            hostname="test-host",
            cpu_count=8,
            memory_total=16.0,
            disk_total=500.0,
            python_version="3.8.0",
            boot_time=datetime.now() - timedelta(hours=2)
        )
        mock_monitor.get_system_info.return_value = mock_system_info
        
        # 模拟当前使用情况
        mock_usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=50.0,
            memory_percent=60.0,
            memory_used=8000,
            memory_available=8000,
            disk_usage={"C:": 70.0},
            network_io={},
            process_count=150
        )
        mock_monitor.collect_current_usage.return_value = mock_usage
        
        # 模拟顶级进程
        mock_processes = [
            ProcessInfo(
                pid=1234,
                name="test.exe",
                cpu_percent=10.0,
                memory_percent=5.0,
                memory_mb=100.0,
                status="running",
                create_time=datetime.now()
            )
        ]
        mock_monitor.get_top_processes.return_value = mock_processes
        
        # 执行测试
        summary = get_system_summary()
        
        assert 'system_info' in summary
        assert 'current_usage' in summary
        assert 'top_processes' in summary
        assert summary['system_info']['hostname'] == "test-host"
        assert summary['current_usage']['cpu_percent'] == 50.0
        assert len(summary['top_processes']) == 1
    
    @patch('src.utils.monitor.SystemMonitor')
    def test_check_system_health(self, mock_monitor_class):
        """测试系统健康检查"""
        # 模拟监控器实例
        mock_monitor = MagicMock()
        mock_monitor_class.return_value = mock_monitor
        
        # 测试健康状态
        mock_usage = ResourceUsage(
            timestamp=datetime.now(),
            cpu_percent=50.0,  # 正常
            memory_percent=60.0,  # 正常
            memory_used=8000,
            memory_available=8000,
            disk_usage={"C:": 70.0},  # 正常
            network_io={},
            process_count=150
        )
        mock_monitor.collect_current_usage.return_value = mock_usage
        
        health = check_system_health()
        assert health['overall'] == 'healthy'
        assert health['cpu'] == 'healthy'
        assert health['memory'] == 'healthy'
        assert health['disk'] == 'healthy'
        
        # 测试警告状态
        mock_usage.cpu_percent = 75.0  # 警告级别
        mock_usage.memory_percent = 85.0  # 警告级别
        
        health = check_system_health()
        assert health['overall'] == 'warning'
        assert health['cpu'] == 'warning'
        assert health['memory'] == 'warning'
        
        # 测试严重状态
        mock_usage.cpu_percent = 95.0  # 严重级别
        mock_usage.memory_percent = 95.0  # 严重级别
        mock_usage.disk_usage = {"C:": 98.0}  # 严重级别
        
        health = check_system_health()
        assert health['overall'] == 'critical'
        assert health['cpu'] == 'critical'
        assert health['memory'] == 'critical'
        assert health['disk'] == 'critical'


if __name__ == "__main__":
    pytest.main([__file__])