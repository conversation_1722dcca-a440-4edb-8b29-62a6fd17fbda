"""
主题管理器

管理应用程序的主题配置
"""

from typing import Dict, Any
from PyQt6.QtCore import QObject

from ...utils.logger import get_logger

logger = get_logger(__name__)


class ThemeManager(QObject):
    """主题管理器"""
    
    def __init__(self):
        """初始化主题管理器"""
        super().__init__()
        
        self.current_theme = "modern_dark"
        
        logger.info("主题管理器初始化完成")
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {
            "modern_dark": "现代深色",
            "modern_light": "现代浅色"
        }
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self.get_available_themes():
            self.current_theme = theme_name
            logger.info(f"主题已切换：{theme_name}")
        else:
            logger.warning(f"未知主题：{theme_name}")
    
    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self.current_theme
