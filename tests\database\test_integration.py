"""
威科夫相对强弱选股系统 - 数据访问层集成测试

测试数据库各组件的集成功能，包括管理器、CRUD操作、迁移、优化等
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pandas as pd

from src.database.manager import DatabaseManager
from src.database.crud import CRUDOperations
from src.database.migration import MigrationManager
from src.database.optimization import DatabaseOptimizer
from src.database.connection_pool import DatabaseConnectionPool


class TestDatabaseIntegration:
    """数据库集成测试"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库文件"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
    
    @pytest.fixture
    def db_manager(self, temp_db_path):
        """创建数据库管理器"""
        manager = DatabaseManager(temp_db_path)
        manager.initialize()
        yield manager
        manager.close()
    
    @pytest.fixture
    def crud_ops(self, db_manager):
        """创建CRUD操作对象"""
        return CRUDOperations(db_manager)
    
    @pytest.fixture
    def migration_manager(self, db_manager):
        """创建迁移管理器"""
        return MigrationManager(db_manager)
    
    @pytest.fixture
    def optimizer(self, db_manager):
        """创建数据库优化器"""
        return DatabaseOptimizer(db_manager)
    
    def test_complete_data_workflow(self, db_manager, crud_ops, optimizer):
        """测试完整的数据工作流"""
        # 1. 插入板块信息
        sector_result = crud_ops.create_sector_info(
            sector_code='BK0001',
            sector_name='银行',
            sector_type='industry',
            parent_code=None,
            level=1
        )
        assert sector_result is True
        
        # 2. 插入股票信息
        stock_result = crud_ops.create_stock_info(
            stock_code='000001',
            stock_name='平安银行',
            market='SZ',
            list_date='2000-01-01',
            industry='银行',
            status='active'
        )
        assert stock_result is True
        
        # 3. 创建板块成分股关系
        constituent_result = crud_ops.create_sector_constituent(
            sector_code='BK0001',
            stock_code='000001',
            weight=0.3
        )
        assert constituent_result is True
        
        # 4. 批量插入股票行情数据
        base_date = datetime.now().date()
        stock_quotes = []
        
        for i in range(100):
            stock_quotes.append({
                'stock_code': '000001',
                'trade_date': base_date - timedelta(days=i),
                'open': 10.0 + i * 0.1,
                'high': 11.0 + i * 0.1,
                'low': 9.0 + i * 0.1,
                'close': 10.5 + i * 0.1,
                'volume': 1000000 + i * 10000,
                'amount': 10000000 + i * 100000,
                'change_pct': i * 0.01,
                'turnover_rate': i * 0.001
            })
        
        # 使用优化器批量插入
        inserted_count = optimizer.bulk_insert_stock_quotes(stock_quotes)
        assert inserted_count == 100
        
        # 5. 查询数据验证
        # 获取股票信息
        retrieved_stocks = crud_ops.get_stock_info(stock_code='000001')
        assert len(retrieved_stocks) > 0
        retrieved_stock = retrieved_stocks[0]
        assert retrieved_stock['stock_name'] == '平安银行'
        
        # 获取最新行情
        latest_quote = crud_ops.get_latest_stock_quote('000001')
        assert latest_quote is not None
        assert latest_quote['stock_code'] == '000001'
        
        # 获取历史行情
        start_date = base_date - timedelta(days=10)
        end_date = base_date
        historical_quotes = crud_ops.get_stock_quotes_by_date_range(
            '000001', start_date, end_date
        )
        assert len(historical_quotes) == 11  # 包含起始和结束日期
        
        # 5. 优化数据库
        # 优化索引
        index_result = optimizer.optimize_table_indexes('stock_quotes')
        assert index_result is True
        
        # 分析统计信息
        stats = optimizer.analyze_table_statistics('stock_quotes')
        assert stats['total_records'] == 100
        assert stats['unique_symbols'] == 1
        
        # 6. 执行清理
        vacuum_result = optimizer.vacuum_and_analyze()
        assert vacuum_result is True
    
    def test_migration_workflow(self, db_manager, migration_manager):
        """测试数据库迁移工作流"""
        # 1. 检查当前版本
        current_version = migration_manager.get_current_version()
        assert current_version == "1.0.0"  # 初始版本
        
        # 2. 获取待应用的迁移
        pending_migrations = migration_manager.get_pending_migrations()
        assert len(pending_migrations) > 0
        
        # 3. 应用所有迁移
        applied_migrations = migration_manager.apply_migrations()
        assert len(applied_migrations) > 0
        
        # 4. 验证版本更新
        new_version = migration_manager.get_current_version()
        assert new_version != "1.0.0"
        
        # 5. 验证新表是否创建
        with db_manager.get_connection() as conn:
            # 检查数据质量监控表
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='data_quality_monitor'
            """)
            assert cursor.fetchone() is not None
            
            # 检查性能监控表
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='performance_monitor'
            """)
            assert cursor.fetchone() is not None
        
        # 6. 获取迁移历史
        history = migration_manager.get_migration_history()
        assert len(history) > 0
        
        for record in history:
            assert 'version' in record
            assert 'applied_at' in record
            assert 'success' in record
    
    def test_crud_operations_comprehensive(self, crud_ops):
        """测试CRUD操作的综合功能"""
        # 1. 创建多个板块
        sectors = [
            {'sector_code': 'BK0001', 'sector_name': '银行', 'level': 2},
            {'sector_code': 'BK0002', 'sector_name': '保险', 'level': 2},
            {'sector_code': 'BK0003', 'sector_name': '证券', 'level': 2}
        ]
        
        sector_ids = []
        for sector in sectors:
            sector_id = crud_ops.create_sector_info(sector)
            sector_ids.append(sector_id)
        
        assert len(sector_ids) == 3
        assert all(sid > 0 for sid in sector_ids)
        
        # 2. 创建多只股票
        stocks = [
            {'stock_code': '000001', 'stock_name': '平安银行', 'sector_code': 'BK0001'},
            {'stock_code': '000002', 'stock_name': '万科A', 'sector_code': 'BK0002'},
            {'stock_code': '000858', 'stock_name': '五粮液', 'sector_code': 'BK0003'}
        ]
        
        stock_ids = []
        for stock in stocks:
            stock_id = crud_ops.create_stock_info(stock)
            stock_ids.append(stock_id)
        
        assert len(stock_ids) == 3
        assert all(sid > 0 for sid in stock_ids)
        
        # 3. 批量创建板块成分股关系
        constituents = [
            {'sector_code': 'BK0001', 'stock_code': '000001', 'weight': 0.3},
            {'sector_code': 'BK0002', 'stock_code': '000002', 'weight': 0.4},
            {'sector_code': 'BK0003', 'stock_code': '000858', 'weight': 0.5}
        ]
        
        for constituent in constituents:
            constituent_id = crud_ops.create_sector_constituent(constituent)
            assert constituent_id > 0
        
        # 4. 查询验证
        # 获取所有活跃股票
        active_stocks = crud_ops.get_active_stocks()
        assert len(active_stocks) == 3
        
        # 获取板块成分股
        sector_stocks = crud_ops.get_sector_constituents('BK0001')
        assert len(sector_stocks) == 1
        assert sector_stocks[0]['stock_code'] == '000001'
        
        # 5. 更新操作
        # 更新股票信息
        update_result = crud_ops.update_stock_info('000001', {'status': 0})
        assert update_result is True
        
        # 验证更新结果
        updated_stock = crud_ops.get_stock_info('000001')
        assert updated_stock['status'] == 0
        
        # 6. 删除操作
        # 删除板块成分股关系
        delete_result = crud_ops.delete_sector_constituent('BK0001', '000001')
        assert delete_result is True
        
        # 验证删除结果
        sector_stocks_after_delete = crud_ops.get_sector_constituents('BK0001')
        assert len(sector_stocks_after_delete) == 0
    
    def test_performance_optimization_integration(self, db_manager, crud_ops, optimizer):
        """测试性能优化集成"""
        # 1. 创建大量测试数据
        base_date = datetime.now().date()
        
        # 创建板块信息
        sectors = []
        for i in range(10):
            sector = {
                'sector_code': f'BK{i:04d}',
                'sector_name': f'板块{i}',
                'level': 2
            }
            crud_ops.create_sector_info(sector)
            sectors.append(sector)
        
        # 创建股票信息
        stocks = []
        for i in range(100):
            stock = {
                'stock_code': f'{i:06d}',
                'stock_name': f'股票{i}',
                'sector_code': f'BK{i % 10:04d}',
                'list_date': '2020-01-01',
                'status': 1
            }
            crud_ops.create_stock_info(stock)
            stocks.append(stock)
        
        # 2. 批量插入行情数据
        stock_quotes = []
        sector_quotes = []
        
        for i in range(100):
            for j in range(30):  # 30天的数据
                # 股票行情
                stock_quotes.append({
                    'stock_code': f'{i:06d}',
                    'trade_date': base_date - timedelta(days=j),
                    'open': 10.0 + i * 0.1 + j * 0.01,
                    'high': 11.0 + i * 0.1 + j * 0.01,
                    'low': 9.0 + i * 0.1 + j * 0.01,
                    'close': 10.5 + i * 0.1 + j * 0.01,
                    'volume': 1000000 + i * 10000 + j * 1000,
                    'amount': 10000000 + i * 100000 + j * 10000
                })
        
        for i in range(10):
            for j in range(30):  # 30天的数据
                # 板块行情
                sector_quotes.append({
                    'sector_code': f'BK{i:04d}',
                    'trade_date': base_date - timedelta(days=j),
                    'open': 1000.0 + i * 10 + j * 0.1,
                    'high': 1100.0 + i * 10 + j * 0.1,
                    'low': 900.0 + i * 10 + j * 0.1,
                    'close': 1050.0 + i * 10 + j * 0.1,
                    'volume': 10000000 + i * 1000000 + j * 100000,
                    'amount': 100000000 + i * 10000000 + j * 1000000
                })
        
        # 使用优化器批量插入
        stock_result = optimizer.bulk_insert_stock_quotes(stock_quotes)
        assert stock_result == 3000  # 100只股票 * 30天
        
        sector_result = optimizer.bulk_insert_sector_quotes(sector_quotes)
        assert sector_result == 300  # 10个板块 * 30天
        
        # 3. 优化所有表的索引
        tables_to_optimize = ['stock_quotes', 'sector_quotes', 'relative_strength_results']
        
        for table in tables_to_optimize:
            result = optimizer.optimize_table_indexes(table)
            if table in ['stock_quotes', 'sector_quotes']:
                assert result is True
        
        # 4. 分析所有表的统计信息
        for table in ['stock_info', 'sector_info', 'stock_quotes', 'sector_quotes']:
            stats = optimizer.analyze_table_statistics(table)
            assert 'total_records' in stats
            assert stats['total_records'] > 0
        
        # 5. 获取优化建议
        recommendations = optimizer.get_optimization_recommendations()
        assert isinstance(recommendations, list)
        
        # 6. 执行数据库清理
        vacuum_result = optimizer.vacuum_and_analyze()
        assert vacuum_result is True
        
        # 7. 性能验证 - 测试查询速度
        import time
        
        # 测试单股票查询
        start_time = time.time()
        latest_quote = crud_ops.get_latest_stock_quote('000001')
        single_query_time = time.time() - start_time
        
        assert latest_quote is not None
        assert single_query_time < 0.1  # 应该在100ms内完成
        
        # 测试范围查询
        start_time = time.time()
        range_quotes = crud_ops.get_stock_quotes_by_date_range(
            '000001', 
            base_date - timedelta(days=10), 
            base_date
        )
        range_query_time = time.time() - start_time
        
        assert len(range_quotes) == 11
        assert range_query_time < 0.2  # 应该在200ms内完成
    
    def test_error_handling_and_recovery(self, db_manager, crud_ops, optimizer):
        """测试错误处理和恢复机制"""
        # 1. 测试无效数据插入
        invalid_stock = {
            'stock_code': None,  # 无效的股票代码
            'stock_name': '测试股票',
            'sector_code': 'INVALID'
        }
        
        # 应该处理错误而不崩溃
        try:
            stock_id = crud_ops.create_stock_info(invalid_stock)
            # 如果没有抛出异常，则应该返回无效ID或0
            assert stock_id is None or stock_id == 0
        except Exception:
            # 如果抛出异常，这也是可以接受的
            pass
        
        # 2. 测试数据库连接错误恢复
        # 临时关闭数据库连接
        original_db_path = db_manager.db_path
        
        # 尝试连接到不存在的数据库
        temp_manager = DatabaseManager("/invalid/path/database.db")
        
        # 应该能够处理连接错误
        try:
            with temp_manager.get_connection() as conn:
                pass
        except Exception as e:
            # 应该抛出适当的异常
            assert "database" in str(e).lower() or "connection" in str(e).lower()
        
        # 3. 测试批量插入错误处理
        invalid_quotes = [
            {'stock_code': '000001', 'trade_date': 'invalid_date'},  # 无效日期
            {'stock_code': None, 'trade_date': datetime.now().date()},  # 无效代码
            {'stock_code': '000002', 'trade_date': datetime.now().date(), 'open': 10.0}  # 部分有效
        ]
        
        # 应该能够处理部分无效数据
        result = optimizer.bulk_insert_stock_quotes(invalid_quotes)
        assert result >= 0  # 可能插入0条或部分记录
        
        # 4. 测试事务回滚
        try:
            with db_manager.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                # 插入一条记录
                conn.execute("""
                    INSERT INTO stock_info (stock_code, stock_name, status)
                    VALUES ('TEST001', '测试股票', 1)
                """)
                
                # 故意执行一个会失败的SQL
                conn.execute("INSERT INTO non_existent_table VALUES (1)")
                
                conn.execute("COMMIT")
                
        except Exception:
            # 事务应该被回滚
            pass
        
        # 验证数据没有被插入
        test_stock = crud_ops.get_stock_info('TEST001')
        assert test_stock is None
    
    def test_concurrent_access_safety(self, db_manager, crud_ops):
        """测试并发访问安全性"""
        import threading
        import time
        
        results = []
        errors = []
        
        def create_stocks(thread_id):
            """线程函数：创建股票"""
            try:
                for i in range(10):
                    stock = {
                        'stock_code': f'T{thread_id:02d}{i:03d}',
                        'stock_name': f'线程{thread_id}股票{i}',
                        'sector_code': 'BK0001',
                        'status': 1
                    }
                    
                    stock_id = crud_ops.create_stock_info(stock)
                    if stock_id and stock_id > 0:
                        results.append(stock_id)
                    
                    # 添加小延迟以增加并发冲突的可能性
                    time.sleep(0.001)
                    
            except Exception as e:
                errors.append(f"线程{thread_id}错误: {str(e)}")
        
        # 首先创建一个板块
        crud_ops.create_sector_info({
            'sector_code': 'BK0001',
            'sector_name': '测试板块',
            'level': 2
        })
        
        # 创建多个线程同时操作数据库
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_stocks, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        assert len(errors) == 0, f"并发操作出现错误: {errors}"
        assert len(results) == 50  # 5个线程 * 每个线程10条记录
        
        # 验证数据完整性
        with db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM stock_info WHERE stock_code LIKE 'T%'")
            count = cursor.fetchone()[0]
            assert count == 50