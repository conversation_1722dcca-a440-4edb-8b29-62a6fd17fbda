#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量检查和异常处理模块
确保数据的实时性、准确性和完整性
"""

import time
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import pandas as pd
import numpy as np
from enum import Enum

from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError

logger = get_logger(__name__)


class DataQualityLevel(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"  # 优秀
    GOOD = "good"           # 良好
    ACCEPTABLE = "acceptable"  # 可接受
    POOR = "poor"           # 较差
    UNACCEPTABLE = "unacceptable"  # 不可接受


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float = 0.0  # 完整性 (0-1)
    accuracy: float = 0.0      # 准确性 (0-1)
    consistency: float = 0.0   # 一致性 (0-1)
    timeliness: float = 0.0    # 及时性 (0-1)
    validity: float = 0.0      # 有效性 (0-1)
    overall_score: float = 0.0 # 综合得分 (0-1)
    quality_level: DataQualityLevel = DataQualityLevel.UNACCEPTABLE
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class DataQualityConfig:
    """数据质量配置"""
    # 完整性阈值
    min_completeness: float = 0.95
    min_data_points: int = 10
    
    # 准确性阈值
    max_price_change_rate: float = 0.15  # 最大单日涨跌幅15%
    max_volume_change_rate: float = 5.0  # 最大成交量变化5倍
    
    # 一致性阈值
    max_price_gap_rate: float = 0.05     # 最大价格跳空5%
    
    # 及时性阈值
    max_data_delay_hours: int = 24       # 最大数据延迟24小时
    
    # 有效性阈值
    min_price: float = 0.01              # 最小价格
    max_price: float = 10000.0           # 最大价格


class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self, config: Optional[DataQualityConfig] = None):
        self.config = config or DataQualityConfig()
        self.check_history = []
        
    def check_market_data_quality(self, 
                                 data: pd.DataFrame,
                                 symbol: str,
                                 data_type: str = "stock") -> DataQualityMetrics:
        """
        检查市场数据质量
        
        Args:
            data: 市场数据DataFrame
            symbol: 股票/指数代码
            data_type: 数据类型 ("stock", "index", "sector")
            
        Returns:
            DataQualityMetrics: 数据质量指标
        """
        try:
            logger.debug(f"开始检查 {symbol} ({data_type}) 数据质量")
            
            metrics = DataQualityMetrics()
            
            if data.empty:
                metrics.issues.append("数据为空")
                metrics.recommendations.append("检查数据源连接和查询条件")
                return metrics
            
            # 1. 检查完整性
            completeness = self._check_completeness(data, symbol)
            metrics.completeness = completeness
            
            # 2. 检查准确性
            accuracy = self._check_accuracy(data, symbol, data_type)
            metrics.accuracy = accuracy
            
            # 3. 检查一致性
            consistency = self._check_consistency(data, symbol)
            metrics.consistency = consistency
            
            # 4. 检查及时性
            timeliness = self._check_timeliness(data, symbol)
            metrics.timeliness = timeliness
            
            # 5. 检查有效性
            validity = self._check_validity(data, symbol)
            metrics.validity = validity
            
            # 6. 计算综合得分
            metrics.overall_score = self._calculate_overall_score(
                completeness, accuracy, consistency, timeliness, validity
            )
            
            # 7. 确定质量等级
            metrics.quality_level = self._determine_quality_level(metrics.overall_score)
            
            # 8. 生成建议
            metrics.recommendations.extend(self._generate_recommendations(metrics))
            
            # 记录检查历史
            self.check_history.append({
                'timestamp': datetime.now(),
                'symbol': symbol,
                'data_type': data_type,
                'metrics': metrics
            })
            
            logger.info(f"{symbol} 数据质量检查完成: {metrics.quality_level.value} "
                       f"(综合得分: {metrics.overall_score:.3f})")
            
            return metrics
            
        except Exception as e:
            logger.error(f"数据质量检查失败: {e}")
            metrics = DataQualityMetrics()
            metrics.issues.append(f"检查过程出错: {e}")
            return metrics
    
    def _check_completeness(self, data: pd.DataFrame, symbol: str) -> float:
        """检查数据完整性"""
        try:
            total_fields = len(['open', 'high', 'low', 'close', 'volume'])
            available_fields = sum(1 for field in ['open', 'high', 'low', 'close', 'volume'] 
                                 if field in data.columns)
            
            # 字段完整性
            field_completeness = available_fields / total_fields
            
            # 数据点完整性
            if len(data) < self.config.min_data_points:
                data_completeness = len(data) / self.config.min_data_points
            else:
                data_completeness = 1.0
            
            # 缺失值检查
            if available_fields > 0:
                required_fields = [f for f in ['open', 'high', 'low', 'close'] if f in data.columns]
                missing_rate = data[required_fields].isnull().sum().sum() / (len(data) * len(required_fields))
                missing_completeness = 1.0 - missing_rate
            else:
                missing_completeness = 0.0
            
            completeness = (field_completeness + data_completeness + missing_completeness) / 3
            
            if completeness < self.config.min_completeness:
                logger.warning(f"{symbol} 数据完整性不足: {completeness:.3f}")
            
            return completeness
            
        except Exception as e:
            logger.error(f"检查数据完整性失败: {e}")
            return 0.0
    
    def _check_accuracy(self, data: pd.DataFrame, symbol: str, data_type: str) -> float:
        """检查数据准确性"""
        try:
            if 'close' not in data.columns:
                return 0.0
            
            accuracy_scores = []
            
            # 1. 价格变动合理性检查
            if len(data) > 1:
                price_changes = data['close'].pct_change().dropna()
                extreme_changes = abs(price_changes) > self.config.max_price_change_rate
                price_accuracy = 1.0 - (extreme_changes.sum() / len(price_changes))
                accuracy_scores.append(price_accuracy)
                
                if extreme_changes.any():
                    logger.warning(f"{symbol} 存在异常价格变动")
            
            # 2. 成交量合理性检查
            if 'volume' in data.columns and len(data) > 1:
                volumes = data['volume'].replace(0, np.nan).dropna()
                if len(volumes) > 1:
                    volume_changes = volumes.pct_change().dropna()
                    extreme_volume_changes = abs(volume_changes) > self.config.max_volume_change_rate
                    volume_accuracy = 1.0 - (extreme_volume_changes.sum() / len(volume_changes))
                    accuracy_scores.append(volume_accuracy)
            
            # 3. 价格关系合理性检查
            if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
                # 检查 high >= max(open, close) 和 low <= min(open, close)
                valid_high = (data['high'] >= data[['open', 'close']].max(axis=1)).all()
                valid_low = (data['low'] <= data[['open', 'close']].min(axis=1)).all()
                price_relation_accuracy = (valid_high + valid_low) / 2
                accuracy_scores.append(price_relation_accuracy)
                
                if not (valid_high and valid_low):
                    logger.warning(f"{symbol} 存在价格关系异常")
            
            return np.mean(accuracy_scores) if accuracy_scores else 0.0
            
        except Exception as e:
            logger.error(f"检查数据准确性失败: {e}")
            return 0.0
    
    def _check_consistency(self, data: pd.DataFrame, symbol: str) -> float:
        """检查数据一致性"""
        try:
            if 'close' not in data.columns or len(data) < 2:
                return 1.0
            
            consistency_scores = []
            
            # 1. 价格连续性检查（检查跳空）
            price_gaps = abs(data['close'].pct_change().dropna())
            large_gaps = price_gaps > self.config.max_price_gap_rate
            gap_consistency = 1.0 - (large_gaps.sum() / len(price_gaps))
            consistency_scores.append(gap_consistency)
            
            if large_gaps.any():
                logger.warning(f"{symbol} 存在较大价格跳空")
            
            # 2. 时间序列连续性检查
            if hasattr(data.index, 'to_series'):
                time_diffs = data.index.to_series().diff().dropna()
                # 检查是否有异常的时间间隔
                if len(time_diffs) > 0:
                    normal_interval = time_diffs.mode().iloc[0] if len(time_diffs.mode()) > 0 else timedelta(days=1)
                    abnormal_intervals = abs(time_diffs - normal_interval) > normal_interval * 2
                    time_consistency = 1.0 - (abnormal_intervals.sum() / len(time_diffs))
                    consistency_scores.append(time_consistency)
            
            return np.mean(consistency_scores) if consistency_scores else 1.0
            
        except Exception as e:
            logger.error(f"检查数据一致性失败: {e}")
            return 0.0
    
    def _check_timeliness(self, data: pd.DataFrame, symbol: str) -> float:
        """检查数据及时性"""
        try:
            if data.empty:
                return 0.0
            
            # 获取最新数据时间
            if hasattr(data.index, 'max'):
                latest_time = data.index.max()
            else:
                latest_time = datetime.now() - timedelta(days=1)  # 默认值
            
            # 计算数据延迟
            now = datetime.now()
            if hasattr(latest_time, 'to_pydatetime'):
                latest_time = latest_time.to_pydatetime()
            
            delay_hours = (now - latest_time).total_seconds() / 3600
            
            # 计算及时性得分
            if delay_hours <= self.config.max_data_delay_hours:
                timeliness = 1.0 - (delay_hours / self.config.max_data_delay_hours)
            else:
                timeliness = 0.0
            
            if delay_hours > self.config.max_data_delay_hours:
                logger.warning(f"{symbol} 数据延迟过长: {delay_hours:.1f} 小时")
            
            return max(0.0, timeliness)
            
        except Exception as e:
            logger.error(f"检查数据及时性失败: {e}")
            return 0.0
    
    def _check_validity(self, data: pd.DataFrame, symbol: str) -> float:
        """检查数据有效性"""
        try:
            if data.empty:
                return 0.0
            
            validity_scores = []
            
            # 1. 价格范围有效性
            if 'close' in data.columns:
                valid_prices = ((data['close'] >= self.config.min_price) & 
                               (data['close'] <= self.config.max_price))
                price_validity = valid_prices.sum() / len(data)
                validity_scores.append(price_validity)
                
                if not valid_prices.all():
                    logger.warning(f"{symbol} 存在无效价格")
            
            # 2. 成交量有效性
            if 'volume' in data.columns:
                valid_volumes = data['volume'] >= 0
                volume_validity = valid_volumes.sum() / len(data)
                validity_scores.append(volume_validity)
            
            # 3. 数值类型有效性
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) > 0:
                finite_values = data[numeric_columns].apply(lambda x: np.isfinite(x)).all(axis=1)
                numeric_validity = finite_values.sum() / len(data)
                validity_scores.append(numeric_validity)
            
            return np.mean(validity_scores) if validity_scores else 0.0
            
        except Exception as e:
            logger.error(f"检查数据有效性失败: {e}")
            return 0.0
    
    def _calculate_overall_score(self, completeness: float, accuracy: float, 
                               consistency: float, timeliness: float, validity: float) -> float:
        """计算综合得分"""
        # 权重配置
        weights = {
            'completeness': 0.25,
            'accuracy': 0.25,
            'consistency': 0.20,
            'timeliness': 0.15,
            'validity': 0.15
        }
        
        overall_score = (
            completeness * weights['completeness'] +
            accuracy * weights['accuracy'] +
            consistency * weights['consistency'] +
            timeliness * weights['timeliness'] +
            validity * weights['validity']
        )
        
        return max(0.0, min(1.0, overall_score))
    
    def _determine_quality_level(self, overall_score: float) -> DataQualityLevel:
        """确定数据质量等级"""
        if overall_score >= 0.9:
            return DataQualityLevel.EXCELLENT
        elif overall_score >= 0.8:
            return DataQualityLevel.GOOD
        elif overall_score >= 0.7:
            return DataQualityLevel.ACCEPTABLE
        elif overall_score >= 0.5:
            return DataQualityLevel.POOR
        else:
            return DataQualityLevel.UNACCEPTABLE
    
    def _generate_recommendations(self, metrics: DataQualityMetrics) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if metrics.completeness < 0.8:
            recommendations.append("增加数据源或检查数据获取流程")
        
        if metrics.accuracy < 0.8:
            recommendations.append("验证数据源质量，检查异常数据处理机制")
        
        if metrics.consistency < 0.8:
            recommendations.append("检查数据处理流程，确保数据连续性")
        
        if metrics.timeliness < 0.8:
            recommendations.append("优化数据更新频率，减少数据延迟")
        
        if metrics.validity < 0.8:
            recommendations.append("加强数据验证规则，过滤无效数据")
        
        return recommendations
    
    def get_quality_summary(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """获取数据质量摘要"""
        try:
            if symbol:
                # 获取特定股票的质量历史
                symbol_history = [h for h in self.check_history if h['symbol'] == symbol]
                if not symbol_history:
                    return {'error': f'没有找到 {symbol} 的质量检查记录'}
                
                latest = symbol_history[-1]
                return {
                    'symbol': symbol,
                    'latest_check': latest['timestamp'],
                    'quality_level': latest['metrics'].quality_level.value,
                    'overall_score': latest['metrics'].overall_score,
                    'issues': latest['metrics'].issues,
                    'recommendations': latest['metrics'].recommendations
                }
            else:
                # 获取整体质量摘要
                if not self.check_history:
                    return {'error': '没有质量检查记录'}
                
                recent_checks = self.check_history[-10:]  # 最近10次检查
                avg_score = np.mean([h['metrics'].overall_score for h in recent_checks])
                
                return {
                    'total_checks': len(self.check_history),
                    'recent_avg_score': avg_score,
                    'recent_checks': len(recent_checks),
                    'last_check': self.check_history[-1]['timestamp']
                }
                
        except Exception as e:
            logger.error(f"获取质量摘要失败: {e}")
            return {'error': str(e)}
