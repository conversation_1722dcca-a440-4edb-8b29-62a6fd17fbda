#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试XtData API调用

详细分析XtData API的调用过程和返回结果
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import xtquant.xtdata as xt
    print("✅ 成功导入xtquant库")
except ImportError as e:
    print(f"❌ 导入xtquant库失败: {e}")
    sys.exit(1)


def debug_api_call(symbol, start_date, end_date):
    """调试API调用"""
    print(f"\n🔍 调试 {symbol} 的API调用")
    print(f"日期范围: {start_date} - {end_date}")
    
    try:
        # 方法1: get_market_data_ex
        print("\n📊 方法1: get_market_data_ex")
        data1 = xt.get_market_data_ex(
            stock_list=[symbol],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        print(f"返回类型: {type(data1)}")
        print(f"返回内容: {data1}")
        
        if data1:
            print(f"数据键: {list(data1.keys())}")
            if symbol in data1:
                df = data1[symbol]
                print(f"DataFrame类型: {type(df)}")
                print(f"DataFrame形状: {df.shape if hasattr(df, 'shape') else 'N/A'}")
                print(f"DataFrame列: {list(df.columns) if hasattr(df, 'columns') else 'N/A'}")
                print(f"DataFrame索引: {df.index if hasattr(df, 'index') else 'N/A'}")
                if hasattr(df, 'head'):
                    print(f"前5行数据:\n{df.head()}")
            else:
                print(f"❌ 返回数据中没有 {symbol}")
        else:
            print("❌ 返回数据为空")
        
        # 方法2: get_local_data
        print("\n📊 方法2: get_local_data")
        try:
            data2 = xt.get_local_data(
                stock_list=[symbol],
                period='1d',
                start_time=start_date,
                end_time=end_date,
                count=-1,
                dividend_type='none',
                fill_data=True
            )
            
            print(f"返回类型: {type(data2)}")
            if data2 and symbol in data2:
                df2 = data2[symbol]
                print(f"DataFrame形状: {df2.shape if hasattr(df2, 'shape') else 'N/A'}")
                print(f"DataFrame列: {list(df2.columns) if hasattr(df2, 'columns') else 'N/A'}")
            else:
                print("❌ get_local_data返回空数据")
        except Exception as e:
            print(f"❌ get_local_data失败: {e}")
        
        # 方法3: 尝试不同的日期范围
        print("\n📊 方法3: 尝试更大的日期范围")
        try:
            # 尝试最近1年的数据
            start_date_1y = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            data3 = xt.get_market_data_ex(
                stock_list=[symbol],
                period='1d',
                start_time=start_date_1y,
                end_time=end_date,
                count=-1,
                dividend_type='none',
                fill_data=True
            )
            
            if data3 and symbol in data3:
                df3 = data3[symbol]
                print(f"1年数据形状: {df3.shape if hasattr(df3, 'shape') else 'N/A'}")
            else:
                print("❌ 1年数据也为空")
        except Exception as e:
            print(f"❌ 1年数据获取失败: {e}")
        
        # 方法4: 尝试不指定日期范围
        print("\n📊 方法4: 不指定日期范围")
        try:
            data4 = xt.get_market_data_ex(
                stock_list=[symbol],
                period='1d',
                count=30,  # 只要最近30条
                dividend_type='none',
                fill_data=True
            )
            
            if data4 and symbol in data4:
                df4 = data4[symbol]
                print(f"最近30条数据形状: {df4.shape if hasattr(df4, 'shape') else 'N/A'}")
                if hasattr(df4, 'head') and not df4.empty:
                    print(f"前5行数据:\n{df4.head()}")
            else:
                print("❌ 最近30条数据也为空")
        except Exception as e:
            print(f"❌ 最近30条数据获取失败: {e}")
        
        return data1
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 XtData API调试工具")
    print("=" * 60)
    
    # 设置日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    # 测试股票列表
    test_symbols = ['600000.SH', '000001.SZ', '000002.SZ']
    
    print(f"测试日期范围: {start_date} - {end_date}")
    print(f"测试股票: {test_symbols}")
    
    # 首先检查股票列表
    try:
        print("\n📋 检查股票列表...")
        stock_list = xt.get_stock_list_in_sector('沪深A股')
        print(f"✅ 获取到 {len(stock_list)} 只股票")
        
        # 检查测试股票是否在列表中
        for symbol in test_symbols:
            if symbol in stock_list:
                print(f"✅ {symbol} 在股票列表中")
            else:
                print(f"❌ {symbol} 不在股票列表中")
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
    
    # 逐个测试股票
    for symbol in test_symbols:
        print(f"\n{'='*60}")
        debug_api_call(symbol, start_date, end_date)
    
    print(f"\n{'='*60}")
    print("🔍 调试完成")


if __name__ == "__main__":
    main()
