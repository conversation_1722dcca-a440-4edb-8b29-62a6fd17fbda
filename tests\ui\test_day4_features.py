"""
第14周第4天功能测试

测试用户偏好设置、主题管理、配置保存恢复等功能
"""

import unittest
import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.ui.utils.theme_manager import ThemeManager, ThemeType, theme_manager
from src.ui.utils.user_preferences import (
    UserPreferencesManager, UserPreferences, UIPreferences,
    DataPreferences, AnalysisPreferences, SelectionPreferences,
    NotificationPreferences, user_preferences
)
from src.ui.dialogs.preferences_dialog import PreferencesDialog


class TestThemeManager(unittest.TestCase):
    """主题管理器测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.theme_manager = ThemeManager()
        
    def test_theme_manager_initialization(self):
        """测试主题管理器初始化"""
        self.assertIsNotNone(self.theme_manager)
        self.assertEqual(self.theme_manager.current_theme, ThemeType.LIGHT)
        self.assertGreater(len(self.theme_manager.themes), 0)
        
    def test_available_themes(self):
        """测试可用主题"""
        themes = self.theme_manager.get_available_themes()
        
        self.assertIn("light", themes)
        self.assertIn("dark", themes)
        self.assertEqual(themes["light"], "明亮主题")
        self.assertEqual(themes["dark"], "深色主题")
        
    def test_theme_switching(self):
        """测试主题切换"""
        # 切换到深色主题
        self.theme_manager.set_theme(ThemeType.DARK)
        self.assertEqual(self.theme_manager.current_theme, ThemeType.DARK)
        
        # 切换回明亮主题
        self.theme_manager.set_theme(ThemeType.LIGHT)
        self.assertEqual(self.theme_manager.current_theme, ThemeType.LIGHT)
        
    def test_theme_config_retrieval(self):
        """测试主题配置获取"""
        light_config = self.theme_manager.get_theme_config(ThemeType.LIGHT)
        dark_config = self.theme_manager.get_theme_config(ThemeType.DARK)
        
        # 验证配置结构
        for config in [light_config, dark_config]:
            self.assertIn("name", config)
            self.assertIn("colors", config)
            self.assertIn("styles", config)
            
        # 验证颜色配置
        light_colors = light_config["colors"]
        dark_colors = dark_config["colors"]
        
        self.assertIn("primary", light_colors)
        self.assertIn("background", light_colors)
        self.assertIn("text_primary", light_colors)
        
        # 验证明亮和深色主题的背景色不同
        self.assertNotEqual(light_colors["background"], dark_colors["background"])


class TestUserPreferencesManager(unittest.TestCase):
    """用户偏好设置管理器测试"""
    
    def setUp(self):
        """设置测试"""
        # 使用临时目录进行测试
        self.temp_dir = tempfile.mkdtemp()
        self.preferences_manager = UserPreferencesManager()
        self.preferences_manager.config_dir = Path(self.temp_dir)
        self.preferences_manager.config_file = self.preferences_manager.config_dir / "test_preferences.json"
        self.preferences_manager.backup_dir = self.preferences_manager.config_dir / "backups"
        self.preferences_manager._ensure_config_dir()
        
    def tearDown(self):
        """清理测试"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_preferences_manager_initialization(self):
        """测试偏好设置管理器初始化"""
        self.assertIsNotNone(self.preferences_manager)
        self.assertIsInstance(self.preferences_manager.preferences, UserPreferences)
        
    def test_default_preferences_structure(self):
        """测试默认偏好设置结构"""
        prefs = self.preferences_manager.preferences
        
        self.assertIsInstance(prefs.ui, UIPreferences)
        self.assertIsInstance(prefs.data, DataPreferences)
        self.assertIsInstance(prefs.analysis, AnalysisPreferences)
        self.assertIsInstance(prefs.selection, SelectionPreferences)
        self.assertIsInstance(prefs.notifications, NotificationPreferences)
        
    def test_preference_get_set(self):
        """测试偏好设置的获取和设置"""
        # 测试设置
        result = self.preferences_manager.set_preference("ui", "theme", "dark")
        self.assertTrue(result)
        
        # 测试获取
        theme = self.preferences_manager.get_preference("ui", "theme")
        self.assertEqual(theme, "dark")
        
        # 测试无效设置
        result = self.preferences_manager.set_preference("invalid", "key", "value")
        self.assertFalse(result)
        
    def test_preferences_save_load(self):
        """测试偏好设置保存和加载"""
        # 修改一些设置
        self.preferences_manager.set_preference("ui", "theme", "dark")
        self.preferences_manager.set_preference("ui", "font_size", 14)
        self.preferences_manager.set_preference("data", "cache_enabled", False)
        
        # 保存设置
        save_result = self.preferences_manager.save_preferences()
        self.assertTrue(save_result)
        self.assertTrue(self.preferences_manager.config_file.exists())
        
        # 创建新的管理器实例并加载设置
        new_manager = UserPreferencesManager()
        new_manager.config_file = self.preferences_manager.config_file
        load_result = new_manager.load_preferences()
        self.assertTrue(load_result)
        
        # 验证设置已正确加载
        self.assertEqual(new_manager.get_preference("ui", "theme"), "dark")
        self.assertEqual(new_manager.get_preference("ui", "font_size"), 14)
        self.assertEqual(new_manager.get_preference("data", "cache_enabled"), False)
        
    def test_preferences_export_import(self):
        """测试偏好设置导出和导入"""
        # 修改一些设置
        self.preferences_manager.set_preference("ui", "theme", "dark")
        self.preferences_manager.set_preference("analysis", "default_period", 300)
        
        # 导出设置
        export_file = self.temp_dir + "/exported_preferences.json"
        export_result = self.preferences_manager.export_preferences(export_file)
        self.assertTrue(export_result)
        self.assertTrue(os.path.exists(export_file))
        
        # 重置设置
        self.preferences_manager.reset_to_defaults()
        self.assertEqual(self.preferences_manager.get_preference("ui", "theme"), "light")
        
        # 导入设置
        import_result = self.preferences_manager.import_preferences(export_file)
        self.assertTrue(import_result)
        
        # 验证设置已恢复
        self.assertEqual(self.preferences_manager.get_preference("ui", "theme"), "dark")
        self.assertEqual(self.preferences_manager.get_preference("analysis", "default_period"), 300)
        
    def test_preferences_reset(self):
        """测试偏好设置重置"""
        # 修改设置
        self.preferences_manager.set_preference("ui", "theme", "dark")
        self.preferences_manager.set_preference("ui", "font_size", 16)
        
        # 重置设置
        self.preferences_manager.reset_to_defaults()
        
        # 验证设置已重置为默认值
        self.assertEqual(self.preferences_manager.get_preference("ui", "theme"), "light")
        self.assertEqual(self.preferences_manager.get_preference("ui", "font_size"), 12)
        
    def test_preferences_backup_creation(self):
        """测试偏好设置备份创建"""
        # 保存初始设置
        self.preferences_manager.save_preferences()
        
        # 修改设置并保存（应该创建备份）
        self.preferences_manager.set_preference("ui", "theme", "dark")
        self.preferences_manager.save_preferences()
        
        # 检查备份目录
        backup_files = list(self.preferences_manager.backup_dir.glob("preferences_backup_*.json"))
        self.assertGreater(len(backup_files), 0)


class TestPreferencesDialog(unittest.TestCase):
    """偏好设置对话框测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        
    def test_preferences_dialog_creation(self):
        """测试偏好设置对话框创建"""
        dialog = PreferencesDialog(self.main_window)
        
        self.assertIsNotNone(dialog)
        self.assertEqual(dialog.windowTitle(), "偏好设置")
        self.assertTrue(dialog.isModal())
        
        # 验证标签页存在
        self.assertIsNotNone(dialog.tab_widget)
        self.assertGreaterEqual(dialog.tab_widget.count(), 5)
        
        dialog.close()
        
    def test_preferences_dialog_ui_elements(self):
        """测试偏好设置对话框UI元素"""
        dialog = PreferencesDialog(self.main_window)
        
        # 验证主要控件存在
        self.assertIsNotNone(dialog.theme_combo)
        self.assertIsNotNone(dialog.layout_mode_combo)
        self.assertIsNotNone(dialog.font_size_spin)
        self.assertIsNotNone(dialog.refresh_mode_combo)
        self.assertIsNotNone(dialog.default_period_spin)
        
        # 验证按钮存在
        self.assertIsNotNone(dialog.ok_button)
        self.assertIsNotNone(dialog.apply_button)
        self.assertIsNotNone(dialog.cancel_button)
        self.assertIsNotNone(dialog.reset_button)
        self.assertIsNotNone(dialog.import_button)
        self.assertIsNotNone(dialog.export_button)
        
        dialog.close()


class TestIntegratedPreferencesSystem(unittest.TestCase):
    """集成偏好设置系统测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_theme_preferences_integration(self):
        """测试主题偏好设置集成"""
        # 创建临时偏好设置管理器
        temp_dir = tempfile.mkdtemp()
        temp_manager = UserPreferencesManager()
        temp_manager.config_dir = Path(temp_dir)
        temp_manager.config_file = temp_manager.config_dir / "test_preferences.json"
        temp_manager._ensure_config_dir()
        
        try:
            # 设置深色主题
            temp_manager.set_preference("ui", "theme", "dark")
            
            # 验证主题管理器可以应用设置
            ui_prefs = temp_manager.get_ui_preferences()
            theme_type = ThemeType.LIGHT if ui_prefs.theme == "light" else ThemeType.DARK
            
            self.assertEqual(theme_type, ThemeType.DARK)
            
        finally:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    def test_preferences_persistence(self):
        """测试偏好设置持久化"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 第一个会话：设置偏好
            manager1 = UserPreferencesManager()
            manager1.config_dir = Path(temp_dir)
            manager1.config_file = manager1.config_dir / "test_preferences.json"
            manager1._ensure_config_dir()
            
            manager1.set_preference("ui", "theme", "dark")
            manager1.set_preference("ui", "font_size", 16)
            manager1.set_preference("data", "auto_refresh_interval", 600)
            manager1.save_preferences()
            
            # 第二个会话：加载偏好
            manager2 = UserPreferencesManager()
            manager2.config_dir = Path(temp_dir)
            manager2.config_file = manager2.config_dir / "test_preferences.json"
            manager2.load_preferences()
            
            # 验证设置已持久化
            self.assertEqual(manager2.get_preference("ui", "theme"), "dark")
            self.assertEqual(manager2.get_preference("ui", "font_size"), 16)
            self.assertEqual(manager2.get_preference("data", "auto_refresh_interval"), 600)
            
        finally:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    def test_default_configuration_quality(self):
        """测试默认配置质量"""
        manager = UserPreferencesManager()
        
        # 验证默认UI设置合理
        ui_prefs = manager.get_ui_preferences()
        self.assertEqual(ui_prefs.theme, "light")
        self.assertEqual(ui_prefs.layout_mode, "standard")
        self.assertGreaterEqual(ui_prefs.window_width, 800)
        self.assertGreaterEqual(ui_prefs.window_height, 600)
        self.assertGreaterEqual(ui_prefs.font_size, 8)
        self.assertLessEqual(ui_prefs.font_size, 24)
        
        # 验证默认数据设置合理
        data_prefs = manager.get_data_preferences()
        self.assertEqual(data_prefs.refresh_mode, "manual")
        self.assertGreaterEqual(data_prefs.auto_refresh_interval, 60)
        self.assertGreaterEqual(data_prefs.data_source_timeout, 10)
        self.assertGreaterEqual(data_prefs.retry_times, 1)
        
        # 验证默认分析设置合理
        analysis_prefs = manager.get_analysis_preferences()
        self.assertGreaterEqual(analysis_prefs.default_period, 50)
        self.assertGreaterEqual(analysis_prefs.min_confidence_threshold, 0.1)
        self.assertLessEqual(analysis_prefs.min_confidence_threshold, 1.0)
        
        # 验证默认选股设置合理
        selection_prefs = manager.get_selection_preferences()
        self.assertGreaterEqual(selection_prefs.max_selection_count, 10)
        self.assertGreaterEqual(selection_prefs.min_market_cap, 1.0)
        self.assertLessEqual(selection_prefs.max_market_cap, 100000.0)


if __name__ == '__main__':
    unittest.main()
