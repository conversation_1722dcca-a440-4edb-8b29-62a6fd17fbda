"""
异常处理框架

定义系统中使用的自定义异常类和异常处理工具
"""

import traceback
import sys
from typing import Any, Dict, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .logger import get_logger

logger = get_logger(__name__)


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误 1xxx
    UNKNOWN_ERROR = 1000
    VALIDATION_ERROR = 1001
    CONFIGURATION_ERROR = 1002
    PERMISSION_ERROR = 1003
    
    # 数据源错误 2xxx
    DATA_SOURCE_CONNECTION_ERROR = 2001
    DATA_SOURCE_TIMEOUT = 2002
    DATA_SOURCE_AUTH_ERROR = 2003
    DATA_SOURCE_API_ERROR = 2004
    DATA_SOURCE_DATA_ERROR = 2005
    
    # 数据库错误 3xxx
    DATABASE_CONNECTION_ERROR = 3001
    DATABASE_TIMEOUT = 3002
    DATABASE_TRANSACTION_ERROR = 3003
    DATABASE_INTEGRITY_ERROR = 3004
    DATABASE_MIGRATION_ERROR = 3005
    
    # 计算错误 4xxx
    CALCULATION_ERROR = 4001
    INVALID_PERIOD_ERROR = 4002
    INSUFFICIENT_DATA_ERROR = 4003
    NUMERICAL_ERROR = 4004
    
    # 界面错误 5xxx
    UI_INITIALIZATION_ERROR = 5001
    UI_RENDER_ERROR = 5002
    UI_EVENT_ERROR = 5003
    
    # 文件/IO错误 6xxx
    FILE_NOT_FOUND_ERROR = 6001
    FILE_PERMISSION_ERROR = 6002
    FILE_FORMAT_ERROR = 6003
    IO_ERROR = 6004


@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: datetime
    function_name: str
    module_name: str
    line_number: int
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Dict[str, Any] = None


class BaseException(Exception):
    """基础异常类"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
                 context: Optional[ErrorContext] = None, 
                 original_exception: Optional[Exception] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            context: 错误上下文
            original_exception: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or self._create_context()
        self.original_exception = original_exception
        
        # 记录异常
        self._log_exception()
    
    def _create_context(self) -> ErrorContext:
        """创建错误上下文"""
        frame = sys._getframe(2)  # 跳过当前frame和__init__
        return ErrorContext(
            timestamp=datetime.now(),
            function_name=frame.f_code.co_name,
            module_name=frame.f_globals.get('__name__', 'unknown'),
            line_number=frame.f_lineno
        )
    
    def _log_exception(self):
        """记录异常到日志"""
        logger.error(f"异常发生: {self.error_code.name}({self.error_code.value}) - {self.message}")
        if self.original_exception:
            logger.error(f"原始异常: {self.original_exception}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_code': self.error_code.value,
            'error_name': self.error_code.name,
            'message': self.message,
            'timestamp': self.context.timestamp.isoformat(),
            'function': self.context.function_name,
            'module': self.context.module_name,
            'line': self.context.line_number,
            'original_exception': str(self.original_exception) if self.original_exception else None
        }
    
    def __str__(self) -> str:
        return f"[{self.error_code.name}] {self.message}"


class ValidationError(BaseException):
    """验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, 
                 value: Any = None, **kwargs):
        super().__init__(message, ErrorCode.VALIDATION_ERROR, **kwargs)
        self.field = field
        self.value = value


class ConfigurationError(BaseException):
    """配置错误"""
    
    def __init__(self, message: str, config_path: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.CONFIGURATION_ERROR, **kwargs)
        self.config_path = config_path


class DataSourceError(BaseException):
    """数据源错误"""
    
    def __init__(self, message: str, source_name: Optional[str] = None,
                 error_code: ErrorCode = ErrorCode.DATA_SOURCE_CONNECTION_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.source_name = source_name


class ConnectionError(BaseException):
    """连接错误"""
    
    def __init__(self, message: str, host: Optional[str] = None, port: Optional[int] = None,
                 error_code: ErrorCode = ErrorCode.DATA_SOURCE_CONNECTION_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.host = host
        self.port = port


class DatabaseError(BaseException):
    """数据库错误"""
    
    def __init__(self, message: str, table_name: Optional[str] = None,
                 error_code: ErrorCode = ErrorCode.DATABASE_CONNECTION_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.table_name = table_name


class CalculationError(BaseException):
    """计算错误"""
    
    def __init__(self, message: str, calculation_type: Optional[str] = None,
                 error_code: ErrorCode = ErrorCode.CALCULATION_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.calculation_type = calculation_type


class UIError(BaseException):
    """界面错误"""
    
    def __init__(self, message: str, widget_name: Optional[str] = None,
                 error_code: ErrorCode = ErrorCode.UI_INITIALIZATION_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.widget_name = widget_name


class FileError(BaseException):
    """文件错误"""
    
    def __init__(self, message: str, file_path: Optional[str] = None,
                 error_code: ErrorCode = ErrorCode.FILE_NOT_FOUND_ERROR, **kwargs):
        super().__init__(message, error_code, **kwargs)
        self.file_path = file_path


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self):
        """初始化异常处理器"""
        self._handlers: Dict[type, List[Callable]] = {}
        self._global_handlers: List[Callable] = []
        self._error_stats: Dict[str, int] = {}
        
        logger.info("异常处理器初始化完成")
    
    def register_handler(self, exception_type: type, handler: Callable):
        """
        注册异常处理器
        
        Args:
            exception_type: 异常类型
            handler: 处理函数
        """
        if exception_type not in self._handlers:
            self._handlers[exception_type] = []
        
        self._handlers[exception_type].append(handler)
        logger.info(f"注册异常处理器: {exception_type.__name__}")
    
    def register_global_handler(self, handler: Callable):
        """
        注册全局异常处理器
        
        Args:
            handler: 处理函数
        """
        self._global_handlers.append(handler)
        logger.info("注册全局异常处理器")
    
    def handle_exception(self, exception: Exception, context: Optional[Dict[str, Any]] = None) -> bool:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 额外上下文信息
            
        Returns:
            是否处理成功
        """
        try:
            # 更新统计
            exception_name = type(exception).__name__
            self._error_stats[exception_name] = self._error_stats.get(exception_name, 0) + 1
            
            # 查找特定类型的处理器
            exception_type = type(exception)
            handled = False
            
            if exception_type in self._handlers:
                for handler in self._handlers[exception_type]:
                    try:
                        handler(exception, context)
                        handled = True
                    except Exception as e:
                        logger.error(f"异常处理器执行失败: {e}")
            
            # 执行全局处理器
            for handler in self._global_handlers:
                try:
                    handler(exception, context)
                    handled = True
                except Exception as e:
                    logger.error(f"全局异常处理器执行失败: {e}")
            
            # 如果没有处理器，记录异常
            if not handled:
                self._default_handler(exception, context)
            
            return True
            
        except Exception as e:
            logger.error(f"异常处理过程出错: {e}")
            return False
    
    def _default_handler(self, exception: Exception, context: Optional[Dict[str, Any]] = None):
        """默认异常处理器"""
        logger.error(f"未处理的异常: {type(exception).__name__}: {exception}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        
        if context:
            logger.error(f"异常上下文: {context}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计"""
        return self._error_stats.copy()
    
    def clear_stats(self):
        """清除统计信息"""
        self._error_stats.clear()


# 全局异常处理器实例
exception_handler = ExceptionHandler()


def handle_exceptions(exception_type: type = Exception):
    """
    异常处理装饰器
    
    Args:
        exception_type: 要捕获的异常类型
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exception_type as e:
                exception_handler.handle_exception(e, {
                    'function': func.__name__,
                    'args': str(args)[:100],  # 限制长度
                    'kwargs': str(kwargs)[:100]
                })
                raise
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default=None, **kwargs) -> Any:
    """
    安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        default: 异常时返回的默认值
        **kwargs: 关键字参数
        
    Returns:
        函数结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        exception_handler.handle_exception(e, {
            'function': func.__name__ if hasattr(func, '__name__') else str(func),
            'safe_execute': True
        })
        return default


def create_error_response(exception: BaseException) -> Dict[str, Any]:
    """
    创建错误响应
    
    Args:
        exception: 异常对象
        
    Returns:
        错误响应字典
    """
    return {
        'success': False,
        'error': exception.to_dict(),
        'timestamp': datetime.now().isoformat()
    }


def format_exception_message(exception: Exception, include_traceback: bool = False) -> str:
    """
    格式化异常消息
    
    Args:
        exception: 异常对象
        include_traceback: 是否包含堆栈跟踪
        
    Returns:
        格式化的异常消息
    """
    message_parts = [f"{type(exception).__name__}: {exception}"]
    
    if isinstance(exception, BaseException):
        message_parts.append(f"错误代码: {exception.error_code.name}")
        if exception.context:
            message_parts.append(f"发生位置: {exception.context.module_name}.{exception.context.function_name}:{exception.context.line_number}")
            message_parts.append(f"发生时间: {exception.context.timestamp}")
    
    if include_traceback:
        message_parts.append(f"堆栈跟踪:\n{traceback.format_exc()}")
    
    return "\n".join(message_parts)


def setup_global_exception_handling():
    """设置全局异常处理"""
    
    def global_exception_handler(exception_type, exception_value, exception_traceback):
        """全局异常处理函数"""
        if issubclass(exception_type, KeyboardInterrupt):
            # 允许键盘中断
            sys.__excepthook__(exception_type, exception_value, exception_traceback)
            return
        
        logger.error(f"未捕获的异常: {exception_type.__name__}: {exception_value}")
        logger.error("".join(traceback.format_exception(exception_type, exception_value, exception_traceback)))
        
        # 尝试保存状态或执行清理
        try:
            # 这里可以添加应用程序的清理逻辑
            pass
        except Exception as e:
            logger.error(f"清理过程中出错: {e}")
    
    # 设置全局异常钩子
    sys.excepthook = global_exception_handler
    
    # 注册一些默认的处理器
    def log_validation_error(exception: ValidationError, context: Optional[Dict[str, Any]] = None):
        """验证错误处理器"""
        logger.warning(f"验证失败: 字段={exception.field}, 值={exception.value}, 消息={exception.message}")
    
    def log_config_error(exception: ConfigurationError, context: Optional[Dict[str, Any]] = None):
        """配置错误处理器"""
        logger.error(f"配置错误: 路径={exception.config_path}, 消息={exception.message}")
    
    def log_database_error(exception: DatabaseError, context: Optional[Dict[str, Any]] = None):
        """数据库错误处理器"""
        logger.error(f"数据库错误: 表={exception.table_name}, 消息={exception.message}")
        # 这里可以添加数据库重连逻辑
    
    # 注册处理器
    exception_handler.register_handler(ValidationError, log_validation_error)
    exception_handler.register_handler(ConfigurationError, log_config_error)
    exception_handler.register_handler(DatabaseError, log_database_error)
    
    logger.info("全局异常处理已设置")


# 常用异常创建函数
def create_validation_error(message: str, field: Optional[str] = None, value: Any = None) -> ValidationError:
    """创建验证错误"""
    return ValidationError(message, field=field, value=value)


def create_config_error(message: str, config_path: Optional[str] = None) -> ConfigurationError:
    """创建配置错误"""
    return ConfigurationError(message, config_path=config_path)


def create_data_source_error(message: str, source_name: Optional[str] = None,
                           error_type: str = "connection") -> DataSourceError:
    """创建数据源错误"""
    error_codes = {
        "connection": ErrorCode.DATA_SOURCE_CONNECTION_ERROR,
        "timeout": ErrorCode.DATA_SOURCE_TIMEOUT,
        "auth": ErrorCode.DATA_SOURCE_AUTH_ERROR,
        "api": ErrorCode.DATA_SOURCE_API_ERROR,
        "data": ErrorCode.DATA_SOURCE_DATA_ERROR
    }
    
    error_code = error_codes.get(error_type, ErrorCode.DATA_SOURCE_CONNECTION_ERROR)
    return DataSourceError(message, source_name=source_name, error_code=error_code)


def create_database_error(message: str, table_name: Optional[str] = None,
                         error_type: str = "connection") -> DatabaseError:
    """创建数据库错误"""
    error_codes = {
        "connection": ErrorCode.DATABASE_CONNECTION_ERROR,
        "timeout": ErrorCode.DATABASE_TIMEOUT,
        "transaction": ErrorCode.DATABASE_TRANSACTION_ERROR,
        "integrity": ErrorCode.DATABASE_INTEGRITY_ERROR,
        "migration": ErrorCode.DATABASE_MIGRATION_ERROR
    }
    
    error_code = error_codes.get(error_type, ErrorCode.DATABASE_CONNECTION_ERROR)
    return DatabaseError(message, table_name=table_name, error_code=error_code)


class CacheError(BaseException):
    """缓存错误"""

    def __init__(self, message: str, cache_type: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.UNKNOWN_ERROR, **kwargs)
        self.cache_type = cache_type


class NetworkError(BaseException):
    """网络错误"""

    def __init__(self, message: str, url: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.DATA_SOURCE_CONNECTION_ERROR, **kwargs)
        self.url = url


class FileSystemError(BaseException):
    """文件系统错误"""

    def __init__(self, message: str, path: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCode.FILE_NOT_FOUND_ERROR, **kwargs)
        self.path = path