"""
工具提示增强系统

为UI控件提供丰富的悬停提示和操作指导
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QLabel, QFrame, QVBoxLayout, QHBoxLayout, 
    QPushButton, QToolTip, QApplication
)
from PyQt6.QtCore import Qt, QTimer, QPoint, QRect, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap, QIcon, QCursor
import time
from ...utils.logger import get_logger

logger = get_logger(__name__)


class EnhancedToolTip(QFrame):
    """增强型工具提示控件"""
    
    def __init__(self, title: str, content: str, shortcut: str = "", parent: Optional[QWidget] = None):
        """
        初始化增强型工具提示
        
        Args:
            title: 提示标题
            content: 提示内容
            shortcut: 快捷键（可选）
            parent: 父控件
        """
        super().__init__(parent)
        self.title = title
        self.content = content
        self.shortcut = shortcut
        
        self._init_ui()
        self._apply_styles()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(4)
        
        # 标题
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    font-size: 14px;
                    color: #2196F3;
                    margin-bottom: 2px;
                }
            """)
            layout.addWidget(title_label)
        
        # 内容
        content_label = QLabel(self.content)
        content_label.setWordWrap(True)
        content_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                line-height: 1.4;
            }
        """)
        layout.addWidget(content_label)
        
        # 快捷键
        if self.shortcut:
            shortcut_layout = QHBoxLayout()
            shortcut_layout.addStretch()
            
            shortcut_label = QLabel(f"快捷键: {self.shortcut}")
            shortcut_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #666;
                    font-style: italic;
                    margin-top: 4px;
                }
            """)
            shortcut_layout.addWidget(shortcut_label)
            
            layout.addLayout(shortcut_layout)
            
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.95);
                border: 1px solid #ccc;
                border-radius: 6px;
                padding: 4px;
            }
        """)
        
    def show_at_position(self, position: QPoint):
        """在指定位置显示提示"""
        self.move(position)
        self.show()
        self.raise_()


class ToolTipEnhancer(QObject):
    """工具提示增强器"""
    
    def __init__(self, main_window):
        """
        初始化工具提示增强器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.enhanced_tooltips: Dict[QWidget, EnhancedToolTip] = {}
        self.current_tooltip: Optional[EnhancedToolTip] = None
        self.show_timer = QTimer()
        self.hide_timer = QTimer()
        
        # 配置定时器
        self.show_timer.setSingleShot(True)
        self.show_timer.timeout.connect(self._show_current_tooltip)
        
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self._hide_current_tooltip)
        
        logger.info("工具提示增强器初始化完成")
        
    def add_enhanced_tooltip(self, widget: QWidget, title: str, content: str, shortcut: str = ""):
        """
        为控件添加增强型工具提示
        
        Args:
            widget: 目标控件
            title: 提示标题
            content: 提示内容
            shortcut: 快捷键（可选）
        """
        try:
            # 创建增强型提示
            tooltip = EnhancedToolTip(title, content, shortcut, self.main_window)
            self.enhanced_tooltips[widget] = tooltip
            
            # 安装事件过滤器
            widget.installEventFilter(self)
            
            logger.debug(f"为控件添加增强型工具提示: {title}")
            
        except Exception as e:
            logger.error(f"添加增强型工具提示失败: {e}")
            
    def eventFilter(self, obj: QObject, event) -> bool:
        """事件过滤器"""
        try:
            if obj in self.enhanced_tooltips:
                if event.type() == event.Type.Enter:
                    self._on_widget_enter(obj)
                elif event.type() == event.Type.Leave:
                    self._on_widget_leave(obj)
                    
        except Exception as e:
            logger.error(f"事件过滤器处理失败: {e}")
            
        return super().eventFilter(obj, event)
        
    def _on_widget_enter(self, widget: QWidget):
        """控件鼠标进入事件"""
        try:
            if widget in self.enhanced_tooltips:
                self.current_tooltip = self.enhanced_tooltips[widget]
                self.current_widget = widget
                
                # 延迟显示提示
                self.hide_timer.stop()
                self.show_timer.start(500)  # 500ms延迟
                
        except Exception as e:
            logger.error(f"处理控件进入事件失败: {e}")
            
    def _on_widget_leave(self, widget: QWidget):
        """控件鼠标离开事件"""
        try:
            self.show_timer.stop()
            
            if self.current_tooltip:
                # 延迟隐藏提示
                self.hide_timer.start(200)  # 200ms延迟
                
        except Exception as e:
            logger.error(f"处理控件离开事件失败: {e}")
            
    def _show_current_tooltip(self):
        """显示当前工具提示"""
        try:
            if self.current_tooltip and hasattr(self, 'current_widget'):
                # 计算提示位置
                widget_rect = self.current_widget.rect()
                global_pos = self.current_widget.mapToGlobal(widget_rect.bottomLeft())
                
                # 调整位置避免超出屏幕
                screen_rect = QApplication.primaryScreen().geometry()
                tooltip_size = self.current_tooltip.sizeHint()
                
                x = global_pos.x()
                y = global_pos.y() + 5
                
                # 水平位置调整
                if x + tooltip_size.width() > screen_rect.right():
                    x = screen_rect.right() - tooltip_size.width() - 10
                    
                # 垂直位置调整
                if y + tooltip_size.height() > screen_rect.bottom():
                    y = global_pos.y() - tooltip_size.height() - 5
                    
                self.current_tooltip.show_at_position(QPoint(x, y))
                
        except Exception as e:
            logger.error(f"显示工具提示失败: {e}")
            
    def _hide_current_tooltip(self):
        """隐藏当前工具提示"""
        try:
            if self.current_tooltip:
                self.current_tooltip.hide()
                self.current_tooltip = None
                
        except Exception as e:
            logger.error(f"隐藏工具提示失败: {e}")
            
    def setup_main_window_tooltips(self):
        """为主窗口控件设置工具提示"""
        try:
            # 为工具栏按钮添加增强提示
            self._setup_toolbar_tooltips()
            
            # 为菜单项添加增强提示
            self._setup_menu_tooltips()
            
            # 为状态栏添加增强提示
            self._setup_statusbar_tooltips()
            
            logger.info("主窗口工具提示设置完成")
            
        except Exception as e:
            logger.error(f"设置主窗口工具提示失败: {e}")
            
    def _setup_toolbar_tooltips(self):
        """设置工具栏工具提示"""
        # 这里可以为具体的工具栏按钮添加增强提示
        # 由于工具栏按钮是动态创建的，需要在创建时调用
        pass
        
    def _setup_menu_tooltips(self):
        """设置菜单工具提示"""
        # 菜单项的提示通过statusTip设置，这里可以添加额外的增强
        pass
        
    def _setup_statusbar_tooltips(self):
        """设置状态栏工具提示"""
        # 为状态栏的各个部分添加说明性提示
        pass


def add_button_tooltip(button: QPushButton, title: str, description: str, shortcut: str = ""):
    """
    为按钮添加增强型工具提示的便捷函数
    
    Args:
        button: 按钮控件
        title: 提示标题
        description: 功能描述
        shortcut: 快捷键
    """
    tooltip_text = f"<b>{title}</b><br/>{description}"
    if shortcut:
        tooltip_text += f"<br/><i>快捷键: {shortcut}</i>"
    
    button.setToolTip(tooltip_text)
    button.setToolTipDuration(3000)  # 3秒后自动隐藏


def add_widget_help_text(widget: QWidget, help_text: str):
    """
    为控件添加帮助文本
    
    Args:
        widget: 目标控件
        help_text: 帮助文本
    """
    widget.setWhatsThis(help_text)


# 预定义的工具提示内容
TOOLTIP_CONTENTS = {
    'refresh_data': {
        'title': '刷新数据',
        'content': '从数据源获取最新的股票信息和价格数据。建议每日开始分析前先刷新数据。',
        'shortcut': 'F5'
    },
    'wyckoff_analysis': {
        'title': '威科夫分析',
        'content': '对选中股票进行威科夫市场结构分析，识别累积、派发等关键阶段。',
        'shortcut': 'Ctrl+W'
    },
    'rs_analysis': {
        'title': '相对强弱分析',
        'content': '计算股票相对于大盘的强弱表现，帮助识别强势股和弱势股。',
        'shortcut': 'Ctrl+R'
    },
    'smart_selection': {
        'title': '智能选股',
        'content': '基于威科夫分析和相对强弱指标，自动筛选优质投资标的。',
        'shortcut': 'Ctrl+S'
    },
    'export_data': {
        'title': '导出数据',
        'content': '将分析结果和选股数据导出为Excel或CSV格式，便于进一步分析。',
        'shortcut': 'Ctrl+E'
    },
    'system_settings': {
        'title': '系统设置',
        'content': '配置数据源连接、分析参数、界面主题等系统设置。',
        'shortcut': 'Ctrl+,'
    }
}
