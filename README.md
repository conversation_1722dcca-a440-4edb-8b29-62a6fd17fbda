# 威科夫相对强弱选股系统

## 项目简介

威科夫相对强弱选股系统（Wyckoff Relative Strength Stock Selection System）是基于威科夫理论和相对强弱分析的智能化股票筛选系统。通过板块-个股双重相对强弱筛选机制，为投资者提供高质量的投资标的。

## 核心特性

- **双重筛选机制**：板块相对大盘 + 个股相对板块的两层筛选
- **多时间段分析**：支持并行多时间区间分析，提升筛选效率
- **智能数据管理**：自动化数据更新和完整性验证
- **可扩展架构**：支持多数据源集成和自定义筛选策略

## 技术栈

- **编程语言**：Python 3.8+
- **GUI框架**：PyQt6
- **数据处理**：Pandas, NumPy
- **数据库**：SQLite
- **数据源**：XtData (MiniQMT)
- **可视化**：PyQtGraph, Matplotlib

## 项目结构

```
xdqr/
├── src/                    # 源代码目录
│   ├── core/              # 核心业务逻辑
│   ├── data_sources/      # 数据源接口
│   ├── database/          # 数据库管理
│   ├── engines/           # 计算引擎
│   ├── ui/                # 用户界面
│   ├── utils/             # 工具函数
│   └── config/            # 配置管理
├── tests/                 # 测试代码
│   ├── unit/              # 单元测试
│   └── integration/       # 集成测试
├── docs/                  # 文档目录
├── scripts/               # 构建和工具脚本
├── data/                  # 数据存储
├── logs/                  # 日志文件
├── resources/             # 静态资源
│   ├── icons/             # 图标文件
│   └── themes/            # 主题文件
├── memory-bank/           # 项目记忆库
├── main.py                # 主程序入口
├── requirements.txt       # 依赖列表
└── README.md              # 项目说明
```

## 安装和运行

### 环境要求

- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 最低4GB内存，推荐8GB
- 至少10GB可用存储空间

### 安装步骤

1. 克隆项目到本地
```bash
git clone <repository_url>
cd xdqr
```

2. 创建虚拟环境
```bash
python -m venv .venv
.venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行程序
```bash
python main.py
```

## 开发指南

### 开发环境设置

1. 安装开发依赖
```bash
pip install -r requirements-dev.txt
```

2. 配置代码规范工具
```bash
# 代码格式化
black src/

# 代码风格检查
flake8 src/

# 类型检查
mypy src/
```

3. 运行测试
```bash
pytest tests/ --cov=src
```

### 代码规范

- 遵循PEP 8编码规范
- 使用类型提示
- 编写完整的文档字符串
- 保持单元测试覆盖率 > 80%

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request！

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**免责声明**：本软件仅供学习和研究使用，不构成投资建议。投资有风险，决策需谨慎。