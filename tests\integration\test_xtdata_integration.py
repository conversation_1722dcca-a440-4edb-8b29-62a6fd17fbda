#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XtData集成测试
测试与真实XtData服务的连接和数据获取
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from typing import List

from src.data_sources.base import DataSourceConfig, MarketData, SectorInfo
from src.data_sources.xtdata_adapter import XtDataAdapter
from src.data_sources.manager import DataSourceManager
from src.data_sources.data_source_config import DataSourceConfigManager


class TestXtDataIntegration:
    """XtData集成测试"""
    
    @pytest.fixture
    def xtdata_config(self):
        """创建XtData配置"""
        return DataSourceConfig(
            name="xtdata_test",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                "ip": "127.0.0.1",
                "port": 58610
            }
        )
    
    @pytest.fixture
    def xtdata_adapter(self, xtdata_config):
        """创建XtData适配器"""
        return XtDataAdapter(xtdata_config)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_xtdata_connection(self, xtdata_adapter):
        """测试XtData连接
        
        注意：此测试需要本地运行MiniQMT服务
        如果服务未运行，测试将被跳过
        """
        try:
            # 尝试连接
            result = await xtdata_adapter.connect()
            
            if result:
                # 连接成功，测试连接状态
                is_connected = await xtdata_adapter.is_connected()
                assert is_connected is True
                
                # 测试健康检查
                health = await xtdata_adapter.health_check()
                assert health["status"] in ["healthy", "connected"]
                assert "timestamp" in health
                
                # 断开连接
                disconnect_result = await xtdata_adapter.disconnect()
                assert disconnect_result is True
                
                # 验证断开状态
                is_connected_after = await xtdata_adapter.is_connected()
                assert is_connected_after is False
                
            else:
                pytest.skip("XtData服务未运行，跳过连接测试")
                
        except Exception as e:
            pytest.skip(f"XtData连接失败，跳过测试: {e}")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_get_market_data(self, xtdata_adapter):
        """测试获取市场数据"""
        try:
            # 连接到服务
            connected = await xtdata_adapter.connect()
            if not connected:
                pytest.skip("无法连接到XtData服务")
            
            # 测试股票代码
            test_symbols = ["000001.SZ", "600000.SH", "000002.SZ"]
            
            # 获取市场数据
            market_data = await xtdata_adapter.get_market_data(test_symbols)
            
            # 验证返回数据
            assert isinstance(market_data, list)
            assert len(market_data) <= len(test_symbols)  # 可能某些股票数据不可用
            
            for data in market_data:
                assert isinstance(data, MarketData)
                assert data.symbol in test_symbols
                assert isinstance(data.timestamp, datetime)
                assert data.price is not None
                assert data.price > 0
                
                # 验证数据完整性
                print(f"股票: {data.symbol}")
                print(f"价格: {data.price}")
                print(f"时间: {data.timestamp}")
                if data.volume:
                    print(f"成交量: {data.volume}")
                if data.change_percent:
                    print(f"涨跌幅: {data.change_percent}%")
                print("-" * 40)
            
            # 断开连接
            await xtdata_adapter.disconnect()
            
        except Exception as e:
            pytest.skip(f"获取市场数据失败: {e}")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_get_sectors(self, xtdata_adapter):
        """测试获取板块信息"""
        try:
            # 连接到服务
            connected = await xtdata_adapter.connect()
            if not connected:
                pytest.skip("无法连接到XtData服务")
            
            # 获取板块信息
            sectors = await xtdata_adapter.get_sectors()
            
            # 验证返回数据
            assert isinstance(sectors, list)
            
            if len(sectors) > 0:
                for sector in sectors[:5]:  # 只检查前5个板块
                    assert isinstance(sector, SectorInfo)
                    assert sector.sector_code is not None
                    assert sector.sector_name is not None
                    
                    print(f"板块代码: {sector.sector_code}")
                    print(f"板块名称: {sector.sector_name}")
                    if sector.stocks:
                        print(f"股票数量: {len(sector.stocks)}")
                        print(f"部分股票: {sector.stocks[:3]}")
                    print("-" * 40)
            else:
                print("未获取到板块数据")
            
            # 断开连接
            await xtdata_adapter.disconnect()
            
        except Exception as e:
            pytest.skip(f"获取板块信息失败: {e}")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_get_sector_stocks(self, xtdata_adapter):
        """测试获取板块股票"""
        try:
            # 连接到服务
            connected = await xtdata_adapter.connect()
            if not connected:
                pytest.skip("无法连接到XtData服务")
            
            # 先获取板块列表
            sectors = await xtdata_adapter.get_sectors()
            
            if len(sectors) > 0:
                # 测试第一个板块的股票
                test_sector = sectors[0]
                stocks = await xtdata_adapter.get_sector_stocks(test_sector.sector_code)
                
                assert isinstance(stocks, list)
                
                print(f"板块: {test_sector.sector_name} ({test_sector.sector_code})")
                print(f"股票数量: {len(stocks)}")
                if len(stocks) > 0:
                    print(f"部分股票: {stocks[:10]}")
                
                # 验证股票代码格式
                for stock in stocks[:5]:  # 只检查前5个
                    assert isinstance(stock, str)
                    assert "." in stock  # 应该包含市场后缀
            else:
                pytest.skip("未获取到板块数据，无法测试板块股票")
            
            # 断开连接
            await xtdata_adapter.disconnect()
            
        except Exception as e:
            pytest.skip(f"获取板块股票失败: {e}")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_data_source_manager_with_xtdata(self):
        """测试数据源管理器与XtData的集成"""
        try:
            # 创建配置
            config = DataSourceConfig(
                name="xtdata_manager_test",
                enabled=True,
                timeout=30,
                config={
                    "ip": "127.0.0.1",
                    "port": 58610
                }
            )
            
            # 创建适配器和管理器
            adapter = XtDataAdapter(config)
            manager = DataSourceManager()
            
            # 注册数据源
            manager.register_source(adapter, is_primary=True)
            
            # 连接所有数据源
            connect_results = await manager.connect_all()
            
            if not connect_results.get("xtdata_manager_test", False):
                pytest.skip("无法通过管理器连接到XtData服务")
            
            # 通过管理器获取市场数据
            test_symbols = ["000001.SZ", "600000.SH"]
            market_data = await manager.get_market_data(test_symbols)
            
            assert isinstance(market_data, list)
            assert len(market_data) > 0
            
            for data in market_data:
                assert isinstance(data, MarketData)
                assert data.symbol in test_symbols
            
            # 获取统计信息
            stats = manager.get_source_stats("xtdata_manager_test")
            assert stats is not None
            assert stats.total_requests > 0
            
            # 健康检查
            health_results = await manager.health_check_all()
            assert "xtdata_manager_test" in health_results
            
            # 断开所有连接
            await manager.disconnect_all()
            
        except Exception as e:
            pytest.skip(f"数据源管理器集成测试失败: {e}")
    
    @pytest.mark.integration
    def test_config_manager_with_xtdata_template(self):
        """测试配置管理器的XtData模板"""
        import tempfile
        import shutil
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 创建配置管理器
            config_manager = DataSourceConfigManager(
                config_dir=temp_dir,
                enable_encryption=False,
                enable_hot_reload=False
            )
            
            # 获取默认XtData模板
            template = config_manager.get_template("xtdata_default")
            assert template is not None
            assert template.name == "xtdata_default"
            assert "data_sources" in template.template_data
            
            # 应用模板创建配置
            overrides = {
                "data_sources": [
                    {
                        "name": "custom_xtdata",
                        "config": {
                            "ip": "*************",
                            "port": 8080
                        }
                    }
                ]
            }
            
            result = config_manager.apply_template(
                "xtdata_default", 
                "custom_xtdata_config", 
                overrides
            )
            assert result is True
            
            # 验证生成的配置
            loaded_config = config_manager.load_config("custom_xtdata_config")
            assert loaded_config["data_sources"][0]["name"] == "custom_xtdata"
            assert loaded_config["data_sources"][0]["config"]["ip"] == "*************"
            assert loaded_config["data_sources"][0]["config"]["port"] == 8080
            
            # 创建数据源配置对象
            ds_config = config_manager.create_data_source_config(
                name="test_xtdata",
                source_type="xtdata",
                ip="127.0.0.1",
                port=58610,
                timeout=45
            )
            
            assert ds_config.name == "test_xtdata"
            assert ds_config.timeout == 45
            assert ds_config.config["ip"] == "127.0.0.1"
            assert ds_config.config["port"] == 58610
            
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, xtdata_adapter):
        """性能基准测试"""
        try:
            # 连接到服务
            connected = await xtdata_adapter.connect()
            if not connected:
                pytest.skip("无法连接到XtData服务")
            
            # 准备测试数据
            test_symbols = [
                "000001.SZ", "000002.SZ", "000858.SZ", "002415.SZ",
                "600000.SH", "600036.SH", "600519.SH", "000858.SZ"
            ]
            
            # 单次请求性能测试
            start_time = datetime.now()
            market_data = await xtdata_adapter.get_market_data(test_symbols)
            end_time = datetime.now()
            
            single_request_time = (end_time - start_time).total_seconds()
            print(f"单次请求时间: {single_request_time:.3f}秒")
            print(f"获取股票数量: {len(market_data)}")
            print(f"平均每股票时间: {single_request_time/len(market_data):.3f}秒")
            
            # 批量请求性能测试
            batch_count = 5
            batch_start_time = datetime.now()
            
            for i in range(batch_count):
                await xtdata_adapter.get_market_data(test_symbols[:4])  # 每次请求4只股票
            
            batch_end_time = datetime.now()
            batch_total_time = (batch_end_time - batch_start_time).total_seconds()
            
            print(f"批量请求总时间: {batch_total_time:.3f}秒")
            print(f"平均每次请求时间: {batch_total_time/batch_count:.3f}秒")
            
            # 性能断言
            assert single_request_time < 10.0  # 单次请求应在10秒内完成
            assert batch_total_time / batch_count < 5.0  # 平均每次请求应在5秒内完成
            
            # 断开连接
            await xtdata_adapter.disconnect()
            
        except Exception as e:
            pytest.skip(f"性能测试失败: {e}")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_error_handling(self, xtdata_adapter):
        """错误处理测试"""
        try:
            # 测试未连接时的操作
            with pytest.raises(Exception):  # 应该抛出连接异常
                await xtdata_adapter.get_market_data(["000001.SZ"])
            
            # 连接到服务
            connected = await xtdata_adapter.connect()
            if not connected:
                pytest.skip("无法连接到XtData服务")
            
            # 测试无效股票代码
            invalid_symbols = ["INVALID.XX", "NONEXISTENT.YY"]
            market_data = await xtdata_adapter.get_market_data(invalid_symbols)
            
            # 应该返回空列表或者有效的错误处理
            assert isinstance(market_data, list)
            # 无效股票可能返回空数据或者被过滤掉
            
            # 测试空股票列表
            empty_data = await xtdata_adapter.get_market_data([])
            assert isinstance(empty_data, list)
            assert len(empty_data) == 0
            
            # 断开连接
            await xtdata_adapter.disconnect()
            
        except Exception as e:
            pytest.skip(f"错误处理测试失败: {e}")


if __name__ == "__main__":
    """
    运行集成测试的说明：
    
    1. 确保本地已安装并运行MiniQMT
    2. 确保XtData服务在127.0.0.1:58610上运行
    3. 运行命令：pytest tests/integration/test_xtdata_integration.py -m integration -v -s
    
    如果没有XtData环境，测试会自动跳过
    """
    print("XtData集成测试")
    print("=" * 50)
    print("运行前请确保：")
    print("1. 已安装MiniQMT")
    print("2. XtData服务正在运行")
    print("3. 服务地址为 127.0.0.1:58610")
    print("=" * 50)
    
    # 可以直接运行单个测试
    pytest.main([__file__, "-m", "integration", "-v", "-s"])