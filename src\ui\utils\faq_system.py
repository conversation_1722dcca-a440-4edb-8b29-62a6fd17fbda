"""
FAQ系统

提供常见问题解答的搜索、分类和动态更新功能
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QListWidget, QListWidgetItem, QTextEdit,
    QComboBox, QSplitter, QFrame, QScrollArea, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon
import json
import os
import re
from ...utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class FAQItem:
    """FAQ条目"""
    id: str                    # 唯一标识
    category: str              # 分类
    question: str              # 问题
    answer: str                # 答案
    keywords: List[str]        # 关键词
    priority: int              # 优先级（数字越大越重要）
    view_count: int            # 查看次数
    helpful_count: int         # 有用评价次数
    last_updated: str          # 最后更新时间


class FAQDatabase:
    """FAQ数据库"""
    
    def __init__(self):
        """初始化FAQ数据库"""
        self.faq_items: Dict[str, FAQItem] = {}
        self.categories: List[str] = []
        self._load_default_faqs()
        
    def _load_default_faqs(self):
        """加载默认FAQ"""
        default_faqs = [
            # 数据源相关
            FAQItem(
                id="ds_001",
                category="数据源",
                question="XtData连接失败，提示模块不可用怎么办？",
                answer="""这个问题通常是因为MiniQMT客户端未正确安装或配置导致的。

解决步骤：
1. 确认已安装MiniQMT客户端
2. 启动MiniQMT并登录您的券商账户
3. 安装xtquant库：pip install xtquant
4. 重启本应用程序

如果问题仍然存在，请检查防火墙设置，确保端口58610未被阻止。""",
                keywords=["xtdata", "连接失败", "模块不可用", "miniqmt"],
                priority=10,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            FAQItem(
                id="ds_002",
                category="数据源",
                question="数据刷新很慢或者超时怎么办？",
                answer="""数据刷新慢通常与网络连接或服务器负载有关。

优化建议：
1. 检查网络连接稳定性
2. 在系统设置中增加超时时间
3. 避免在交易时间高峰期刷新
4. 减少同时刷新的股票数量
5. 使用有线网络而非WiFi

如果问题持续，可以尝试重启数据源软件。""",
                keywords=["数据刷新", "超时", "慢", "网络"],
                priority=8,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            
            # 威科夫分析相关
            FAQItem(
                id="wa_001",
                category="威科夫分析",
                question="威科夫分析结果的置信度是什么意思？",
                answer="""置信度表示分析结果的可靠程度，范围从0到100%。

置信度解释：
• 80%以上：高置信度，结果较为可靠
• 60-80%：中等置信度，需要结合其他指标
• 40-60%：低置信度，建议谨慎参考
• 40%以下：极低置信度，不建议依据此结果操作

置信度受数据质量、市场环境、股票特性等因素影响。""",
                keywords=["威科夫", "置信度", "可靠性", "分析结果"],
                priority=9,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            FAQItem(
                id="wa_002",
                category="威科夫分析",
                question="如何理解累积期和派发期？",
                answer="""累积期和派发期是威科夫理论的核心概念。

累积期特征：
• 价格在相对低位横盘整理
• 成交量逐渐放大
• 主力资金悄悄收集筹码
• 适合逢低买入

派发期特征：
• 价格在相对高位震荡
• 成交量不规律变化
• 主力资金逐步出货
• 建议谨慎操作或减仓

识别这些阶段有助于把握买卖时机。""",
                keywords=["累积期", "派发期", "威科夫理论", "市场阶段"],
                priority=9,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            
            # 相对强弱分析相关
            FAQItem(
                id="rs_001",
                category="相对强弱分析",
                question="相对强弱值如何计算和理解？",
                answer="""相对强弱(RS)值衡量个股相对于大盘的表现。

计算公式：RS = 个股涨幅 ÷ 大盘涨幅

数值含义：
• RS > 1.2：强势股，明显跑赢大盘
• 1.0 < RS < 1.2：相对强势
• 0.8 < RS < 1.0：相对弱势
• RS < 0.8：弱势股，明显跑输大盘

建议在牛市中选择RS值较高的股票，在熊市中避开RS值较低的股票。""",
                keywords=["相对强弱", "rs值", "计算", "强势股"],
                priority=8,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            
            # 智能选股相关
            FAQItem(
                id="ss_001",
                category="智能选股",
                question="智能选股没有结果或结果很少怎么办？",
                answer="""选股结果少通常是因为筛选条件过于严格。

调整建议：
1. 放宽威科夫策略要求
2. 降低相对强弱阈值
3. 增加风险容忍度
4. 扩大市值范围
5. 减少必要条件数量

建议从宽松条件开始，逐步收紧筛选标准，找到合适的平衡点。""",
                keywords=["智能选股", "无结果", "筛选条件", "参数调整"],
                priority=7,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            
            # 系统使用相关
            FAQItem(
                id="sys_001",
                category="系统使用",
                question="软件运行缓慢或卡顿怎么办？",
                answer="""系统性能问题可能由多种原因导致。

优化方法：
1. 关闭其他占用内存的程序
2. 减少同时分析的股票数量
3. 清理系统临时文件
4. 重启应用程序
5. 检查硬盘空间是否充足

如果问题持续，建议升级硬件配置或联系技术支持。""",
                keywords=["运行缓慢", "卡顿", "性能", "优化"],
                priority=6,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            ),
            FAQItem(
                id="sys_002",
                category="系统使用",
                question="如何备份和恢复设置？",
                answer="""系统设置可以通过以下方式备份和恢复。

备份设置：
1. 进入系统管理→设置管理
2. 点击"导出设置"按钮
3. 选择保存位置和文件名
4. 确认导出完成

恢复设置：
1. 进入系统管理→设置管理
2. 点击"导入设置"按钮
3. 选择之前备份的设置文件
4. 确认导入并重启应用

建议定期备份重要设置。""",
                keywords=["备份", "恢复", "设置", "导入导出"],
                priority=5,
                view_count=0,
                helpful_count=0,
                last_updated="2024-01-15"
            )
        ]
        
        # 添加到数据库
        for faq in default_faqs:
            self.faq_items[faq.id] = faq
            if faq.category not in self.categories:
                self.categories.append(faq.category)
                
        logger.info(f"加载了{len(default_faqs)}个默认FAQ")
        
    def search_faqs(self, query: str, category: Optional[str] = None) -> List[FAQItem]:
        """
        搜索FAQ
        
        Args:
            query: 搜索关键词
            category: 分类筛选
            
        Returns:
            匹配的FAQ列表
        """
        results = []
        query_lower = query.lower()
        
        for faq in self.faq_items.values():
            # 分类筛选
            if category and faq.category != category:
                continue
                
            # 关键词匹配
            score = 0
            
            # 问题标题匹配（权重最高）
            if query_lower in faq.question.lower():
                score += 10
                
            # 答案内容匹配
            if query_lower in faq.answer.lower():
                score += 5
                
            # 关键词匹配
            for keyword in faq.keywords:
                if query_lower in keyword.lower():
                    score += 3
                    
            # 优先级加分
            score += faq.priority * 0.1
            
            if score > 0:
                results.append((score, faq))
                
        # 按分数排序
        results.sort(key=lambda x: x[0], reverse=True)
        return [faq for score, faq in results]
        
    def get_faqs_by_category(self, category: str) -> List[FAQItem]:
        """获取指定分类的FAQ"""
        return [faq for faq in self.faq_items.values() if faq.category == category]
        
    def get_popular_faqs(self, limit: int = 10) -> List[FAQItem]:
        """获取热门FAQ"""
        faqs = list(self.faq_items.values())
        faqs.sort(key=lambda x: (x.view_count, x.helpful_count, x.priority), reverse=True)
        return faqs[:limit]
        
    def increment_view_count(self, faq_id: str):
        """增加查看次数"""
        if faq_id in self.faq_items:
            self.faq_items[faq_id].view_count += 1
            
    def increment_helpful_count(self, faq_id: str):
        """增加有用评价"""
        if faq_id in self.faq_items:
            self.faq_items[faq_id].helpful_count += 1


class FAQDialog(QDialog):
    """FAQ对话框"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化FAQ对话框
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        self.faq_db = FAQDatabase()
        self.current_faq: Optional[FAQItem] = None
        
        self._init_ui()
        self._apply_styles()
        self._load_popular_faqs()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("常见问题解答")
        self.setFixedSize(800, 600)
        self.setModal(False)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        search_label = QLabel("搜索问题:")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索...")
        self.search_input.textChanged.connect(self._on_search_text_changed)
        search_layout.addWidget(self.search_input, 1)
        
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部分类")
        self.category_combo.addItems(self.faq_db.categories)
        self.category_combo.currentTextChanged.connect(self._on_category_changed)
        search_layout.addWidget(self.category_combo)
        
        layout.addLayout(search_layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 问题列表
        self.question_list = QListWidget()
        self.question_list.itemClicked.connect(self._on_question_selected)
        splitter.addWidget(self.question_list)
        
        # 答案显示区域
        answer_frame = QFrame()
        answer_layout = QVBoxLayout(answer_frame)
        
        self.answer_title = QLabel("选择一个问题查看答案")
        self.answer_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        answer_layout.addWidget(self.answer_title)
        
        self.answer_content = QTextEdit()
        self.answer_content.setReadOnly(True)
        answer_layout.addWidget(self.answer_content)
        
        # 有用按钮
        helpful_layout = QHBoxLayout()
        helpful_layout.addStretch()
        
        self.helpful_button = QPushButton("👍 这个答案有用")
        self.helpful_button.clicked.connect(self._on_helpful_clicked)
        self.helpful_button.setEnabled(False)
        helpful_layout.addWidget(self.helpful_button)
        
        answer_layout.addLayout(helpful_layout)
        
        splitter.addWidget(answer_frame)
        splitter.setSizes([300, 500])
        
        layout.addWidget(splitter)
        
        # 关闭按钮
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        close_layout.addWidget(close_button)
        
        layout.addLayout(close_layout)
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
            }
            QListWidget {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
                font-size: 14px;
                line-height: 1.6;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        
    def _load_popular_faqs(self):
        """加载热门FAQ"""
        popular_faqs = self.faq_db.get_popular_faqs()
        self._update_question_list(popular_faqs)
        
    def _update_question_list(self, faqs: List[FAQItem]):
        """更新问题列表"""
        self.question_list.clear()
        
        for faq in faqs:
            item = QListWidgetItem(faq.question)
            item.setData(Qt.ItemDataRole.UserRole, faq)
            self.question_list.addItem(item)
            
    def _on_search_text_changed(self, text: str):
        """搜索文本变化"""
        if len(text) >= 2:  # 至少2个字符才搜索
            category = self.category_combo.currentText()
            if category == "全部分类":
                category = None
            results = self.faq_db.search_faqs(text, category)
            self._update_question_list(results)
        elif len(text) == 0:
            self._load_popular_faqs()
            
    def _on_category_changed(self, category: str):
        """分类变化"""
        if category == "全部分类":
            self._load_popular_faqs()
        else:
            faqs = self.faq_db.get_faqs_by_category(category)
            self._update_question_list(faqs)
            
    def _on_question_selected(self, item: QListWidgetItem):
        """问题选择"""
        faq = item.data(Qt.ItemDataRole.UserRole)
        if faq:
            self.current_faq = faq
            self.answer_title.setText(faq.question)
            self.answer_content.setPlainText(faq.answer)
            self.helpful_button.setEnabled(True)
            
            # 增加查看次数
            self.faq_db.increment_view_count(faq.id)
            
    def _on_helpful_clicked(self):
        """有用按钮点击"""
        if self.current_faq:
            self.faq_db.increment_helpful_count(self.current_faq.id)
            self.helpful_button.setText("👍 感谢反馈")
            self.helpful_button.setEnabled(False)


class FAQManager:
    """FAQ管理器"""
    
    def __init__(self, main_window):
        """
        初始化FAQ管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.faq_dialog: Optional[FAQDialog] = None
        
    def show_faq_dialog(self):
        """显示FAQ对话框"""
        try:
            if self.faq_dialog is None:
                self.faq_dialog = FAQDialog(self.main_window)
                self.faq_dialog.finished.connect(self._on_faq_dialog_closed)
                
            self.faq_dialog.show()
            self.faq_dialog.raise_()
            self.faq_dialog.activateWindow()
            
            logger.info("显示FAQ对话框")
            
        except Exception as e:
            logger.error(f"显示FAQ对话框失败: {e}")
            
    def _on_faq_dialog_closed(self):
        """FAQ对话框关闭"""
        self.faq_dialog = None
