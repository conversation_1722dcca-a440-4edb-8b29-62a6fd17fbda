"""
日志查看器控件

提供系统日志查看和管理功能
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QComboBox, QCheckBox, QTextEdit,
    QLineEdit, QDateEdit, QSplitter, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt6.QtGui import QFont, QColor, QTextCharFormat, QTextCursor

from ...utils.logger import get_logger
import os
from datetime import datetime, timedelta

logger = get_logger(__name__)


class LogViewerWidget(QWidget):
    """日志查看器控件"""
    
    # 信号定义
    log_selected = pyqtSignal(dict)  # 日志条目选中
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化日志查看器控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 日志数据
        self.log_entries: List[Dict[str, Any]] = []
        self.filtered_entries: List[Dict[str, Any]] = []
        
        # 控件引用
        self.log_table: Optional[QTableWidget] = None
        self.log_detail: Optional[QTextEdit] = None
        self.level_filter: Optional[QComboBox] = None
        self.search_input: Optional[QLineEdit] = None
        self.date_filter: Optional[QDateEdit] = None
        self.auto_refresh: Optional[QCheckBox] = None
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_logs)
        
        self._init_ui()
        self._load_logs()
        
        logger.info("日志查看器控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📝 系统日志查看器")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 过滤控制区域
        filter_group = QGroupBox("过滤条件")
        filter_layout = QHBoxLayout(filter_group)
        
        # 日志级别过滤
        filter_layout.addWidget(QLabel("级别:"))
        self.level_filter = QComboBox()
        self.level_filter.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_filter.currentTextChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.level_filter)
        
        # 日期过滤
        filter_layout.addWidget(QLabel("日期:"))
        self.date_filter = QDateEdit()
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        self.date_filter.dateChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.date_filter)
        
        # 搜索框
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索日志内容...")
        self.search_input.textChanged.connect(self._apply_filters)
        filter_layout.addWidget(self.search_input)
        
        # 自动刷新
        self.auto_refresh = QCheckBox("自动刷新")
        self.auto_refresh.toggled.connect(self._toggle_auto_refresh)
        filter_layout.addWidget(self.auto_refresh)
        
        # 控制按钮
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self._refresh_logs)
        filter_layout.addWidget(refresh_button)
        
        clear_button = QPushButton("🗑️ 清空")
        clear_button.clicked.connect(self._clear_logs)
        filter_layout.addWidget(clear_button)
        
        export_button = QPushButton("📤 导出")
        export_button.clicked.connect(self._export_logs)
        filter_layout.addWidget(export_button)
        
        layout.addWidget(filter_group)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 日志列表
        self.log_table = QTableWidget()
        self._setup_log_table()
        splitter.addWidget(self.log_table)
        
        # 日志详情
        detail_group = QGroupBox("日志详情")
        detail_layout = QVBoxLayout(detail_group)
        
        self.log_detail = QTextEdit()
        self.log_detail.setReadOnly(True)
        self.log_detail.setFont(QFont("Consolas", 9))
        detail_layout.addWidget(self.log_detail)
        
        splitter.addWidget(detail_group)
        
        # 设置分割比例
        splitter.setSizes([400, 200])
        
        layout.addWidget(splitter)
    
    def _setup_log_table(self):
        """设置日志表格"""
        if not self.log_table:
            return
        
        # 设置列
        columns = ["时间", "级别", "模块", "消息"]
        self.log_table.setColumnCount(len(columns))
        self.log_table.setHorizontalHeaderLabels(columns)
        
        # 表格属性
        self.log_table.setAlternatingRowColors(True)
        self.log_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.log_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.log_table.setSortingEnabled(True)
        
        # 列宽设置
        header = self.log_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)   # 时间
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)   # 级别
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)   # 模块
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch) # 消息
        
        self.log_table.setColumnWidth(0, 150)  # 时间
        self.log_table.setColumnWidth(1, 80)   # 级别
        self.log_table.setColumnWidth(2, 200)  # 模块
        
        # 连接选择事件
        self.log_table.itemSelectionChanged.connect(self._on_log_selected)
    
    def _load_logs(self):
        """加载日志"""
        try:
            # 生成示例日志数据
            self.log_entries = self._generate_sample_logs()
            self.filtered_entries = self.log_entries.copy()
            self._update_log_table()
            
        except Exception as e:
            logger.error(f"加载日志失败: {e}")
    
    def _generate_sample_logs(self) -> List[Dict[str, Any]]:
        """生成示例日志数据"""
        import random
        
        sample_logs = []
        levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
        modules = [
            "src.engines.wyckoff", "src.engines.relative_strength", 
            "src.engines.selection", "src.ui.main_window",
            "src.services.data_service", "src.utils.cache"
        ]
        messages = [
            "威科夫分析引擎初始化完成",
            "相对强弱计算引擎初始化完成",
            "选股策略引擎初始化完成",
            "主窗口初始化完成",
            "数据服务初始化完成",
            "缓存管理器初始化完成",
            "股票数据加载完成",
            "分析结果已更新",
            "选股任务已启动",
            "系统设置已应用",
            "缓存已清空",
            "配置文件加载失败",
            "网络连接超时",
            "数据解析错误"
        ]
        
        # 生成最近24小时的日志
        now = datetime.now()
        for i in range(100):
            log_time = now - timedelta(minutes=random.randint(0, 1440))
            level = random.choice(levels)
            module = random.choice(modules)
            message = random.choice(messages)
            
            sample_logs.append({
                'timestamp': log_time,
                'level': level,
                'module': module,
                'message': message,
                'details': f"详细信息: {message}\n文件: {module}.py\n行号: {random.randint(10, 500)}"
            })
        
        # 按时间排序
        sample_logs.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return sample_logs
    
    def _update_log_table(self):
        """更新日志表格"""
        if not self.log_table:
            return
        
        self.log_table.setRowCount(len(self.filtered_entries))
        
        for row, entry in enumerate(self.filtered_entries):
            # 时间
            time_item = QTableWidgetItem(entry['timestamp'].strftime("%Y-%m-%d %H:%M:%S"))
            self.log_table.setItem(row, 0, time_item)
            
            # 级别
            level_item = QTableWidgetItem(entry['level'])
            level_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置级别颜色
            level_colors = {
                'DEBUG': QColor("#9E9E9E"),
                'INFO': QColor("#2196F3"),
                'WARNING': QColor("#FF9800"),
                'ERROR': QColor("#F44336"),
                'CRITICAL': QColor("#9C27B0")
            }
            
            if entry['level'] in level_colors:
                level_item.setForeground(level_colors[entry['level']])
            
            self.log_table.setItem(row, 1, level_item)
            
            # 模块
            module_item = QTableWidgetItem(entry['module'])
            self.log_table.setItem(row, 2, module_item)
            
            # 消息
            message_item = QTableWidgetItem(entry['message'])
            self.log_table.setItem(row, 3, message_item)
    
    def _apply_filters(self):
        """应用过滤条件"""
        level_filter = self.level_filter.currentText()
        date_filter = self.date_filter.date().toPython()
        search_text = self.search_input.text().lower()
        
        self.filtered_entries = []
        
        for entry in self.log_entries:
            # 级别过滤
            if level_filter != "全部" and entry['level'] != level_filter:
                continue
            
            # 日期过滤
            if entry['timestamp'].date() != date_filter:
                continue
            
            # 搜索过滤
            if search_text and search_text not in entry['message'].lower():
                continue
            
            self.filtered_entries.append(entry)
        
        self._update_log_table()
    
    def _on_log_selected(self):
        """日志选择事件"""
        current_row = self.log_table.currentRow()
        if 0 <= current_row < len(self.filtered_entries):
            entry = self.filtered_entries[current_row]
            self.log_detail.setText(entry['details'])
            self.log_selected.emit(entry)
    
    def _toggle_auto_refresh(self, enabled: bool):
        """切换自动刷新"""
        if enabled:
            self.refresh_timer.start(5000)  # 每5秒刷新
        else:
            self.refresh_timer.stop()
    
    def _refresh_logs(self):
        """刷新日志"""
        self._load_logs()
    
    def _clear_logs(self):
        """清空日志"""
        self.log_entries.clear()
        self.filtered_entries.clear()
        self._update_log_table()
        self.log_detail.clear()
    
    def _export_logs(self):
        """导出日志"""
        logger.info("导出日志功能开发中...")
    
    # 公共方法
    def add_log_entry(self, level: str, module: str, message: str):
        """添加日志条目"""
        entry = {
            'timestamp': datetime.now(),
            'level': level,
            'module': module,
            'message': message,
            'details': f"详细信息: {message}\n模块: {module}\n时间: {datetime.now()}"
        }
        
        self.log_entries.insert(0, entry)  # 插入到开头
        
        # 限制日志数量
        if len(self.log_entries) > 1000:
            self.log_entries = self.log_entries[:1000]
        
        self._apply_filters()
    
    def clear_logs(self):
        """清空所有日志"""
        self._clear_logs()
    
    def get_log_count(self) -> int:
        """获取日志数量"""
        return len(self.log_entries)
