#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XtData历史数据获取测试脚本

测试XtData API的历史数据获取功能
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import xtquant.xtdata as xt
    print("✅ 成功导入xtquant库")
except ImportError as e:
    print(f"❌ 导入xtquant库失败: {e}")
    sys.exit(1)


def test_connection():
    """测试连接"""
    try:
        print("📡 正在连接XtData...")

        # 查看可用的方法
        print("可用方法:")
        methods = [method for method in dir(xt) if not method.startswith('_')]
        for method in methods[:10]:  # 显示前10个方法
            print(f"  - {method}")

        # 尝试不同的连接方法
        try:
            # 方法1: 直接获取股票列表
            stock_list = xt.get_stock_list_in_sector('沪深A股')
            print(f"✅ 直接获取股票列表成功: {len(stock_list)} 只股票")
            return stock_list[:5]
        except Exception as e1:
            print(f"方法1失败: {e1}")

        try:
            # 方法2: 尝试其他连接方式
            if hasattr(xt, 'run'):
                xt.run()
                print("✅ 使用run()方法连接成功")

            stock_list = xt.get_stock_list_in_sector('沪深A股')
            print(f"✅ 获取到 {len(stock_list)} 只股票")
            return stock_list[:5]
        except Exception as e2:
            print(f"方法2失败: {e2}")

        return None

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None


def test_history_data(symbol):
    """测试历史数据获取"""
    try:
        print(f"\n🔍 测试获取 {symbol} 的历史数据...")
        
        # 设置日期范围（最近30天）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"日期范围: {start_date} - {end_date}")
        
        # 方法1：使用get_market_data
        print("方法1: get_market_data")
        data1 = xt.get_market_data(
            stock_list=[symbol],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if data1 and symbol in data1:
            raw_data = data1[symbol]
            print(f"✅ 方法1成功: 获取到 {len(raw_data.get('time', []))} 条记录")
            print(f"数据字段: {list(raw_data.keys())}")
            if raw_data.get('time'):
                print(f"时间范围: {raw_data['time'][0]} - {raw_data['time'][-1]}")
        else:
            print("❌ 方法1失败: 未获取到数据")
        
        # 方法2：使用get_market_data_ex
        print("\n方法2: get_market_data_ex")
        try:
            data2 = xt.get_market_data_ex(
                stock_list=[symbol],
                period='1d',
                start_time=start_date,
                end_time=end_date,
                count=-1,
                dividend_type='none',
                fill_data=True
            )
            
            if data2 and symbol in data2:
                print(f"✅ 方法2成功: 获取到数据")
                print(f"数据类型: {type(data2[symbol])}")
            else:
                print("❌ 方法2失败: 未获取到数据")
        except Exception as e:
            print(f"❌ 方法2异常: {e}")
        
        # 方法3：使用get_local_data（如果有本地数据）
        print("\n方法3: get_local_data")
        try:
            data3 = xt.get_local_data(
                stock_list=[symbol],
                period='1d',
                start_time=start_date,
                end_time=end_date,
                count=-1,
                dividend_type='none',
                fill_data=True
            )
            
            if data3 and symbol in data3:
                print(f"✅ 方法3成功: 获取到数据")
            else:
                print("❌ 方法3失败: 未获取到数据")
        except Exception as e:
            print(f"❌ 方法3异常: {e}")
        
        return data1
        
    except Exception as e:
        print(f"❌ 测试历史数据失败: {e}")
        return None


def test_realtime_data(symbol):
    """测试实时数据获取"""
    try:
        print(f"\n📊 测试获取 {symbol} 的实时数据...")
        
        # 获取最新行情
        data = xt.get_full_tick([symbol])
        
        if data and symbol in data:
            tick_data = data[symbol]
            print(f"✅ 实时数据成功: {tick_data}")
        else:
            print("❌ 实时数据失败: 未获取到数据")
        
        return data
        
    except Exception as e:
        print(f"❌ 测试实时数据失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 XtData历史数据获取测试")
    print("=" * 50)
    
    # 测试连接
    stock_list = test_connection()
    if not stock_list:
        print("❌ 连接失败，退出测试")
        return
    
    # 选择测试股票
    test_symbols = stock_list[:3]  # 测试前3只股票
    print(f"\n📋 测试股票: {test_symbols}")
    
    for symbol in test_symbols:
        print(f"\n{'='*60}")
        print(f"测试股票: {symbol}")
        print(f"{'='*60}")
        
        # 测试历史数据
        history_data = test_history_data(symbol)
        
        # 测试实时数据
        realtime_data = test_realtime_data(symbol)
        
        # 如果历史数据获取成功，显示详细信息
        if history_data and symbol in history_data:
            raw_data = history_data[symbol]
            print(f"\n📈 详细数据分析:")
            for key, value in raw_data.items():
                if isinstance(value, list):
                    print(f"  {key}: {len(value)} 条记录")
                    if value:
                        print(f"    首条: {value[0]}")
                        print(f"    末条: {value[-1]}")
                else:
                    print(f"  {key}: {value}")
    
    print(f"\n✅ 测试完成")


if __name__ == "__main__":
    main()
