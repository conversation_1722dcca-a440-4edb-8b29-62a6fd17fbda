"""
第14周第3天功能测试

测试错误消息优化、操作成功反馈、帮助文档集成、FAQ系统等功能
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.ui.utils.user_friendly_errors import (
    UserFriendlyErrorTranslator, UserFriendlyErrorDialog, 
    ErrorSeverity, error_translator
)
from src.ui.utils.operation_feedback import (
    OperationFeedbackManager, OperationProgressTracker, 
    OperationResult, create_data_refresh_result
)
from src.ui.utils.faq_system import FAQDatabase, FAQManager, FAQDialog
from src.ui.utils.context_help import CONTEXT_HELP_CONTENT
from src.utils.exceptions import DataSourceError, CalculationError


class TestUserFriendlyErrors(unittest.TestCase):
    """用户友好错误系统测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_error_translator_initialization(self):
        """测试错误翻译器初始化"""
        translator = UserFriendlyErrorTranslator()
        
        self.assertIsNotNone(translator.error_patterns)
        self.assertIsNotNone(translator.error_solutions)
        self.assertGreater(len(translator.error_patterns), 0)
        self.assertGreater(len(translator.error_solutions), 0)
        
    def test_xtdata_error_translation(self):
        """测试XtData错误翻译"""
        error = DataSourceError("XtData模块不可用，请检查MiniQMT安装")
        
        friendly_error = error_translator.translate_error(error)
        
        self.assertEqual(friendly_error.title, "XtData数据源连接失败")
        self.assertIn("MiniQMT", friendly_error.description)
        self.assertEqual(friendly_error.severity, ErrorSeverity.ERROR)
        self.assertGreater(len(friendly_error.solutions), 0)
        
        # 验证解决方案内容
        for solution in friendly_error.solutions:
            self.assertIsNotNone(solution.title)
            self.assertGreater(len(solution.steps), 0)
            
    def test_network_timeout_error_translation(self):
        """测试网络超时错误翻译"""
        error = DataSourceError("连接超时")
        
        friendly_error = error_translator.translate_error(error)
        
        self.assertEqual(friendly_error.title, "网络连接超时")
        self.assertEqual(friendly_error.severity, ErrorSeverity.WARNING)
        self.assertGreater(len(friendly_error.solutions), 0)
        
    def test_generic_error_translation(self):
        """测试通用错误翻译"""
        error = ValueError("未知的计算错误")
        
        friendly_error = error_translator.translate_error(error)
        
        self.assertIn("操作失败", friendly_error.title)
        self.assertEqual(friendly_error.severity, ErrorSeverity.ERROR)
        self.assertGreater(len(friendly_error.solutions), 0)
        
    def test_error_pattern_matching(self):
        """测试错误模式匹配"""
        translator = UserFriendlyErrorTranslator()
        
        # 测试XtData模式
        xtdata_pattern = None
        for pattern in translator.error_patterns:
            if pattern['error_id'] == 'xtdata_not_available':
                xtdata_pattern = pattern
                break
                
        self.assertIsNotNone(xtdata_pattern)
        
        # 测试模式匹配
        self.assertTrue(translator._match_error_pattern(
            "XtData模块不可用", "ConnectionException", xtdata_pattern
        ))
        
    def test_error_solutions_completeness(self):
        """测试错误解决方案完整性"""
        translator = UserFriendlyErrorTranslator()
        
        for error_id, solutions in translator.error_solutions.items():
            with self.subTest(error_id=error_id):
                self.assertGreater(len(solutions), 0)
                
                for solution in solutions:
                    self.assertIsNotNone(solution.title)
                    self.assertGreater(len(solution.steps), 0)
                    # 验证步骤内容不为空
                    for step in solution.steps:
                        self.assertGreater(len(step.strip()), 0)


class TestOperationFeedback(unittest.TestCase):
    """操作反馈系统测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        self.feedback_manager = OperationFeedbackManager(self.main_window)
        
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        
    def test_operation_feedback_manager_creation(self):
        """测试操作反馈管理器创建"""
        self.assertIsNotNone(self.feedback_manager)
        self.assertIsNotNone(self.feedback_manager.progress_tracker)
        self.assertEqual(len(self.feedback_manager.operation_history), 0)
        
    def test_operation_progress_tracking(self):
        """测试操作进度跟踪"""
        tracker = self.feedback_manager.start_operation_tracking("测试操作", 100)
        
        self.assertEqual(tracker.current_operation, "测试操作")
        self.assertEqual(tracker.total_steps, 100)
        self.assertEqual(tracker.current_step, 0)
        
        # 测试进度更新
        tracker.update_progress(50, "进行中...")
        self.assertEqual(tracker.current_step, 50)
        
    def test_operation_result_creation(self):
        """测试操作结果创建"""
        start_time = datetime.now()
        end_time = datetime.now()
        
        result = OperationResult(
            operation_name="测试操作",
            success=True,
            start_time=start_time,
            end_time=end_time,
            data_processed=100,
            results_count=50,
            details={'test': 'value'},
            warnings=['测试警告']
        )
        
        self.assertEqual(result.operation_name, "测试操作")
        self.assertTrue(result.success)
        self.assertEqual(result.data_processed, 100)
        self.assertEqual(result.results_count, 50)
        self.assertGreaterEqual(result.duration, 0)
        
    def test_data_refresh_result_creation(self):
        """测试数据刷新结果创建"""
        result_data = create_data_refresh_result(100, 95, ["部分数据缺失"])
        
        self.assertEqual(result_data['data_processed'], 100)
        self.assertEqual(result_data['results_count'], 95)
        self.assertIn('成功率', result_data['details'])
        self.assertEqual(len(result_data['warnings']), 1)
        
    def test_operation_history_management(self):
        """测试操作历史管理"""
        # 模拟操作完成
        result = OperationResult(
            operation_name="测试操作",
            success=True,
            start_time=datetime.now(),
            end_time=datetime.now(),
            data_processed=100,
            results_count=50,
            details={},
            warnings=[]
        )
        
        self.feedback_manager._on_operation_completed(result)
        
        # 验证历史记录
        history = self.feedback_manager.get_operation_history()
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].operation_name, "测试操作")


class TestFAQSystem(unittest.TestCase):
    """FAQ系统测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.faq_db = FAQDatabase()
        
    def test_faq_database_initialization(self):
        """测试FAQ数据库初始化"""
        self.assertGreater(len(self.faq_db.faq_items), 0)
        self.assertGreater(len(self.faq_db.categories), 0)
        
        # 验证默认分类存在
        expected_categories = ["数据源", "威科夫分析", "相对强弱分析", "智能选股", "系统使用"]
        for category in expected_categories:
            self.assertIn(category, self.faq_db.categories)
            
    def test_faq_search_functionality(self):
        """测试FAQ搜索功能"""
        # 搜索XtData相关问题
        results = self.faq_db.search_faqs("XtData")
        self.assertGreater(len(results), 0)
        
        # 验证搜索结果相关性
        for faq in results:
            self.assertTrue(
                "xtdata" in faq.question.lower() or 
                "xtdata" in faq.answer.lower() or
                any("xtdata" in keyword.lower() for keyword in faq.keywords)
            )
            
    def test_faq_category_filtering(self):
        """测试FAQ分类筛选"""
        # 获取数据源分类的FAQ
        data_source_faqs = self.faq_db.get_faqs_by_category("数据源")
        self.assertGreater(len(data_source_faqs), 0)
        
        # 验证所有结果都属于数据源分类
        for faq in data_source_faqs:
            self.assertEqual(faq.category, "数据源")
            
    def test_faq_popular_items(self):
        """测试热门FAQ获取"""
        popular_faqs = self.faq_db.get_popular_faqs(5)
        self.assertLessEqual(len(popular_faqs), 5)
        
        # 验证按优先级排序
        if len(popular_faqs) > 1:
            for i in range(len(popular_faqs) - 1):
                current_score = (popular_faqs[i].view_count, 
                               popular_faqs[i].helpful_count, 
                               popular_faqs[i].priority)
                next_score = (popular_faqs[i+1].view_count, 
                            popular_faqs[i+1].helpful_count, 
                            popular_faqs[i+1].priority)
                self.assertGreaterEqual(current_score, next_score)
                
    def test_faq_view_count_increment(self):
        """测试FAQ查看次数增加"""
        # 获取第一个FAQ
        faq_id = list(self.faq_db.faq_items.keys())[0]
        original_count = self.faq_db.faq_items[faq_id].view_count
        
        # 增加查看次数
        self.faq_db.increment_view_count(faq_id)
        
        # 验证计数增加
        self.assertEqual(
            self.faq_db.faq_items[faq_id].view_count, 
            original_count + 1
        )
        
    def test_faq_content_quality(self):
        """测试FAQ内容质量"""
        for faq_id, faq in self.faq_db.faq_items.items():
            with self.subTest(faq_id=faq_id):
                # 验证基本字段不为空
                self.assertGreater(len(faq.question), 0)
                self.assertGreater(len(faq.answer), 0)
                self.assertGreater(len(faq.category), 0)
                
                # 验证答案内容充实
                self.assertGreater(len(faq.answer), 50)
                
                # 验证关键词不为空
                self.assertGreater(len(faq.keywords), 0)


class TestHelpDocumentationIntegration(unittest.TestCase):
    """帮助文档集成测试"""
    
    def test_context_help_content_expansion(self):
        """测试上下文帮助内容扩展"""
        # 验证新增的帮助内容
        required_contexts = [
            'main_window', 'stock_data', 'wyckoff_analysis',
            'analysis_results', 'chart_analysis', 'selection_config',
            'system_management'
        ]
        
        for context in required_contexts:
            with self.subTest(context=context):
                self.assertIn(context, CONTEXT_HELP_CONTENT)
                content = CONTEXT_HELP_CONTENT[context]
                
                # 验证内容结构完整
                self.assertIn('title', content)
                self.assertIn('overview', content)
                self.assertIn('steps', content)
                self.assertIn('faq', content)
                self.assertIn('shortcuts', content)
                
                # 验证内容质量
                self.assertGreater(len(content['overview']), 100)
                self.assertGreater(len(content['steps']), 0)
                
    def test_help_content_consistency(self):
        """测试帮助内容一致性"""
        for context, content in CONTEXT_HELP_CONTENT.items():
            with self.subTest(context=context):
                # 验证FAQ格式
                for faq_item in content.get('faq', []):
                    self.assertIn('question', faq_item)
                    self.assertIn('answer', faq_item)
                    self.assertGreater(len(faq_item['question']), 0)
                    self.assertGreater(len(faq_item['answer']), 0)
                    
                # 验证快捷键格式
                for shortcut in content.get('shortcuts', []):
                    self.assertIn('key', shortcut)
                    self.assertIn('description', shortcut)
                    self.assertGreater(len(shortcut['key']), 0)
                    self.assertGreater(len(shortcut['description']), 0)


class TestIntegratedUserExperience(unittest.TestCase):
    """集成用户体验测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        # 模拟XtData连接错误
        error = ConnectionException("XtData模块不可用，请检查MiniQMT安装")
        
        # 翻译错误
        friendly_error = error_translator.translate_error(error)
        
        # 验证用户友好性
        self.assertNotIn("Exception", friendly_error.title)
        self.assertNotIn("Traceback", friendly_error.description)
        self.assertIn("解决", friendly_error.solutions[0].title)
        
    def test_success_feedback_workflow(self):
        """测试成功反馈工作流程"""
        main_window = QMainWindow()
        feedback_manager = OperationFeedbackManager(main_window)
        
        # 开始操作跟踪
        tracker = feedback_manager.start_operation_tracking("数据刷新", 100)
        
        # 更新进度
        tracker.update_progress(50, "正在处理...")
        tracker.update_progress(100, "处理完成")
        
        # 完成操作
        tracker.complete_operation(
            success=True,
            data_processed=1000,
            results_count=950,
            details={'成功率': '95%'}
        )
        
        # 验证历史记录
        history = feedback_manager.get_operation_history()
        self.assertEqual(len(history), 1)
        self.assertTrue(history[0].success)
        
        main_window.close()
        
    def test_help_system_accessibility(self):
        """测试帮助系统可访问性"""
        # 验证所有主要功能都有帮助内容
        main_contexts = ['stock_data', 'wyckoff_analysis', 'selection_config']
        
        for context in main_contexts:
            with self.subTest(context=context):
                self.assertIn(context, CONTEXT_HELP_CONTENT)
                content = CONTEXT_HELP_CONTENT[context]
                
                # 验证操作步骤存在
                self.assertGreater(len(content['steps']), 0)
                
                # 验证FAQ存在
                self.assertGreater(len(content['faq']), 0)


if __name__ == '__main__':
    unittest.main()
