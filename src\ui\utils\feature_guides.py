"""
功能特定操作向导

为威科夫分析、相对强弱分析、智能选股等功能提供专门的操作向导
"""

from typing import Optional, List, Dict, Any, Callable
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QScrollArea, QWidget, QTextEdit, QCheckBox, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QPen
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


class FeatureGuideDialog(QDialog):
    """功能向导对话框基类"""
    
    guide_completed = pyqtSignal(str)  # 功能名称
    
    def __init__(self, feature_name: str, parent: Optional[QWidget] = None):
        """
        初始化功能向导
        
        Args:
            feature_name: 功能名称
            parent: 父控件
        """
        super().__init__(parent)
        self.feature_name = feature_name
        self.current_step = 0
        self.steps = self._create_guide_steps()
        
        self._init_ui()
        self._apply_styles()
        self._show_current_step()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"{self.feature_name} - 操作向导")
        self.setFixedSize(700, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel(f"{self.feature_name}操作向导")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 步骤指示器
        self.step_indicator = QLabel()
        self.step_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.step_indicator)
        
        # 内容区域
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.Shape.Box)
        content_layout = QVBoxLayout(content_frame)
        
        # 步骤标题
        self.step_title = QLabel()
        self.step_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
        """)
        content_layout.addWidget(self.step_title)
        
        # 步骤内容
        self.step_content = QTextEdit()
        self.step_content.setReadOnly(True)
        self.step_content.setMaximumHeight(250)
        self.step_content.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                background: white;
                font-size: 14px;
                line-height: 1.5;
                color: #333;
                padding: 10px;
                border-radius: 4px;
            }
        """)
        content_layout.addWidget(self.step_content)
        
        layout.addWidget(content_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)
        
        button_layout.addStretch()
        
        # 上一步按钮
        self.prev_button = QPushButton("上一步")
        self.prev_button.clicked.connect(self._prev_step)
        button_layout.addWidget(self.prev_button)
        
        # 下一步/完成按钮
        self.next_button = QPushButton("下一步")
        self.next_button.clicked.connect(self._next_step)
        button_layout.addWidget(self.next_button)
        
        layout.addLayout(button_layout)
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 20px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        
    def _create_guide_steps(self) -> List[Dict[str, str]]:
        """创建向导步骤 - 子类需要重写"""
        return []
        
    def _show_current_step(self):
        """显示当前步骤"""
        if 0 <= self.current_step < len(self.steps):
            step = self.steps[self.current_step]
            
            # 更新步骤指示器
            self.step_indicator.setText(f"第 {self.current_step + 1} 步，共 {len(self.steps)} 步")
            
            # 更新内容
            self.step_title.setText(step['title'])
            self.step_content.setPlainText(step['content'])
            
            # 更新按钮状态
            self.prev_button.setEnabled(self.current_step > 0)
            
            if self.current_step == len(self.steps) - 1:
                self.next_button.setText("完成")
            else:
                self.next_button.setText("下一步")
                
    def _prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._show_current_step()
            
    def _next_step(self):
        """下一步"""
        if self.current_step < len(self.steps) - 1:
            self.current_step += 1
            self._show_current_step()
        else:
            self._complete_guide()
            
    def _complete_guide(self):
        """完成向导"""
        self.guide_completed.emit(self.feature_name)
        self.accept()


class WyckoffAnalysisGuide(FeatureGuideDialog):
    """威科夫分析操作向导"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("威科夫分析", parent)
        
    def _create_guide_steps(self) -> List[Dict[str, str]]:
        """创建威科夫分析向导步骤"""
        return [
            {
                'title': '威科夫分析简介',
                'content': '''🎯 威科夫分析是什么？

威科夫分析是一种基于价格和成交量关系的技术分析方法，由理查德·威科夫(Richard Wyckoff)在20世纪初开发。

📊 核心理念：
• 市场由大资金（聪明钱）主导
• 价格变动反映供需关系
• 成交量确认价格走势
• 市场存在可识别的周期性模式

🔍 分析要素：
• 价格行为（Price Action）
• 成交量分析（Volume Analysis）
• 市场结构（Market Structure）
• 供需关系（Supply & Demand）

💡 应用价值：
• 识别市场主力行为
• 判断趋势强弱
• 发现买卖时机
• 评估风险水平'''
            },
            {
                'title': '市场周期识别',
                'content': '''📈 威科夫市场周期四阶段：

1️⃣ 累积阶段（Accumulation）
• 特征：横盘整理，成交量逐渐放大
• 主力行为：悄悄收集筹码
• 价格表现：在支撑位附近震荡
• 成交量：逐步增加，显示吸筹行为

2️⃣ 上涨阶段（Markup）
• 特征：价格突破上涨，成交量配合
• 主力行为：推高股价，吸引跟风
• 价格表现：连续上涨，创新高
• 成交量：初期放量，后期可能缩量

3️⃣ 派发阶段（Distribution）
• 特征：高位震荡，成交量不规律
• 主力行为：逐步出货，维持价格
• 价格表现：在阻力位附近波动
• 成交量：忽大忽小，显示分歧

4️⃣ 下跌阶段（Markdown）
• 特征：价格下跌，成交量萎缩
• 主力行为：已经出货，任由下跌
• 价格表现：持续下跌，创新低
• 成交量：逐步萎缩，缺乏买盘'''
            },
            {
                'title': '如何使用系统进行威科夫分析',
                'content': '''🖥️ 系统操作步骤：

1️⃣ 选择分析标的
• 在"股票数据"页面选择要分析的股票
• 建议选择流通性好、成交活跃的股票
• 避免选择停牌或异常波动的股票

2️⃣ 执行威科夫分析
• 点击工具栏的"威科夫分析"按钮
• 或使用快捷键 Ctrl+W
• 系统会自动分析选中股票的威科夫结构

3️⃣ 查看分析结果
• 切换到"分析结果"标签页
• 查看市场阶段判断
• 观察支撑阻力位标识
• 分析供需关系评估

4️⃣ 结合图表分析
• 在"图表分析"页面查看K线图
• 观察价格和成交量的配合关系
• 识别关键的威科夫信号
• 确认分析结论的可靠性'''
            },
            {
                'title': '分析结果解读',
                'content': '''📊 如何解读威科夫分析结果：

🎯 市场阶段判断
• 累积期：考虑逢低买入
• 上涨期：持有或追涨，注意风险控制
• 派发期：谨慎操作，考虑减仓
• 下跌期：避免买入，等待机会

📈 支撑阻力位
• 支撑位：价格下跌时的潜在买点
• 阻力位：价格上涨时的潜在卖点
• 突破确认：成交量配合的突破更可靠
• 假突破：无量突破往往是陷阱

⚖️ 供需关系
• 供过于求：卖压大，价格承压
• 供需平衡：价格相对稳定
• 供不应求：买盘强，价格上涨
• 极度失衡：可能出现急涨急跌

💡 实战要点
• 结合多个时间周期分析
• 关注成交量的确认作用
• 注意市场情绪和外部因素
• 设置合理的止损止盈位'''
            },
            {
                'title': '注意事项与风险提示',
                'content': '''⚠️ 使用威科夫分析的注意事项：

🎯 分析局限性
• 威科夫分析不是万能的
• 需要结合其他技术指标
• 市场环境变化会影响效果
• 个股特性需要单独考虑

📊 数据质量要求
• 确保数据的准确性和及时性
• 关注除权除息对分析的影响
• 注意特殊事件（重组、停牌等）
• 验证成交量数据的真实性

🔍 实战应用建议
• 从大周期到小周期分析
• 多只股票对比验证
• 结合基本面分析
• 保持客观理性的态度

⚠️ 风险控制
• 设置合理的止损位
• 控制单笔投资比例
• 分散投资降低风险
• 持续学习提高技能

💡 持续改进
• 记录分析过程和结果
• 总结成功和失败的经验
• 关注市场变化调整策略
• 与其他投资者交流学习'''
            }
        ]


class RelativeStrengthGuide(FeatureGuideDialog):
    """相对强弱分析操作向导"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("相对强弱分析", parent)
        
    def _create_guide_steps(self) -> List[Dict[str, str]]:
        """创建相对强弱分析向导步骤"""
        return [
            {
                'title': '相对强弱分析原理',
                'content': '''📊 什么是相对强弱分析？

相对强弱(Relative Strength, RS)分析是比较个股与大盘表现的技术分析方法。

🎯 核心概念：
• RS值 = 个股涨幅 ÷ 大盘涨幅
• RS值 > 1：个股强于大盘
• RS值 < 1：个股弱于大盘
• RS值 = 1：个股与大盘同步

📈 分析意义：
• 识别强势股和弱势股
• 发现资金流向和热点
• 辅助选股和择时决策
• 评估投资组合表现

🔍 应用场景：
• 牛市中选择强势股
• 熊市中避开弱势股
• 震荡市中寻找相对机会
• 行业轮动中把握节奏'''
            },
            {
                'title': '系统RS分析功能',
                'content': '''🖥️ 系统相对强弱分析功能：

📊 RS值计算
• 自动计算个股相对大盘的强弱
• 支持多个时间周期（日、周、月）
• 提供历史RS值变化趋势
• 显示RS值的统计分布

📈 RS排名
• 全市场股票RS值排序
• 分行业RS排名对比
• 强势股和弱势股筛选
• 动态更新排名变化

🎯 RS信号识别
• RS值突破信号
• RS趋势转折点
• 相对强弱背离信号
• 异常RS值预警

📋 分析报告
• 详细的RS分析报告
• 图表化展示RS趋势
• 与同行业股票对比
• 投资建议和风险提示'''
            },
            {
                'title': '如何进行RS分析',
                'content': '''🔍 RS分析操作步骤：

1️⃣ 设置分析参数
• 选择基准指数（沪深300、上证指数等）
• 设定分析时间周期
• 确定RS计算方法
• 配置筛选条件

2️⃣ 执行RS计算
• 点击"相对强弱分析"按钮
• 或使用快捷键 Ctrl+R
• 系统自动计算所有股票的RS值
• 生成RS排名列表

3️⃣ 查看分析结果
• 在"分析结果"页面查看RS值
• 观察RS趋势图表
• 查看RS排名和百分位
• 分析RS值的历史变化

4️⃣ 筛选目标股票
• 根据RS值筛选强势股
• 关注RS值上升的股票
• 避开RS值持续下降的股票
• 结合其他指标确认信号'''
            },
            {
                'title': 'RS分析实战技巧',
                'content': '''💡 RS分析实战应用技巧：

🎯 选股策略
• 选择RS值持续上升的股票
• 关注RS排名前20%的强势股
• 避开RS排名后20%的弱势股
• 重点关注RS值突破的股票

📊 时机把握
• 牛市初期：选择RS值快速上升的股票
• 牛市中期：持有RS值稳定的强势股
• 牛市后期：关注RS值见顶的信号
• 熊市中：避开所有弱势股

🔍 风险控制
• RS值急剧下降时及时止损
• 不要追涨RS值过高的股票
• 注意RS值与价格的背离
• 结合成交量确认RS信号

⚖️ 组合管理
• 构建RS值分散的投资组合
• 定期调整持仓的RS分布
• 平衡不同行业的RS表现
• 动态优化组合的RS水平'''
            }
        ]


class SmartSelectionGuide(FeatureGuideDialog):
    """智能选股操作向导"""
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__("智能选股", parent)
        
    def _create_guide_steps(self) -> List[Dict[str, str]]:
        """创建智能选股向导步骤"""
        return [
            {
                'title': '智能选股系统介绍',
                'content': '''🤖 智能选股系统功能：

智能选股系统结合威科夫分析和相对强弱分析，为您自动筛选优质投资标的。

🎯 核心优势：
• 多维度分析：结合技术面和资金面
• 自动化筛选：减少人工分析工作量
• 量化评分：客观评估投资价值
• 风险控制：内置风险评估机制

📊 分析维度：
• 威科夫市场结构分析
• 相对强弱表现评估
• 技术指标综合评分
• 资金流向分析
• 风险收益比评估

🔍 选股策略：
• 趋势跟踪策略
• 价值发现策略
• 动量投资策略
• 均值回归策略
• 组合优化策略'''
            },
            {
                'title': '配置选股参数',
                'content': '''⚙️ 选股参数配置指南：

1️⃣ 威科夫策略参数
• 市场阶段偏好：累积期/上涨期
• 供需关系要求：供不应求/平衡
• 支撑阻力位强度：强/中/弱
• 成交量确认要求：是/否

2️⃣ 相对强弱参数
• RS值范围：最小值和最大值
• RS排名要求：前N%
• RS趋势方向：上升/下降/平稳
• 时间周期：日/周/月

3️⃣ 风险控制参数
• 最大回撤限制
• 波动率要求
• 流动性门槛
• 市值范围限制

4️⃣ 输出设置
• 选股数量限制
• 评分权重分配
• 排序方式选择
• 报告详细程度'''
            },
            {
                'title': '执行智能选股',
                'content': '''🚀 智能选股执行流程：

1️⃣ 数据准备
• 确保股票数据已更新
• 检查基准指数数据
• 验证技术指标计算
• 确认参数配置正确

2️⃣ 启动选股
• 点击"智能选股"按钮
• 或使用快捷键 Ctrl+S
• 系统开始多维度分析
• 显示实时进度信息

3️⃣ 分析过程
• 威科夫结构分析
• 相对强弱计算
• 技术指标评估
• 风险指标计算
• 综合评分排序

4️⃣ 结果输出
• 生成选股结果列表
• 显示详细评分信息
• 提供分析报告
• 保存历史记录'''
            },
            {
                'title': '解读选股结果',
                'content': '''📋 选股结果解读指南：

🏆 综合评分
• 总分范围：0-100分
• 80分以上：优秀标的
• 60-80分：良好标的
• 60分以下：谨慎考虑

📊 分项得分
• 威科夫得分：市场结构质量
• RS得分：相对强弱表现
• 技术得分：技术指标状态
• 风险得分：风险控制水平

🎯 投资建议
• 强烈推荐：综合条件优秀
• 推荐：大部分条件良好
• 中性：条件一般，需谨慎
• 不推荐：存在明显风险

⚠️ 风险提示
• 注意市场环境变化
• 关注个股基本面
• 设置合理止损位
• 分散投资降低风险'''
            }
        ]


class FeatureGuideManager:
    """功能向导管理器"""
    
    def __init__(self, main_window):
        """
        初始化功能向导管理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.active_guides: Dict[str, FeatureGuideDialog] = {}
        
    def show_wyckoff_guide(self):
        """显示威科夫分析向导"""
        if "wyckoff" not in self.active_guides:
            guide = WyckoffAnalysisGuide(self.main_window)
            guide.guide_completed.connect(self._on_guide_completed)
            self.active_guides["wyckoff"] = guide
            
        self.active_guides["wyckoff"].show()
        
    def show_rs_guide(self):
        """显示相对强弱分析向导"""
        if "rs" not in self.active_guides:
            guide = RelativeStrengthGuide(self.main_window)
            guide.guide_completed.connect(self._on_guide_completed)
            self.active_guides["rs"] = guide
            
        self.active_guides["rs"].show()
        
    def show_selection_guide(self):
        """显示智能选股向导"""
        if "selection" not in self.active_guides:
            guide = SmartSelectionGuide(self.main_window)
            guide.guide_completed.connect(self._on_guide_completed)
            self.active_guides["selection"] = guide
            
        self.active_guides["selection"].show()
        
    def _on_guide_completed(self, feature_name: str):
        """向导完成处理"""
        if feature_name in self.active_guides:
            del self.active_guides[feature_name]
        logger.info(f"用户完成了{feature_name}功能向导")
