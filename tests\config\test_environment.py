"""
环境管理测试用例

测试Environment类的功能：
- 环境变量读取
- 环境检测
- 配置路径管理
- 环境特定设置
"""

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock
from pathlib import Path

from src.config.environment import Environment
from src.utils.exceptions import ConfigurationError


class TestEnvironment:
    """环境管理基础测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 保存原始环境变量
        self.original_env = os.environ.copy()
        
        # 清理测试相关的环境变量
        test_vars = ['XDQR_ENV', 'XDQR_CONFIG_PATH', 'XDQR_DATA_PATH', 'XDQR_LOG_PATH']
        for var in test_vars:
            if var in os.environ:
                del os.environ[var]
    
    def teardown_method(self):
        """测试后清理"""
        # 恢复原始环境变量
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_default_environment(self):
        """测试默认环境"""
        env = Environment()
        assert env.name == 'development'
        assert env.is_development() is True
        assert env.is_production() is False
        assert env.is_testing() is False
    
    def test_environment_from_env_var(self):
        """测试从环境变量读取环境"""
        os.environ['XDQR_ENV'] = 'production'
        env = Environment()
        assert env.name == 'production'
        assert env.is_production() is True
        assert env.is_development() is False
    
    def test_invalid_environment(self):
        """测试无效环境"""
        os.environ['XDQR_ENV'] = 'invalid'
        with pytest.raises(ConfigurationError):
            Environment()
    
    def test_environment_detection_methods(self):
        """测试环境检测方法"""
        # 测试开发环境
        os.environ['XDQR_ENV'] = 'development'
        env = Environment()
        assert env.is_development() is True
        assert env.is_production() is False
        assert env.is_testing() is False
        
        # 测试生产环境
        os.environ['XDQR_ENV'] = 'production'
        env = Environment()
        assert env.is_development() is False
        assert env.is_production() is True
        assert env.is_testing() is False
        
        # 测试测试环境
        os.environ['XDQR_ENV'] = 'testing'
        env = Environment()
        assert env.is_development() is False
        assert env.is_production() is False
        assert env.is_testing() is True


class TestEnvironmentPaths:
    """环境路径管理测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.original_env = os.environ.copy()
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        os.environ.clear()
        os.environ.update(self.original_env)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_paths(self):
        """测试默认路径"""
        env = Environment()
        
        # 检查默认路径
        assert env.config_path.endswith('config.yaml')
        assert env.data_path.endswith('data')
        assert env.log_path.endswith('logs')
        
        # 路径应该是绝对路径
        assert os.path.isabs(env.config_path)
        assert os.path.isabs(env.data_path)
        assert os.path.isabs(env.log_path)
    
    def test_custom_paths_from_env(self):
        """测试从环境变量自定义路径"""
        custom_config = os.path.join(self.temp_dir, 'custom_config.yaml')
        custom_data = os.path.join(self.temp_dir, 'custom_data')
        custom_log = os.path.join(self.temp_dir, 'custom_logs')
        
        os.environ['XDQR_CONFIG_PATH'] = custom_config
        os.environ['XDQR_DATA_PATH'] = custom_data
        os.environ['XDQR_LOG_PATH'] = custom_log
        
        env = Environment()
        
        assert env.config_path == custom_config
        assert env.data_path == custom_data
        assert env.log_path == custom_log
    
    def test_path_creation(self):
        """测试路径创建"""
        custom_data = os.path.join(self.temp_dir, 'new_data_dir')
        custom_log = os.path.join(self.temp_dir, 'new_log_dir')
        
        os.environ['XDQR_DATA_PATH'] = custom_data
        os.environ['XDQR_LOG_PATH'] = custom_log
        
        env = Environment()
        env.ensure_paths_exist()
        
        # 目录应该被创建
        assert os.path.exists(custom_data)
        assert os.path.exists(custom_log)
        assert os.path.isdir(custom_data)
        assert os.path.isdir(custom_log)
    
    def test_get_database_path(self):
        """测试获取数据库路径"""
        env = Environment()
        db_path = env.get_database_path()
        
        assert db_path.endswith('.db')
        assert env.data_path in db_path
    
    def test_get_database_path_custom(self):
        """测试自定义数据库路径"""
        custom_data = os.path.join(self.temp_dir, 'custom_data')
        os.environ['XDQR_DATA_PATH'] = custom_data
        
        env = Environment()
        db_path = env.get_database_path('custom.db')
        
        assert db_path == os.path.join(custom_data, 'custom.db')


class TestEnvironmentConfiguration:
    """环境配置测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.original_env = os.environ.copy()
    
    def teardown_method(self):
        """测试后清理"""
        os.environ.clear()
        os.environ.update(self.original_env)
    
    def test_get_env_var(self):
        """测试获取环境变量"""
        os.environ['TEST_VAR'] = 'test_value'
        env = Environment()
        
        assert env.get_env_var('TEST_VAR') == 'test_value'
        assert env.get_env_var('NONEXISTENT_VAR') is None
        assert env.get_env_var('NONEXISTENT_VAR', 'default') == 'default'
    
    def test_get_env_var_with_type_conversion(self):
        """测试带类型转换的环境变量获取"""
        os.environ['INT_VAR'] = '123'
        os.environ['BOOL_VAR'] = 'true'
        os.environ['FLOAT_VAR'] = '3.14'
        
        env = Environment()
        
        assert env.get_env_var('INT_VAR', var_type=int) == 123
        assert env.get_env_var('BOOL_VAR', var_type=bool) is True
        assert env.get_env_var('FLOAT_VAR', var_type=float) == 3.14
    
    def test_get_env_var_invalid_type_conversion(self):
        """测试无效类型转换"""
        os.environ['INVALID_INT'] = 'not_a_number'
        env = Environment()
        
        # 应该返回默认值或抛出异常
        result = env.get_env_var('INVALID_INT', default=0, var_type=int)
        assert result == 0
    
    def test_environment_specific_config(self):
        """测试环境特定配置"""
        # 开发环境
        os.environ['XDQR_ENV'] = 'development'
        dev_env = Environment()
        dev_config = dev_env.get_environment_config()
        
        assert dev_config['debug'] is True
        assert dev_config['log_level'] == 'DEBUG'
        
        # 生产环境
        os.environ['XDQR_ENV'] = 'production'
        prod_env = Environment()
        prod_config = prod_env.get_environment_config()
        
        assert prod_config['debug'] is False
        assert prod_config['log_level'] == 'INFO'
    
    def test_config_override(self):
        """测试配置覆盖"""
        env = Environment()
        
        base_config = {
            'database': {'pool_size': 10},
            'logging': {'level': 'INFO'}
        }
        
        env_overrides = {
            'database': {'pool_size': 20},
            'new_setting': {'value': 'test'}
        }
        
        merged_config = env.apply_environment_overrides(base_config, env_overrides)
        
        assert merged_config['database']['pool_size'] == 20
        assert merged_config['logging']['level'] == 'INFO'  # 保持原值
        assert merged_config['new_setting']['value'] == 'test'  # 新增


class TestEnvironmentIntegration:
    """环境管理集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.original_env = os.environ.copy()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        os.environ.clear()
        os.environ.update(self.original_env)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_full_environment_setup(self):
        """测试完整环境设置"""
        # 设置环境变量
        os.environ['XDQR_ENV'] = 'testing'
        os.environ['XDQR_DATA_PATH'] = os.path.join(self.temp_dir, 'data')
        os.environ['XDQR_LOG_PATH'] = os.path.join(self.temp_dir, 'logs')
        
        env = Environment()
        
        # 验证环境设置
        assert env.is_testing() is True
        assert env.data_path == os.path.join(self.temp_dir, 'data')
        assert env.log_path == os.path.join(self.temp_dir, 'logs')
        
        # 确保路径存在
        env.ensure_paths_exist()
        assert os.path.exists(env.data_path)
        assert os.path.exists(env.log_path)
        
        # 获取环境特定配置
        config = env.get_environment_config()
        assert config['debug'] is True  # 测试环境应该启用调试
    
    def test_environment_validation(self):
        """测试环境验证"""
        env = Environment()
        
        # 验证应该成功
        assert env.validate() is True
        
        # 模拟无效状态
        env.data_path = '/nonexistent/path'
        assert env.validate() is False
    
    @patch('src.config.environment.os.path.exists')
    def test_path_validation(self, mock_exists):
        """测试路径验证"""
        mock_exists.return_value = False
        
        env = Environment()
        
        # 路径不存在时验证应该失败
        assert env.validate() is False
        
        # 路径存在时验证应该成功
        mock_exists.return_value = True
        assert env.validate() is True


if __name__ == '__main__':
    pytest.main([__file__])
