"""
用户操作向导系统

提供首次使用向导、功能介绍和操作提示功能
"""

from typing import Optional, List, Dict, Any, Callable
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QScrollArea, QWidget, QTextEdit, QCheckBox, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QPen
import json
import os
from ...utils.logger import get_logger

logger = get_logger(__name__)


class GuideStep:
    """向导步骤"""
    
    def __init__(self, title: str, content: str, target_widget: Optional[str] = None, 
                 action: Optional[Callable] = None):
        """
        初始化向导步骤
        
        Args:
            title: 步骤标题
            content: 步骤内容
            target_widget: 目标控件名称
            action: 可选的操作回调
        """
        self.title = title
        self.content = content
        self.target_widget = target_widget
        self.action = action


class WelcomeGuideDialog(QDialog):
    """欢迎向导对话框"""
    
    guide_completed = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化欢迎向导
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        self.current_step = 0
        self.steps = self._create_guide_steps()
        
        self._init_ui()
        self._apply_styles()
        self._show_current_step()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("威科夫相对强弱选股系统 - 使用向导")
        self.setFixedSize(700, 550)  # 增加窗口大小
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # 标题
        title_label = QLabel("欢迎使用威科夫相对强弱选股系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
                margin-bottom: 10px;
                padding: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 步骤指示器
        self.step_indicator = QLabel()
        self.step_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.step_indicator.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin-bottom: 15px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.step_indicator)
        
        # 内容区域 - 使用滚动区域确保内容完整显示
        content_scroll = QScrollArea()
        content_scroll.setWidgetResizable(True)
        content_scroll.setFrameStyle(QFrame.Shape.Box)
        content_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
            }
        """)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)
        
        # 步骤标题
        self.step_title = QLabel()
        self.step_title.setWordWrap(True)
        self.step_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
                padding: 5px;
            }
        """)
        content_layout.addWidget(self.step_title)
        
        # 步骤内容
        self.step_content = QTextEdit()
        self.step_content.setReadOnly(True)
        self.step_content.setMinimumHeight(280)  # 确保有足够的高度
        self.step_content.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.step_content.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                background: white;
                font-size: 14px;
                line-height: 1.6;
                color: #333;
                padding: 15px;
                border-radius: 4px;
            }
        """)
        content_layout.addWidget(self.step_content)
        
        content_scroll.setWidget(content_widget)
        layout.addWidget(content_scroll)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 跳过按钮
        self.skip_button = QPushButton("跳过向导")
        self.skip_button.setMinimumSize(100, 35)
        self.skip_button.clicked.connect(self._skip_guide)
        button_layout.addWidget(self.skip_button)
        
        button_layout.addStretch()
        
        # 上一步按钮
        self.prev_button = QPushButton("上一步")
        self.prev_button.setMinimumSize(100, 35)
        self.prev_button.clicked.connect(self._prev_step)
        button_layout.addWidget(self.prev_button)
        
        # 下一步/完成按钮
        self.next_button = QPushButton("下一步")
        self.next_button.setMinimumSize(100, 35)
        self.next_button.clicked.connect(self._next_step)
        button_layout.addWidget(self.next_button)
        
        layout.addLayout(button_layout)
        
        # 不再显示选项
        self.dont_show_again = QCheckBox("不再显示此向导")
        self.dont_show_again.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }
        """)
        layout.addWidget(self.dont_show_again)
        
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #999;
            }
        """)
        
    def _create_guide_steps(self) -> List[GuideStep]:
        """创建向导步骤"""
        return [
            GuideStep(
                "欢迎使用威科夫相对强弱选股系统",
                """🎉 欢迎使用威科夫相对强弱选股系统！

本系统是基于威科夫理论和相对强弱分析的专业股票分析工具，帮助您：

📊 核心功能：
• 威科夫市场结构分析 - 识别累积和派发阶段
• 相对强弱计算和排名 - 找出强势股票
• 智能选股策略 - 多维度筛选优质标的
• 实时数据监控 - 获取最新市场信息
• 系统性能管理 - 确保稳定运行

🚀 开始使用：
接下来的几个步骤将带您快速了解系统的主要功能和使用方法。

💡 提示：您可以随时通过"帮助"菜单重新打开此向导。

⚠️ 重要提醒：
首次使用前，请务必完成数据源配置，这是系统正常运行的基础。"""
            ),
            GuideStep(
                "第一步：数据源配置（重要）",
                """🔌 数据源配置是使用系统的第一步，也是最重要的一步！

📋 配置步骤：
1. 点击"系统管理"标签页
2. 选择"数据源配置"子标签
3. 配置 XtData 数据源连接

🛠️ XtData 配置要求：
• 确保已安装 xtquant 库：pip install xtquant
• 配置正确的服务器地址：通常为 127.0.0.1
• 设置正确的端口号：默认为 58610
• 确保 XtData 客户端正在运行

📊 数据获取功能：
• 获取所有个股基本信息
• 获取行业板块和概念板块数据
• 获取开盘价、最高价、最低价、收盘价
• 获取涨跌幅和主力资金净额数据
• 支持自定义时间范围（最近一年或用户指定）

🔍 连接测试：
配置完成后，请点击"测试连接"按钮验证配置是否正确。

⚠️ 常见问题：
• 如果连接失败，请检查 XtData 客户端是否正在运行
• 确认防火墙设置允许相关端口通信
• 检查 xtquant 库是否正确安装

💡 提示：
只有数据源配置成功后，系统的其他功能才能正常使用。"""
            ),
            GuideStep(
                "第二步：股票数据浏览",
                """📈 在"股票分析"标签页中，您可以进行以下操作：

🔍 数据浏览：
• 查看所有可用股票列表（A股主要标的）
• 使用搜索框快速找到目标股票
• 通过市场筛选器（沪市/深市）过滤股票
• 查看股票的基本信息（代码、名称、市场）

🔄 数据更新：
• 点击"刷新"按钮获取最新股票列表
• 系统会自动缓存数据以提高性能
• 建议每日开始使用前先刷新一次

📊 数据选择：
• 单击任意股票行选中该股票
• 选中的股票会在其他标签页中显示分析结果
• 支持键盘上下键快速选择

💡 使用技巧：
• 首次使用建议先点击刷新按钮加载数据
• 可以通过股票代码或名称快速搜索
• 选择活跃度高的股票进行分析效果更好

🎯 数据来源：
• 通过 XtData 获取实时股票数据
• 支持获取历史数据和实时行情
• 数据包括基本面和技术面信息"""
            ),
            GuideStep(
                "第三步：威科夫分析",
                """📊 威科夫分析功能帮助您理解市场结构和主力行为：

🔍 核心理论：
• 威科夫理论基于供需关系分析市场
• 识别累积（Accumulation）和派发（Distribution）阶段
• 分析大资金的进出动向
• 判断市场趋势的转折点

📈 分析内容：
• 市场阶段判断：累积期、上升期、派发期、下降期
• 供需关系分析：买盘强度 vs 卖盘强度
• 支撑阻力位识别：关键价位和成交量分析
• 价量关系验证：价格与成交量的背离分析

🎯 实际应用：
• 选择股票后，系统自动进行威科夫分析
• 在"分析结果"区域查看详细分析报告
• 结合图表直观理解市场结构
• 识别最佳买入和卖出时机

💡 分析技巧：
• 关注成交量的变化趋势
• 观察价格在关键位置的表现
• 结合多个时间周期分析
• 注意背离信号的出现

🔗 与选股结合：
威科夫分析结果将作为智能选股的重要依据。"""
            ),
            GuideStep(
                "第四步：相对强弱分析",
                """📊 相对强弱(RS)分析帮助您找到强势股票：

🎯 RS 指标说明：
• RS值 = 个股涨跌幅 / 基准指数涨跌幅
• RS > 1：股票强于大盘
• RS < 1：股票弱于大盘
• RS = 1：股票与大盘同步

📈 分析维度：
• 短期相对强弱（1-5日）
• 中期相对强弱（5-20日）
• 长期相对强弱（20-60日）
• 综合相对强弱评分

🔍 强势股特征：
• 持续的正相对强弱
• 在市场调整中表现抗跌
• 成交量配合价格上涨
• 基本面支撑技术面表现

📊 RS 排名功能：
• 系统自动计算所有股票的RS值
• 按RS值进行排序和排名
• 筛选出相对强势的股票
• 动态更新排名变化

💡 使用策略：
• 优先关注RS值持续大于1的股票
• 结合威科夫分析确认买入时机
• 关注RS值的趋势变化
• 避免选择RS值持续下降的股票

🎯 实战应用：
RS分析结果将与威科夫分析结合，为智能选股提供量化依据。"""
            ),
            GuideStep(
                "第五步：智能选股策略",
                """🎯 智能选股是系统的核心功能，结合威科夫和RS分析：

🔧 选股配置：
• 在"选股配置"标签页设置选股参数
• 威科夫阶段筛选：选择累积期股票
• RS值范围设置：筛选强势股
• 成交量要求：确保流动性充足
• 价格区间限制：符合投资预算

📊 多重筛选条件：
• 市场阶段：累积期、上升期优先
• 相对强弱：RS值大于设定阈值
• 成交量活跃度：满足最低成交量要求
• 价格波动性：控制风险水平
• 基本面指标：可选的财务筛选

🎯 评分系统：
• 威科夫评分：基于市场结构分析
• RS评分：基于相对强弱表现
• 成交量评分：基于活跃度分析
• 综合评分：加权平均得出最终分数

📈 结果展示：
• 按综合评分排序显示选股结果
• 提供详细的选股理由说明
• 显示关键指标和风险提示
• 支持导出选股结果

💡 使用建议：
• 根据市场环境调整选股参数
• 定期更新选股结果
• 结合个人投资策略使用
• 注意风险控制和仓位管理

🔄 动态更新：
系统会根据最新数据动态更新选股结果，确保信息的时效性。"""
            ),
            GuideStep(
                "第六步：系统管理和监控",
                """⚙️ 在"系统管理"标签页中，您可以进行全面的系统管理：

🔌 数据源管理：
• 配置和测试数据源连接
• 监控数据获取状态
• 管理数据缓存和更新
• 处理连接异常和重连

📊 系统监控：
• 实时监控系统性能
• 查看内存和CPU使用情况
• 监控数据获取速度
• 跟踪系统运行状态

📋 日志管理：
• 查看系统运行日志
• 分析错误和警告信息
• 导出日志文件供分析
• 设置日志级别和保留策略

🛠️ 系统设置：
• 调整系统运行参数
• 配置缓存策略
• 设置自动更新间隔
• 管理用户偏好设置

❓ 帮助系统：
• 查看功能使用说明
• 获取常见问题解答
• 访问在线帮助文档
• 联系技术支持

🔧 维护功能：
• 清理系统缓存
• 重置配置设置
• 备份和恢复数据
• 系统健康检查

💡 最佳实践：
• 定期检查系统状态
• 及时处理警告信息
• 保持数据源连接稳定
• 定期备份重要配置"""
            ),
            GuideStep(
                "开始使用系统",
                """🎉 恭喜！您已经了解了系统的所有主要功能！

📋 建议的使用流程：
1. 🔌 配置数据源连接（XtData）
2. 🔄 刷新股票数据列表
3. 📊 选择目标股票进行分析
4. 🎯 查看威科夫和RS分析结果
5. ⚙️ 设置智能选股参数
6. 🚀 执行选股并查看结果
7. 📈 跟踪和管理选股结果

🎯 快速开始指南：
• 首先确保数据源配置成功
• 从"股票分析"标签开始
• 选择几只熟悉的股票进行分析
• 了解系统的分析结果展示
• 尝试使用智能选股功能

💡 使用技巧：
• 保持数据源连接稳定
• 定期更新股票数据
• 结合市场环境调整参数
• 注意风险控制
• 持续学习威科夫理论

📞 获取帮助：
• 系统管理 → 帮助系统
• 查看功能使用说明
• 常见问题解答
• 在线文档和教程

🌟 投资提醒：
• 本系统仅供分析参考
• 投资决策需结合多方面因素
• 注意风险控制和仓位管理
• 理性投资，量力而行

祝您投资顺利，收益满满！🚀"""
            )
        ]
        
    def _show_current_step(self):
        """显示当前步骤"""
        if 0 <= self.current_step < len(self.steps):
            step = self.steps[self.current_step]
            
            # 更新步骤指示器
            self.step_indicator.setText(f"第 {self.current_step + 1} 步，共 {len(self.steps)} 步")
            
            # 更新内容
            self.step_title.setText(step.title)
            self.step_content.setPlainText(step.content)
            
            # 确保内容滚动到顶部
            self.step_content.verticalScrollBar().setValue(0)
            
            # 更新按钮状态
            self.prev_button.setEnabled(self.current_step > 0)
            
            if self.current_step == len(self.steps) - 1:
                self.next_button.setText("完成")
                self.next_button.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
            else:
                self.next_button.setText("下一步")
                self.next_button.setStyleSheet("""
                    QPushButton {
                        background-color: #2196F3;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #1976D2;
                    }
                """)
                
    def _prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._show_current_step()
            
    def _next_step(self):
        """下一步"""
        if self.current_step < len(self.steps) - 1:
            self.current_step += 1
            self._show_current_step()
        else:
            self._complete_guide()
            
    def _skip_guide(self):
        """跳过向导"""
        self._complete_guide()
        
    def _complete_guide(self):
        """完成向导"""
        # 保存用户偏好
        if self.dont_show_again.isChecked():
            self._save_guide_preference()
            
        self.guide_completed.emit()
        self.accept()
        
    def _save_guide_preference(self):
        """保存向导偏好"""
        try:
            config_dir = os.path.expanduser("~/.wyckoff_rs_system")
            os.makedirs(config_dir, exist_ok=True)
            
            config_file = os.path.join(config_dir, "user_preferences.json")
            preferences = {}
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    preferences = json.load(f)
                    
            preferences['show_welcome_guide'] = False
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(preferences, f, ensure_ascii=False, indent=2)
                
            logger.info("用户向导偏好设置已保存")
            
        except Exception as e:
            logger.error(f"保存向导偏好设置失败: {e}")


class UserGuideManager:
    """用户向导管理器"""
    
    def __init__(self, main_window):
        """初始化用户向导管理器"""
        self.main_window = main_window
        self.welcome_dialog: Optional[WelcomeGuideDialog] = None
        
        logger.info("用户向导管理器初始化完成")
    
    def should_show_welcome_guide(self) -> bool:
        """判断是否应该显示欢迎向导"""
        try:
            config_dir = os.path.expanduser("~/.wyckoff_rs_system")
            config_file = os.path.join(config_dir, "user_preferences.json")
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    preferences = json.load(f)
                    return preferences.get('show_welcome_guide', True)
            
            return True  # 默认显示向导
            
        except Exception as e:
            logger.error(f"读取向导偏好设置失败: {e}")
            return True
    
    def show_welcome_guide(self):
        """显示欢迎向导"""
        if self.welcome_dialog is None:
            self.welcome_dialog = WelcomeGuideDialog(self.main_window)
            self.welcome_dialog.guide_completed.connect(self._on_guide_completed)
        
        self.welcome_dialog.show()
        self.welcome_dialog.raise_()
        self.welcome_dialog.activateWindow()
    
    def _on_guide_completed(self):
        """向导完成处理"""
        logger.info("用户向导完成")
    
    def show_feature_guide(self, feature_name: str):
        """显示功能向导"""
        # 这里可以扩展显示特定功能的向导
        logger.info(f"显示功能向导: {feature_name}")
