"""
威科夫分析引擎

实现威科夫理论的核心分析算法：
- 价格行为分析
- 成交量分析
- 供需关系判断
- 市场阶段识别
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import warnings

from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


class MarketPhase(Enum):
    """市场阶段枚举"""
    ACCUMULATION = "accumulation"      # 累积阶段
    MARKUP = "markup"                  # 上涨阶段
    DISTRIBUTION = "distribution"      # 派发阶段
    MARKDOWN = "markdown"              # 下跌阶段
    UNKNOWN = "unknown"                # 未知阶段


class VolumePattern(Enum):
    """成交量模式枚举"""
    CLIMAX_VOLUME = "climax_volume"           # 高潮成交量
    DIMINISHING_VOLUME = "diminishing_volume" # 递减成交量
    NORMAL_VOLUME = "normal_volume"           # 正常成交量
    LOW_VOLUME = "low_volume"                 # 低成交量
    ABNORMAL_VOLUME = "abnormal_volume"       # 异常成交量


class PriceAction(Enum):
    """价格行为枚举"""
    SPRING = "spring"                   # 弹簧
    UPTHRUST = "upthrust"              # 上冲
    TEST = "test"                      # 测试
    BACKUP = "backup"                  # 回踩
    SIGN_OF_STRENGTH = "sos"           # 强势信号
    SIGN_OF_WEAKNESS = "sow"           # 弱势信号


@dataclass
class WyckoffSignal:
    """威科夫信号数据类"""
    timestamp: pd.Timestamp
    signal_type: PriceAction
    strength: float  # 信号强度 0-1
    price: float
    volume: float
    description: str
    confidence: float = 0.0  # 置信度 0-1


@dataclass
class MarketStructure:
    """市场结构数据类"""
    phase: MarketPhase
    phase_start: pd.Timestamp
    phase_duration: int  # 天数
    support_level: float
    resistance_level: float
    volume_trend: str  # "increasing", "decreasing", "stable"
    price_trend: str   # "up", "down", "sideways"
    confidence: float = 0.0


class WyckoffAnalysisEngine:
    """威科夫分析引擎"""
    
    def __init__(self, 
                 volume_ma_period: int = 20,
                 price_ma_period: int = 20,
                 volume_threshold: float = 1.5,
                 min_phase_duration: int = 10):
        """
        初始化威科夫分析引擎
        
        Args:
            volume_ma_period: 成交量移动平均周期
            price_ma_period: 价格移动平均周期
            volume_threshold: 成交量异常阈值
            min_phase_duration: 最小阶段持续时间
        """
        self.volume_ma_period = volume_ma_period
        self.price_ma_period = price_ma_period
        self.volume_threshold = volume_threshold
        self.min_phase_duration = min_phase_duration
        
        logger.info("威科夫分析引擎初始化完成")
    
    def analyze_market_structure(self, data: pd.DataFrame) -> MarketStructure:
        """
        分析市场结构
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            MarketStructure: 市场结构分析结果
        """
        try:
            if data.empty or len(data) < self.min_phase_duration:
                raise CalculationError("数据不足，无法进行威科夫分析")
            
            # 计算技术指标
            data = self._calculate_indicators(data.copy())
            
            # 识别市场阶段
            phase = self._identify_market_phase(data)
            
            # 计算支撑阻力位
            support, resistance = self._calculate_support_resistance(data)
            
            # 分析成交量趋势
            volume_trend = self._analyze_volume_trend(data)
            
            # 分析价格趋势
            price_trend = self._analyze_price_trend(data)
            
            # 计算置信度
            confidence = self._calculate_phase_confidence(data, phase)
            
            return MarketStructure(
                phase=phase,
                phase_start=data.index[-self.min_phase_duration],
                phase_duration=self.min_phase_duration,
                support_level=support,
                resistance_level=resistance,
                volume_trend=volume_trend,
                price_trend=price_trend,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"市场结构分析失败: {e}")
            raise CalculationError(f"市场结构分析失败: {e}")
    
    def detect_wyckoff_signals(self, data: pd.DataFrame) -> List[WyckoffSignal]:
        """
        检测威科夫信号
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            List[WyckoffSignal]: 威科夫信号列表
        """
        try:
            signals = []
            
            if data.empty or len(data) < 20:
                return signals
            
            # 计算技术指标
            data = self._calculate_indicators(data.copy())
            
            # 检测弹簧信号
            spring_signals = self._detect_spring(data)
            signals.extend(spring_signals)
            
            # 检测上冲信号
            upthrust_signals = self._detect_upthrust(data)
            signals.extend(upthrust_signals)
            
            # 检测测试信号
            test_signals = self._detect_test(data)
            signals.extend(test_signals)
            
            # 检测强弱势信号
            sos_sow_signals = self._detect_sos_sow(data)
            signals.extend(sos_sow_signals)
            
            # 按时间排序
            signals.sort(key=lambda x: x.timestamp)
            
            logger.info(f"检测到 {len(signals)} 个威科夫信号")
            return signals
            
        except Exception as e:
            logger.error(f"威科夫信号检测失败: {e}")
            raise CalculationError(f"威科夫信号检测失败: {e}")
    
    def analyze_volume_price_relationship(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析量价关系
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            Dict: 量价关系分析结果
        """
        try:
            if data.empty:
                return {}
            
            # 计算价格变化
            data['price_change'] = data['close'].pct_change()
            data['volume_ma'] = data['volume'].rolling(self.volume_ma_period).mean()
            data['volume_ratio'] = data['volume'] / data['volume_ma']
            
            # 分析量价背离
            divergence = self._analyze_volume_price_divergence(data)
            
            # 分析成交量模式
            volume_patterns = self._analyze_volume_patterns(data)
            
            # 计算量价相关性
            correlation = data['price_change'].corr(data['volume_ratio'])
            
            return {
                'volume_price_divergence': divergence,
                'volume_patterns': volume_patterns,
                'volume_price_correlation': correlation,
                'average_volume_ratio': data['volume_ratio'].mean(),
                'volume_volatility': data['volume_ratio'].std()
            }
            
        except Exception as e:
            logger.error(f"量价关系分析失败: {e}")
            raise CalculationError(f"量价关系分析失败: {e}")
    
    def _calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # 移动平均线
        data['price_ma'] = data['close'].rolling(self.price_ma_period).mean()
        data['volume_ma'] = data['volume'].rolling(self.volume_ma_period).mean()
        
        # 价格变化
        data['price_change'] = data['close'].pct_change()
        data['price_range'] = data['high'] - data['low']
        
        # 成交量比率
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        
        # 真实波幅
        data['tr'] = np.maximum(
            data['high'] - data['low'],
            np.maximum(
                abs(data['high'] - data['close'].shift(1)),
                abs(data['low'] - data['close'].shift(1))
            )
        )
        data['atr'] = data['tr'].rolling(14).mean()
        
        return data
    
    def _identify_market_phase(self, data: pd.DataFrame) -> MarketPhase:
        """识别市场阶段"""
        # 简化的阶段识别逻辑
        recent_data = data.tail(self.min_phase_duration)
        
        price_trend = recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]
        volume_trend = recent_data['volume'].mean() / data['volume'].mean()
        
        if price_trend > 0 and volume_trend > 1.2:
            return MarketPhase.MARKUP
        elif price_trend < 0 and volume_trend > 1.2:
            return MarketPhase.MARKDOWN
        elif abs(price_trend) < recent_data['atr'].mean() and volume_trend < 0.8:
            return MarketPhase.ACCUMULATION
        elif abs(price_trend) < recent_data['atr'].mean() and volume_trend > 1.0:
            return MarketPhase.DISTRIBUTION
        else:
            return MarketPhase.UNKNOWN
    
    def _calculate_support_resistance(self, data: pd.DataFrame) -> Tuple[float, float]:
        """计算支撑阻力位"""
        recent_data = data.tail(50)  # 最近50个交易日
        
        # 简化的支撑阻力计算
        support = recent_data['low'].min()
        resistance = recent_data['high'].max()
        
        return support, resistance
    
    def _analyze_volume_trend(self, data: pd.DataFrame) -> str:
        """分析成交量趋势"""
        recent_volume = data['volume'].tail(10).mean()
        historical_volume = data['volume'].mean()
        
        if recent_volume > historical_volume * 1.2:
            return "increasing"
        elif recent_volume < historical_volume * 0.8:
            return "decreasing"
        else:
            return "stable"
    
    def _analyze_price_trend(self, data: pd.DataFrame) -> str:
        """分析价格趋势"""
        recent_data = data.tail(10)
        price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
        
        if price_change > 0.02:  # 2%以上上涨
            return "up"
        elif price_change < -0.02:  # 2%以上下跌
            return "down"
        else:
            return "sideways"
    
    def _calculate_phase_confidence(self, data: pd.DataFrame, phase: MarketPhase) -> float:
        """计算阶段识别置信度"""
        # 简化的置信度计算
        if phase == MarketPhase.UNKNOWN:
            return 0.3
        else:
            return 0.7  # 基础置信度，实际应该更复杂
    
    def _detect_spring(self, data: pd.DataFrame) -> List[WyckoffSignal]:
        """检测弹簧信号"""
        signals = []
        # 简化的弹簧检测逻辑
        # 实际实现需要更复杂的模式识别
        return signals
    
    def _detect_upthrust(self, data: pd.DataFrame) -> List[WyckoffSignal]:
        """检测上冲信号"""
        signals = []
        # 简化的上冲检测逻辑
        return signals
    
    def _detect_test(self, data: pd.DataFrame) -> List[WyckoffSignal]:
        """检测测试信号"""
        signals = []
        # 简化的测试检测逻辑
        return signals
    
    def _detect_sos_sow(self, data: pd.DataFrame) -> List[WyckoffSignal]:
        """检测强弱势信号"""
        signals = []
        # 简化的强弱势检测逻辑
        return signals
    
    def _analyze_volume_price_divergence(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析量价背离"""
        return {
            'has_divergence': False,
            'divergence_type': None,
            'strength': 0.0
        }
    
    def _analyze_volume_patterns(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """分析成交量模式"""
        return []
