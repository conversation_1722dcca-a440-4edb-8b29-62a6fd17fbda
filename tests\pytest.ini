[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    ui: 用户界面测试
    performance: 性能测试
    slow: 运行时间较长的测试
    data_source: 数据源相关测试
    database: 数据库相关测试
    engine: 引擎相关测试
    service: 服务相关测试
    week_test: 周测试

# 最小版本要求
minversion = 6.0

# 测试目录
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
