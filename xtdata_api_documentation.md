# XTData API 完整技术文档

## 目录
1. [简介](#简介)
2. [安装和配置](#安装和配置)
3. [连接管理](#连接管理)
4. [数据获取](#数据获取)
5. [数据订阅](#数据订阅)
6. [股票信息查询](#股票信息查询)
7. [数据字段说明](#数据字段说明)
8. [错误处理](#错误处理)
9. [示例代码](#示例代码)

## 简介

XTData是一个专业的股票数据接口系统，为量化交易和金融分析提供全面的市场数据服务。该系统支持实时行情、历史数据、tick数据等多种数据类型，能够满足不同层次的数据需求。

### 主要特性
- **实时数据推送**：支持实时行情数据推送
- **历史数据查询**：提供完整的历史K线数据
- **多种数据类型**：支持分钟线、日线、周线、月线等
- **高性能接口**：优化的数据传输和处理机制
- **灵活的订阅机制**：支持按需订阅和批量订阅

## 安装和配置

### 系统要求
- Python 3.6+
- Windows 10/11 或 Linux
- 内存：至少 4GB RAM
- 硬盘：至少 1GB 可用空间

### 安装步骤

1. **下载XTData客户端**
```bash
# 从官方网站下载安装包
# 或通过pip安装（如果可用）
pip install xtdata
```

2. **配置环境变量**
```python
import os
os.environ['XTDATA_PATH'] = 'C:/xtdata'  # 设置安装路径
```

3. **验证安装**
```python
import xtdata
print(xtdata.__version__)
```

## 连接管理

### 建立连接

```python
import xtdata

# 连接到数据源
def connect_to_data_source():
    try:
        # 连接到XTData服务器
        result = xtdata.connect()
        if result:
            print("连接成功")
            return True
        else:
            print("连接失败")
            return False
    except Exception as e:
        print(f"连接异常: {e}")
        return False

# 使用示例
if connect_to_data_source():
    print("可以开始获取数据")
```

### 断开连接

```python
def disconnect_from_data_source():
    try:
        xtdata.disconnect()
        print("已断开连接")
    except Exception as e:
        print(f"断开连接异常: {e}")

# 程序结束时调用
disconnect_from_data_source()
```

### 连接状态检查

```python
def check_connection_status():
    """检查连接状态"""
    try:
        status = xtdata.get_connect_status()
        return status
    except Exception as e:
        print(f"检查连接状态异常: {e}")
        return False

# 使用示例
if check_connection_status():
    print("连接正常")
else:
    print("连接异常，需要重新连接")
```

## 数据获取

### 实时行情数据

```python
def get_real_time_data(stock_codes, period='1m'):
    """
    获取实时行情数据
    
    参数:
    stock_codes: 股票代码列表，如 ['000001.SZ', '000002.SZ']
    period: 数据周期，支持 '1m', '5m', '15m', '30m', '1h', '1d'
    
    返回:
    DataFrame: 包含实时行情数据
    """
    try:
        data = xtdata.get_market_data(
            stock_list=stock_codes,
            period=period,
            count=1  # 获取最新一条数据
        )
        return data
    except Exception as e:
        print(f"获取实时数据异常: {e}")
        return None

# 使用示例
stocks = ['000001.SZ', '000002.SZ', '600000.SH']
real_time_data = get_real_time_data(stocks)
if real_time_data is not None:
    print(real_time_data)
```

### 历史K线数据

```python
def get_historical_data(stock_codes, period='1d', start_time='', end_time='', count=100):
    """
    获取历史K线数据
    
    参数:
    stock_codes: 股票代码列表
    period: 数据周期 ('1m', '5m', '15m', '30m', '1h', '1d', '1w', '1M')
    start_time: 开始时间 (格式: '20230101' 或 '20230101 09:30:00')
    end_time: 结束时间 (格式: '20231231' 或 '20231231 15:00:00')
    count: 数据条数（当不指定时间范围时使用）
    
    返回:
    DataFrame: 历史K线数据
    """
    try:
        if start_time and end_time:
            # 按时间范围获取
            data = xtdata.get_local_data(
                stock_list=stock_codes,
                period=period,
                start_time=start_time,
                end_time=end_time
            )
        else:
            # 按数据条数获取
            data = xtdata.get_local_data(
                stock_list=stock_codes,
                period=period,
                count=count
            )
        return data
    except Exception as e:
        print(f"获取历史数据异常: {e}")
        return None

# 使用示例
# 获取最近100个交易日的日线数据
historical_data = get_historical_data(
    stock_codes=['000001.SZ'],
    period='1d',
    count=100
)

# 获取指定时间范围的数据
historical_data_range = get_historical_data(
    stock_codes=['000001.SZ'],
    period='1d',
    start_time='20230101',
    end_time='20231231'
)
```

### Tick数据获取

```python
def get_tick_data(stock_codes, start_time='', end_time=''):
    """
    获取tick数据（逐笔成交数据）
    
    参数:
    stock_codes: 股票代码列表
    start_time: 开始时间
    end_time: 结束时间
    
    返回:
    DataFrame: tick数据
    """
    try:
        data = xtdata.get_full_tick(
            stock_list=stock_codes,
            start_time=start_time,
            end_time=end_time
        )
        return data
    except Exception as e:
        print(f"获取tick数据异常: {e}")
        return None

# 使用示例
tick_data = get_tick_data(['000001.SZ'])
if tick_data is not None:
    print(f"获取到 {len(tick_data)} 条tick数据")
```

## 数据订阅

### 实时行情订阅

```python
class MarketDataCallback:
    """行情数据回调类"""
    
    def __init__(self):
        self.data_count = 0
    
    def on_data(self, data):
        """数据回调函数"""
        self.data_count += 1
        print(f"收到第 {self.data_count} 条数据:")
        print(data)
    
    def on_error(self, error):
        """错误回调函数"""
        print(f"数据订阅错误: {error}")

def subscribe_market_data(stock_codes, period='1m'):
    """
    订阅实时行情数据
    
    参数:
    stock_codes: 股票代码列表
    period: 数据周期
    
    返回:
    bool: 订阅是否成功
    """
    try:
        callback = MarketDataCallback()
        
        # 订阅行情数据
        result = xtdata.subscribe_quote(
            stock_list=stock_codes,
            period=period,
            callback=callback.on_data,
            error_callback=callback.on_error
        )
        
        if result:
            print(f"成功订阅 {len(stock_codes)} 只股票的行情数据")
            return True
        else:
            print("订阅失败")
            return False
            
    except Exception as e:
        print(f"订阅异常: {e}")
        return False

# 使用示例
stocks_to_subscribe = ['000001.SZ', '000002.SZ', '600000.SH']
subscribe_market_data(stocks_to_subscribe, '1m')

# 取消订阅
def unsubscribe_market_data(stock_codes):
    """取消行情数据订阅"""
    try:
        result = xtdata.unsubscribe_quote(stock_list=stock_codes)
        if result:
            print("成功取消订阅")
        else:
            print("取消订阅失败")
        return result
    except Exception as e:
        print(f"取消订阅异常: {e}")
        return False
```

### 订阅管理

```python
class SubscriptionManager:
    """订阅管理器"""
    
    def __init__(self):
        self.subscriptions = {}
    
    def add_subscription(self, subscription_id, stock_codes, period='1m'):
        """添加订阅"""
        try:
            result = xtdata.subscribe_quote(
                stock_list=stock_codes,
                period=period,
                callback=self._on_data
            )
            
            if result:
                self.subscriptions[subscription_id] = {
                    'stock_codes': stock_codes,
                    'period': period,
                    'active': True
                }
                print(f"订阅 {subscription_id} 添加成功")
                return True
            else:
                print(f"订阅 {subscription_id} 添加失败")
                return False
                
        except Exception as e:
            print(f"添加订阅异常: {e}")
            return False
    
    def remove_subscription(self, subscription_id):
        """移除订阅"""
        if subscription_id in self.subscriptions:
            subscription = self.subscriptions[subscription_id]
            try:
                result = xtdata.unsubscribe_quote(
                    stock_list=subscription['stock_codes']
                )
                
                if result:
                    del self.subscriptions[subscription_id]
                    print(f"订阅 {subscription_id} 移除成功")
                    return True
                else:
                    print(f"订阅 {subscription_id} 移除失败")
                    return False
                    
            except Exception as e:
                print(f"移除订阅异常: {e}")
                return False
        else:
            print(f"订阅 {subscription_id} 不存在")
            return False
    
    def _on_data(self, data):
        """数据回调处理"""
        print(f"收到订阅数据: {data}")
    
    def get_active_subscriptions(self):
        """获取活跃订阅列表"""
        return [sub_id for sub_id, sub_info in self.subscriptions.items() 
                if sub_info['active']]

# 使用示例
manager = SubscriptionManager()
manager.add_subscription('main_stocks', ['000001.SZ', '000002.SZ'])
manager.add_subscription('etf_stocks', ['510050.SH', '510300.SH'])

print("活跃订阅:", manager.get_active_subscriptions())
```

## 股票信息查询

### 股票列表获取

```python
def get_stock_list(market=''):
    """
    获取股票列表
    
    参数:
    market: 市场代码 ('SZ' - 深圳, 'SH' - 上海, '' - 全部)
    
    返回:
    list: 股票代码列表
    """
    try:
        if market:
            stock_list = xtdata.get_stock_list_in_sector(market)
        else:
            stock_list = xtdata.get_stock_list()
        
        return stock_list
    except Exception as e:
        print(f"获取股票列表异常: {e}")
        return []

# 使用示例
all_stocks = get_stock_list()  # 获取所有股票
sz_stocks = get_stock_list('SZ')  # 获取深圳股票
sh_stocks = get_stock_list('SH')  # 获取上海股票

print(f"总股票数: {len(all_stocks)}")
print(f"深圳股票数: {len(sz_stocks)}")
print(f"上海股票数: {len(sh_stocks)}")
```

### 板块信息查询

```python
def get_sector_info():
    """获取板块信息"""
    try:
        sectors = xtdata.get_sector_list()
        return sectors
    except Exception as e:
        print(f"获取板块信息异常: {e}")
        return []

def get_stocks_in_sector(sector_name):
    """
    获取板块内股票列表
    
    参数:
    sector_name: 板块名称
    
    返回:
    list: 股票代码列表
    """
    try:
        stocks = xtdata.get_stock_list_in_sector(sector_name)
        return stocks
    except Exception as e:
        print(f"获取板块股票异常: {e}")
        return []

# 使用示例
sectors = get_sector_info()
if sectors:
    print("可用板块:", sectors[:10])  # 显示前10个板块
    
    # 获取某个板块的股票
    if len(sectors) > 0:
        sector_stocks = get_stocks_in_sector(sectors[0])
        print(f"板块 {sectors[0]} 包含 {len(sector_stocks)} 只股票")
```

### 股票基本信息

```python
def get_stock_info(stock_codes):
    """
    获取股票基本信息
    
    参数:
    stock_codes: 股票代码列表
    
    返回:
    dict: 股票信息字典
    """
    try:
        info = xtdata.get_instrument_detail(stock_codes)
        return info
    except Exception as e:
        print(f"获取股票信息异常: {e}")
        return {}

# 使用示例
stock_info = get_stock_info(['000001.SZ', '600000.SH'])
for code, info in stock_info.items():
    print(f"股票代码: {code}")
    print(f"股票名称: {info.get('InstrumentName', '未知')}")
    print(f"上市板块: {info.get('ExchangeID', '未知')}")
    print("-" * 30)
```

## 数据字段说明

### K线数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| time | datetime | 时间戳 |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| close | float | 收盘价 |
| volume | int | 成交量（手） |
| amount | float | 成交额（元） |
| settle | float | 结算价（期货用） |
| openInterest | int | 持仓量（期货用） |

### Tick数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| time | datetime | 时间戳 |
| lastPrice | float | 最新价 |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| lastClose | float | 昨收价 |
| amount | float | 成交额 |
| volume | int | 成交量 |
| pvolume | int | 累计成交量 |
| stockStatus | int | 股票状态 |
| openInt | int | 持仓量 |
| lastSettlementPrice | float | 昨结算 |
| askPrice | list | 卖价列表（5档） |
| bidPrice | list | 买价列表（5档） |
| askVol | list | 卖量列表（5档） |
| bidVol | list | 买量列表（5档） |

### 股票状态说明

| 状态码 | 说明 |
|--------|------|
| 0 | 正常交易 |
| 1 | 停牌 |
| 2 | 涨停 |
| 3 | 跌停 |
| 4 | 熔断 |

## 错误处理

### 常见错误类型

```python
import xtdata

class XTDataError:
    """XTData错误处理类"""
    
    # 错误码定义
    ERROR_CODES = {
        -1: "连接失败",
        -2: "认证失败", 
        -3: "数据获取超时",
        -4: "股票代码无效",
        -5: "时间参数错误",
        -6: "订阅失败",
        -7: "权限不足"
    }
    
    @staticmethod
    def handle_error(error_code, context=""):
        """处理错误"""
        error_msg = XTDataError.ERROR_CODES.get(error_code, "未知错误")
        full_msg = f"{context}: {error_msg} (错误码: {error_code})"
        print(full_msg)
        return full_msg

def safe_get_data(stock_codes, period='1d', count=100):
    """安全的数据获取函数"""
    try:
        # 检查连接状态
        if not xtdata.get_connect_status():
            print("连接已断开，尝试重新连接...")
            if not xtdata.connect():
                raise Exception("重新连接失败")
        
        # 验证股票代码
        if not stock_codes or not isinstance(stock_codes, list):
            raise ValueError("股票代码列表不能为空")
        
        # 获取数据
        data = xtdata.get_local_data(
            stock_list=stock_codes,
            period=period,
            count=count
        )
        
        if data is None or data.empty:
            raise Exception("获取到的数据为空")
        
        return data
        
    except ValueError as e:
        print(f"参数错误: {e}")
        return None
    except ConnectionError as e:
        print(f"连接错误: {e}")
        return None
    except Exception as e:
        print(f"数据获取异常: {e}")
        return None

# 使用示例
data = safe_get_data(['000001.SZ'], '1d', 100)
if data is not None:
    print("数据获取成功")
else:
    print("数据获取失败")
```

### 重连机制

```python
import time
import threading

class ConnectionManager:
    """连接管理器"""
    
    def __init__(self, max_retry=3, retry_interval=5):
        self.max_retry = max_retry
        self.retry_interval = retry_interval
        self.is_connected = False
        self.retry_count = 0
    
    def connect_with_retry(self):
        """带重试的连接"""
        while self.retry_count < self.max_retry:
            try:
                if xtdata.connect():
                    self.is_connected = True
                    self.retry_count = 0
                    print("连接成功")
                    return True
                else:
                    self.retry_count += 1
                    print(f"连接失败，{self.retry_interval}秒后重试... ({self.retry_count}/{self.max_retry})")
                    time.sleep(self.retry_interval)
                    
            except Exception as e:
                self.retry_count += 1
                print(f"连接异常: {e}，{self.retry_interval}秒后重试... ({self.retry_count}/{self.max_retry})")
                time.sleep(self.retry_interval)
        
        print("连接失败，已达到最大重试次数")
        return False
    
    def start_heartbeat(self, interval=30):
        """启动心跳检测"""
        def heartbeat():
            while True:
                try:
                    if not xtdata.get_connect_status():
                        print("检测到连接断开，尝试重连...")
                        self.is_connected = False
                        self.connect_with_retry()
                    time.sleep(interval)
                except Exception as e:
                    print(f"心跳检测异常: {e}")
                    time.sleep(interval)
        
        thread = threading.Thread(target=heartbeat, daemon=True)
        thread.start()
        print(f"心跳检测已启动，间隔 {interval} 秒")

# 使用示例
conn_manager = ConnectionManager(max_retry=5, retry_interval=3)
if conn_manager.connect_with_retry():
    conn_manager.start_heartbeat(30)  # 30秒心跳检测
```

## 示例代码

### 完整的数据获取示例

```python
import xtdata
import pandas as pd
import time
from datetime import datetime, timedelta

class XTDataClient:
    """XTData客户端封装类"""
    
    def __init__(self):
        self.is_connected = False
        self.subscriptions = {}
    
    def initialize(self):
        """初始化连接"""
        try:
            if xtdata.connect():
                self.is_connected = True
                print("XTData初始化成功")
                return True
            else:
                print("XTData初始化失败")
                return False
        except Exception as e:
            print(f"初始化异常: {e}")
            return False
    
    def get_daily_data(self, stock_codes, days=100):
        """获取日线数据"""
        if not self.is_connected:
            print("请先初始化连接")
            return None
        
        try:
            data = xtdata.get_local_data(
                stock_list=stock_codes,
                period='1d',
                count=days
            )
            return data
        except Exception as e:
            print(f"获取日线数据异常: {e}")
            return None
    
    def get_minute_data(self, stock_codes, start_time, end_time):
        """获取分钟数据"""
        if not self.is_connected:
            print("请先初始化连接")
            return None
        
        try:
            data = xtdata.get_local_data(
                stock_list=stock_codes,
                period='1m',
                start_time=start_time,
                end_time=end_time
            )
            return data
        except Exception as e:
            print(f"获取分钟数据异常: {e}")
            return None
    
    def calculate_technical_indicators(self, data):
        """计算技术指标"""
        if data is None or data.empty:
            return None
        
        try:
            # 计算移动平均线
            data['MA5'] = data['close'].rolling(window=5).mean()
            data['MA10'] = data['close'].rolling(window=10).mean()
            data['MA20'] = data['close'].rolling(window=20).mean()
            
            # 计算RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))
            
            # 计算MACD
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            data['MACD'] = exp1 - exp2
            data['Signal'] = data['MACD'].ewm(span=9).mean()
            data['Histogram'] = data['MACD'] - data['Signal']
            
            return data
            
        except Exception as e:
            print(f"计算技术指标异常: {e}")
            return data
    
    def cleanup(self):
        """清理资源"""
        try:
            xtdata.disconnect()
            self.is_connected = False
            print("连接已断开")
        except Exception as e:
            print(f"断开连接异常: {e}")

# 使用示例
def main():
    # 创建客户端
    client = XTDataClient()
    
    # 初始化连接
    if not client.initialize():
        return
    
    try:
        # 定义股票列表
        stocks = ['000001.SZ', '000002.SZ', '600000.SH']
        
        # 获取日线数据
        print("获取日线数据...")
        daily_data = client.get_daily_data(stocks, days=100)
        
        if daily_data is not None:
            print(f"获取到 {len(daily_data)} 条日线数据")
            
            # 计算技术指标
            print("计算技术指标...")
            daily_data_with_indicators = client.calculate_technical_indicators(daily_data)
            
            # 显示最近5天的数据
            print("最近5天数据:")
            for stock in stocks:
                if stock in daily_data_with_indicators:
                    stock_data = daily_data_with_indicators[stock].tail()
                    print(f"\n{stock}:")
                    print(stock_data[['close', 'MA5', 'MA10', 'MA20', 'RSI']])
        
        # 获取分钟数据
        print("\n获取今日分钟数据...")
        today = datetime.now().strftime('%Y%m%d')
        minute_data = client.get_minute_data(
            stocks, 
            start_time=f"{today} 09:30:00",
            end_time=f"{today} 15:00:00"
        )
        
        if minute_data is not None:
            print(f"获取到分钟数据")
            for stock in stocks:
                if stock in minute_data:
                    count = len(minute_data[stock])
                    print(f"{stock}: {count} 条分钟数据")
    
    except Exception as e:
        print(f"主程序异常: {e}")
    
    finally:
        # 清理资源
        client.cleanup()

if __name__ == "__main__":
    main()
```

### 实时数据监控示例

```python
import xtdata
import threading
import queue
import time
from datetime import datetime

class RealTimeMonitor:
    """实时数据监控器"""
    
    def __init__(self, stock_codes):
        self.stock_codes = stock_codes
        self.data_queue = queue.Queue()
        self.is_running = False
        self.monitor_thread = None
    
    def data_callback(self, data):
        """数据回调函数"""
        try:
            timestamp = datetime.now()
            self.data_queue.put((timestamp, data))
        except Exception as e:
            print(f"数据回调异常: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        try:
            # 订阅实时数据
            result = xtdata.subscribe_quote(
                stock_list=self.stock_codes,
                period='1m',
                callback=self.data_callback
            )
            
            if result:
                self.is_running = True
                self.monitor_thread = threading.Thread(target=self._process_data)
                self.monitor_thread.start()
                print(f"开始监控 {len(self.stock_codes)} 只股票")
                return True
            else:
                print("订阅失败")
                return False
                
        except Exception as e:
            print(f"启动监控异常: {e}")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        try:
            self.is_running = False
            
            # 取消订阅
            xtdata.unsubscribe_quote(stock_list=self.stock_codes)
            
            if self.monitor_thread:
                self.monitor_thread.join()
            
            print("监控已停止")
            
        except Exception as e:
            print(f"停止监控异常: {e}")
    
    def _process_data(self):
        """处理数据"""
        while self.is_running:
            try:
                if not self.data_queue.empty():
                    timestamp, data = self.data_queue.get(timeout=1)
                    self._handle_data(timestamp, data)
                else:
                    time.sleep(0.1)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"处理数据异常: {e}")
    
    def _handle_data(self, timestamp, data):
        """处理单条数据"""
        print(f"[{timestamp.strftime('%H:%M:%S')}] 收到数据: {data}")
        
        # 这里可以添加自定义的数据处理逻辑
        # 例如：价格变动提醒、技术指标计算等

# 使用示例
def run_real_time_monitor():
    # 连接XTData
    if not xtdata.connect():
        print("连接失败")
        return
    
    try:
        # 创建监控器
        stocks = ['000001.SZ', '000002.SZ']
        monitor = RealTimeMonitor(stocks)
        
        # 开始监控
        if monitor.start_monitoring():
            print("监控运行中，按 Ctrl+C 停止...")
            
            # 运行一段时间
            time.sleep(60)  # 监控60秒
            
        # 停止监控
        monitor.stop_monitoring()
        
    except KeyboardInterrupt:
        print("用户中断")
        monitor.stop_monitoring()
    except Exception as e:
        print(f"监控异常: {e}")
    finally:
        xtdata.disconnect()

if __name__ == "__main__":
    run_real_time_monitor()
```

## 注意事项

1. **连接管理**：确保在程序结束时正确断开连接
2. **数据频率**：注意API调用频率限制，避免过于频繁的请求
3. **内存管理**：长时间运行时注意内存使用，定期清理不需要的数据
4. **异常处理**：网络异常时要有适当的重试机制
5. **数据验证**：使用前验证数据的完整性和准确性

## 技术支持

如需技术支持，请联系：
- 官方网站：[XTData官网]
- 技术文档：[在线文档]
- 社区论坛：[用户社区]

---

*本文档版本：v2.0*  
*最后更新：2024年*