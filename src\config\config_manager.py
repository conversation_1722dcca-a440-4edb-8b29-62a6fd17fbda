"""
配置管理器

负责配置文件的加载、验证、保存和热重载
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from threading import Lock, Thread
import time
from datetime import datetime

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConfigInfo:
    """配置信息数据类"""
    file_path: str
    last_modified: float
    checksum: str
    is_valid: bool
    error_message: Optional[str] = None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, Any] = {}
        self._config_info: Dict[str, ConfigInfo] = {}
        self._lock = Lock()
        self._watchers: Dict[str, Thread] = {}
        self._watching = True
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"配置管理器初始化完成: {self.config_dir}")
    
    def load_config(self, config_name: str, config_file: Optional[str] = None) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置名称
            config_file: 配置文件路径，如果为None则自动查找
            
        Returns:
            配置字典
        """
        with self._lock:
            if config_file is None:
                config_file = self._find_config_file(config_name)
            
            if not config_file:
                logger.warning(f"未找到配置文件: {config_name}")
                return {}
            
            try:
                config_path = Path(config_file)
                
                # 检查文件是否存在
                if not config_path.exists():
                    logger.error(f"配置文件不存在: {config_file}")
                    return {}
                
                # 读取配置文件
                with open(config_path, 'r', encoding='utf-8') as f:
                    if config_path.suffix.lower() in ['.yaml', '.yml']:
                        config_data = yaml.safe_load(f)
                    elif config_path.suffix.lower() == '.json':
                        config_data = json.load(f)
                    else:
                        logger.error(f"不支持的配置文件格式: {config_path.suffix}")
                        return {}
                
                # 验证配置
                if not isinstance(config_data, dict):
                    logger.error(f"配置文件格式错误，必须是字典格式: {config_file}")
                    return {}
                
                # 更新配置信息
                self._configs[config_name] = config_data
                self._config_info[config_name] = ConfigInfo(
                    file_path=str(config_path),
                    last_modified=config_path.stat().st_mtime,
                    checksum=self._calculate_checksum(config_path),
                    is_valid=True
                )
                
                logger.info(f"配置加载成功: {config_name} from {config_file}")
                return config_data.copy()
                
            except Exception as e:
                logger.error(f"加载配置文件失败: {config_file}, 错误: {e}")
                self._config_info[config_name] = ConfigInfo(
                    file_path=str(config_file) if config_file else "",
                    last_modified=0,
                    checksum="",
                    is_valid=False,
                    error_message=str(e)
                )
                return {}
    
    def save_config(self, config_name: str, config_data: Dict[str, Any], 
                   config_file: Optional[str] = None) -> bool:
        """
        保存配置文件
        
        Args:
            config_name: 配置名称
            config_data: 配置数据
            config_file: 配置文件路径
            
        Returns:
            是否保存成功
        """
        try:
            if config_file is None:
                config_file = self.config_dir / f"{config_name}.yaml"
            
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份现有配置
            if config_path.exists():
                backup_path = config_path.with_suffix(f".backup.{int(time.time())}")
                config_path.rename(backup_path)
                logger.info(f"配置文件已备份: {backup_path}")
            
            # 保存新配置
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif config_path.suffix.lower() == '.json':
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
                else:
                    logger.error(f"不支持的配置文件格式: {config_path.suffix}")
                    return False
            
            # 更新内存中的配置
            with self._lock:
                self._configs[config_name] = config_data.copy()
                self._config_info[config_name] = ConfigInfo(
                    file_path=str(config_path),
                    last_modified=config_path.stat().st_mtime,
                    checksum=self._calculate_checksum(config_path),
                    is_valid=True
                )
            
            logger.info(f"配置保存成功: {config_name} to {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {config_file}, 错误: {e}")
            return False
    
    def get_config(self, config_name: str, key_path: Optional[str] = None, 
                  default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            config_name: 配置名称
            key_path: 配置键路径，支持点分割的嵌套键，如 'database.host'
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            if config_name not in self._configs:
                logger.warning(f"配置不存在: {config_name}")
                return default
            
            config_data = self._configs[config_name]
            
            if key_path is None:
                return config_data.copy()
            
            # 处理嵌套键
            keys = key_path.split('.')
            current_data = config_data
            
            try:
                for key in keys:
                    if isinstance(current_data, dict) and key in current_data:
                        current_data = current_data[key]
                    else:
                        return default
                
                return current_data
                
            except Exception as e:
                logger.error(f"获取配置值失败: {config_name}.{key_path}, 错误: {e}")
                return default
    
    def set_config(self, config_name: str, key_path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            config_name: 配置名称
            key_path: 配置键路径
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                if config_name not in self._configs:
                    self._configs[config_name] = {}
                
                config_data = self._configs[config_name]
                keys = key_path.split('.')
                
                # 创建嵌套结构
                current_data = config_data
                for key in keys[:-1]:
                    if key not in current_data:
                        current_data[key] = {}
                    current_data = current_data[key]
                
                # 设置值
                current_data[keys[-1]] = value
                
            logger.info(f"配置值已更新: {config_name}.{key_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置值失败: {config_name}.{key_path}, 错误: {e}")
            return False
    
    def reload_config(self, config_name: str) -> bool:
        """
        重新加载配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            是否重新加载成功
        """
        if config_name in self._config_info:
            config_file = self._config_info[config_name].file_path
            new_config = self.load_config(config_name, config_file)
            return bool(new_config)
        else:
            logger.warning(f"无法重新加载未知配置: {config_name}")
            return False
    
    def start_watching(self, config_name: str, callback=None) -> bool:
        """
        开始监控配置文件变化
        
        Args:
            config_name: 配置名称
            callback: 变化回调函数
            
        Returns:
            是否启动成功
        """
        if config_name not in self._config_info:
            logger.error(f"无法监控未加载的配置: {config_name}")
            return False
        
        if config_name in self._watchers:
            logger.warning(f"配置已在监控中: {config_name}")
            return True
        
        def watch_thread():
            config_info = self._config_info[config_name]
            config_path = Path(config_info.file_path)
            
            while self._watching and config_name in self._config_info:
                try:
                    if config_path.exists():
                        current_mtime = config_path.stat().st_mtime
                        current_checksum = self._calculate_checksum(config_path)
                        
                        if (current_mtime != config_info.last_modified or 
                            current_checksum != config_info.checksum):
                            
                            logger.info(f"检测到配置文件变化: {config_name}")
                            
                            # 重新加载配置
                            old_config = self._configs.get(config_name, {}).copy()
                            if self.reload_config(config_name):
                                new_config = self._configs.get(config_name, {})
                                
                                # 调用回调函数
                                if callback:
                                    try:
                                        callback(config_name, old_config, new_config)
                                    except Exception as e:
                                        logger.error(f"配置变化回调执行失败: {e}")
                    
                    time.sleep(1)  # 检查间隔
                    
                except Exception as e:
                    logger.error(f"监控配置文件失败: {config_name}, 错误: {e}")
                    time.sleep(5)
        
        # 启动监控线程
        watcher = Thread(target=watch_thread, daemon=True)
        watcher.start()
        self._watchers[config_name] = watcher
        
        logger.info(f"开始监控配置文件: {config_name}")
        return True
    
    def stop_watching(self, config_name: Optional[str] = None):
        """
        停止监控配置文件
        
        Args:
            config_name: 配置名称，如果为None则停止所有监控
        """
        if config_name:
            if config_name in self._watchers:
                del self._watchers[config_name]
                logger.info(f"停止监控配置文件: {config_name}")
        else:
            self._watching = False
            self._watchers.clear()
            logger.info("停止所有配置文件监控")
    
    def get_config_info(self, config_name: str) -> Optional[ConfigInfo]:
        """
        获取配置信息
        
        Args:
            config_name: 配置名称
            
        Returns:
            配置信息
        """
        return self._config_info.get(config_name)
    
    def list_configs(self) -> List[str]:
        """
        获取所有配置名称列表
        
        Returns:
            配置名称列表
        """
        return list(self._configs.keys())
    
    def export_config(self, config_name: str, export_file: str) -> bool:
        """
        导出配置到文件
        
        Args:
            config_name: 配置名称
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        if config_name not in self._configs:
            logger.error(f"配置不存在，无法导出: {config_name}")
            return False
        
        return self.save_config(config_name, self._configs[config_name], export_file)
    
    def cleanup_backups(self, max_backups: int = 10):
        """
        清理备份文件
        
        Args:
            max_backups: 保留的最大备份数量
        """
        try:
            backup_files = list(self.config_dir.glob("*.backup.*"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除超出数量的备份
            for backup_file in backup_files[max_backups:]:
                backup_file.unlink()
                logger.info(f"删除旧备份文件: {backup_file}")
        
        except Exception as e:
            logger.error(f"清理备份文件失败: {e}")
    
    def _find_config_file(self, config_name: str) -> Optional[str]:
        """查找配置文件"""
        possible_extensions = ['.yaml', '.yml', '.json']
        
        for ext in possible_extensions:
            config_file = self.config_dir / f"{config_name}{ext}"
            if config_file.exists():
                return str(config_file)
        
        return None
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        import hashlib
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def close(self):
        """关闭配置管理器"""
        self.stop_watching()
        logger.info("配置管理器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()