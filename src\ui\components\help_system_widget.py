"""
用户帮助系统控件

提供使用说明、功能介绍和快捷键帮助
"""

from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QGridLayout, QTextEdit, QTabWidget, QScrollArea,
    QTreeWidget, QTreeWidgetItem, QSplitter, QLineEdit,
    QFrame, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal, QUrl
from PyQt6.QtGui import QFont, QColor, QDesktopServices

from ...utils.logger import get_logger

logger = get_logger(__name__)


class HelpSystemWidget(QWidget):
    """用户帮助系统控件"""
    
    # 信号定义
    help_topic_selected = pyqtSignal(str)  # 帮助主题选中
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化用户帮助系统控件
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        # 帮助内容数据
        self.help_content: Dict[str, Dict[str, Any]] = {}
        
        # 控件引用
        self.help_tabs: Optional[QTabWidget] = None
        self.help_tree: Optional[QTreeWidget] = None
        self.help_content_text: Optional[QTextEdit] = None
        self.search_input: Optional[QLineEdit] = None
        self.shortcut_list: Optional[QListWidget] = None
        
        self._init_ui()
        self._load_help_content()
        
        logger.info("用户帮助系统控件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("❓ 用户帮助系统")
        title_label.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        self.help_tabs = QTabWidget()
        
        # 使用指南标签页
        guide_tab = self._create_guide_tab()
        self.help_tabs.addTab(guide_tab, "📖 使用指南")
        
        # 功能介绍标签页
        features_tab = self._create_features_tab()
        self.help_tabs.addTab(features_tab, "⚙️ 功能介绍")
        
        # 快捷键标签页
        shortcuts_tab = self._create_shortcuts_tab()
        self.help_tabs.addTab(shortcuts_tab, "⌨️ 快捷键")
        
        # 常见问题标签页
        faq_tab = self._create_faq_tab()
        self.help_tabs.addTab(faq_tab, "❓ 常见问题")
        
        # 关于标签页
        about_tab = self._create_about_tab()
        self.help_tabs.addTab(about_tab, "ℹ️ 关于")
        
        layout.addWidget(self.help_tabs)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        online_help_button = QPushButton("🌐 在线帮助")
        online_help_button.clicked.connect(self._open_online_help)
        button_layout.addWidget(online_help_button)
        
        feedback_button = QPushButton("📝 意见反馈")
        feedback_button.clicked.connect(self._open_feedback)
        button_layout.addWidget(feedback_button)
        
        button_layout.addStretch()
        
        version_label = QLabel("版本: v1.0.0")
        version_label.setStyleSheet("color: #666; font-size: 9pt;")
        button_layout.addWidget(version_label)
        
        layout.addLayout(button_layout)
    
    def _create_guide_tab(self) -> QWidget:
        """创建使用指南标签页"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧目录树
        tree_widget = QWidget()
        tree_layout = QVBoxLayout(tree_widget)
        
        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索帮助内容...")
        self.search_input.textChanged.connect(self._search_help_content)
        tree_layout.addWidget(self.search_input)
        
        # 帮助目录树
        self.help_tree = QTreeWidget()
        self.help_tree.setHeaderLabel("帮助目录")
        self.help_tree.itemClicked.connect(self._on_help_item_clicked)
        tree_layout.addWidget(self.help_tree)
        
        splitter.addWidget(tree_widget)
        
        # 右侧内容显示
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        content_title = QLabel("帮助内容")
        content_title.setFont(QFont("Microsoft YaHei UI", 10, QFont.Weight.Bold))
        content_layout.addWidget(content_title)
        
        self.help_content_text = QTextEdit()
        self.help_content_text.setReadOnly(True)
        self.help_content_text.setFont(QFont("Microsoft YaHei UI", 9))
        content_layout.addWidget(self.help_content_text)
        
        splitter.addWidget(content_widget)
        
        # 设置分割比例
        splitter.setSizes([250, 500])
        
        layout.addWidget(splitter)
        
        return widget
    
    def _create_features_tab(self) -> QWidget:
        """创建功能介绍标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 威科夫分析功能
        wyckoff_group = QGroupBox("威科夫分析功能")
        wyckoff_layout = QVBoxLayout(wyckoff_group)
        
        wyckoff_text = QTextEdit()
        wyckoff_text.setReadOnly(True)
        wyckoff_text.setMaximumHeight(120)
        wyckoff_text.setText("""
威科夫分析是本系统的核心功能之一，基于理查德·威科夫的市场分析理论：

• 市场阶段识别：自动识别累积、上涨、派发、下跌四个市场阶段
• 供需关系分析：通过价格和成交量的关系判断市场供需状况
• 量价背离检测：识别价格与成交量的异常关系
• 支撑阻力位计算：自动计算关键的支撑和阻力价位
        """)
        wyckoff_layout.addWidget(wyckoff_text)
        
        scroll_layout.addWidget(wyckoff_group)
        
        # 相对强弱分析功能
        rs_group = QGroupBox("相对强弱分析功能")
        rs_layout = QVBoxLayout(rs_group)
        
        rs_text = QTextEdit()
        rs_text.setReadOnly(True)
        rs_text.setMaximumHeight(120)
        rs_text.setText("""
相对强弱分析帮助您识别市场中表现最强的股票：

• RS值计算：计算股票相对于基准指数的强弱程度
• 多时间周期分析：支持日线、周线、月线多个时间维度
• 动态基准选择：可选择不同的市场指数或板块作为基准
• 强弱排名：对所有股票进行相对强弱排名
        """)
        rs_layout.addWidget(rs_text)
        
        scroll_layout.addWidget(rs_group)
        
        # 智能选股功能
        selection_group = QGroupBox("智能选股功能")
        selection_layout = QVBoxLayout(selection_group)
        
        selection_text = QTextEdit()
        selection_text.setReadOnly(True)
        selection_text.setMaximumHeight(120)
        selection_text.setText("""
智能选股系统结合威科夫分析和相对强弱分析：

• 多策略组合：支持威科夫策略、相对强弱策略等多种选股策略
• 参数自定义：可根据个人偏好调整各种分析参数
• 风险控制：内置风险控制机制，避免过度集中投资
• 回测验证：提供历史回测功能验证策略有效性
        """)
        selection_layout.addWidget(selection_text)
        
        scroll_layout.addWidget(selection_group)
        
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_shortcuts_tab(self) -> QWidget:
        """创建快捷键标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 快捷键列表
        self.shortcut_list = QListWidget()
        layout.addWidget(self.shortcut_list)
        
        return widget
    
    def _create_faq_tab(self) -> QWidget:
        """创建常见问题标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # FAQ内容
        faq_items = [
            {
                "question": "Q: 如何连接数据源？",
                "answer": "A: 在系统设置中配置XtData连接参数，确保MiniQMT客户端正在运行。"
            },
            {
                "question": "Q: 威科夫分析的准确性如何？",
                "answer": "A: 威科夫分析是一种技术分析方法，准确性取决于市场环境和参数设置。建议结合其他分析方法使用。"
            },
            {
                "question": "Q: 如何设置选股参数？",
                "answer": "A: 在选股配置界面可以调整威科夫参数、相对强弱参数等，系统提供预设方案供参考。"
            },
            {
                "question": "Q: 系统支持哪些市场？",
                "answer": "A: 目前主要支持A股市场，包括沪深两市的股票数据。"
            },
            {
                "question": "Q: 如何导出分析结果？",
                "answer": "A: 在各个分析界面都提供导出功能，可以导出为Excel或CSV格式。"
            }
        ]
        
        for item in faq_items:
            faq_group = QGroupBox(item["question"])
            faq_layout = QVBoxLayout(faq_group)
            
            answer_label = QLabel(item["answer"])
            answer_label.setWordWrap(True)
            answer_label.setStyleSheet("padding: 10px; background-color: #f5f5f5; border-radius: 5px;")
            faq_layout.addWidget(answer_label)
            
            scroll_layout.addWidget(faq_group)
        
        scroll_layout.addStretch()
        
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_about_tab(self) -> QWidget:
        """创建关于标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 居中布局
        center_layout = QVBoxLayout()
        center_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 应用图标和名称
        app_name = QLabel("威科夫相对强弱选股系统")
        app_name.setFont(QFont("Microsoft YaHei UI", 16, QFont.Weight.Bold))
        app_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        center_layout.addWidget(app_name)
        
        # 版本信息
        version_info = QLabel("版本: v1.0.0")
        version_info.setFont(QFont("Microsoft YaHei UI", 12))
        version_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_info.setStyleSheet("color: #666; margin: 10px;")
        center_layout.addWidget(version_info)
        
        # 描述信息
        description = QLabel("""
基于威科夫理论和相对强弱分析的智能选股系统

• 技术栈：Python 3.8+, PyQt6, SQLite
• 核心算法：威科夫分析、相对强弱计算
• 数据源：XtData (MiniQMT)
• 开发团队：Wyckoff RS System Team
        """)
        description.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description.setStyleSheet("padding: 20px; background-color: #f9f9f9; border-radius: 10px; margin: 20px;")
        center_layout.addWidget(description)
        
        # 版权信息
        copyright_info = QLabel("© 2025 威科夫相对强弱选股系统. All rights reserved.")
        copyright_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_info.setStyleSheet("color: #999; font-size: 9pt; margin-top: 20px;")
        center_layout.addWidget(copyright_info)
        
        layout.addLayout(center_layout)
        layout.addStretch()
        
        return widget

    def _load_help_content(self):
        """加载帮助内容"""
        try:
            # 定义帮助内容结构
            self.help_content = {
                "getting_started": {
                    "title": "快速开始",
                    "content": """
# 快速开始指南

欢迎使用威科夫相对强弱选股系统！本指南将帮助您快速上手。

## 第一步：连接数据源
1. 确保MiniQMT客户端正在运行
2. 打开"系统设置" -> "数据源配置"
3. 配置XtData连接参数（默认：127.0.0.1:58610）
4. 点击"测试连接"确认连接成功

## 第二步：查看股票数据
1. 在主界面的"股票数据"标签页中查看股票列表
2. 使用搜索功能快速找到目标股票
3. 点击股票查看详细的K线图和技术指标

## 第三步：进行威科夫分析
1. 选择目标股票
2. 在"分析结果"标签页查看威科夫分析结果
3. 关注市场阶段、供需关系等关键信息

## 第四步：执行智能选股
1. 在"选股配置"标签页设置选股参数
2. 选择合适的策略组合
3. 点击"开始选股"执行智能选股算法
                    """
                },
                "wyckoff_analysis": {
                    "title": "威科夫分析详解",
                    "content": """
# 威科夫分析详解

威科夫分析是本系统的核心功能，基于理查德·威科夫的市场分析理论。

## 市场阶段识别
系统自动识别四个主要市场阶段：
- **累积阶段**：大资金悄悄建仓，价格在底部区域震荡
- **上涨阶段**：价格突破累积区域，开始上涨趋势
- **派发阶段**：大资金开始出货，价格在高位震荡
- **下跌阶段**：价格跌破派发区域，开始下跌趋势

## 供需关系分析
通过分析价格和成交量的关系判断市场供需：
- **需求增加**：价格上涨伴随成交量放大
- **供应增加**：价格下跌伴随成交量放大
- **需求减少**：价格上涨但成交量萎缩
- **供应减少**：价格下跌但成交量萎缩

## 关键信号识别
系统会自动识别重要的威科夫信号：
- **春天信号**：累积阶段的假跌破后快速回升
- **跳跃小溪**：突破累积区域的关键信号
- **冰山信号**：派发阶段的假突破后快速回落
- **销售高潮**：下跌阶段的恐慌性抛售
                    """
                },
                "relative_strength": {
                    "title": "相对强弱分析",
                    "content": """
# 相对强弱分析

相对强弱分析帮助您识别市场中表现最强的股票。

## RS值计算
RS值 = (股票价格变化率 / 基准指数价格变化率) × 100

## 多时间周期分析
- **日线RS**：短期相对强弱，适合短线操作
- **周线RS**：中期相对强弱，适合波段操作
- **月线RS**：长期相对强弱，适合长线投资

## 基准选择
可选择不同的基准进行比较：
- **沪深300**：大盘蓝筹股基准
- **中证500**：中盘股基准
- **创业板指**：成长股基准
- **行业指数**：同行业比较基准

## 强弱排名
系统对所有股票进行相对强弱排名：
- **前20%**：相对强势股票，值得关注
- **中间60%**：表现平平的股票
- **后20%**：相对弱势股票，需要谨慎
                    """
                }
            }

            # 填充帮助目录树
            self._populate_help_tree()

            # 加载快捷键列表
            self._load_shortcuts()

            # 显示默认内容
            self._show_default_help_content()

        except Exception as e:
            logger.error(f"加载帮助内容失败: {e}")

    def _populate_help_tree(self):
        """填充帮助目录树"""
        if not self.help_tree:
            return

        self.help_tree.clear()

        for key, content in self.help_content.items():
            item = QTreeWidgetItem(self.help_tree)
            item.setText(0, content["title"])
            item.setData(0, Qt.ItemDataRole.UserRole, key)

    def _load_shortcuts(self):
        """加载快捷键列表"""
        if not self.shortcut_list:
            return

        shortcuts = [
            ("Ctrl+N", "新建项目"),
            ("Ctrl+O", "打开项目"),
            ("Ctrl+S", "保存项目"),
            ("Ctrl+R", "刷新数据"),
            ("Ctrl+W", "威科夫分析"),
            ("Ctrl+E", "相对强弱分析"),
            ("Ctrl+I", "智能选股"),
            ("Ctrl+,", "系统设置"),
            ("Ctrl+L", "查看日志"),
            ("F1", "帮助"),
            ("F5", "刷新"),
            ("F11", "全屏模式"),
            ("Esc", "取消操作"),
            ("Ctrl+Q", "退出程序")
        ]

        for shortcut, description in shortcuts:
            item = QListWidgetItem(f"{shortcut:<15} {description}")
            item.setFont(QFont("Consolas", 9))
            self.shortcut_list.addItem(item)

    def _show_default_help_content(self):
        """显示默认帮助内容"""
        if self.help_content_text:
            welcome_text = """
# 欢迎使用威科夫相对强弱选股系统

请从左侧目录选择您需要了解的内容，或使用搜索功能快速找到相关信息。

## 主要功能
- 威科夫分析：基于威科夫理论的市场分析
- 相对强弱分析：识别市场中的强势股票
- 智能选股：多策略组合的自动选股系统
- 数据管理：完整的股票数据管理功能

## 快速导航
- 点击"快速开始"了解基本使用方法
- 查看"功能介绍"了解各模块详细功能
- 参考"快捷键"提高操作效率
- 浏览"常见问题"解决使用中的疑问

如有任何问题，请通过意见反馈功能联系我们。
            """
            self.help_content_text.setText(welcome_text)

    def _on_help_item_clicked(self, item: QTreeWidgetItem, column: int):
        """帮助项目点击事件"""
        if not item:
            return

        key = item.data(0, Qt.ItemDataRole.UserRole)
        if key and key in self.help_content:
            content = self.help_content[key]["content"]
            self.help_content_text.setText(content)
            self.help_topic_selected.emit(key)

    def _search_help_content(self, text: str):
        """搜索帮助内容"""
        if not text.strip():
            self._show_default_help_content()
            return

        # 简单的搜索实现
        search_results = []
        for key, content in self.help_content.items():
            if text.lower() in content["title"].lower() or text.lower() in content["content"].lower():
                search_results.append(f"# 搜索结果：{content['title']}\n\n{content['content']}")

        if search_results:
            result_text = "\n\n" + "="*50 + "\n\n".join(search_results)
            self.help_content_text.setText(result_text)
        else:
            self.help_content_text.setText(f"未找到包含 '{text}' 的帮助内容。")

    def _open_online_help(self):
        """打开在线帮助"""
        # 这里应该打开在线帮助文档
        QDesktopServices.openUrl(QUrl("https://github.com/your-repo/help"))
        logger.info("打开在线帮助")

    def _open_feedback(self):
        """打开意见反馈"""
        # 这里应该打开反馈页面或邮件客户端
        QDesktopServices.openUrl(QUrl("mailto:<EMAIL>"))
        logger.info("打开意见反馈")

    # 公共方法
    def show_help_topic(self, topic: str):
        """显示指定的帮助主题"""
        if topic in self.help_content:
            content = self.help_content[topic]["content"]
            self.help_content_text.setText(content)

            # 选中对应的树项
            for i in range(self.help_tree.topLevelItemCount()):
                item = self.help_tree.topLevelItem(i)
                if item.data(0, Qt.ItemDataRole.UserRole) == topic:
                    self.help_tree.setCurrentItem(item)
                    break

    def add_help_content(self, key: str, title: str, content: str):
        """添加帮助内容"""
        self.help_content[key] = {
            "title": title,
            "content": content
        }
        self._populate_help_tree()
