# XTTrader API 完整技术文档

## 目录
1. [简介](#简介)
2. [安装和配置](#安装和配置)
3. [连接和认证](#连接和认证)
4. [交易操作](#交易操作)
5. [账户查询](#账户查询)
6. [委托管理](#委托管理)
7. [风险控制](#风险控制)
8. [回调处理](#回调处理)
9. [错误处理](#错误处理)
10. [示例代码](#示例代码)

## 简介

XTTrader是一个专业的股票交易接口系统，为程序化交易提供完整的交易解决方案。该系统支持多账户管理、实时交易、委托查询、资金查询等功能，能够满足不同层次的交易需求。

### 主要特性
- **多账户支持**：支持同时管理多个交易账户
- **实时交易**：提供实时的买卖交易功能
- **委托管理**：完整的委托下单、撤单、查询功能
- **资金查询**：实时的资金状况和持仓查询
- **风险控制**：内置风险控制机制
- **回调通知**：支持交易状态实时回调

## 安装和配置

### 系统要求
- Python 3.6+
- Windows 10/11
- 内存：至少 4GB RAM
- 需要有效的交易账户

### 安装步骤

1. **下载XTTrader客户端**
```bash
# 从官方网站下载安装包
# 或通过pip安装（如果可用）
pip install xttrader
```

2. **配置交易环境**
```python
import xttrader

# 设置交易环境
xttrader.set_config({
    'log_level': 'INFO',
    'timeout': 30,
    'retry_count': 3
})
```

3. **验证安装**
```python
import xttrader
print(xttrader.__version__)
print("XTTrader安装成功")
```

## 连接和认证

### 建立连接

```python
import xttrader

def connect_to_trader():
    """连接到交易服务器"""
    try:
        # 连接到交易服务器
        result = xttrader.connect()
        if result:
            print("交易服务器连接成功")
            return True
        else:
            print("交易服务器连接失败")
            return False
    except Exception as e:
        print(f"连接异常: {e}")
        return False

# 使用示例
if connect_to_trader():
    print("可以开始交易操作")
```

### 账户登录

```python
def login_account(account_id, password, account_type='stock'):
    """
    账户登录
    
    参数:
    account_id: 账户ID
    password: 密码
    account_type: 账户类型 ('stock', 'future', 'option')
    
    返回:
    bool: 登录是否成功
    """
    try:
        result = xttrader.login(
            account=account_id,
            password=password,
            account_type=account_type
        )
        
        if result['success']:
            print(f"账户 {account_id} 登录成功")
            return True
        else:
            print(f"账户 {account_id} 登录失败: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"登录异常: {e}")
        return False

# 使用示例
account_info = {
    'account_id': 'your_account_id',
    'password': 'your_password',
    'account_type': 'stock'
}

if login_account(**account_info):
    print("登录成功，可以进行交易")
```

### 多账户管理

```python
class AccountManager:
    """账户管理器"""
    
    def __init__(self):
        self.accounts = {}
        self.active_account = None
    
    def add_account(self, account_id, password, account_type='stock', alias=None):
        """添加账户"""
        try:
            result = xttrader.login(
                account=account_id,
                password=password,
                account_type=account_type
            )
            
            if result['success']:
                account_info = {
                    'account_id': account_id,
                    'account_type': account_type,
                    'alias': alias or account_id,
                    'login_time': result.get('login_time'),
                    'status': 'active'
                }
                
                self.accounts[account_id] = account_info
                if self.active_account is None:
                    self.active_account = account_id
                
                print(f"账户 {alias or account_id} 添加成功")
                return True
            else:
                print(f"账户 {alias or account_id} 添加失败")
                return False
                
        except Exception as e:
            print(f"添加账户异常: {e}")
            return False
    
    def switch_account(self, account_id):
        """切换活跃账户"""
        if account_id in self.accounts:
            self.active_account = account_id
            print(f"已切换到账户: {self.accounts[account_id]['alias']}")
            return True
        else:
            print(f"账户 {account_id} 不存在")
            return False
    
    def get_active_account(self):
        """获取当前活跃账户"""
        return self.active_account
    
    def list_accounts(self):
        """列出所有账户"""
        for account_id, info in self.accounts.items():
            status = "★" if account_id == self.active_account else " "
            print(f"{status} {info['alias']} ({account_id}) - {info['status']}")

# 使用示例
manager = AccountManager()
manager.add_account('account1', 'password1', 'stock', 'Main Account')
manager.add_account('account2', 'password2', 'stock', 'Test Account')
manager.list_accounts()
```

## 交易操作

### 股票买卖

```python
def buy_stock(account_id, stock_code, quantity, price, price_type='limit'):
    """
    买入股票
    
    参数:
    account_id: 账户ID
    stock_code: 股票代码 (如 '000001.SZ')
    quantity: 买入数量（股）
    price: 买入价格
    price_type: 价格类型 ('limit'-限价, 'market'-市价)
    
    返回:
    dict: 下单结果
    """
    try:
        # 转换价格类型
        if price_type == 'limit':
            order_type = 11  # 限价单
        elif price_type == 'market':
            order_type = 12  # 市价单
        else:
            order_type = 11  # 默认限价单
        
        # 下买单
        result = xttrader.order_stock(
            account=account_id,
            stock_code=stock_code,
            order_type=23,  # 买入
            order_volume=quantity,
            price_type=order_type,
            price=price
        )
        
        if result['order_id']:
            print(f"买入委托成功 - 委托号: {result['order_id']}")
            return {
                'success': True,
                'order_id': result['order_id'],
                'message': '下单成功'
            }
        else:
            print(f"买入委托失败: {result.get('error_msg', '未知错误')}")
            return {
                'success': False,
                'order_id': None,
                'message': result.get('error_msg', '下单失败')
            }
            
    except Exception as e:
        print(f"买入异常: {e}")
        return {
            'success': False,
            'order_id': None,
            'message': str(e)
        }

def sell_stock(account_id, stock_code, quantity, price, price_type='limit'):
    """
    卖出股票
    
    参数:
    account_id: 账户ID
    stock_code: 股票代码
    quantity: 卖出数量（股）
    price: 卖出价格
    price_type: 价格类型
    
    返回:
    dict: 下单结果
    """
    try:
        # 转换价格类型
        if price_type == 'limit':
            order_type = 11  # 限价单
        elif price_type == 'market':
            order_type = 12  # 市价单
        else:
            order_type = 11  # 默认限价单
        
        # 下卖单
        result = xttrader.order_stock(
            account=account_id,
            stock_code=stock_code,
            order_type=24,  # 卖出
            order_volume=quantity,
            price_type=order_type,
            price=price
        )
        
        if result['order_id']:
            print(f"卖出委托成功 - 委托号: {result['order_id']}")
            return {
                'success': True,
                'order_id': result['order_id'],
                'message': '下单成功'
            }
        else:
            print(f"卖出委托失败: {result.get('error_msg', '未知错误')}")
            return {
                'success': False,
                'order_id': None,
                'message': result.get('error_msg', '下单失败')
            }
            
    except Exception as e:
        print(f"卖出异常: {e}")
        return {
            'success': False,
            'order_id': None,
            'message': str(e)
        }

# 使用示例
account = 'your_account_id'

# 限价买入
buy_result = buy_stock(
    account_id=account,
    stock_code='000001.SZ',
    quantity=100,
    price=10.50,
    price_type='limit'
)

if buy_result['success']:
    print(f"买入成功，委托号: {buy_result['order_id']}")

# 限价卖出
sell_result = sell_stock(
    account_id=account,
    stock_code='000001.SZ',
    quantity=100,
    price=11.00,
    price_type='limit'
)

if sell_result['success']:
    print(f"卖出成功，委托号: {sell_result['order_id']}")
```

### 批量交易

```python
def batch_order(account_id, orders):
    """
    批量下单
    
    参数:
    account_id: 账户ID
    orders: 订单列表，每个订单包含：
           {
               'stock_code': '000001.SZ',
               'action': 'buy' or 'sell',
               'quantity': 100,
               'price': 10.50,
               'price_type': 'limit'
           }
    
    返回:
    list: 下单结果列表
    """
    results = []
    
    for i, order in enumerate(orders):
        try:
            print(f"处理第 {i+1} 个订单: {order['stock_code']} {order['action']}")
            
            if order['action'] == 'buy':
                result = buy_stock(
                    account_id=account_id,
                    stock_code=order['stock_code'],
                    quantity=order['quantity'],
                    price=order['price'],
                    price_type=order.get('price_type', 'limit')
                )
            elif order['action'] == 'sell':
                result = sell_stock(
                    account_id=account_id,
                    stock_code=order['stock_code'],
                    quantity=order['quantity'],
                    price=order['price'],
                    price_type=order.get('price_type', 'limit')
                )
            else:
                result = {
                    'success': False,
                    'order_id': None,
                    'message': f"无效的操作类型: {order['action']}"
                }
            
            results.append({
                'order_index': i,
                'stock_code': order['stock_code'],
                'action': order['action'],
                'result': result
            })
            
            # 添加延迟避免频繁下单
            time.sleep(0.1)
            
        except Exception as e:
            results.append({
                'order_index': i,
                'stock_code': order.get('stock_code', 'Unknown'),
                'action': order.get('action', 'Unknown'),
                'result': {
                    'success': False,
                    'order_id': None,
                    'message': str(e)
                }
            })
    
    return results

# 使用示例
orders = [
    {
        'stock_code': '000001.SZ',
        'action': 'buy',
        'quantity': 100,
        'price': 10.50,
        'price_type': 'limit'
    },
    {
        'stock_code': '000002.SZ',
        'action': 'buy',
        'quantity': 200,
        'price': 15.20,
        'price_type': 'limit'
    },
    {
        'stock_code': '600000.SH',
        'action': 'sell',
        'quantity': 100,
        'price': 8.80,
        'price_type': 'limit'
    }
]

batch_results = batch_order('your_account_id', orders)

# 显示结果
for result in batch_results:
    status = "成功" if result['result']['success'] else "失败"
    print(f"{result['stock_code']} {result['action']}: {status}")
    if result['result']['success']:
        print(f"  委托号: {result['result']['order_id']}")
    else:
        print(f"  错误: {result['result']['message']}")
```

### 撤单操作

```python
def cancel_order(account_id, order_id):
    """
    撤销委托
    
    参数:
    account_id: 账户ID
    order_id: 委托号
    
    返回:
    dict: 撤单结果
    """
    try:
        result = xttrader.cancel_order(
            account=account_id,
            order_id=order_id
        )
        
        if result['success']:
            print(f"委托 {order_id} 撤单成功")
            return {
                'success': True,
                'message': '撤单成功'
            }
        else:
            print(f"委托 {order_id} 撤单失败: {result.get('error_msg', '未知错误')}")
            return {
                'success': False,
                'message': result.get('error_msg', '撤单失败')
            }
            
    except Exception as e:
        print(f"撤单异常: {e}")
        return {
            'success': False,
            'message': str(e)
        }

def cancel_all_orders(account_id, stock_code=None):
    """
    撤销所有委托或指定股票的所有委托
    
    参数:
    account_id: 账户ID
    stock_code: 股票代码（可选，为空则撤销所有）
    
    返回:
    dict: 撤单结果统计
    """
    try:
        # 先查询当前委托
        orders = get_orders(account_id, status='pending')
        
        if not orders:
            return {
                'success': True,
                'total': 0,
                'cancelled': 0,
                'failed': 0,
                'message': '没有待撤销的委托'
            }
        
        # 过滤指定股票的委托
        if stock_code:
            orders = [order for order in orders if order['stock_code'] == stock_code]
        
        total = len(orders)
        cancelled = 0
        failed = 0
        
        for order in orders:
            result = cancel_order(account_id, order['order_id'])
            if result['success']:
                cancelled += 1
            else:
                failed += 1
            
            # 添加延迟
            time.sleep(0.1)
        
        return {
            'success': True,
            'total': total,
            'cancelled': cancelled,
            'failed': failed,
            'message': f'撤单完成：成功{cancelled}笔，失败{failed}笔'
        }
        
    except Exception as e:
        print(f"批量撤单异常: {e}")
        return {
            'success': False,
            'total': 0,
            'cancelled': 0,
            'failed': 0,
            'message': str(e)
        }

# 使用示例
# 撤销单个委托
cancel_result = cancel_order('your_account_id', 'order_12345')

# 撤销所有委托
cancel_all_result = cancel_all_orders('your_account_id')
print(cancel_all_result['message'])

# 撤销指定股票的所有委托
cancel_stock_result = cancel_all_orders('your_account_id', '000001.SZ')
print(cancel_stock_result['message'])
```

## 账户查询

### 资金查询

```python
def get_account_funds(account_id):
    """
    查询账户资金
    
    参数:
    account_id: 账户ID
    
    返回:
    dict: 资金信息
    """
    try:
        result = xttrader.query_stock_asset(account=account_id)
        
        if result:
            funds_info = {
                'total_asset': result.get('total_asset', 0),      # 总资产
                'available_cash': result.get('cash', 0),          # 可用资金
                'frozen_cash': result.get('frozen_cash', 0),      # 冻结资金
                'market_value': result.get('market_value', 0),    # 持仓市值
                'profit_loss': result.get('profit_loss', 0),      # 浮动盈亏
                'profit_ratio': result.get('profit_ratio', 0),    # 盈亏比例
            }
            
            return {
                'success': True,
                'data': funds_info
            }
        else:
            return {
                'success': False,
                'data': None,
                'message': '查询资金失败'
            }
            
    except Exception as e:
        print(f"查询资金异常: {e}")
        return {
            'success': False,
            'data': None,
            'message': str(e)
        }

def print_account_summary(account_id):
    """打印账户资金摘要"""
    funds = get_account_funds(account_id)
    
    if funds['success']:
        data = funds['data']
        print(f"\n=== 账户资金摘要 ===")
        print(f"总资产:     ¥{data['total_asset']:,.2f}")
        print(f"可用资金:   ¥{data['available_cash']:,.2f}")
        print(f"冻结资金:   ¥{data['frozen_cash']:,.2f}")
        print(f"持仓市值:   ¥{data['market_value']:,.2f}")
        print(f"浮动盈亏:   ¥{data['profit_loss']:,.2f}")
        print(f"盈亏比例:   {data['profit_ratio']:.2f}%")
        print("=" * 20)
    else:
        print(f"查询失败: {funds['message']}")

# 使用示例
print_account_summary('your_account_id')
```

### 持仓查询

```python
def get_positions(account_id):
    """
    查询持仓
    
    参数:
    account_id: 账户ID
    
    返回:
    list: 持仓列表
    """
    try:
        result = xttrader.query_stock_positions(account=account_id)
        
        if result:
            positions = []
            for position in result:
                pos_info = {
                    'stock_code': position.get('stock_code', ''),
                    'stock_name': position.get('stock_name', ''),
                    'volume': position.get('volume', 0),              # 持仓数量
                    'available_volume': position.get('can_use_volume', 0),  # 可用数量
                    'avg_price': position.get('open_price', 0),       # 成本价
                    'current_price': position.get('last_price', 0),   # 现价
                    'market_value': position.get('market_value', 0),  # 市值
                    'profit_loss': position.get('unrealized_pnl', 0), # 浮动盈亏
                    'profit_ratio': position.get('profit_ratio', 0),  # 盈亏比例
                }
                positions.append(pos_info)
            
            return {
                'success': True,
                'data': positions
            }
        else:
            return {
                'success': True,
                'data': [],
                'message': '无持仓'
            }
            
    except Exception as e:
        print(f"查询持仓异常: {e}")
        return {
            'success': False,
            'data': [],
            'message': str(e)
        }

def print_positions(account_id):
    """打印持仓信息"""
    positions = get_positions(account_id)
    
    if positions['success']:
        data = positions['data']
        if data:
            print(f"\n=== 持仓信息 ===")
            print(f"{'股票代码':<12} {'股票名称':<10} {'数量':<8} {'成本价':<8} {'现价':<8} {'盈亏':<10} {'比例':<8}")
            print("-" * 80)
            
            total_value = 0
            total_pnl = 0
            
            for pos in data:
                total_value += pos['market_value']
                total_pnl += pos['profit_loss']
                
                print(f"{pos['stock_code']:<12} {pos['stock_name']:<10} "
                      f"{pos['volume']:<8} {pos['avg_price']:<8.2f} "
                      f"{pos['current_price']:<8.2f} {pos['profit_loss']:<10.2f} "
                      f"{pos['profit_ratio']:<8.2f}%")
            
            print("-" * 80)
            print(f"总市值: ¥{total_value:,.2f}  总盈亏: ¥{total_pnl:,.2f}")
        else:
            print("无持仓")
    else:
        print(f"查询失败: {positions['message']}")

# 使用示例
print_positions('your_account_id')
```

### 委托查询

```python
def get_orders(account_id, status='all', start_date='', end_date=''):
    """
    查询委托
    
    参数:
    account_id: 账户ID
    status: 委托状态 ('all', 'pending', 'filled', 'cancelled')
    start_date: 开始日期 (格式: '********')
    end_date: 结束日期 (格式: '********')
    
    返回:
    list: 委托列表
    """
    try:
        # 查询委托
        if start_date and end_date:
            result = xttrader.query_stock_orders(
                account=account_id,
                start_time=start_date,
                end_time=end_date
            )
        else:
            result = xttrader.query_stock_orders(account=account_id)
        
        if result:
            orders = []
            for order in result:
                order_status = order.get('order_status', 0)
                
                # 状态过滤
                if status == 'pending' and order_status not in [48, 49]:  # 未成交状态
                    continue
                elif status == 'filled' and order_status != 50:  # 已成交状态
                    continue
                elif status == 'cancelled' and order_status != 51:  # 已撤销状态
                    continue
                
                order_info = {
                    'order_id': order.get('order_id', ''),
                    'stock_code': order.get('stock_code', ''),
                    'stock_name': order.get('stock_name', ''),
                    'order_type': order.get('order_type', 0),      # 买卖类型
                    'order_volume': order.get('order_volume', 0),  # 委托数量
                    'price': order.get('price', 0),               # 委托价格
                    'filled_volume': order.get('filled_volume', 0), # 成交数量
                    'order_time': order.get('order_time', ''),    # 委托时间
                    'order_status': order_status,                 # 委托状态
                    'status_name': get_order_status_name(order_status)
                }
                orders.append(order_info)
            
            return {
                'success': True,
                'data': orders
            }
        else:
            return {
                'success': True,
                'data': [],
                'message': '无委托记录'
            }
            
    except Exception as e:
        print(f"查询委托异常: {e}")
        return {
            'success': False,
            'data': [],
            'message': str(e)
        }

def get_order_status_name(status_code):
    """获取委托状态名称"""
    status_map = {
        48: '未成交',
        49: '部分成交',
        50: '全部成交',
        51: '已撤销',
        52: '部撤',
        53: '废单'
    }
    return status_map.get(status_code, '未知状态')

def print_orders(account_id, status='all'):
    """打印委托信息"""
    orders = get_orders(account_id, status)
    
    if orders['success']:
        data = orders['data']
        if data:
            print(f"\n=== 委托信息 ===")
            print(f"{'委托号':<15} {'股票代码':<12} {'买卖':<4} {'委托量':<8} {'价格':<8} {'成交量':<8} {'状态':<8} {'时间':<20}")
            print("-" * 100)
            
            for order in data:
                order_type_name = '买入' if order['order_type'] == 23 else '卖出'
                print(f"{order['order_id']:<15} {order['stock_code']:<12} "
                      f"{order_type_name:<4} {order['order_volume']:<8} "
                      f"{order['price']:<8.2f} {order['filled_volume']:<8} "
                      f"{order['status_name']:<8} {order['order_time']:<20}")
        else:
            print("无委托记录")
    else:
        print(f"查询失败: {orders['message']}")

# 使用示例
print_orders('your_account_id', 'pending')  # 查看未成交委托
print_orders('your_account_id', 'filled')   # 查看已成交委托
```

### 成交查询

```python
def get_trades(account_id, start_date='', end_date=''):
    """
    查询成交
    
    参数:
    account_id: 账户ID
    start_date: 开始日期
    end_date: 结束日期
    
    返回:
    list: 成交列表
    """
    try:
        if start_date and end_date:
            result = xttrader.query_stock_trades(
                account=account_id,
                start_time=start_date,
                end_time=end_date
            )
        else:
            result = xttrader.query_stock_trades(account=account_id)
        
        if result:
            trades = []
            for trade in result:
                trade_info = {
                    'trade_id': trade.get('trade_id', ''),
                    'order_id': trade.get('order_id', ''),
                    'stock_code': trade.get('stock_code', ''),
                    'stock_name': trade.get('stock_name', ''),
                    'trade_type': trade.get('trade_type', 0),      # 买卖类型
                    'trade_volume': trade.get('trade_volume', 0),  # 成交数量
                    'trade_price': trade.get('trade_price', 0),    # 成交价格
                    'trade_amount': trade.get('trade_amount', 0),  # 成交金额
                    'trade_time': trade.get('trade_time', ''),     # 成交时间
                    'commission': trade.get('commission', 0),      # 手续费
                }
                trades.append(trade_info)
            
            return {
                'success': True,
                'data': trades
            }
        else:
            return {
                'success': True,
                'data': [],
                'message': '无成交记录'
            }
            
    except Exception as e:
        print(f"查询成交异常: {e}")
        return {
            'success': False,
            'data': [],
            'message': str(e)
        }

def print_trades(account_id, start_date='', end_date=''):
    """打印成交信息"""
    trades = get_trades(account_id, start_date, end_date)
    
    if trades['success']:
        data = trades['data']
        if data:
            print(f"\n=== 成交信息 ===")
            print(f"{'成交号':<15} {'股票代码':<12} {'买卖':<4} {'数量':<8} {'价格':<8} {'金额':<12} {'手续费':<8} {'时间':<20}")
            print("-" * 110)
            
            total_amount = 0
            total_commission = 0
            
            for trade in data:
                trade_type_name = '买入' if trade['trade_type'] == 23 else '卖出'
                total_amount += trade['trade_amount']
                total_commission += trade['commission']
                
                print(f"{trade['trade_id']:<15} {trade['stock_code']:<12} "
                      f"{trade_type_name:<4} {trade['trade_volume']:<8} "
                      f"{trade['trade_price']:<8.2f} {trade['trade_amount']:<12.2f} "
                      f"{trade['commission']:<8.2f} {trade['trade_time']:<20}")
            
            print("-" * 110)
            print(f"总成交金额: ¥{total_amount:,.2f}  总手续费: ¥{total_commission:,.2f}")
        else:
            print("无成交记录")
    else:
        print(f"查询失败: {trades['message']}")

# 使用示例
from datetime import datetime, timedelta

# 查询今日成交
today = datetime.now().strftime('%Y%m%d')
print_trades('your_account_id', today, today)

# 查询最近一周成交
week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
print_trades('your_account_id', week_ago, today)
```

## 委托管理

### 智能委托

```python
class SmartOrderManager:
    """智能委托管理器"""
    
    def __init__(self, account_id):
        self.account_id = account_id
        self.active_orders = {}
    
    def place_conditional_order(self, stock_code, condition_type, condition_value, 
                              action, quantity, price_offset=0):
        """
        下条件单
        
        参数:
        stock_code: 股票代码
        condition_type: 条件类型 ('price_above', 'price_below', 'volume_above')
        condition_value: 条件值
        action: 操作 ('buy', 'sell')
        quantity: 数量
        price_offset: 价格偏移（相对于触发价格）
        
        返回:
        str: 条件单ID
        """
        import uuid
        
        condition_order = {
            'id': str(uuid.uuid4()),
            'stock_code': stock_code,
            'condition_type': condition_type,
            'condition_value': condition_value,
            'action': action,
            'quantity': quantity,
            'price_offset': price_offset,
            'status': 'waiting',
            'created_time': datetime.now()
        }
        
        self.active_orders[condition_order['id']] = condition_order
        print(f"条件单已创建: {condition_order['id']}")
        
        return condition_order['id']
    
    def check_conditions(self, market_data):
        """检查条件单触发条件"""
        triggered_orders = []
        
        for order_id, order in self.active_orders.items():
            if order['status'] != 'waiting':
                continue
            
            stock_code = order['stock_code']
            if stock_code not in market_data:
                continue
            
            current_price = market_data[stock_code]['price']
            current_volume = market_data[stock_code]['volume']
            
            triggered = False
            
            # 检查触发条件
            if order['condition_type'] == 'price_above' and current_price >= order['condition_value']:
                triggered = True
            elif order['condition_type'] == 'price_below' and current_price <= order['condition_value']:
                triggered = True
            elif order['condition_type'] == 'volume_above' and current_volume >= order['condition_value']:
                triggered = True
            
            if triggered:
                order['status'] = 'triggered'
                order['trigger_time'] = datetime.now()
                order['trigger_price'] = current_price
                triggered_orders.append(order)
        
        return triggered_orders
    
    def execute_triggered_orders(self, triggered_orders):
        """执行已触发的条件单"""
        for order in triggered_orders:
            try:
                # 计算执行价格
                trigger_price = order['trigger_price']
                execute_price = trigger_price + order['price_offset']
                
                # 执行交易
                if order['action'] == 'buy':
                    result = buy_stock(
                        account_id=self.account_id,
                        stock_code=order['stock_code'],
                        quantity=order['quantity'],
                        price=execute_price
                    )
                else:
                    result = sell_stock(
                        account_id=self.account_id,
                        stock_code=order['stock_code'],
                        quantity=order['quantity'],
                        price=execute_price
                    )
                
                if result['success']:
                    order['status'] = 'executed'
                    order['order_id'] = result['order_id']
                    order['execute_time'] = datetime.now()
                    print(f"条件单 {order['id']} 执行成功，委托号: {result['order_id']}")
                else:
                    order['status'] = 'failed'
                    order['error_message'] = result['message']
                    print(f"条件单 {order['id']} 执行失败: {result['message']}")
                
            except Exception as e:
                order['status'] = 'failed'
                order['error_message'] = str(e)
                print(f"条件单 {order['id']} 执行异常: {e}")
    
    def cancel_conditional_order(self, order_id):
        """取消条件单"""
        if order_id in self.active_orders:
            self.active_orders[order_id]['status'] = 'cancelled'
            print(f"条件单 {order_id} 已取消")
            return True
        else:
            print(f"条件单 {order_id} 不存在")
            return False
    
    def list_conditional_orders(self):
        """列出所有条件单"""
        print("\n=== 条件单列表 ===")
        for order_id, order in self.active_orders.items():
            print(f"ID: {order_id[:8]}...")
            print(f"  股票: {order['stock_code']}")
            print(f"  条件: {order['condition_type']} {order['condition_value']}")
            print(f"  操作: {order['action']} {order['quantity']}股")
            print(f"  状态: {order['status']}")
            print("-" * 30)

# 使用示例
smart_manager = SmartOrderManager('your_account_id')

# 创建条件单：当价格突破10.50时买入
order_id = smart_manager.place_conditional_order(
    stock_code='000001.SZ',
    condition_type='price_above',
    condition_value=10.50,
    action='buy',
    quantity=100,
    price_offset=0.01  # 比触发价高0.01元执行
)

# 模拟市场数据检查
market_data = {
    '000001.SZ': {'price': 10.52, 'volume': 1000000}
}

triggered = smart_manager.check_conditions(market_data)
if triggered:
    smart_manager.execute_triggered_orders(triggered)

smart_manager.list_conditional_orders()
```

## 风险控制

### 风险检查

```python
class RiskManager:
    """风险管理器"""
    
    def __init__(self, account_id):
        self.account_id = account_id
        self.risk_rules = {
            'max_position_ratio': 0.3,    # 单只股票最大持仓比例
            'max_daily_loss': 0.05,       # 日最大亏损比例
            'max_total_position': 0.9,    # 最大总持仓比例
            'min_available_cash': 10000,  # 最小可用资金
        }
    
    def check_buy_risk(self, stock_code, quantity, price):
        """
        检查买入风险
        
        返回:
        dict: 风险检查结果
        """
        try:
            # 获取账户信息
            funds = get_account_funds(self.account_id)
            if not funds['success']:
                return {'allowed': False, 'reason': '无法获取账户信息'}
            
            positions = get_positions(self.account_id)
            if not positions['success']:
                return {'allowed': False, 'reason': '无法获取持仓信息'}
            
            fund_data = funds['data']
            position_data = positions['data']
            
            # 计算买入金额
            buy_amount = quantity * price
            
            # 检查可用资金
            if buy_amount > fund_data['available_cash']:
                return {'allowed': False, 'reason': '可用资金不足'}
            
            # 检查最小可用资金
            remaining_cash = fund_data['available_cash'] - buy_amount
            if remaining_cash < self.risk_rules['min_available_cash']:
                return {'allowed': False, 'reason': f'买入后可用资金将低于{self.risk_rules["min_available_cash"]}元'}
            
            # 检查单只股票持仓比例
            current_position = 0
            for pos in position_data:
                if pos['stock_code'] == stock_code:
                    current_position = pos['market_value']
                    break
            
            new_position_value = current_position + buy_amount
            position_ratio = new_position_value / fund_data['total_asset']
            
            if position_ratio > self.risk_rules['max_position_ratio']:
                return {'allowed': False, 'reason': f'单只股票持仓比例将超过{self.risk_rules["max_position_ratio"]*100}%'}
            
            # 检查总持仓比例
            total_position_value = fund_data['market_value'] + buy_amount
            total_position_ratio = total_position_value / fund_data['total_asset']
            
            if total_position_ratio > self.risk_rules['max_total_position']:
                return {'allowed': False, 'reason': f'总持仓比例将超过{self.risk_rules["max_total_position"]*100}%'}
            
            return {'allowed': True, 'reason': '风险检查通过'}
            
        except Exception as e:
            return {'allowed': False, 'reason': f'风险检查异常: {e}'}
    
    def check_sell_risk(self, stock_code, quantity):
        """检查卖出风险"""
        try:
            # 获取持仓信息
            positions = get_positions(self.account_id)
            if not positions['success']:
                return {'allowed': False, 'reason': '无法获取持仓信息'}
            
            # 查找对应持仓
            target_position = None
            for pos in positions['data']:
                if pos['stock_code'] == stock_code:
                    target_position = pos
                    break
            
            if not target_position:
                return {'allowed': False, 'reason': '无该股票持仓'}
            
            # 检查可用数量
            if quantity > target_position['available_volume']:
                return {'allowed': False, 'reason': f'可用数量不足，可卖出{target_position["available_volume"]}股'}
            
            return {'allowed': True, 'reason': '风险检查通过'}
            
        except Exception as e:
            return {'allowed': False, 'reason': f'风险检查异常: {e}'}
    
    def check_daily_loss(self):
        """检查日亏损"""
        try:
            # 这里需要获取当日盈亏数据
            # 实际实现需要根据具体API调整
            funds = get_account_funds(self.account_id)
            if not funds['success']:
                return {'exceeded': False, 'reason': '无法获取账户信息'}
            
            # 假设有当日盈亏字段
            daily_pnl = funds['data'].get('daily_pnl', 0)
            total_asset = funds['data']['total_asset']
            
            if daily_pnl < 0:
                loss_ratio = abs(daily_pnl) / total_asset
                if loss_ratio > self.risk_rules['max_daily_loss']:
                    return {'exceeded': True, 'reason': f'日亏损已超过{self.risk_rules["max_daily_loss"]*100}%'}
            
            return {'exceeded': False, 'reason': '日亏损检查通过'}
            
        except Exception as e:
            return {'exceeded': False, 'reason': f'日亏损检查异常: {e}'}
    
    def safe_buy(self, stock_code, quantity, price):
        """安全买入（带风险检查）"""
        # 风险检查
        risk_check = self.check_buy_risk(stock_code, quantity, price)
        if not risk_check['allowed']:
            print(f"买入风险检查失败: {risk_check['reason']}")
            return {'success': False, 'message': risk_check['reason']}
        
        # 日亏损检查
        daily_check = self.check_daily_loss()
        if daily_check['exceeded']:
            print(f"日亏损检查失败: {daily_check['reason']}")
            return {'success': False, 'message': daily_check['reason']}
        
        # 执行买入
        return buy_stock(self.account_id, stock_code, quantity, price)
    
    def safe_sell(self, stock_code, quantity, price):
        """安全卖出（带风险检查）"""
        # 风险检查
        risk_check = self.check_sell_risk(stock_code, quantity)
        if not risk_check['allowed']:
            print(f"卖出风险检查失败: {risk_check['reason']}")
            return {'success': False, 'message': risk_check['reason']}
        
        # 执行卖出
        return sell_stock(self.account_id, stock_code, quantity, price)

# 使用示例
risk_manager = RiskManager('your_account_id')

# 安全买入
result = risk_manager.safe_buy('000001.SZ', 100, 10.50)
if result['success']:
    print("安全买入成功")
else:
    print(f"买入被拒绝: {result['message']}")
```

## 回调处理

### 交易回调

```python
class TradeCallback:
    """交易回调处理器"""
    
    def __init__(self):
        self.order_callbacks = {}
        self.trade_callbacks = {}
    
    def on_order_update(self, order_data):
        """委托状态更新回调"""
        try:
            order_id = order_data.get('order_id', '')
            status = order_data.get('order_status', 0)
            
            print(f"委托更新: {order_id} - {get_order_status_name(status)}")
            
            # 处理特定委托的回调
            if order_id in self.order_callbacks:
                callback_func = self.order_callbacks[order_id]
                callback_func(order_data)
            
            # 处理委托完成
            if status in [50, 51, 53]:  # 全部成交、已撤销、废单
                self._handle_order_finished(order_data)
                
        except Exception as e:
            print(f"委托回调处理异常: {e}")
    
    def on_trade_update(self, trade_data):
        """成交回调"""
        try:
            trade_id = trade_data.get('trade_id', '')
            order_id = trade_data.get('order_id', '')
            
            print(f"成交通知: {trade_id} (委托号: {order_id})")
            
            # 处理特定成交的回调
            if trade_id in self.trade_callbacks:
                callback_func = self.trade_callbacks[trade_id]
                callback_func(trade_data)
                
        except Exception as e:
            print(f"成交回调处理异常: {e}")
    
    def register_order_callback(self, order_id, callback_func):
        """注册委托回调"""
        self.order_callbacks[order_id] = callback_func
    
    def register_trade_callback(self, trade_id, callback_func):
        """注册成交回调"""
        self.trade_callbacks[trade_id] = callback_func
    
    def _handle_order_finished(self, order_data):
        """处理委托完成"""
        order_id = order_data.get('order_id', '')
        status = order_data.get('order_status', 0)
        
        if status == 50:  # 全部成交
            print(f"委托 {order_id} 全部成交")
        elif status == 51:  # 已撤销
            print(f"委托 {order_id} 已撤销")
        elif status == 53:  # 废单
            print(f"委托 {order_id} 成为废单")
        
        # 清理回调
        if order_id in self.order_callbacks:
            del self.order_callbacks[order_id]

# 设置回调
def setup_callbacks():
    """设置交易回调"""
    callback_handler = TradeCallback()
    
    try:
        # 注册回调函数
        xttrader.set_order_callback(callback_handler.on_order_update)
        xttrader.set_trade_callback(callback_handler.on_trade_update)
        
        print("交易回调设置成功")
        return callback_handler
        
    except Exception as e:
        print(f"设置回调异常: {e}")
        return None

# 使用示例
callback_handler = setup_callbacks()

if callback_handler:
    # 注册特定委托的回调
    def my_order_callback(order_data):
        print(f"我的委托状态更新: {order_data}")
    
    # 下单后注册回调
    buy_result = buy_stock('your_account_id', '000001.SZ', 100, 10.50)
    if buy_result['success']:
        callback_handler.register_order_callback(
            buy_result['order_id'], 
            my_order_callback
        )
```

## 错误处理

### 错误码定义

```python
class XTTraderError:
    """XTTrader错误处理"""
    
    # 错误码定义
    ERROR_CODES = {
        # 连接错误
        -1: "连接服务器失败",
        -2: "网络连接中断",
        -3: "连接超时",
        
        # 认证错误
        -10: "账户不存在",
        -11: "密码错误",
        -12: "账户被锁定",
        -13: "认证超时",
        
        # 交易错误
        -20: "股票代码无效",
        -21: "价格超出涨跌停限制",
        -22: "数量不符合要求",
        -23: "资金不足",
        -24: "持仓不足",
        -25: "委托价格无效",
        -26: "委托数量无效",
        -27: "非交易时间",
        -28: "股票停牌",
        
        # 系统错误
        -30: "系统繁忙",
        -31: "服务暂不可用",
        -32: "数据库错误",
        -33: "内部错误",
        
        # 权限错误
        -40: "无交易权限",
        -41: "功能未开通",
        -42: "操作被禁止",
    }
    
    @staticmethod
    def get_error_message(error_code):
        """获取错误信息"""
        return XTTraderError.ERROR_CODES.get(error_code, f"未知错误 ({error_code})")
    
    @staticmethod
    def handle_error(error_code, context=""):
        """处理错误"""
        error_msg = XTTraderError.get_error_message(error_code)
        full_msg = f"{context}: {error_msg}"
        print(full_msg)
        return full_msg

def safe_trade_operation(operation_func, *args, **kwargs):
    """安全的交易操作包装器"""
    max_retry = 3
    retry_count = 0
    
    while retry_count < max_retry:
        try:
            result = operation_func(*args, **kwargs)
            
            # 检查结果
            if isinstance(result, dict) and not result.get('success', True):
                error_code = result.get('error_code', -1)
                
                # 某些错误可以重试
                if error_code in [-1, -2, -3, -30, -31]:  # 连接或系统错误
                    retry_count += 1
                    print(f"操作失败，{2}秒后重试... ({retry_count}/{max_retry})")
                    time.sleep(2)
                    continue
                else:
                    # 其他错误不重试
                    XTTraderError.handle_error(error_code, "交易操作")
                    return result
            
            return result
            
        except Exception as e:
            retry_count += 1
            if retry_count < max_retry:
                print(f"操作异常，{2}秒后重试... ({retry_count}/{max_retry}): {e}")
                time.sleep(2)
            else:
                print(f"操作最终失败: {e}")
                return {
                    'success': False,
                    'error_code': -33,
                    'message': str(e)
                }
    
    return {
        'success': False,
        'error_code': -30,
        'message': '重试次数已用完'
    }

# 使用示例
def safe_buy_stock(account_id, stock_code, quantity, price):
    """安全买入股票"""
    return safe_trade_operation(
        buy_stock, 
        account_id, stock_code, quantity, price
    )

# 使用安全操作
result = safe_buy_stock('your_account_id', '000001.SZ', 100, 10.50)
```

## 示例代码

### 完整交易系统示例

```python
import xttrader
import time
import threading
from datetime import datetime

class TradingSystem:
    """完整的交易系统"""
    
    def __init__(self, account_id, password):
        self.account_id = account_id
        self.password = password
        self.is_connected = False
        self.is_logged_in = False
        self.risk_manager = None
        self.callback_handler = None
        
    def initialize(self):
        """初始化系统"""
        try:
            # 连接服务器
            if not xttrader.connect():
                print("连接服务器失败")
                return False
            
            self.is_connected = True
            print("服务器连接成功")
            
            # 账户登录
            login_result = xttrader.login(
                account=self.account_id,
                password=self.password,
                account_type='stock'
            )
            
            if not login_result['success']:
                print(f"账户登录失败: {login_result.get('message', '未知错误')}")
                return False
            
            self.is_logged_in = True
            print("账户登录成功")
            
            # 初始化风险管理
            self.risk_manager = RiskManager(self.account_id)
            
            # 设置回调
            self.callback_handler = setup_callbacks()
            
            print("交易系统初始化完成")
            return True
            
        except Exception as e:
            print(f"初始化异常: {e}")
            return False
    
    def get_account_status(self):
        """获取账户状态"""
        if not self.is_logged_in:
            return None
        
        try:
            funds = get_account_funds(self.account_id)
            positions = get_positions(self.account_id)
            
            return {
                'funds': funds['data'] if funds['success'] else None,
                'positions': positions['data'] if positions['success'] else [],
                'position_count': len(positions['data']) if positions['success'] else 0
            }
            
        except Exception as e:
            print(f"获取账户状态异常: {e}")
            return None
    
    def execute_trade_plan(self, trade_plan):
        """
        执行交易计划
        
        trade_plan: 交易计划列表
        [
            {
                'action': 'buy',
                'stock_code': '000001.SZ',
                'quantity': 100,
                'price': 10.50,
                'condition': None  # 可选的条件
            }
        ]
        """
        results = []
        
        for i, trade in enumerate(trade_plan):
            try:
                print(f"执行第 {i+1} 个交易: {trade['stock_code']} {trade['action']}")
                
                if trade['action'] == 'buy':
                    result = self.risk_manager.safe_buy(
                        trade['stock_code'],
                        trade['quantity'],
                        trade['price']
                    )
                elif trade['action'] == 'sell':
                    result = self.risk_manager.safe_sell(
                        trade['stock_code'],
                        trade['quantity'],
                        trade['price']
                    )
                else:
                    result = {
                        'success': False,
                        'message': f"无效的操作类型: {trade['action']}"
                    }
                
                results.append({
                    'trade_index': i,
                    'trade': trade,
                    'result': result
                })
                
                # 添加延迟
                time.sleep(0.5)
                
            except Exception as e:
                results.append({
                    'trade_index': i,
                    'trade': trade,
                    'result': {
                        'success': False,
                        'message': str(e)
                    }
                })
        
        return results
    
    def monitor_positions(self, check_interval=30):
        """监控持仓"""
        def monitor_loop():
            while self.is_logged_in:
                try:
                    status = self.get_account_status()
                    if status:
                        self._check_position_alerts(status)
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    print(f"持仓监控异常: {e}")
                    time.sleep(check_interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        print(f"持仓监控已启动，检查间隔 {check_interval} 秒")
    
    def _check_position_alerts(self, status):
        """检查持仓警报"""
        positions = status['positions']
        
        for pos in positions:
            # 检查盈亏比例
            if pos['profit_ratio'] <= -10:  # 亏损超过10%
                print(f"警告: {pos['stock_code']} 亏损达到 {pos['profit_ratio']:.2f}%")
            
            elif pos['profit_ratio'] >= 20:  # 盈利超过20%
                print(f"提醒: {pos['stock_code']} 盈利达到 {pos['profit_ratio']:.2f}%")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.is_logged_in = False
            xttrader.disconnect()
            self.is_connected = False
            print("交易系统已关闭")
        except Exception as e:
            print(f"清理异常: {e}")

# 使用示例
def main():
    # 创建交易系统
    trading_system = TradingSystem('your_account_id', 'your_password')
    
    # 初始化
    if not trading_system.initialize():
        return
    
    try:
        # 获取账户状态
        status = trading_system.get_account_status()
        if status:
            print(f"可用资金: ¥{status['funds']['available_cash']:,.2f}")
            print(f"持仓数量: {status['position_count']} 只")
        
        # 制定交易计划
        trade_plan = [
            {
                'action': 'buy',
                'stock_code': '000001.SZ',
                'quantity': 100,
                'price': 10.50
            },
            {
                'action': 'buy',
                'stock_code': '000002.SZ',
                'quantity': 200,
                'price': 15.20
            }
        ]
        
        # 执行交易计划
        print("\n执行交易计划...")
        results = trading_system.execute_trade_plan(trade_plan)
        
        # 显示结果
        success_count = 0
        for result in results:
            if result['result']['success']:
                success_count += 1
                print(f"✓ {result['trade']['stock_code']} {result['trade']['action']} 成功")
            else:
                print(f"✗ {result['trade']['stock_code']} {result['trade']['action']} 失败: {result['result']['message']}")
        
        print(f"\n交易完成: {success_count}/{len(results)} 成功")
        
        # 启动持仓监控
        trading_system.monitor_positions(30)
        
        # 等待一段时间
        print("系统运行中，按 Ctrl+C 退出...")
        time.sleep(60)
        
    except KeyboardInterrupt:
        print("用户中断")
    except Exception as e:
        print(f"系统异常: {e}")
    finally:
        trading_system.cleanup()

if __name__ == "__main__":
    main()
```

## 注意事项

1. **安全性**：
   - 妥善保管账户密码
   - 使用安全的网络环境
   - 定期更换密码

2. **风险控制**：
   - 设置合理的止损点
   - 控制单笔交易金额
   - 分散投资风险

3. **合规性**：
   - 遵守交易所规则
   - 注意交易时间限制
   - 避免违规操作

4. **系统稳定性**：
   - 处理网络异常
   - 实现重连机制
   - 记录操作日志

5. **性能优化**：
   - 避免频繁查询
   - 合理使用缓存
   - 优化代码逻辑

## 技术支持

如需技术支持，请联系：
- 官方网站：[XTTrader官网]
- 技术文档：[在线文档]
- 客服热线：[客服电话]

---

*本文档版本：v2.0*  
*最后更新：2024年*