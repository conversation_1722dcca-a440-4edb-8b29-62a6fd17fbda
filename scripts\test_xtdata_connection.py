#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XtData连接集成测试脚本
测试XtData适配器的连接和数据获取功能
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data_sources import (
    DataSourceConfig, XtDataAdapter, DataSourceManager,
    ConnectionPool, DataFormatter
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


def test_xtdata_adapter():
    """测试XtData适配器基本功能"""
    print("=" * 60)
    print("测试XtData适配器基本功能")
    print("=" * 60)
    
    try:
        # 创建配置
        config = DataSourceConfig(
            name="xtdata_test",
            enabled=True,
            timeout=30,
            retry_times=3,
            auto_reconnect=True,
            config={
                "ip": "127.0.0.1",
                "port": 58610
            }
        )
        
        print(f"✓ 创建配置成功: {config.name}")
        
        # 创建适配器
        adapter = XtDataAdapter(config)
        print(f"✓ 创建适配器成功: {adapter.name}")
        
        # 测试连接
        print("\n正在测试连接...")
        if adapter.connect():
            print("✓ 连接成功")
            
            # 测试连接状态
            if adapter.test_connection():
                print("✓ 连接状态正常")
            else:
                print("✗ 连接状态异常")
                return False
                
        else:
            print("✗ 连接失败")
            return False
        
        # 测试获取市场数据
        print("\n正在测试数据获取...")
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
            
            market_data = adapter.get_market_data(
                symbol="000001.SZ",
                period="1d",
                start_date=start_date,
                end_date=end_date,
                dividend_type="none"
            )
            
            print(f"✓ 获取市场数据成功")
            print(f"  - 股票代码: {market_data.symbol}")
            print(f"  - 数据行数: {len(market_data.data)}")
            print(f"  - 数据列数: {len(market_data.data.columns)}")
            print(f"  - 数据源: {market_data.source}")
            print(f"  - 更新时间: {market_data.update_time}")
            
            # 显示数据样例
            if not market_data.data.empty:
                print("\n数据样例:")
                print(market_data.data.head(3))
            
        except Exception as e:
            print(f"✗ 获取市场数据失败: {e}")
            return False
        
        # 测试获取股票列表
        print("\n正在测试股票列表获取...")
        try:
            stock_list = adapter.get_stock_list(market="SZ")
            if stock_list:
                print(f"✓ 获取股票列表成功，共 {len(stock_list)} 只股票")
                print("样例股票:")
                for i, stock in enumerate(stock_list[:3]):
                    print(f"  {i+1}. {stock}")
            else:
                print("✗ 获取股票列表失败或为空")
                
        except Exception as e:
            print(f"✗ 获取股票列表失败: {e}")
        
        # 断开连接
        adapter.disconnect()
        print("\n✓ 断开连接成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        logger.exception("XtData适配器测试失败")
        return False


def test_data_source_manager():
    """测试数据源管理器"""
    print("\n" + "=" * 60)
    print("测试数据源管理器")
    print("=" * 60)
    
    try:
        # 创建管理器
        manager = DataSourceManager()
        print("✓ 创建数据源管理器成功")
        
        # 创建XtData适配器
        config = DataSourceConfig(
            name="xtdata_manager_test",
            enabled=True,
            timeout=30,
            config={"ip": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        print("✓ 创建XtData适配器成功")
        
        # 注册数据源
        manager.register_source(adapter)
        print("✓ 注册数据源成功")
        
        # 设置主数据源
        manager.set_primary_source("xtdata_manager_test")
        print("✓ 设置主数据源成功")
        
        # 健康检查
        print("\n正在进行健康检查...")
        health_status = manager.health_check()
        
        for source_name, status in health_status.items():
            print(f"  - {source_name}: {status.value}")
        
        # 获取统计信息
        stats = manager.get_statistics()
        print("\n数据源统计信息:")
        print(f"  - 总数据源数: {stats['total_sources']}")
        print(f"  - 健康数据源数: {stats['healthy_sources']}")
        print(f"  - 故障数据源数: {stats['failed_sources']}")
        print(f"  - 主数据源: {stats['primary_source']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据源管理器测试失败: {e}")
        logger.exception("数据源管理器测试失败")
        return False


def test_connection_pool():
    """测试连接池"""
    print("\n" + "=" * 60)
    print("测试连接池")
    print("=" * 60)
    
    try:
        # 定义连接工厂
        def connection_factory():
            config = DataSourceConfig(
                name="xtdata_pool_test",
                config={"ip": "127.0.0.1", "port": 58610}
            )
            return XtDataAdapter(config)
        
        # 创建连接池
        pool = ConnectionPool(
            name="test_pool",
            factory=connection_factory,
            max_size=5,
            min_size=2
        )
        print("✓ 创建连接池成功")
        
        # 获取连接
        print("\n正在测试连接获取...")
        connections = []
        for i in range(3):
            conn = pool.get_connection()
            if conn:
                connections.append(conn)
                print(f"✓ 获取连接 {i+1} 成功")
            else:
                print(f"✗ 获取连接 {i+1} 失败")
        
        # 获取连接池状态
        status = pool.get_status()
        print(f"\n连接池状态:")
        print(f"  - 总连接数: {status['total_connections']}")
        print(f"  - 活跃连接数: {status['active_connections']}")
        print(f"  - 空闲连接数: {status['idle_connections']}")
        
        # 归还连接
        print("\n正在归还连接...")
        for i, conn in enumerate(connections):
            if pool.return_connection(conn):
                print(f"✓ 归还连接 {i+1} 成功")
            else:
                print(f"✗ 归还连接 {i+1} 失败")
        
        # 关闭连接池
        pool.close()
        print("✓ 关闭连接池成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 连接池测试失败: {e}")
        logger.exception("连接池测试失败")
        return False


def test_data_formatter():
    """测试数据格式化工具"""
    print("\n" + "=" * 60)
    print("测试数据格式化工具")
    print("=" * 60)
    
    try:
        # 创建格式化工具
        formatter = DataFormatter()
        print("✓ 创建数据格式化工具成功")
        
        # 获取测试数据
        config = DataSourceConfig(
            name="xtdata_formatter_test",
            config={"ip": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        
        if adapter.connect():
            print("✓ 连接数据源成功")
            
            # 获取原始数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
            
            market_data = adapter.get_market_data(
                symbol="000001.SZ",
                period="1d",
                start_date=start_date,
                end_date=end_date
            )
            
            print("✓ 获取测试数据成功")
            
            # 数据质量验证
            print("\n正在进行数据质量验证...")
            quality_report = formatter.validate_data_quality(market_data.data)
            
            print(f"数据质量报告:")
            print(f"  - 总行数: {quality_report.total_rows}")
            print(f"  - 缺失值数量: {quality_report.missing_values}")
            print(f"  - 重复行数量: {quality_report.duplicates}")
            print(f"  - 异常值数量: {quality_report.anomalies}")
            print(f"  - 数据质量评分: {quality_report.quality_score:.2f}")
            print(f"  - 数据是否有效: {'是' if quality_report.is_valid else '否'}")
            
            # 数据清洗
            if not quality_report.is_valid:
                print("\n正在进行数据清洗...")
                cleaned_data = formatter.clean_data(market_data.data)
                print(f"✓ 数据清洗完成，清洗后行数: {len(cleaned_data)}")
            
            adapter.disconnect()
            
        else:
            print("✗ 连接数据源失败，跳过数据格式化测试")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据格式化工具测试失败: {e}")
        logger.exception("数据格式化工具测试失败")
        return False


def performance_test():
    """性能测试"""
    print("\n" + "=" * 60)
    print("性能测试")
    print("=" * 60)
    
    try:
        config = DataSourceConfig(
            name="xtdata_performance_test",
            config={"ip": "127.0.0.1", "port": 58610}
        )
        
        adapter = XtDataAdapter(config)
        
        if not adapter.connect():
            print("✗ 连接失败，跳过性能测试")
            return False
        
        # 测试数据获取性能
        symbols = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"]
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"正在测试 {len(symbols)} 只股票的数据获取性能...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        start_time = time.time()
        success_count = 0
        
        for i, symbol in enumerate(symbols, 1):
            try:
                print(f"  正在获取 {symbol} 数据... ({i}/{len(symbols)})")
                market_data = adapter.get_market_data(
                    symbol=symbol,
                    period="1d",
                    start_date=start_date,
                    end_date=end_date
                )
                
                if market_data and not market_data.data.empty:
                    success_count += 1
                    print(f"    ✓ 成功，获取 {len(market_data.data)} 行数据")
                else:
                    print(f"    ✗ 失败，数据为空")
                    
            except Exception as e:
                print(f"    ✗ 失败，错误: {e}")
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n性能测试结果:")
        print(f"  - 总耗时: {elapsed_time:.2f} 秒")
        print(f"  - 成功获取: {success_count}/{len(symbols)} 只股票")
        print(f"  - 平均每只股票耗时: {elapsed_time/len(symbols):.2f} 秒")
        print(f"  - 成功率: {success_count/len(symbols)*100:.1f}%")
        
        adapter.disconnect()
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        logger.exception("性能测试失败")
        return False


def main():
    """主函数"""
    print("威科夫相对强弱选股系统 - XtData集成测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查MiniQMT是否运行
    print("注意: 请确保MiniQMT客户端已启动并正常运行")
    input("按回车键开始测试...")
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("XtData适配器基本功能", test_xtdata_adapter),
        ("数据源管理器", test_data_source_manager),
        ("连接池", test_connection_pool),
        ("数据格式化工具", test_data_formatter),
        ("性能测试", performance_test),
    ]
    
    for test_name, test_func in tests:
        print(f"\n开始执行: {test_name}")
        try:
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
                
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
            logger.exception(f"{test_name} 测试异常")
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试数: {passed_tests}")
    print(f"失败测试数: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in test_results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"  {symbol} {test_name}: {status}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！XtData集成测试成功完成。")
        return 0
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息并修复问题。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 