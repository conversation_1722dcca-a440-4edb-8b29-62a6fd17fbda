"""
数据服务层

整合威科夫分析引擎、相对强弱引擎、选股引擎，
为UI层提供统一的数据接口
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..engines.wyckoff import WyckoffAnalysisEngine, MarketStructure, WyckoffSignal
from ..engines.relative_strength import RelativeStrengthEngine, RSResult, RSAnalysis
from ..engines.selection import StockSelectionEngine, SelectionResult, StrategyConfig
from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError, CalculationError
from ..utils.cache import CacheManager
from ..database.database_manager import DatabaseManager
from ..data_sources.manager import DataSourceManager
from .data_download_service import DataDownloadService, DownloadProgress

logger = get_logger(__name__)


@dataclass
class StockInfo:
    """股票基本信息"""
    symbol: str
    name: str
    sector: str
    market_cap: float
    price: float
    change: float
    change_percent: float
    volume: int
    last_update: datetime


@dataclass
class AnalysisResult:
    """综合分析结果"""
    stock_info: StockInfo
    wyckoff_analysis: Optional[MarketStructure]
    wyckoff_signals: List[WyckoffSignal]
    rs_analysis: Optional[RSAnalysis]
    rs_ranking: Optional[RSResult]
    analysis_time: datetime


class DataService:
    """数据服务类"""

    def __init__(self, data_source_manager: Optional[DataSourceManager] = None, use_real_data: bool = True):
        """
        初始化数据服务

        Args:
            data_source_manager: 数据源管理器
            use_real_data: 是否使用真实数据
        """
        # 初始化分析引擎
        self.wyckoff_engine = WyckoffAnalysisEngine()
        self.rs_engine = RelativeStrengthEngine()
        self.selection_engine = StockSelectionEngine()

        # 缓存管理器
        self.cache_manager = CacheManager()

        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 数据管理
        self.use_real_data = use_real_data
        self.data_source_manager = data_source_manager
        self.db_manager = DatabaseManager() if use_real_data else None
        self.download_service = None

        if use_real_data and data_source_manager:
            self.download_service = DataDownloadService(data_source_manager, self.db_manager)

        # 数据缓存
        self.stocks_data: Dict[str, pd.DataFrame] = {}
        self.benchmark_data: Optional[pd.DataFrame] = None
        self.stock_list: List[StockInfo] = []

        logger.info(f"数据服务初始化完成 (使用{'真实' if use_real_data else '模拟'}数据)")
    
    def create_sample_data(self) -> Dict[str, pd.DataFrame]:
        """创建示例数据用于演示"""
        np.random.seed(42)
        
        # 创建日期范围
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        dates = dates[dates.weekday < 5]  # 只保留工作日
        
        stocks_data = {}
        
        # 示例股票列表
        stock_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '002594.SZ',
            '600000.SH', '600036.SH', '600519.SH', '600887.SH', '601318.SH'
        ]
        
        for symbol in stock_symbols:
            # 生成价格数据
            base_price = np.random.uniform(10, 100)
            returns = np.random.normal(0.001, 0.02, len(dates))
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # 生成成交量数据
            base_volume = np.random.randint(1000000, 10000000)
            volumes = np.random.lognormal(np.log(base_volume), 0.5, len(dates))
            
            # 创建OHLC数据
            data = pd.DataFrame({
                'date': dates,
                'open': [p * np.random.uniform(0.98, 1.02) for p in prices],
                'high': [p * np.random.uniform(1.00, 1.05) for p in prices],
                'low': [p * np.random.uniform(0.95, 1.00) for p in prices],
                'close': prices,
                'volume': volumes.astype(int)
            })
            
            data.set_index('date', inplace=True)
            stocks_data[symbol] = data
        
        return stocks_data
    
    def load_stock_list(self) -> List[StockInfo]:
        """加载股票列表"""
        try:
            if self.use_real_data and self.db_manager:
                # 从数据库加载真实股票数据
                return self._load_real_stock_list()
            else:
                # 使用模拟数据
                return self._load_sample_stock_list()

        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            raise DataSourceError(f"加载股票列表失败: {e}")

    def _load_real_stock_list(self) -> List[StockInfo]:
        """从数据库加载真实股票列表（仅基本信息，不获取实时价格）"""
        try:
            stock_records = self.db_manager.get_stock_list()
            stock_list = []

            for record in stock_records:
                # 只加载基本信息，不获取实时价格（避免启动时大量API调用）
                stock_info = StockInfo(
                    symbol=record['stock_code'],
                    name=record['stock_name'],
                    sector=record.get('industry', '未知'),
                    market_cap=record.get('market_cap', 0.0),
                    price=0.0,  # 启动时不获取实时价格
                    change=0.0,
                    change_percent=0.0,
                    volume=0,
                    last_update=datetime.now()
                )
                stock_list.append(stock_info)

            self.stock_list = stock_list
            logger.info(f"从数据库加载股票列表完成，共{len(stock_list)}只股票（不含实时价格）")
            return stock_list

        except Exception as e:
            logger.error(f"从数据库加载股票列表失败: {e}")
            # 如果数据库加载失败，返回示例数据
            return self._load_sample_stock_list()

    def _load_sample_stock_list(self) -> List[StockInfo]:
        """加载示例股票列表"""
        sample_stocks = [
            StockInfo(
                symbol='000001.SZ',
                name='平安银行',
                sector='银行',
                market_cap=2500.0,
                price=12.50,
                change=0.15,
                change_percent=1.22,
                volume=15000000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='000002.SZ',
                name='万科A',
                sector='房地产',
                market_cap=1800.0,
                price=16.80,
                change=-0.25,
                change_percent=-1.47,
                volume=8500000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='600519.SH',
                name='贵州茅台',
                sector='食品饮料',
                market_cap=25000.0,
                price=2050.0,
                change=25.0,
                change_percent=1.23,
                volume=1200000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='000858.SZ',
                name='五粮液',
                sector='食品饮料',
                market_cap=8500.0,
                price=220.0,
                change=3.5,
                change_percent=1.61,
                volume=2800000,
                last_update=datetime.now()
            ),
            StockInfo(
                symbol='601318.SH',
                name='中国平安',
                sector='保险',
                market_cap=12000.0,
                price=65.5,
                change=-1.2,
                change_percent=-1.80,
                volume=18000000,
                last_update=datetime.now()
            )
        ]

        self.stock_list = sample_stocks
        logger.info(f"加载示例股票列表完成，共{len(sample_stocks)}只股票")
        return sample_stocks

    def _load_from_data_source(self) -> List[StockInfo]:
        """从数据源直接加载股票列表（仅基本信息，避免大量API调用）"""
        try:
            if not self.data_source_manager:
                return self._load_sample_stock_list()

            data_source = self.data_source_manager.get_best_source()
            if not data_source:
                return self._load_sample_stock_list()

            # 获取股票列表
            stock_codes = data_source.get_stock_list()
            if not stock_codes:
                return self._load_sample_stock_list()

            stock_list = []
            # 限制数量以避免界面卡顿，并且不获取实时价格
            limited_codes = stock_codes[:100] if len(stock_codes) > 100 else stock_codes

            for stock_code in limited_codes:
                # 只创建基本信息，不获取实时数据（避免启动时大量API调用）
                stock_info = StockInfo(
                    symbol=stock_code,
                    name=stock_code.split('.')[0],  # 暂时使用代码作为名称
                    sector='未知',
                    market_cap=0.0,
                    price=0.0,  # 启动时不获取实时价格
                    change=0.0,
                    change_percent=0.0,
                    volume=0,
                    last_update=datetime.now()
                )
                stock_list.append(stock_info)

            if stock_list:
                self.stock_list = stock_list
                logger.info(f"从数据源加载股票列表完成，共{len(stock_list)}只股票（不含实时价格）")
                return stock_list
            else:
                return self._load_sample_stock_list()

        except Exception as e:
            logger.error(f"从数据源加载股票列表失败: {e}")
            return self._load_sample_stock_list()

    def _get_latest_quote(self, stock_code: str) -> Dict[str, Any]:
        """获取最新行情（仅从数据库获取，避免API调用）"""
        try:
            if self.db_manager:
                # 从数据库获取最新行情
                df = self.db_manager.get_stock_quotes(stock_code, limit=2)
                if not df.empty:
                    latest = df.iloc[0]
                    prev = df.iloc[1] if len(df) > 1 else latest

                    change = latest['close'] - prev['close']
                    change_percent = (change / prev['close']) * 100 if prev['close'] != 0 else 0

                    return {
                        'close': latest['close'],
                        'change': change,
                        'change_percent': change_percent,
                        'volume': latest['volume']
                    }

            # 如果数据库没有数据，返回默认值（不调用API）
            return {'close': 0.0, 'change': 0.0, 'change_percent': 0.0, 'volume': 0}

        except Exception as e:
            logger.warning(f"获取最新行情失败 {stock_code}: {e}")
            return {'close': 0.0, 'change': 0.0, 'change_percent': 0.0, 'volume': 0}

    async def update_real_time_quotes(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """异步更新实时行情（批量获取）"""
        try:
            if not self.data_source_manager:
                return {}

            data_source = self.data_source_manager.get_best_source()
            if not data_source:
                return {}

            # 批量获取实时行情
            quotes = {}
            batch_size = 50  # 每批处理50只股票

            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]

                try:
                    # 使用批量API获取数据
                    market_data = data_source.get_market_data(
                        symbols=batch_codes,
                        period='1d'
                    )

                    if market_data:
                        for code in batch_codes:
                            if hasattr(market_data, 'data') and not market_data.data.empty:
                                latest_data = market_data.data.iloc[-1]
                                quotes[code] = {
                                    'close': float(latest_data.get('close', 0.0)),
                                    'change': 0.0,  # 需要计算
                                    'change_percent': 0.0,  # 需要计算
                                    'volume': int(latest_data.get('volume', 0))
                                }

                except Exception as e:
                    logger.warning(f"批量获取行情失败 {batch_codes}: {e}")
                    continue

            return quotes

        except Exception as e:
            logger.error(f"更新实时行情失败: {e}")
            return {}
    
    def get_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        try:
            # 先检查缓存
            if symbol in self.stocks_data:
                return self.stocks_data[symbol]

            # 从数据库或数据源获取数据
            if self.use_real_data and self.db_manager:
                df = self.db_manager.get_stock_quotes(symbol, limit=500)  # 获取最近500天数据
                if not df.empty:
                    self.stocks_data[symbol] = df
                    return df

            # 如果没有真实数据，尝试从数据源获取
            if self.data_source_manager:
                data_source = self.data_source_manager.get_best_source()
                if data_source:
                    try:
                        # 获取历史数据
                        end_date = datetime.now().strftime('%Y%m%d')
                        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')

                        market_data = data_source.get_market_data(
                            symbols=[symbol],
                            period='1d',
                            start_date=start_date,
                            end_date=end_date
                        )

                        if market_data and len(market_data) > 0:
                            # 转换为DataFrame格式
                            df = self._convert_market_data_to_df(market_data)
                            if not df.empty:
                                self.stocks_data[symbol] = df
                                return df
                    except Exception as e:
                        logger.warning(f"从数据源获取股票数据失败 {symbol}: {e}")

            # 如果都失败了，使用示例数据
            if symbol not in self.stocks_data:
                sample_data = self.create_sample_data()
                self.stocks_data.update(sample_data)

            return self.stocks_data.get(symbol)

        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return None

    def _convert_market_data_to_df(self, market_data: Any) -> pd.DataFrame:
        """将市场数据转换为DataFrame格式"""
        try:
            if isinstance(market_data, pd.DataFrame):
                return market_data
            elif isinstance(market_data, list) and len(market_data) > 0:
                df = pd.DataFrame(market_data)

                # 标准化列名
                column_mapping = {
                    'date': 'date',
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                }

                df = df.rename(columns=column_mapping)

                # 处理日期索引
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)

                return df
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.warning(f"转换市场数据格式失败: {e}")
            return pd.DataFrame()
    
    def analyze_stock(self, symbol: str) -> Optional[AnalysisResult]:
        """分析单只股票"""
        try:
            # 获取股票数据
            stock_data = self.get_stock_data(symbol)
            if stock_data is None or stock_data.empty:
                raise DataSourceError(f"无法获取股票数据: {symbol}")
            
            # 获取股票基本信息
            stock_info = next((s for s in self.stock_list if s.symbol == symbol), None)
            if stock_info is None:
                # 创建默认股票信息
                stock_info = StockInfo(
                    symbol=symbol,
                    name=symbol,
                    sector='未知',
                    market_cap=0.0,
                    price=float(stock_data['close'].iloc[-1]),
                    change=0.0,
                    change_percent=0.0,
                    volume=int(stock_data['volume'].iloc[-1]),
                    last_update=datetime.now()
                )
            
            # 威科夫分析
            wyckoff_analysis = None
            wyckoff_signals = []
            try:
                wyckoff_analysis = self.wyckoff_engine.analyze_market_structure(stock_data)
                wyckoff_signals = self.wyckoff_engine.detect_wyckoff_signals(stock_data)
            except Exception as e:
                logger.warning(f"威科夫分析失败 {symbol}: {e}")
            
            # 相对强弱分析
            rs_analysis = None
            rs_ranking = None
            try:
                if self.benchmark_data is None:
                    # 创建基准数据
                    self.benchmark_data = self._create_benchmark_data()
                
                rs_analysis = self.rs_engine.analyze_relative_strength(
                    {'symbol': symbol}, self.benchmark_data
                )
                
                # 简化的RS排名
                rs_value = self.rs_engine.calculate_rs_value(stock_data, self.benchmark_data)
                rs_ranking = RSResult(
                    symbol=symbol,
                    rs_value=rs_value,
                    rs_rank=0,
                    rs_percentile=50.0,
                    benchmark="market_index",
                    timeframe=self.rs_engine.TimeFrame.DAILY,
                    calculation_date=pd.Timestamp.now(),
                    momentum_score=0.5,
                    trend_strength=0.5
                )
            except Exception as e:
                logger.warning(f"相对强弱分析失败 {symbol}: {e}")
            
            result = AnalysisResult(
                stock_info=stock_info,
                wyckoff_analysis=wyckoff_analysis,
                wyckoff_signals=wyckoff_signals,
                rs_analysis=rs_analysis,
                rs_ranking=rs_ranking,
                analysis_time=datetime.now()
            )
            
            logger.info(f"股票分析完成: {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"股票分析失败 {symbol}: {e}")
            return None
    
    def _create_benchmark_data(self) -> pd.DataFrame:
        """创建基准数据"""
        # 创建简单的基准指数数据
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        dates = dates[dates.weekday < 5]
        
        np.random.seed(123)
        base_price = 3000
        returns = np.random.normal(0.0005, 0.015, len(dates))
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        benchmark_data = pd.DataFrame({
            'date': dates,
            'open': [p * np.random.uniform(0.995, 1.005) for p in prices],
            'high': [p * np.random.uniform(1.000, 1.02) for p in prices],
            'low': [p * np.random.uniform(0.98, 1.000) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000000, 2000000000, len(dates))
        })
        
        benchmark_data.set_index('date', inplace=True)
        return benchmark_data
    
    def refresh_data(self) -> bool:
        """刷新数据"""
        try:
            logger.info("开始刷新数据...")

            # 重新加载股票列表
            self.load_stock_list()

            # 清除缓存
            self.stocks_data.clear()
            self.benchmark_data = None

            if self.use_real_data:
                # 真实数据模式：清除缓存，下次获取时会从数据库重新加载
                logger.info("真实数据刷新完成")
            else:
                # 示例数据模式：重新创建示例数据
                sample_data = self.create_sample_data()
                self.stocks_data.update(sample_data)
                logger.info("示例数据刷新完成")

            return True

        except Exception as e:
            logger.error(f"数据刷新失败: {e}")
            return False

    async def download_all_stock_data(
        self,
        progress_callback: Optional[Callable[[DownloadProgress], None]] = None,
        include_history: bool = True
    ) -> bool:
        """
        下载所有股票数据

        Args:
            progress_callback: 进度回调函数
            include_history: 是否包含历史数据

        Returns:
            bool: 下载是否成功
        """
        if not self.use_real_data or not self.download_service:
            logger.warning("未启用真实数据模式或下载服务不可用")
            return False

        try:
            success = await self.download_service.download_all_data(
                progress_callback=progress_callback,
                include_history=include_history
            )

            if success:
                # 下载完成后刷新数据
                self.refresh_data()

            return success

        except Exception as e:
            logger.error(f"下载股票数据失败: {e}")
            return False

    def stop_download(self):
        """停止数据下载"""
        if self.download_service:
            self.download_service.stop_download()

    def get_download_progress(self) -> Optional[DownloadProgress]:
        """获取下载进度"""
        if self.download_service:
            return self.download_service.get_progress()
        return None

    def is_downloading(self) -> bool:
        """检查是否正在下载"""
        if self.download_service:
            return self.download_service.is_downloading
        return False

    def get_data_update_info(self) -> Dict[str, Any]:
        """获取数据更新信息"""
        try:
            if not self.use_real_data or not self.db_manager:
                return {
                    'last_update': None,
                    'total_stocks': len(self.stock_list),
                    'data_source': '模拟数据',
                    'need_update': False
                }

            last_update = self.db_manager.get_last_update_time()
            stats = self.db_manager.get_database_stats()

            # 判断是否需要更新（超过1天）
            need_update = True
            if last_update:
                time_diff = datetime.now() - last_update
                need_update = time_diff.total_seconds() > 24 * 3600  # 24小时

            return {
                'last_update': last_update,
                'total_stocks': stats.get('total_stocks', 0),
                'total_quotes': stats.get('total_quotes', 0),
                'latest_trade_date': stats.get('latest_trade_date'),
                'db_size_mb': stats.get('db_size_mb', 0),
                'data_source': '真实数据',
                'need_update': need_update
            }

        except Exception as e:
            logger.error(f"获取数据更新信息失败: {e}")
            return {
                'last_update': None,
                'total_stocks': 0,
                'data_source': '错误',
                'need_update': True
            }
