"""
数据下载服务

从XtData数据源下载真实股票数据并存储到本地数据库
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
import time
import sqlite3
from pathlib import Path

from ..data_sources.manager import DataSourceManager
from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError, DatabaseError

logger = get_logger(__name__)


@dataclass
class DownloadProgress:
    """下载进度信息"""
    total_stocks: int = 0
    completed_stocks: int = 0
    current_stock: str = ""
    stage: str = ""  # "stock_list", "basic_info", "price_data"
    progress_percent: float = 0.0
    error_count: int = 0
    start_time: Optional[datetime] = None
    estimated_remaining: Optional[timedelta] = None





class DataDownloadService:
    """数据下载服务"""

    def __init__(self, data_source_manager: DataSourceManager, db_manager):
        """
        初始化数据下载服务
        
        Args:
            data_source_manager: 数据源管理器
            db_manager: 数据库管理器
        """
        self.data_source_manager = data_source_manager
        self.db_manager = db_manager
        self.progress = DownloadProgress()
        self.is_downloading = False
        self.should_stop = False
        
        logger.info("数据下载服务初始化完成")
    
    async def download_all_data(
        self,
        progress_callback: Optional[Callable[[DownloadProgress], None]] = None,
        include_history: bool = True,
        history_days: int = 365,
        skip_basic_info: bool = False
    ) -> bool:
        """
        下载所有股票数据
        
        Args:
            progress_callback: 进度回调函数
            include_history: 是否包含历史数据
            history_days: 历史数据天数
            
        Returns:
            bool: 下载是否成功
        """
        if self.is_downloading:
            logger.warning("数据下载正在进行中")
            return False
        
        try:
            self.is_downloading = True
            self.should_stop = False
            self.progress = DownloadProgress(start_time=datetime.now())
            
            logger.info("开始下载所有股票数据")
            
            # 阶段1：获取股票列表
            self.progress.stage = "stock_list"
            if progress_callback:
                progress_callback(self.progress)
            
            stock_list = await self._download_stock_list()
            if not stock_list:
                raise DataSourceError("获取股票列表失败")
            
            self.progress.total_stocks = len(stock_list)
            logger.info(f"获取到 {len(stock_list)} 只股票")
            
            # 阶段2：下载基本信息（如果需要）
            if not skip_basic_info:
                self.progress.stage = "basic_info"
                if progress_callback:
                    progress_callback(self.progress)

                success_count = await self._download_basic_info(stock_list, progress_callback)
                logger.info(f"成功下载 {success_count}/{len(stock_list)} 只股票的基本信息")
            else:
                logger.info("跳过基本信息下载，直接下载历史数据")
            
            # 阶段3：下载历史数据（如果需要）
            if include_history and not self.should_stop:
                self.progress.stage = "price_data"
                self.progress.completed_stocks = 0
                if progress_callback:
                    progress_callback(self.progress)
                
                history_success = await self._download_history_data(
                    stock_list, history_days, progress_callback
                )
                logger.info(f"成功下载 {history_success}/{len(stock_list)} 只股票的历史数据")
            
            # 更新最后更新时间
            self.db_manager.update_last_update_time()
            
            self.progress.progress_percent = 100.0
            if progress_callback:
                progress_callback(self.progress)
            
            logger.info("所有股票数据下载完成")
            return True
            
        except Exception as e:
            logger.error(f"下载股票数据失败: {e}")
            return False
        finally:
            self.is_downloading = False
    
    async def _download_stock_list(self) -> List[str]:
        """下载股票列表"""
        try:
            data_source = self.data_source_manager.get_best_source()
            if not data_source:
                raise DataSourceError("没有可用的数据源")
            
            # 获取股票列表
            stock_list = data_source.get_stock_list()
            if not stock_list:
                raise DataSourceError("获取股票列表为空")
            
            logger.info(f"从数据源获取到 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"下载股票列表失败: {e}")
            raise
    
    async def _download_basic_info(
        self,
        stock_list: List,
        progress_callback: Optional[Callable[[DownloadProgress], None]] = None
    ) -> int:
        """下载股票基本信息"""
        success_count = 0
        data_source = self.data_source_manager.get_best_source()

        for i, stock_item in enumerate(stock_list):
            if self.should_stop:
                break

            try:
                # 处理股票代码格式
                if isinstance(stock_item, dict):
                    stock_code = stock_item.get('symbol', '')
                    stock_name = stock_item.get('name', '')
                else:
                    stock_code = str(stock_item)
                    stock_name = stock_code

                self.progress.current_stock = stock_code
                self.progress.completed_stocks = i
                self.progress.progress_percent = (i / len(stock_list)) * 33.33  # 基本信息占1/3

                if progress_callback:
                    progress_callback(self.progress)

                # 获取股票基本信息
                basic_info = await self._get_stock_basic_info(data_source, stock_code, stock_name)
                if basic_info:
                    # 存储到数据库
                    self.db_manager.insert_stock_info(basic_info)
                    success_count += 1

                # 避免请求过快
                await asyncio.sleep(0.05)

            except Exception as e:
                logger.warning(f"获取股票 {stock_code} 基本信息失败: {e}")
                self.progress.error_count += 1
                continue

        return success_count
    
    async def _get_stock_basic_info(self, data_source, stock_code: str, stock_name: str = "") -> Optional[Dict[str, Any]]:
        """获取单只股票的基本信息"""
        try:
            # 从股票代码解析市场
            if stock_code.endswith('.SH'):
                market = 'SH'
                code_only = stock_code[:-3]
            elif stock_code.endswith('.SZ'):
                market = 'SZ'
                code_only = stock_code[:-3]
            elif stock_code.endswith('.BJ'):
                market = 'BJ'
                code_only = stock_code[:-3]
            else:
                market = 'SH'  # 默认
                code_only = stock_code

            # 如果没有提供股票名称，使用代码作为名称
            if not stock_name:
                stock_name = stock_code

            # 尝试获取实时行情（包含基本信息）
            market_cap = 0.0
            try:
                market_data = data_source.get_market_data(stock_code, period='1d')

                if market_data and hasattr(market_data, 'data') and not market_data.data.empty:
                    # 尝试从行情数据中提取信息
                    latest_data = market_data.data.iloc[-1]
                    # 暂时无法获取股本信息，市值设为0
                    market_cap = 0.0
            except Exception as e:
                logger.debug(f"获取股票 {stock_code} 行情数据失败: {e}")

            # 判断是否为ST股票
            is_st = 'ST' in stock_name or '*ST' in stock_name

            # 根据代码判断行业（简单分类）
            industry = self._guess_industry_by_code(code_only)

            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'market': market,
                'industry': industry,
                'market_cap': market_cap,
                'is_st': is_st,
                'status': 'active'
            }

        except Exception as e:
            logger.warning(f"获取股票 {stock_code} 基本信息失败: {e}")
            return None
    
    def _guess_industry_by_code(self, code: str) -> str:
        """根据股票代码猜测行业"""
        try:
            code_num = int(code)
            
            # 深交所主板
            if code.startswith('000'):
                if code_num < 1000:
                    return '综合'
                else:
                    return '制造业'
            
            # 深交所中小板
            elif code.startswith('002'):
                return '中小企业'
            
            # 深交所创业板
            elif code.startswith('300'):
                return '创业板'
            
            # 上交所主板
            elif code.startswith('600') or code.startswith('601') or code.startswith('603'):
                if code.startswith('600'):
                    return '制造业'
                elif code.startswith('601'):
                    return '金融业'
                else:
                    return '服务业'
            
            # 科创板
            elif code.startswith('688'):
                return '科技创新'
            
            # 北交所
            elif code.startswith('8') or code.startswith('4'):
                return '新三板'
            
            else:
                return '其他'
                
        except:
            return '未知'
    
    async def _download_history_data(
        self,
        stock_list: List,
        days: int,
        progress_callback: Optional[Callable[[DownloadProgress], None]] = None
    ) -> int:
        """下载历史数据"""
        success_count = 0
        data_source = self.data_source_manager.get_best_source()

        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

        for i, stock_item in enumerate(stock_list):
            if self.should_stop:
                break

            try:
                # 处理股票代码格式
                if isinstance(stock_item, dict):
                    stock_code = stock_item.get('symbol', '')
                else:
                    stock_code = str(stock_item)

                self.progress.current_stock = stock_code
                self.progress.completed_stocks = i
                self.progress.progress_percent = 33.33 + (i / len(stock_list)) * 66.67  # 历史数据占2/3

                if progress_callback:
                    progress_callback(self.progress)

                # 获取历史数据
                history_data = data_source.get_market_data(
                    symbol=stock_code,
                    period='1d',
                    start_date=start_date,
                    end_date=end_date
                )

                if history_data and len(history_data) > 0:
                    # 存储历史数据
                    self.db_manager.insert_stock_quotes(stock_code, history_data)
                    success_count += 1

                # 避免请求过快
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.warning(f"获取股票 {stock_code} 历史数据失败: {e}")
                self.progress.error_count += 1
                continue

        return success_count
    
    def stop_download(self):
        """停止下载"""
        self.should_stop = True
        logger.info("数据下载停止请求已发送")
    
    def get_progress(self) -> DownloadProgress:
        """获取下载进度"""
        return self.progress
