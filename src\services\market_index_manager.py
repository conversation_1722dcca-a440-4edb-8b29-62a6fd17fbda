#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场指数管理器
负责真实市场指数数据的获取、存储和管理
"""

import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd

from ..database.manager import DatabaseManager
from ..data_sources.manager import DataSourceManager
from ..utils.logger import get_logger
from ..utils.exceptions import DataSourceError

logger = get_logger(__name__)


@dataclass
class MarketIndexData:
    """市场指数数据类"""
    index_code: str
    index_name: str
    trade_date: str
    open: float
    high: float
    low: float
    close: float
    volume: int = 0
    amount: float = 0.0
    change_pct: Optional[float] = None
    prev_close: Optional[float] = None


class MarketIndexManager:
    """市场指数管理器"""
    
    def __init__(self, db_manager: DatabaseManager, data_source_manager: DataSourceManager):
        self.db_manager = db_manager
        self.data_source_manager = data_source_manager
        
        # 主要市场指数配置
        self.major_indices = {
            "000001.SH": "上证指数",
            "399001.SZ": "深证成指",
            "399006.SZ": "创业板指",
            "000300.SH": "沪深300",
            "000905.SH": "中证500",
            "000852.SH": "中证1000",
            "399905.SZ": "中证500",
            "000016.SH": "上证50",
            "399330.SZ": "深证100"
        }
        
        # 数据质量配置
        self.quality_config = {
            'min_data_points': 10,  # 最少数据点数
            'max_missing_rate': 0.1,  # 最大缺失率
            'price_change_threshold': 0.15,  # 价格变动阈值（15%）
            'data_freshness_hours': 24  # 数据新鲜度（小时）
        }
        
        # 状态跟踪
        self.update_status = {
            'last_update': None,
            'success_count': 0,
            'error_count': 0,
            'total_indices': len(self.major_indices)
        }
    
    async def initialize_indices(self) -> bool:
        """初始化市场指数数据"""
        try:
            logger.info("开始初始化市场指数数据...")
            
            # 检查数据源连接
            if not await self._check_data_source_connection():
                logger.error("数据源连接失败，无法初始化市场指数")
                return False
            
            # 创建指数信息表记录
            success_count = 0
            for index_code, index_name in self.major_indices.items():
                try:
                    await self._insert_index_info(index_code, index_name)
                    success_count += 1
                except Exception as e:
                    logger.warning(f"创建指数 {index_code} 信息失败: {e}")
                    continue
            
            logger.info(f"市场指数初始化完成，成功创建 {success_count}/{len(self.major_indices)} 个指数")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"市场指数初始化失败: {e}")
            return False
    
    async def download_index_data(self, 
                                index_codes: Optional[List[str]] = None,
                                days: int = 365,
                                progress_callback: Optional[callable] = None) -> bool:
        """下载市场指数历史数据"""
        try:
            # 获取要下载的指数列表
            if index_codes is None:
                index_codes = list(self.major_indices.keys())
            
            logger.info(f"开始下载 {len(index_codes)} 个市场指数的历史数据，时间范围：{days}天")
            
            # 检查数据源连接
            if not await self._check_data_source_connection():
                logger.error("数据源连接失败，无法下载指数数据")
                return False
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            success_count = 0
            total_count = len(index_codes)
            
            for i, index_code in enumerate(index_codes):
                try:
                    # 更新进度
                    if progress_callback:
                        progress = (i / total_count) * 100
                        progress_callback(f"下载指数数据: {index_code}", progress)
                    
                    # 下载指数历史数据
                    index_data = await self._download_real_index_data(
                        index_code, start_date, end_date
                    )
                    
                    if index_data:
                        # 数据质量检查
                        if await self._validate_index_data(index_data, index_code):
                            # 存储到数据库
                            await self._insert_index_data(index_code, index_data)
                            success_count += 1
                        else:
                            logger.warning(f"指数 {index_code} 数据质量检查失败")
                    else:
                        logger.warning(f"指数 {index_code} 未获取到数据")
                    
                    # 避免请求过快
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"下载指数 {index_code} 数据失败: {e}")
                    continue
            
            # 更新状态
            self.update_status.update({
                'last_update': datetime.now(),
                'success_count': success_count,
                'error_count': total_count - success_count
            })
            
            success_rate = success_count / total_count if total_count > 0 else 0
            logger.info(f"指数数据下载完成，成功 {success_count}/{total_count} 个指数 (成功率: {success_rate:.1%})")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"下载指数数据失败: {e}")
            return False
    
    async def _check_data_source_connection(self) -> bool:
        """检查数据源连接状态"""
        try:
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                logger.warning("没有可用的数据源")
                return False
            
            if not best_source.test_connection():
                logger.warning("数据源连接测试失败")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查数据源连接失败: {e}")
            return False
    
    async def _download_real_index_data(self, 
                                      index_code: str, 
                                      start_date: datetime, 
                                      end_date: datetime) -> List[MarketIndexData]:
        """从真实数据源下载指数数据"""
        try:
            # 获取最佳数据源
            best_source = self.data_source_manager.get_best_source()
            if not best_source:
                raise Exception("没有可用的数据源")
            
            # 格式化日期
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            # 获取指数行情数据
            market_data = best_source.get_market_data(
                symbol=index_code,
                period='1d',
                start_date=start_date_str,
                end_date=end_date_str
            )
            
            if not market_data or not hasattr(market_data, 'data') or market_data.data.empty:
                logger.warning(f"指数 {index_code} 未获取到真实数据")
                return []
            
            # 转换为MarketIndexData格式
            index_data_list = []
            df = market_data.data
            prev_close = None
            
            for index, row in df.iterrows():
                try:
                    close_price = float(row.get('close', 0))
                    
                    # 计算涨跌幅
                    change_pct = None
                    if 'change_pct' in row:
                        change_pct = row['change_pct']
                    elif prev_close and prev_close > 0:
                        change_pct = (close_price - prev_close) / prev_close * 100
                    
                    index_data = MarketIndexData(
                        index_code=index_code,
                        index_name=self.major_indices.get(index_code, index_code),
                        trade_date=index.strftime('%Y-%m-%d') if hasattr(index, 'strftime') else str(index),
                        open=float(row.get('open', 0)),
                        high=float(row.get('high', 0)),
                        low=float(row.get('low', 0)),
                        close=close_price,
                        volume=int(row.get('volume', 0)),
                        amount=float(row.get('amount', 0)),
                        change_pct=change_pct,
                        prev_close=prev_close
                    )
                    
                    index_data_list.append(index_data)
                    prev_close = close_price
                    
                except Exception as e:
                    logger.warning(f"处理指数 {index_code} 数据失败: {e}")
                    continue
            
            logger.debug(f"成功获取指数 {index_code} 数据 {len(index_data_list)} 条")
            return index_data_list
            
        except Exception as e:
            logger.error(f"获取指数 {index_code} 数据失败: {e}")
            return []
    
    async def _validate_index_data(self, data_list: List[MarketIndexData], index_code: str) -> bool:
        """验证指数数据质量"""
        try:
            if not data_list:
                return False
            
            # 检查数据数量
            if len(data_list) < self.quality_config['min_data_points']:
                logger.warning(f"指数 {index_code} 数据量不足: {len(data_list)} 条")
                return False
            
            # 检查数据完整性和合理性
            valid_count = 0
            for data in data_list:
                if (data.open > 0 and data.high > 0 and data.low > 0 and data.close > 0 and
                    data.high >= data.low and data.high >= data.open and data.high >= data.close and
                    data.low <= data.open and data.low <= data.close):
                    
                    # 检查价格变动是否合理
                    if data.change_pct is not None:
                        if abs(data.change_pct) <= self.quality_config['price_change_threshold'] * 100:
                            valid_count += 1
                    else:
                        valid_count += 1
            
            valid_rate = valid_count / len(data_list)
            if valid_rate < (1 - self.quality_config['max_missing_rate']):
                logger.warning(f"指数 {index_code} 数据质量不佳: 有效率 {valid_rate:.1%}")
                return False
            
            logger.debug(f"指数 {index_code} 数据质量检查通过: {valid_count}/{len(data_list)} 条有效")
            return True
            
        except Exception as e:
            logger.error(f"验证指数 {index_code} 数据质量失败: {e}")
            return False
    
    async def _insert_index_info(self, index_code: str, index_name: str) -> bool:
        """插入指数基本信息"""
        try:
            sql = """
            INSERT OR REPLACE INTO market_indices 
            (index_code, index_name, market, is_active, update_time)
            VALUES (?, ?, ?, ?, ?)
            """
            
            market = "SH" if ".SH" in index_code else "SZ"
            
            with self.db_manager.get_connection() as conn:
                conn.execute(sql, (
                    index_code, index_name, market, True, datetime.now()
                ))
                conn.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"插入指数信息失败: {e}")
            return False
    
    async def _insert_index_data(self, index_code: str, data_list: List[MarketIndexData]) -> bool:
        """插入指数历史数据"""
        try:
            sql = """
            INSERT OR REPLACE INTO market_index_quotes 
            (index_code, trade_date, open, high, low, close, volume, amount, change_pct, prev_close)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            with self.db_manager.get_connection() as conn:
                for data in data_list:
                    conn.execute(sql, (
                        data.index_code, data.trade_date, data.open, data.high,
                        data.low, data.close, data.volume, data.amount,
                        data.change_pct, data.prev_close
                    ))
                conn.commit()
            
            logger.debug(f"插入指数 {index_code} 历史数据 {len(data_list)} 条")
            return True
            
        except Exception as e:
            logger.error(f"插入指数历史数据失败: {e}")
            return False
    
    def get_index_data(self, 
                      index_code: str,
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None,
                      limit: Optional[int] = None) -> pd.DataFrame:
        """获取指数历史数据"""
        try:
            sql = "SELECT * FROM market_index_quotes WHERE index_code = ?"
            params = [index_code]
            
            if start_date:
                sql += " AND trade_date >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND trade_date <= ?"
                params.append(end_date)
            
            sql += " ORDER BY trade_date DESC"
            
            if limit:
                sql += f" LIMIT {limit}"
            
            results = self.db_manager.execute_query(sql, tuple(params))
            
            if not results:
                return pd.DataFrame()
            
            # 转换为DataFrame
            columns = ['id', 'index_code', 'trade_date', 'open', 'high', 'low', 
                      'close', 'volume', 'amount', 'change_pct', 'prev_close', 'update_time']
            
            df = pd.DataFrame(results, columns=columns)
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df.set_index('trade_date', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"获取指数数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_index_return(self, 
                             index_code: str,
                             start_date: str,
                             end_date: str) -> Optional[float]:
        """计算指数收益率"""
        try:
            df = self.get_index_data(index_code, start_date, end_date)
            
            if df.empty or len(df) < 2:
                return None
            
            # 按日期排序
            df = df.sort_index()
            
            start_price = df['close'].iloc[0]
            end_price = df['close'].iloc[-1]
            
            if start_price <= 0:
                return None
            
            return (end_price - start_price) / start_price
            
        except Exception as e:
            logger.error(f"计算指数收益率失败: {e}")
            return None
    
    def get_available_indices(self) -> List[Dict[str, str]]:
        """获取可用的市场指数列表"""
        return [
            {'code': code, 'name': name}
            for code, name in self.major_indices.items()
        ]
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        return self.update_status.copy()
