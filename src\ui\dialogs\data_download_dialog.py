"""
数据下载进度对话框

显示股票数据下载进度和状态
"""

from typing import Optional, Callable
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QTextEdit, QGroupBox, QCheckBox, QSpinBox,
    QFrame, QWidget
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPixmap, QIcon
import asyncio
from datetime import datetime, timedelta

from ...utils.logger import get_logger
from ...services.data_download_service import DownloadProgress
from ...services.data_service import DataService

logger = get_logger(__name__)


class DownloadWorker(QThread):
    """下载工作线程"""
    
    progress_updated = pyqtSignal(object)  # DownloadProgress
    download_finished = pyqtSignal(bool)   # success
    
    def __init__(self, data_service: DataService, include_history: bool = True):
        super().__init__()
        self.data_service = data_service
        self.include_history = include_history
        self.should_stop = False
    
    def run(self):
        """运行下载任务"""
        try:
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 运行下载任务
                success = loop.run_until_complete(
                    self.data_service.download_all_stock_data(
                        progress_callback=self._on_progress_update,
                        include_history=self.include_history
                    )
                )

                self.download_finished.emit(success)

            except Exception as e:
                logger.error(f"下载任务执行失败: {e}")
                self.download_finished.emit(False)
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"事件循环创建失败: {e}")
            self.download_finished.emit(False)
    
    def _on_progress_update(self, progress: DownloadProgress):
        """进度更新回调"""
        self.progress_updated.emit(progress)
    
    def stop(self):
        """停止下载"""
        self.should_stop = True
        if self.data_service:
            self.data_service.stop_download()


class DataDownloadDialog(QDialog):
    """数据下载对话框"""
    
    download_completed = pyqtSignal(bool)  # 下载完成信号
    
    def __init__(self, data_service: DataService, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.download_worker = None
        self.start_time = None
        
        self.setWindowTitle("股票数据下载")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        self._setup_ui()
        self._connect_signals()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_display)
        
        logger.info("数据下载对话框初始化完成")
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📥 股票数据下载")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 下载选项
        options_group = QGroupBox("下载选项")
        options_layout = QVBoxLayout(options_group)
        
        self.include_history_cb = QCheckBox("包含历史数据")
        self.include_history_cb.setChecked(True)
        options_layout.addWidget(self.include_history_cb)
        
        history_layout = QHBoxLayout()
        history_layout.addWidget(QLabel("历史数据天数:"))
        self.history_days_spin = QSpinBox()
        self.history_days_spin.setRange(30, 1095)  # 30天到3年
        self.history_days_spin.setValue(365)
        self.history_days_spin.setSuffix(" 天")
        history_layout.addWidget(self.history_days_spin)
        history_layout.addStretch()
        options_layout.addLayout(history_layout)
        
        layout.addWidget(options_group)
        
        # 进度信息
        progress_group = QGroupBox("下载进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 当前阶段
        self.stage_label = QLabel("准备中...")
        self.stage_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        progress_layout.addWidget(self.stage_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        # 详细信息
        info_layout = QHBoxLayout()
        
        self.current_stock_label = QLabel("当前股票: -")
        info_layout.addWidget(self.current_stock_label)
        
        self.speed_label = QLabel("速度: -")
        info_layout.addWidget(self.speed_label)
        
        self.eta_label = QLabel("剩余时间: -")
        info_layout.addWidget(self.eta_label)
        
        progress_layout.addLayout(info_layout)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.completed_label = QLabel("已完成: 0/0")
        stats_layout.addWidget(self.completed_label)
        
        self.error_label = QLabel("错误: 0")
        stats_layout.addWidget(self.error_label)
        
        self.elapsed_label = QLabel("已用时间: 00:00:00")
        stats_layout.addWidget(self.elapsed_label)
        
        progress_layout.addLayout(stats_layout)
        
        layout.addWidget(progress_group)
        
        # 日志区域
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 开始下载")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ 停止")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        button_layout.addWidget(self.stop_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("关闭")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(self.close_button)

        # 创建按钮容器widget
        button_widget = QWidget()
        button_widget.setLayout(button_layout)
        layout.addWidget(button_widget)
    
    def _connect_signals(self):
        """连接信号"""
        self.start_button.clicked.connect(self._start_download)
        self.stop_button.clicked.connect(self._stop_download)
        self.close_button.clicked.connect(self.close)
    
    def _start_download(self):
        """开始下载"""
        try:
            self.start_time = datetime.now()
            
            # 更新界面状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.include_history_cb.setEnabled(False)
            self.history_days_spin.setEnabled(False)
            
            # 清空日志
            self.log_text.clear()
            self._add_log("开始下载股票数据...")
            
            # 创建下载工作线程
            include_history = self.include_history_cb.isChecked()
            self.download_worker = DownloadWorker(self.data_service, include_history)
            self.download_worker.progress_updated.connect(self._on_progress_updated)
            self.download_worker.download_finished.connect(self._on_download_finished)
            
            # 启动下载
            self.download_worker.start()
            
            # 启动更新定时器
            self.update_timer.start(1000)  # 每秒更新一次
            
            logger.info("数据下载已启动")
            
        except Exception as e:
            logger.error(f"启动下载失败: {e}")
            self._add_log(f"启动下载失败: {e}")
            self._reset_ui_state()
    
    def _stop_download(self):
        """停止下载"""
        try:
            if self.download_worker:
                self.download_worker.stop()
                self._add_log("正在停止下载...")
            
        except Exception as e:
            logger.error(f"停止下载失败: {e}")
    
    def _on_progress_updated(self, progress: DownloadProgress):
        """进度更新"""
        try:
            # 更新阶段信息
            stage_text = {
                'stock_list': '📋 获取股票列表',
                'basic_info': '📊 下载基本信息',
                'price_data': '📈 下载历史数据'
            }.get(progress.stage, progress.stage)
            
            self.stage_label.setText(stage_text)
            
            # 更新进度条
            self.progress_bar.setValue(int(progress.progress_percent))
            
            # 更新详细信息
            self.current_stock_label.setText(f"当前股票: {progress.current_stock}")
            self.completed_label.setText(f"已完成: {progress.completed_stocks}/{progress.total_stocks}")
            self.error_label.setText(f"错误: {progress.error_count}")
            
            # 计算速度和剩余时间
            if self.start_time and progress.completed_stocks > 0:
                elapsed = datetime.now() - self.start_time
                speed = progress.completed_stocks / elapsed.total_seconds()
                
                if speed > 0:
                    remaining_stocks = progress.total_stocks - progress.completed_stocks
                    eta_seconds = remaining_stocks / speed
                    eta = timedelta(seconds=int(eta_seconds))
                    
                    self.speed_label.setText(f"速度: {speed:.1f} 股票/秒")
                    self.eta_label.setText(f"剩余时间: {str(eta)}")
            
        except Exception as e:
            logger.warning(f"更新进度显示失败: {e}")
    
    def _on_download_finished(self, success: bool):
        """下载完成"""
        try:
            self.update_timer.stop()

            if success:
                self._add_log("✅ 数据下载完成！")
                self.stage_label.setText("✅ 下载完成")
                self.progress_bar.setValue(100)

                # 显示成功统计信息
                if hasattr(self, 'download_worker') and self.download_worker:
                    progress = self.download_worker.data_service.get_download_progress()
                    if progress:
                        self._add_log(f"📊 总计处理 {progress.total_stocks} 只股票")
                        self._add_log(f"✅ 成功 {progress.completed_stocks} 只")
                        if progress.error_count > 0:
                            self._add_log(f"❌ 失败 {progress.error_count} 只")
            else:
                self._add_log("❌ 数据下载失败")
                self.stage_label.setText("❌ 下载失败")

                # 提供失败原因和解决建议
                self._add_log("💡 可能的解决方案：")
                self._add_log("   1. 检查网络连接")
                self._add_log("   2. 确认XtData服务正在运行")
                self._add_log("   3. 检查数据源配置")
                self._add_log("   4. 稍后重试")

            self._reset_ui_state()
            self.download_completed.emit(success)

            logger.info(f"数据下载完成，结果: {'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"处理下载完成事件失败: {e}")
            self._add_log(f"❌ 处理下载结果时出错: {e}")
    
    def _update_display(self):
        """更新显示"""
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.elapsed_label.setText(f"已用时间: {str(elapsed).split('.')[0]}")
    
    def _reset_ui_state(self):
        """重置界面状态"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.include_history_cb.setEnabled(True)
        self.history_days_spin.setEnabled(True)
    
    def _add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.stop()
            self.download_worker.wait(3000)  # 等待3秒
        
        event.accept()
