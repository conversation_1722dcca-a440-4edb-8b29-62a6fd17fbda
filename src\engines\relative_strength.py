"""
相对强弱计算引擎

实现相对强弱分析的核心算法：
- RS值计算
- 动态基准选择
- 多时间周期分析
- 强弱排名系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from enum import Enum
import warnings

from ..utils.logger import get_logger
from ..utils.exceptions import CalculationError

logger = get_logger(__name__)


class TimeFrame(Enum):
    """时间周期枚举"""
    DAILY = "1D"
    WEEKLY = "1W"
    MONTHLY = "1M"
    QUARTERLY = "3M"


class BenchmarkType(Enum):
    """基准类型枚举"""
    MARKET_INDEX = "market_index"      # 市场指数
    SECTOR_INDEX = "sector_index"      # 板块指数
    CUSTOM_BASKET = "custom_basket"    # 自定义篮子
    PEER_GROUP = "peer_group"          # 同行组


@dataclass
class RSResult:
    """相对强弱结果数据类"""
    symbol: str
    rs_value: float
    rs_rank: int
    rs_percentile: float
    benchmark: str
    timeframe: TimeFrame
    calculation_date: pd.Timestamp
    momentum_score: float = 0.0
    trend_strength: float = 0.0


@dataclass
class RSAnalysis:
    """相对强弱分析结果"""
    symbol: str
    current_rs: float
    rs_trend: str  # "strengthening", "weakening", "stable"
    rs_history: pd.Series
    multi_timeframe_rs: Dict[TimeFrame, float]
    sector_rs: float
    market_rs: float
    ranking_info: Dict[str, Any]


class RelativeStrengthEngine:
    """相对强弱计算引擎"""
    
    def __init__(self,
                 default_periods: List[int] = None,
                 min_data_points: int = 252,
                 smoothing_period: int = 10):
        """
        初始化相对强弱引擎
        
        Args:
            default_periods: 默认计算周期列表
            min_data_points: 最少数据点数
            smoothing_period: 平滑周期
        """
        self.default_periods = default_periods or [20, 60, 120, 252]
        self.min_data_points = min_data_points
        self.smoothing_period = smoothing_period
        
        # 缓存基准数据
        self._benchmark_cache: Dict[str, pd.DataFrame] = {}
        
        logger.info("相对强弱计算引擎初始化完成")
    
    def calculate_rs_value(self,
                          stock_data: pd.DataFrame,
                          benchmark_data: pd.DataFrame,
                          period: int = 252) -> float:
        """
        计算相对强弱值
        
        Args:
            stock_data: 股票价格数据
            benchmark_data: 基准价格数据
            period: 计算周期
            
        Returns:
            float: RS值
        """
        try:
            if len(stock_data) < period or len(benchmark_data) < period:
                raise CalculationError(f"数据不足，需要至少{period}个数据点")
            
            # 确保数据对齐
            stock_data = stock_data.tail(period)
            benchmark_data = benchmark_data.tail(period)
            
            # 计算收益率
            stock_return = (stock_data['close'].iloc[-1] / stock_data['close'].iloc[0]) - 1
            benchmark_return = (benchmark_data['close'].iloc[-1] / benchmark_data['close'].iloc[0]) - 1
            
            # 计算相对强弱值
            if benchmark_return != 0:
                rs_value = (1 + stock_return) / (1 + benchmark_return)
            else:
                rs_value = 1 + stock_return
            
            return rs_value
            
        except Exception as e:
            logger.error(f"RS值计算失败: {e}")
            raise CalculationError(f"RS值计算失败: {e}")
    
    def calculate_multi_timeframe_rs(self,
                                   stock_data: pd.DataFrame,
                                   benchmark_data: pd.DataFrame) -> Dict[TimeFrame, float]:
        """
        计算多时间周期相对强弱
        
        Args:
            stock_data: 股票价格数据
            benchmark_data: 基准价格数据
            
        Returns:
            Dict[TimeFrame, float]: 各时间周期的RS值
        """
        try:
            rs_results = {}
            
            # 定义时间周期对应的天数
            timeframe_days = {
                TimeFrame.DAILY: 20,      # 20个交易日
                TimeFrame.WEEKLY: 60,     # 约3个月
                TimeFrame.MONTHLY: 120,   # 约6个月
                TimeFrame.QUARTERLY: 252  # 约1年
            }
            
            for timeframe, days in timeframe_days.items():
                if len(stock_data) >= days and len(benchmark_data) >= days:
                    rs_value = self.calculate_rs_value(stock_data, benchmark_data, days)
                    rs_results[timeframe] = rs_value
                else:
                    logger.warning(f"数据不足，跳过{timeframe.value}周期计算")
            
            return rs_results
            
        except Exception as e:
            logger.error(f"多时间周期RS计算失败: {e}")
            raise CalculationError(f"多时间周期RS计算失败: {e}")
    
    def rank_stocks_by_rs(self,
                         stock_data_dict: Dict[str, pd.DataFrame],
                         benchmark_data: pd.DataFrame,
                         period: int = 252) -> List[RSResult]:
        """
        按相对强弱对股票进行排名
        
        Args:
            stock_data_dict: 股票数据字典 {symbol: DataFrame}
            benchmark_data: 基准数据
            period: 计算周期
            
        Returns:
            List[RSResult]: 排名结果列表
        """
        try:
            results = []
            
            # 计算每只股票的RS值
            for symbol, stock_data in stock_data_dict.items():
                try:
                    rs_value = self.calculate_rs_value(stock_data, benchmark_data, period)
                    
                    # 计算动量分数
                    momentum_score = self._calculate_momentum_score(stock_data, period)
                    
                    # 计算趋势强度
                    trend_strength = self._calculate_trend_strength(stock_data, period)
                    
                    result = RSResult(
                        symbol=symbol,
                        rs_value=rs_value,
                        rs_rank=0,  # 稍后填充
                        rs_percentile=0.0,  # 稍后填充
                        benchmark="market_index",
                        timeframe=TimeFrame.DAILY,
                        calculation_date=pd.Timestamp.now(),
                        momentum_score=momentum_score,
                        trend_strength=trend_strength
                    )
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"计算{symbol}的RS值失败: {e}")
                    continue
            
            # 按RS值排序
            results.sort(key=lambda x: x.rs_value, reverse=True)
            
            # 填充排名和百分位
            total_stocks = len(results)
            for i, result in enumerate(results):
                result.rs_rank = i + 1
                result.rs_percentile = (total_stocks - i) / total_stocks * 100
            
            logger.info(f"完成{total_stocks}只股票的RS排名")
            return results
            
        except Exception as e:
            logger.error(f"股票RS排名失败: {e}")
            raise CalculationError(f"股票RS排名失败: {e}")
    
    def analyze_rs_trend(self,
                        stock_data: pd.DataFrame,
                        benchmark_data: pd.DataFrame,
                        lookback_period: int = 60) -> RSAnalysis:
        """
        分析相对强弱趋势
        
        Args:
            stock_data: 股票数据
            benchmark_data: 基准数据
            lookback_period: 回看周期
            
        Returns:
            RSAnalysis: RS分析结果
        """
        try:
            symbol = stock_data.get('symbol', 'UNKNOWN')
            
            # 计算历史RS值
            rs_history = self._calculate_rs_history(stock_data, benchmark_data, lookback_period)
            
            # 当前RS值
            current_rs = rs_history.iloc[-1] if not rs_history.empty else 0.0
            
            # 分析RS趋势
            rs_trend = self._analyze_rs_trend_direction(rs_history)
            
            # 多时间周期RS
            multi_timeframe_rs = self.calculate_multi_timeframe_rs(stock_data, benchmark_data)
            
            # 计算板块和市场RS（简化版本）
            sector_rs = current_rs  # 实际应该与板块指数比较
            market_rs = current_rs  # 实际应该与市场指数比较
            
            # 排名信息
            ranking_info = {
                'rs_percentile': 0.0,  # 需要与其他股票比较
                'sector_rank': 0,
                'market_rank': 0
            }
            
            return RSAnalysis(
                symbol=symbol,
                current_rs=current_rs,
                rs_trend=rs_trend,
                rs_history=rs_history,
                multi_timeframe_rs=multi_timeframe_rs,
                sector_rs=sector_rs,
                market_rs=market_rs,
                ranking_info=ranking_info
            )
            
        except Exception as e:
            logger.error(f"RS趋势分析失败: {e}")
            raise CalculationError(f"RS趋势分析失败: {e}")
    
    def select_dynamic_benchmark(self,
                               stock_symbol: str,
                               available_benchmarks: Dict[str, pd.DataFrame]) -> str:
        """
        动态选择基准
        
        Args:
            stock_symbol: 股票代码
            available_benchmarks: 可用基准字典
            
        Returns:
            str: 选择的基准名称
        """
        try:
            # 简化的基准选择逻辑
            # 实际应该根据股票所属行业、市值等因素选择
            
            if 'sector_index' in available_benchmarks:
                return 'sector_index'
            elif 'market_index' in available_benchmarks:
                return 'market_index'
            else:
                # 返回第一个可用基准
                return list(available_benchmarks.keys())[0]
                
        except Exception as e:
            logger.error(f"动态基准选择失败: {e}")
            return 'market_index'  # 默认基准
    
    def _calculate_momentum_score(self, data: pd.DataFrame, period: int) -> float:
        """计算动量分数"""
        try:
            if len(data) < period:
                return 0.0
            
            recent_data = data.tail(period)
            
            # 计算价格动量
            price_momentum = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[0]) - 1
            
            # 计算成交量动量
            volume_momentum = recent_data['volume'].tail(10).mean() / recent_data['volume'].mean()
            
            # 综合动量分数
            momentum_score = (price_momentum * 0.7 + (volume_momentum - 1) * 0.3)
            
            return momentum_score
            
        except Exception as e:
            logger.warning(f"动量分数计算失败: {e}")
            return 0.0
    
    def _calculate_trend_strength(self, data: pd.DataFrame, period: int) -> float:
        """计算趋势强度"""
        try:
            if len(data) < period:
                return 0.0
            
            recent_data = data.tail(period)
            
            # 计算移动平均线
            ma_short = recent_data['close'].rolling(10).mean()
            ma_long = recent_data['close'].rolling(30).mean()
            
            # 趋势强度基于价格与移动平均线的关系
            current_price = recent_data['close'].iloc[-1]
            ma_short_current = ma_short.iloc[-1]
            ma_long_current = ma_long.iloc[-1]
            
            if ma_short_current > ma_long_current and current_price > ma_short_current:
                trend_strength = (current_price - ma_long_current) / ma_long_current
            elif ma_short_current < ma_long_current and current_price < ma_short_current:
                trend_strength = (ma_long_current - current_price) / ma_long_current
            else:
                trend_strength = 0.0
            
            return min(abs(trend_strength), 1.0)  # 限制在0-1之间
            
        except Exception as e:
            logger.warning(f"趋势强度计算失败: {e}")
            return 0.0
    
    def _calculate_rs_history(self,
                            stock_data: pd.DataFrame,
                            benchmark_data: pd.DataFrame,
                            lookback_period: int) -> pd.Series:
        """计算历史RS值序列"""
        try:
            rs_values = []
            dates = []
            
            # 确保有足够的数据
            min_length = min(len(stock_data), len(benchmark_data))
            if min_length < lookback_period:
                return pd.Series()
            
            # 计算滚动RS值
            for i in range(20, min_length):  # 从第20个数据点开始
                try:
                    stock_subset = stock_data.iloc[max(0, i-20):i+1]
                    benchmark_subset = benchmark_data.iloc[max(0, i-20):i+1]
                    
                    rs_value = self.calculate_rs_value(stock_subset, benchmark_subset, min(20, len(stock_subset)))
                    rs_values.append(rs_value)
                    dates.append(stock_data.index[i])
                    
                except Exception:
                    continue
            
            return pd.Series(rs_values, index=dates)
            
        except Exception as e:
            logger.warning(f"RS历史计算失败: {e}")
            return pd.Series()
    
    def _analyze_rs_trend_direction(self, rs_history: pd.Series) -> str:
        """分析RS趋势方向"""
        try:
            if len(rs_history) < 10:
                return "stable"
            
            # 计算最近趋势
            recent_rs = rs_history.tail(10)
            trend_slope = np.polyfit(range(len(recent_rs)), recent_rs.values, 1)[0]
            
            if trend_slope > 0.01:
                return "strengthening"
            elif trend_slope < -0.01:
                return "weakening"
            else:
                return "stable"
                
        except Exception as e:
            logger.warning(f"RS趋势方向分析失败: {e}")
            return "stable"
