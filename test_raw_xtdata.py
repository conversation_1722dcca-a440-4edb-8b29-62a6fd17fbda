#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始XtData API测试脚本
直接测试XtData API的各种调用方式
"""

import sys
from datetime import datetime, timedelta

def test_basic_api():
    """测试基本API调用"""
    print("=" * 60)
    print("测试基本XtData API调用")
    print("=" * 60)
    
    try:
        import xtquant.xtdata as xtdata
        print("✅ xtquant库导入成功")
        
        # 1. 测试获取股票列表
        print("\n1. 测试获取股票列表...")
        stocks = xtdata.get_stock_list_in_sector("沪深A股")
        print(f"获取到 {len(stocks)} 只股票")
        print(f"前10只股票: {stocks[:10]}")
        
        # 2. 测试获取实时行情
        print("\n2. 测试获取实时行情...")
        test_symbol = "000001.SZ"
        quotes = xtdata.get_full_tick([test_symbol])
        print(f"实时行情数据: {quotes}")
        
        # 3. 测试不同的历史数据获取方法
        print(f"\n3. 测试历史数据获取 - {test_symbol}")
        
        # 方法1: get_market_data_ex
        print("\n方法1: get_market_data_ex")
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"日期范围: {start_date} - {end_date}")
        
        data1 = xtdata.get_market_data_ex(
            stock_list=[test_symbol],
            period='1d',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        print(f"get_market_data_ex 返回: {type(data1)}")
        if data1:
            print(f"数据键: {list(data1.keys())}")
            if test_symbol in data1:
                df = data1[test_symbol]
                print(f"DataFrame类型: {type(df)}")
                print(f"DataFrame形状: {df.shape if hasattr(df, 'shape') else 'N/A'}")
                print(f"DataFrame为空: {df.empty if hasattr(df, 'empty') else 'N/A'}")
                if hasattr(df, 'head'):
                    print("前几行数据:")
                    print(df.head())
        else:
            print("返回数据为None")
        
        # 方法2: get_market_data (如果存在)
        print("\n方法2: get_market_data")
        try:
            data2 = xtdata.get_market_data(
                stock_list=[test_symbol],
                period='1d',
                start_time=start_date,
                end_time=end_date
            )
            print(f"get_market_data 返回: {type(data2)}")
            if data2:
                print(f"数据内容: {data2}")
        except AttributeError:
            print("get_market_data 方法不存在")
        except Exception as e:
            print(f"get_market_data 调用失败: {e}")
        
        # 方法3: 尝试不同的参数组合
        print("\n方法3: 尝试不同参数组合")
        
        # 尝试更长的时间范围
        long_start = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        print(f"尝试更长时间范围: {long_start} - {end_date}")
        
        data3 = xtdata.get_market_data_ex(
            stock_list=[test_symbol],
            period='1d',
            start_time=long_start,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )
        
        if data3 and test_symbol in data3:
            df3 = data3[test_symbol]
            print(f"长时间范围数据形状: {df3.shape if hasattr(df3, 'shape') else 'N/A'}")
            print(f"长时间范围数据为空: {df3.empty if hasattr(df3, 'empty') else 'N/A'}")
        
        # 尝试不同的股票
        print("\n4. 测试不同股票")
        test_stocks = ["600000.SH", "000002.SZ", "600036.SH"]
        
        for stock in test_stocks:
            print(f"\n测试股票: {stock}")
            data = xtdata.get_market_data_ex(
                stock_list=[stock],
                period='1d',
                start_time=long_start,
                end_time=end_date,
                count=-1,
                dividend_type='none',
                fill_data=True
            )
            
            if data and stock in data:
                df = data[stock]
                has_data = not (hasattr(df, 'empty') and df.empty)
                print(f"  {stock}: {'有数据' if has_data else '无数据'}")
                if has_data and hasattr(df, 'shape'):
                    print(f"  数据形状: {df.shape}")
            else:
                print(f"  {stock}: 返回None或无此股票数据")
        
        # 5. 检查数据源状态
        print("\n5. 检查数据源状态")
        try:
            # 尝试获取一些系统信息
            print("尝试获取系统信息...")
            # 这里可以添加更多的系统状态检查
        except Exception as e:
            print(f"获取系统信息失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ xtquant库导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_download_permissions():
    """测试数据下载权限"""
    print("\n" + "=" * 60)
    print("测试数据下载权限")
    print("=" * 60)
    
    try:
        import xtquant.xtdata as xtdata
        
        # 检查是否有历史数据下载权限
        print("检查历史数据下载权限...")
        
        # 尝试下载数据
        test_symbol = "000001.SZ"
        print(f"尝试下载 {test_symbol} 的历史数据...")
        
        # 使用download_history_data方法（如果存在）
        try:
            result = xtdata.download_history_data(
                stock_list=[test_symbol],
                period='1d',
                start_time='20240101',
                end_time='20241231'
            )
            print(f"下载结果: {result}")
        except AttributeError:
            print("download_history_data 方法不存在")
        except Exception as e:
            print(f"下载失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"权限测试失败: {e}")
        return False


def main():
    """主函数"""
    print("原始XtData API测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    test1 = test_basic_api()
    test2 = test_data_download_permissions()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"基本API测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"权限测试: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if not test1:
        print("\n建议:")
        print("1. 检查MiniQMT客户端是否正常运行")
        print("2. 检查是否有历史数据权限")
        print("3. 检查网络连接")
        print("4. 重启MiniQMT客户端")


if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
