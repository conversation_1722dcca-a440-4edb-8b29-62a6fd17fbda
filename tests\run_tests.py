#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本
提供便捷的测试执行和管理功能
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_tests_in_directory(test_dir: str, pattern: str = "test_*.py", verbose: bool = False) -> bool:
    """
    运行指定目录中的测试
    
    Args:
        test_dir: 测试目录
        pattern: 文件匹配模式
        verbose: 是否显示详细输出
        
    Returns:
        bool: 是否所有测试都通过
    """
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        return False
    
    test_files = list(Path(test_dir).glob(pattern))
    if not test_files:
        print(f"⚠️ 在 {test_dir} 中未找到匹配的测试文件")
        return True
    
    print(f"\n🧪 运行 {test_dir} 中的测试...")
    print("-" * 50)
    
    success_count = 0
    total_count = len(test_files)
    
    for test_file in test_files:
        print(f"运行: {test_file.name}")
        
        try:
            # 运行测试文件
            result = subprocess.run(
                [sys.executable, str(test_file)],
                capture_output=not verbose,
                text=True,
                timeout=60  # 60秒超时
            )
            
            if result.returncode == 0:
                print(f"  ✅ 通过")
                success_count += 1
            else:
                print(f"  ❌ 失败")
                if not verbose and result.stderr:
                    print(f"     错误: {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 超时")
        except Exception as e:
            print(f"  💥 异常: {e}")
    
    print(f"\n📊 {test_dir} 测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count

def run_all_tests(verbose: bool = False) -> bool:
    """
    运行所有测试
    
    Args:
        verbose: 是否显示详细输出
        
    Returns:
        bool: 是否所有测试都通过
    """
    print("🚀 运行威科夫相对强弱选股系统测试套件")
    print("=" * 60)
    
    # 测试目录列表（按重要性排序）
    test_directories = [
        ("tests/unit", "单元测试"),
        ("tests/core", "核心功能测试"),
        ("tests/engines", "引擎测试"),
        ("tests/services", "服务测试"),
        ("tests/data_sources", "数据源测试"),
        ("tests/database", "数据库测试"),
        ("tests/ui", "用户界面测试"),
        ("tests/integration", "集成测试"),
        ("tests/performance", "性能测试"),
        ("tests/week_tests", "周测试"),
        ("tests/utils", "工具测试"),
        ("tests/config", "配置测试")
    ]
    
    results = []
    
    for test_dir, description in test_directories:
        if os.path.exists(test_dir):
            result = run_tests_in_directory(test_dir, verbose=verbose)
            results.append((description, result))
        else:
            print(f"⚠️ 跳过不存在的目录: {test_dir}")
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for description, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{description}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试套件通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查上述输出")
        return False

def run_specific_tests(test_names: List[str], verbose: bool = False) -> bool:
    """
    运行指定的测试
    
    Args:
        test_names: 测试名称列表
        verbose: 是否显示详细输出
        
    Returns:
        bool: 是否所有测试都通过
    """
    print(f"🎯 运行指定测试: {', '.join(test_names)}")
    print("-" * 50)
    
    success_count = 0
    
    for test_name in test_names:
        # 查找测试文件
        test_file = None
        for root, dirs, files in os.walk("tests"):
            for file in files:
                if file == test_name or file == f"{test_name}.py":
                    test_file = os.path.join(root, file)
                    break
            if test_file:
                break
        
        if not test_file:
            print(f"❌ 未找到测试文件: {test_name}")
            continue
        
        print(f"运行: {test_file}")
        
        try:
            result = subprocess.run(
                [sys.executable, test_file],
                capture_output=not verbose,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print(f"  ✅ 通过")
                success_count += 1
            else:
                print(f"  ❌ 失败")
                if not verbose and result.stderr:
                    print(f"     错误: {result.stderr.strip()}")
                    
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 超时")
        except Exception as e:
            print(f"  💥 异常: {e}")
    
    print(f"\n📊 指定测试结果: {success_count}/{len(test_names)} 通过")
    return success_count == len(test_names)

def list_available_tests():
    """列出所有可用的测试"""
    print("📋 可用的测试文件:")
    print("=" * 50)
    
    for root, dirs, files in os.walk("tests"):
        test_files = [f for f in files if f.startswith("test_") and f.endswith(".py")]
        if test_files:
            rel_path = os.path.relpath(root, "tests")
            if rel_path == ".":
                rel_path = "根目录"
            print(f"\n📁 {rel_path}:")
            for test_file in sorted(test_files):
                print(f"  • {test_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="威科夫相对强弱选股系统测试运行器")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--list", action="store_true", help="列出所有可用测试")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细输出")
    parser.add_argument("--dir", help="运行指定目录中的测试")
    parser.add_argument("tests", nargs="*", help="要运行的特定测试文件名")
    
    args = parser.parse_args()
    
    if args.list:
        list_available_tests()
        return
    
    if args.dir:
        success = run_tests_in_directory(args.dir, verbose=args.verbose)
        sys.exit(0 if success else 1)
    
    if args.tests:
        success = run_specific_tests(args.tests, verbose=args.verbose)
        sys.exit(0 if success else 1)
    
    if args.all:
        success = run_all_tests(verbose=args.verbose)
        sys.exit(0 if success else 1)
    
    # 默认显示帮助
    parser.print_help()
    print("\n💡 使用示例:")
    print("  python run_tests.py --all              # 运行所有测试")
    print("  python run_tests.py --list             # 列出所有测试")
    print("  python run_tests.py --dir tests/ui     # 运行UI测试")
    print("  python run_tests.py test_ui_simple     # 运行特定测试")

if __name__ == "__main__":
    main()
