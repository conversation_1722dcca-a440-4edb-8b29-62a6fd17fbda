"""
第14周第2天功能测试

测试功能特定向导、工具提示增强、上下文帮助等新功能
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from src.ui.utils.feature_guides import (
    FeatureGuideManager, WyckoffAnalysisGuide, 
    RelativeStrengthGuide, SmartSelectionGuide
)
from src.ui.utils.tooltip_enhancer import ToolTipEnhancer, TOOLTIP_CONTENTS
from src.ui.utils.context_help import ContextHelpManager, CONTEXT_HELP_CONTENT
from src.ui.main_window import MainWindow


class TestFeatureGuides(unittest.TestCase):
    """功能向导测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        self.feature_guide_manager = FeatureGuideManager(self.main_window)
        
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        
    def test_feature_guide_manager_creation(self):
        """测试功能向导管理器创建"""
        self.assertIsNotNone(self.feature_guide_manager)
        self.assertEqual(self.feature_guide_manager.main_window, self.main_window)
        self.assertEqual(len(self.feature_guide_manager.active_guides), 0)
        
    def test_wyckoff_guide_creation(self):
        """测试威科夫分析向导创建"""
        guide = WyckoffAnalysisGuide(self.main_window)
        
        self.assertIsNotNone(guide)
        self.assertEqual(guide.feature_name, "威科夫分析")
        self.assertGreater(len(guide.steps), 0)
        
        # 验证向导步骤内容
        for step in guide.steps:
            self.assertIn('title', step)
            self.assertIn('content', step)
            self.assertGreater(len(step['content']), 50)  # 确保内容充实
            
    def test_rs_guide_creation(self):
        """测试相对强弱分析向导创建"""
        guide = RelativeStrengthGuide(self.main_window)
        
        self.assertIsNotNone(guide)
        self.assertEqual(guide.feature_name, "相对强弱分析")
        self.assertGreater(len(guide.steps), 0)
        
        # 验证向导步骤内容
        for step in guide.steps:
            self.assertIn('title', step)
            self.assertIn('content', step)
            self.assertGreater(len(step['content']), 50)
            
    def test_selection_guide_creation(self):
        """测试智能选股向导创建"""
        guide = SmartSelectionGuide(self.main_window)
        
        self.assertIsNotNone(guide)
        self.assertEqual(guide.feature_name, "智能选股")
        self.assertGreater(len(guide.steps), 0)
        
        # 验证向导步骤内容
        for step in guide.steps:
            self.assertIn('title', step)
            self.assertIn('content', step)
            self.assertGreater(len(step['content']), 50)
            
    def test_guide_navigation(self):
        """测试向导导航功能"""
        guide = WyckoffAnalysisGuide(self.main_window)
        
        initial_step = guide.current_step
        self.assertEqual(initial_step, 0)
        
        # 测试下一步
        guide._next_step()
        self.assertEqual(guide.current_step, initial_step + 1)
        
        # 测试上一步
        guide._prev_step()
        self.assertEqual(guide.current_step, initial_step)
        
    def test_guide_content_quality(self):
        """测试向导内容质量"""
        guides = [
            WyckoffAnalysisGuide(self.main_window),
            RelativeStrengthGuide(self.main_window),
            SmartSelectionGuide(self.main_window)
        ]
        
        for guide in guides:
            with self.subTest(guide=guide.feature_name):
                # 验证步骤数量合理
                self.assertGreaterEqual(len(guide.steps), 3)
                self.assertLessEqual(len(guide.steps), 8)
                
                # 验证每个步骤都有实质内容
                for step in guide.steps:
                    self.assertGreater(len(step['content']), 100)
                    self.assertIn('•', step['content'])  # 包含要点列表


class TestToolTipEnhancer(unittest.TestCase):
    """工具提示增强器测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        self.tooltip_enhancer = ToolTipEnhancer(self.main_window)
        
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        
    def test_tooltip_enhancer_creation(self):
        """测试工具提示增强器创建"""
        self.assertIsNotNone(self.tooltip_enhancer)
        self.assertEqual(self.tooltip_enhancer.main_window, self.main_window)
        self.assertEqual(len(self.tooltip_enhancer.enhanced_tooltips), 0)
        
    def test_enhanced_tooltip_addition(self):
        """测试增强型工具提示添加"""
        button = QPushButton("测试按钮")
        
        self.tooltip_enhancer.add_enhanced_tooltip(
            button, "测试标题", "测试内容", "Ctrl+T"
        )
        
        self.assertIn(button, self.tooltip_enhancer.enhanced_tooltips)
        tooltip = self.tooltip_enhancer.enhanced_tooltips[button]
        self.assertEqual(tooltip.title, "测试标题")
        self.assertEqual(tooltip.content, "测试内容")
        self.assertEqual(tooltip.shortcut, "Ctrl+T")
        
    def test_tooltip_contents_completeness(self):
        """测试工具提示内容完整性"""
        required_tooltips = [
            'refresh_data', 'wyckoff_analysis', 'rs_analysis',
            'smart_selection', 'export_data', 'system_settings'
        ]
        
        for tooltip_key in required_tooltips:
            with self.subTest(tooltip=tooltip_key):
                self.assertIn(tooltip_key, TOOLTIP_CONTENTS)
                content = TOOLTIP_CONTENTS[tooltip_key]
                
                self.assertIn('title', content)
                self.assertIn('content', content)
                self.assertIn('shortcut', content)
                
                self.assertGreater(len(content['title']), 0)
                self.assertGreater(len(content['content']), 20)
                self.assertGreater(len(content['shortcut']), 0)


class TestContextHelp(unittest.TestCase):
    """上下文帮助测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.main_window = QMainWindow()
        self.context_help_manager = ContextHelpManager(self.main_window)
        
    def tearDown(self):
        """清理测试"""
        self.main_window.close()
        
    def test_context_help_manager_creation(self):
        """测试上下文帮助管理器创建"""
        self.assertIsNotNone(self.context_help_manager)
        self.assertEqual(self.context_help_manager.main_window, self.main_window)
        self.assertEqual(len(self.context_help_manager.active_help_dialogs), 0)
        
    def test_context_help_content_completeness(self):
        """测试上下文帮助内容完整性"""
        required_contexts = [
            'main_window', 'stock_data', 'wyckoff_analysis'
        ]
        
        for context in required_contexts:
            with self.subTest(context=context):
                self.assertIn(context, CONTEXT_HELP_CONTENT)
                content = CONTEXT_HELP_CONTENT[context]
                
                self.assertIn('title', content)
                self.assertIn('overview', content)
                self.assertIn('steps', content)
                self.assertIn('faq', content)
                self.assertIn('shortcuts', content)
                
                # 验证内容质量
                self.assertGreater(len(content['title']), 0)
                self.assertGreater(len(content['overview']), 50)
                self.assertIsInstance(content['steps'], list)
                self.assertIsInstance(content['faq'], list)
                self.assertIsInstance(content['shortcuts'], list)
                
    def test_context_detection(self):
        """测试上下文检测"""
        # 模拟主窗口的tab_widget
        mock_tab_widget = Mock()
        mock_tab_widget.currentIndex.return_value = 0
        self.main_window.tab_widget = mock_tab_widget
        
        context = self.context_help_manager.get_current_context()
        self.assertEqual(context, 'stock_data')
        
        # 测试不同标签页
        mock_tab_widget.currentIndex.return_value = 1
        context = self.context_help_manager.get_current_context()
        self.assertEqual(context, 'analysis_results')


class TestMainWindowIntegration(unittest.TestCase):
    """主窗口集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_main_window_ux_components_initialization(self):
        """测试主窗口UX组件初始化"""
        # 使用Mock避免完整初始化
        with patch('src.ui.main_window.MainWindow._init_ui'), \
             patch('src.ui.main_window.MainWindow._init_menu_bar'), \
             patch('src.ui.main_window.MainWindow._init_tool_bar'), \
             patch('src.ui.main_window.MainWindow._init_status_bar'), \
             patch('src.ui.main_window.MainWindow._init_tabs'), \
             patch('src.ui.main_window.MainWindow._connect_signals'), \
             patch('src.ui.main_window.MainWindow._show_welcome_guide_if_needed'):

            main_window = MainWindow()

            # 验证UX组件已初始化
            self.assertIsNotNone(main_window.ux_enhancer)
            self.assertIsNotNone(main_window.guide_manager)
            self.assertIsNotNone(main_window.feature_guide_manager)
            self.assertIsNotNone(main_window.tooltip_enhancer)
            self.assertIsNotNone(main_window.context_help_manager)


class TestUserExperienceWorkflow(unittest.TestCase):
    """用户体验工作流程测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_feature_guide_workflow(self):
        """测试功能向导工作流程"""
        main_window = QMainWindow()
        feature_guide_manager = FeatureGuideManager(main_window)
        
        # 测试显示威科夫向导
        feature_guide_manager.show_wyckoff_guide()
        self.assertIn("wyckoff", feature_guide_manager.active_guides)
        
        # 测试显示RS向导
        feature_guide_manager.show_rs_guide()
        self.assertIn("rs", feature_guide_manager.active_guides)
        
        # 测试显示选股向导
        feature_guide_manager.show_selection_guide()
        self.assertIn("selection", feature_guide_manager.active_guides)
        
        main_window.close()
        
    def test_tooltip_enhancement_workflow(self):
        """测试工具提示增强工作流程"""
        main_window = QMainWindow()
        tooltip_enhancer = ToolTipEnhancer(main_window)
        
        # 创建测试按钮
        button = QPushButton("测试")
        
        # 添加增强型提示
        tooltip_enhancer.add_enhanced_tooltip(
            button, "测试功能", "这是一个测试功能", "F1"
        )
        
        # 验证提示已添加
        self.assertIn(button, tooltip_enhancer.enhanced_tooltips)
        
        main_window.close()
        
    def test_context_help_workflow(self):
        """测试上下文帮助工作流程"""
        main_window = QMainWindow()
        context_help_manager = ContextHelpManager(main_window)
        
        # 测试显示主窗口帮助
        context_help_manager.show_context_help('main_window')
        self.assertIn('main_window', context_help_manager.active_help_dialogs)
        
        # 测试显示股票数据帮助
        context_help_manager.show_context_help('stock_data')
        self.assertIn('stock_data', context_help_manager.active_help_dialogs)
        
        main_window.close()


class TestAccessibilityAndUsability(unittest.TestCase):
    """可访问性和可用性测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def test_keyboard_shortcuts_coverage(self):
        """测试键盘快捷键覆盖"""
        # 验证主要功能都有快捷键
        expected_shortcuts = {
            'F5': 'refresh_data',
            'Ctrl+W': 'wyckoff_analysis',
            'Ctrl+R': 'rs_analysis',
            'Ctrl+S': 'smart_selection',
            'Ctrl+E': 'export_data',
            'F1': 'context_help'
        }
        
        for shortcut, function in expected_shortcuts.items():
            # 验证快捷键在工具提示内容中有定义
            found = False
            for content in TOOLTIP_CONTENTS.values():
                if content.get('shortcut') == shortcut:
                    found = True
                    break
            
            # 或者在上下文帮助的快捷键列表中
            if not found:
                for help_content in CONTEXT_HELP_CONTENT.values():
                    for shortcut_info in help_content.get('shortcuts', []):
                        if shortcut_info.get('key') == shortcut:
                            found = True
                            break
                    if found:
                        break
            
            self.assertTrue(found, f"快捷键 {shortcut} 未在帮助系统中定义")
            
    def test_help_content_accessibility(self):
        """测试帮助内容可访问性"""
        for context, content in CONTEXT_HELP_CONTENT.items():
            with self.subTest(context=context):
                # 验证内容结构完整
                self.assertIn('title', content)
                self.assertIn('overview', content)
                self.assertIn('steps', content)
                
                # 验证步骤列表不为空（对于主要功能）
                if context in ['stock_data', 'wyckoff_analysis']:
                    self.assertGreater(len(content['steps']), 0)
                    
                # 验证FAQ格式正确
                for faq_item in content.get('faq', []):
                    self.assertIn('question', faq_item)
                    self.assertIn('answer', faq_item)


if __name__ == '__main__':
    unittest.main()
