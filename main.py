#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
威科夫相对强弱选股系统 - 主程序入口

启动PyQt6桌面应用程序
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ui.main_window import MainWindow
from src.utils.logger import get_logger, setup_logger
from src.config.environment import Environment
from src.data_sources.manager import DataSourceManager
from src.data_sources.xtdata_adapter import XtDataAdapter

logger = get_logger(__name__)


class XDQRApplication(QApplication):
    """XDQR应用程序类"""

    def __init__(self, argv):
        """初始化应用程序"""
        super().__init__(argv)

        # 设置应用程序信息
        self.setApplicationName("威科夫相对强弱选股系统")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("量化投资系统")
        self.setOrganizationDomain("xdqr.com")

        # 设置应用程序图标和字体
        self._setup_app_properties()

        # 初始化环境
        self.environment = Environment()

        # 数据源管理器
        self.data_manager = None

        # 主窗口
        self.main_window = None

        logger.info("XDQR应用程序初始化完成")

    def _setup_app_properties(self):
        """设置应用程序属性"""
        # 设置默认字体
        font = QFont("Microsoft YaHei UI", 9)
        self.setFont(font)

        # 设置样式
        self.setStyle('Fusion')  # 使用Fusion样式

        logger.debug("应用程序属性设置完成")

    def show_splash_screen(self):
        """显示启动画面"""
        # 创建启动画面
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.GlobalColor.white)

        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.SplashScreen)

        # 显示启动信息
        splash.showMessage(
            "威科夫相对强弱选股系统\n\n正在启动...",
            Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
            Qt.GlobalColor.black
        )

        splash.show()

        # 处理事件，确保启动画面显示
        self.processEvents()

        return splash

    def initialize_system(self):
        """初始化系统"""
        try:
            # 确保必要的目录存在
            self.environment.ensure_paths_exist()

            # 初始化日志系统
            setup_logger(
                name="xdqr",
                level="INFO",
                log_to_file=True,
                log_to_console=True
            )

            logger.info("系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False

    def initialize_data_sources(self):
        """初始化数据源"""
        try:
            logger.info("正在初始化数据源...")

            # 创建数据源管理器
            self.data_manager = DataSourceManager()

            # 加载配置文件
            from src.config.config_manager import ConfigManager
            config_manager = ConfigManager()
            # 指定配置文件的完整路径
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
            config_data = config_manager.load_config("config", config_path)

            # 检查xtdata配置
            xtdata_config = config_data.get("data_sources", {}).get("xtdata", {})
            if xtdata_config.get("enabled", True):
                logger.info("正在初始化XtData数据源...")

                # 创建数据源配置对象
                from src.data_sources.base import DataSourceConfig
                config = DataSourceConfig(
                    name=xtdata_config.get("name", "XtData"),
                    enabled=xtdata_config.get("enabled", True),
                    timeout=xtdata_config.get("timeout", 30),
                    retry_times=xtdata_config.get("retry_times", 3),
                    auto_reconnect=xtdata_config.get("auto_reconnect", True),
                    config=xtdata_config.get("config", {})
                )

                # 创建XtData数据源
                xtdata_source = XtDataAdapter(config)

                # 添加到管理器
                self.data_manager.add_source("xtdata", xtdata_source, priority=10)

                # 尝试连接
                try:
                    if self.data_manager.test_connection("xtdata"):
                        logger.info("✅ XtData数据源连接成功")
                        return True
                    else:
                        logger.warning("⚠️ XtData数据源连接失败")
                        return False
                except Exception as e:
                    logger.error(f"❌ XtData连接异常: {e}")
                    return False
            else:
                logger.info("XtData数据源已禁用")
                return False

        except Exception as e:
            logger.error(f"数据源初始化失败: {e}")
            return False

    def create_main_window(self):
        """创建主窗口"""
        try:
            from src.services.data_service import DataService

            # 获取数据下载配置
            from src.config.config_manager import ConfigManager
            config_manager = ConfigManager()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
            config_data = config_manager.load_config("config", config_path)

            download_config = config_data.get('data_download', {})
            use_real_data = download_config.get('use_real_data', True)

            # 创建数据服务
            data_service = DataService(
                data_source_manager=self.data_manager,
                use_real_data=use_real_data
            )

            self.main_window = MainWindow(
                data_manager=self.data_manager,
                data_service=data_service
            )

            # 连接应用程序信号
            self._connect_signals()

            logger.info("主窗口创建完成")
            return True

        except Exception as e:
            logger.error(f"主窗口创建失败: {e}")
            return False

    def _connect_signals(self):
        """连接信号"""
        if self.main_window:
            # 连接数据刷新信号
            self.main_window.data_refresh_requested.connect(self._on_data_refresh)

            # 连接分析请求信号
            self.main_window.analysis_requested.connect(self._on_analysis_request)

            # 连接选股请求信号
            self.main_window.selection_requested.connect(self._on_selection_request)

    def _on_data_refresh(self):
        """处理数据刷新请求"""
        logger.info("收到数据刷新请求")
        # 这里可以添加实际的数据刷新逻辑

        if self.main_window:
            self.main_window.set_status("数据刷新完成", timeout=3000)

    def _on_analysis_request(self, symbol: str):
        """处理分析请求"""
        logger.info(f"收到分析请求：{symbol}")
        # 这里可以添加实际的分析逻辑

        if self.main_window:
            self.main_window.set_status(f"正在分析 {symbol}...")

    def _on_selection_request(self, params: dict):
        """处理选股请求"""
        logger.info(f"收到选股请求：{params}")
        # 这里可以添加实际的选股逻辑

        if self.main_window:
            self.main_window.set_status("正在执行选股...")

    def run(self):
        """运行应用程序"""
        # 显示启动画面
        splash = self.show_splash_screen()

        try:
            # 初始化系统
            splash.showMessage("正在初始化系统...", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.processEvents()

            if not self.initialize_system():
                raise Exception("系统初始化失败")

            # 初始化数据源
            splash.showMessage("正在连接数据源...", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.processEvents()

            data_connected = self.initialize_data_sources()
            if data_connected:
                splash.showMessage("数据源连接成功", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            else:
                splash.showMessage("数据源连接失败，使用离线模式", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.processEvents()

            # 创建主窗口
            splash.showMessage("正在创建主界面...", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.processEvents()

            if not self.create_main_window():
                raise Exception("主窗口创建失败")

            # 显示主窗口
            splash.showMessage("启动完成", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.processEvents()

            # 延迟关闭启动画面
            QTimer.singleShot(1000, splash.close)
            QTimer.singleShot(1200, self.main_window.show)

            logger.info("应用程序启动成功")

            # 运行事件循环
            return self.exec()

        except Exception as e:
            splash.close()
            logger.error(f"应用程序启动失败: {e}")

            # 显示错误对话框
            QMessageBox.critical(
                None,
                "启动失败",
                f"应用程序启动失败：\n{str(e)}\n\n请检查日志文件获取详细信息。"
            )

            return 1


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = XDQRApplication(sys.argv)

        # 运行应用程序
        exit_code = app.run()

        logger.info(f"应用程序退出，退出码：{exit_code}")
        sys.exit(exit_code)

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()